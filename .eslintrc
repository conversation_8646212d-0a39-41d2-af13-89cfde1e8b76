{
  "parser": "@typescript-eslint/parser",
  "extends": ["plugin:@typescript-eslint/recommended"],
  "plugins": ["simple-import-sort", "unused-imports", "prettier"],
  "parserOptions": {
    "project": "./tsconfig.json",
    "ecmaVersion": 2024
  },
  "env": {
    "browser": true,
    "jest": true,
    "mocha": true,
    "node": true,
    "es6": true
  },
  "rules": {
      "no-undef": "error",
      "prettier/prettier": ["error", { "endOfLine": "auto" }, { "usePrettierrc": true }],
      "simple-import-sort/imports": "error",
      "simple-import-sort/exports": "error",
      "unused-imports/no-unused-imports": "error",
      "no-multiple-empty-lines": ["error", { "max": 1, "maxEOF": 0 }],
      "semi": ["error", "never"],
      // "indent": ["error", 2, { "SwitchCase": 1 }],
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          "args": "all",
          "argsIgnorePattern": "^_",
          "caughtErrors": "all",
          "caughtErrorsIgnorePattern": "^_",
          "destructuredArrayIgnorePattern": "^_",
          "varsIgnorePattern": "^_",
          "ignoreRestSiblings": true
        }
      ],
      "@typescript-eslint/naming-convention": [
        "warn",
        {
          "selector": "enumMember",
          "format": ["StrictPascalCase"],
        }
      ]
  },
  "overrides": [
    {
      "files": [
        "**/*.js",
        "**/*.ts"
      ],
      "env": {
        "jest": true
      },
      "rules": {
        "simple-import-sort/imports": [
          "error",
          {
            "groups": [
              // `react` first, `next` second, then packages starting with a character
              [
                "reflect-metadata",
                "./instrument",
                "^react$",
                "^next",
                "^@next",
                "^[a-z]"
              ],
              // Packages starting with `@/`
              [
                "^@/"
              ],
              // Imports starting with `../`
              [
                "^\\.\\.(?!/?$)",
                "^\\.\\./?$"
              ],
              // Imports starting with `./`
              [
                "^\\./(?=.*/)(?!/?$)",
                "^\\.(?!/?$)",
                "^\\./?$"
              ]
            ]
          }
        ]
      }
    }
  ],
  "globals": {
   
  }
}
