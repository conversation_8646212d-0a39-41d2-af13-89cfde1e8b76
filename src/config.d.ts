import { ContextUser } from './index'

export interface IConfigApp {
  slack: {
    token: string
    channels: {
      messages: string
      newAccount: string
      newStripeAccount: string
      videoUpload: string
      externalLinks: string
      boost: string
      deleteAccount: string
      agentInvitation: string
      dbUserAgency: string
      shadowAccountClaim: string
      gameStatsNotifications: string
      dbProfessionals: string
    }
  }
  geoip: {
    accountId: string
    key: string
  }
  aws: {
    region: string
    assetsBucket: string
    emailFrom: string
    sns: {
      androidArn: string
      iosArn: string
      iosSandboxArn: string
    }
    sqs: {
      segmentationQueueUrl: string
    }
  }
  mongo: {
    url: string
    autoSearchIndex: boolean
  }
  cache: {
    memcached: {
      url: string
    }
  }
  port: number
  logging: {
    level: string
  }
  jwt: {
    secret: string
    expiresIn: string
  }
  assetsUrl: string
  webUrl: string
  domain: string
  amplitude: {
    apiKey: string
  }
  stripe: {
    secretKey: string
    publishableKey: string
    webhookConnectSecret: string
    url: string
  }
  emailTemplates: {
    VerifyEmail: string
    ContactMessage: string
    BoostProfileSender: string
    BoostProfileReceiver: string
    AgentInvitation: string
    UnreadMessage: string
  }
  ep: {
    apiKey: string
  }
  pushNotifications: {
    delayInMinutes: number
  }
  segmentation: {
    url: string
  }
  openai: {
    apiKey: string
  }
  getstream: {
    apiKey: string
    apiSecret: string
    appId: string
  }
  sentry: {
    dsn: string
  }
  clientStaticConfig: {
    inviteContest: {
      enabled: boolean
    }
  }
  oxylabs: {
    username: string
    password: string
    host: string
    port: number
  }
}

declare module 'config' {
  interface IConfig extends IConfigApp {
    // Add any additional properties or methods here
  }
}

declare module '@fastify/request-context' {
  interface RequestContextData {
    user: ContextUser
  }
}
