import axios from 'axios'
import config from 'config'
import pino from 'pino'
import Container, { Service } from 'typedi'

import { UserModel, WallVideoModel } from '../../modules/user/models'
import { VideoSegmentationMessageBody } from '../../modules/user/types/video'
import { MediaConverter } from '../../services/mediaConverter'
import { PushNotificationService, PushNotificationType } from '../../services/pushNotification'
import { S3Service } from '../../services/s3'

@Service()
export class VideoSegmentationConsumer {
  private s3Service: S3Service
  private pushService: PushNotificationService
  private logger: pino.Logger
  private mediaConverter: MediaConverter

  constructor() {
    this.s3Service = Container.get('s3.service')
    this.pushService = Container.get('pushNotification.service')
    this.logger = Container.get('app.logger')
    this.mediaConverter = Container.get('mediaConverter.service')
  }

  public async process(messageBody: VideoSegmentationMessageBody): Promise<void> {
    const wallVideo = await WallVideoModel.findOne({ _id: messageBody.videoId })
    const user = await UserModel.findOne({ _id: messageBody.userId })

    if (!wallVideo) {
      throw new Error('Wall video not found')
    }

    if (!user) {
      throw new Error('User not found')
    }

    const startTime = new Date().getTime()

    const segmentationVideoKey = `videos/${messageBody.userId}/wall-video/${messageBody.videoId}-segmentation.mp4`

    const presignedUrl = await this.s3Service.createPresignedUrl(
      segmentationVideoKey,
      'video/mp4',
      60 * 60 * 24 * 7 // 7 days is limit for video segmentation
    )

    const videoPath = `${config.assetsUrl}${wallVideo.path}`
    let queryParams = `frameIndex=${wallVideo.segmentation.frame}&url=${videoPath}&x=${Math.round(wallVideo.segmentation.selectionX)}&y=${Math.round(wallVideo.segmentation.selectionY)}&fps=${wallVideo.frameRate}&presignedUrl=${encodeURIComponent(presignedUrl)}`

    if (wallVideo.repeat) {
      const repeatStart = wallVideo.repeat.end
      // repeat start and end is for video before replay was added to the video
      // therefore we need to calculate correct end time
      const repeatEnd = wallVideo.repeat.end + (wallVideo.repeat.end - wallVideo.repeat.start) * 2

      queryParams += `&repeatStart=${repeatStart}&repeatEnd=${repeatEnd}`
    }

    // call axios to process the message
    const response = await axios.get(`${config.segmentation.url}/process-video?${queryParams}`, {
      headers: {
        'Content-Type': 'application/json',
      },
    })

    const endTime = new Date().getTime()
    this.logger.info(`Video segmentation took ${(endTime - startTime) / 1000}s`)

    if (response.status !== 200) {
      throw new Error('Failed to process the video')
    }

    // upload segmentation result to s3
    const segmentation = response.data
    const segmentationPath = `videos/${messageBody.userId}/wall-video/${messageBody.videoId}-segmentation.json`

    // generate HLS video with segmentation
    wallVideo.segmentation.videoHlsPath = `videos/${messageBody.userId}/wall-video/${messageBody.videoId}-segmentation-hls`
    wallVideo.segmentation.videoPath = segmentationVideoKey

    await this.mediaConverter.createHLSVideo(wallVideo, true)

    // convert json to buffer
    await this.s3Service.uploadFromBuffer(
      segmentationPath,
      Buffer.from(JSON.stringify(segmentation))
    )

    // update wall video with segmentation path
    wallVideo.segmentation.path = segmentationPath
    wallVideo.segmentation.finished = true

    wallVideo.segmentation.createdAt = new Date()
    await wallVideo.save()

    // send push notification to user
    await this.pushService.sendPushNotification(user, {
      title: '🎥 Your Clip is live!',
      message: 'Your Clip has been published. View it now and share your amazing play!',
      type: PushNotificationType.VIDEO_SEGMENTATION_FINISHED,
      forcePush: true,
      params: {
        destinationPath: '(app)/(tabs)/?initialTab=clips',
      },
    })
  }
}
