import config from 'config'
import { connect as streamConnect } from 'getstream'
import { OAuth2Client } from 'google-auth-library'
import pino, { Logger } from 'pino'
import { StreamChat } from 'stream-chat'
import <PERSON><PERSON> from 'stripe'
import Container from 'typedi'

import { VideoSegmentationConsumer } from './consumers/video-segmentation/consumer'
import { wrapLogger } from './helpers/logger'
import EliteProspectsService from './modules/stats/services/eliteprospects/ep'
import GameSyncerService from './modules/stats/services/gameSyncer.service'
import LeijonatService from './modules/stats/services/leijonat/leijonat'
import { Scraper } from './modules/stats/services/scraper/scraper'
import UserStatsSyncerService from './modules/stats/services/userStatsSyncer.service'
import Analytics from './services/analytics'
import { EmailService } from './services/email'
import { MediaConverter } from './services/mediaConverter'
import { PushNotificationService } from './services/pushNotification'
import { S3Service } from './services/s3'
import { SlackService } from './services/slack'
import { SNSService } from './services/sns'
import { SQSService } from './services/sqs'
import { AxiosFetcher } from './services/webcrawler/fetchers/axiosFetcher'
import { PuppeteerFetcher } from './services/webcrawler/fetchers/puppeteerFetcher'

import * as AWSMediaConverter from '@aws-sdk/client-mediaconvert'
import { S3Client } from '@aws-sdk/client-s3'
import { SESClient } from '@aws-sdk/client-ses'
import { SNS } from '@aws-sdk/client-sns'
import { SQSClient } from '@aws-sdk/client-sqs'
import { LogLevel, WebClient } from '@slack/web-api'
import { MemcachedElastiCacheStorage, useStorage } from '@t.voslar/ts-cache-decorator'

export const cacheStorage = new MemcachedElastiCacheStorage(config.cache.memcached.url, {})
useStorage(cacheStorage)

const googleOAuth2 = new OAuth2Client()
Container.set({ id: 'google.oauth2', factory: () => googleOAuth2 })

const SlackClient = new WebClient(config.slack.token, {
  logLevel: LogLevel.INFO,
})
Container.set({ id: 'slack.client', factory: () => SlackClient })

export const logger = wrapLogger(
  pino({
    level: config.get('logging.level'),
  })
) as Logger
Container.set({ id: 'app.logger', factory: () => logger })

const axiosFetcher = new AxiosFetcher()
Container.set({ id: 'axios.fetcher', factory: () => axiosFetcher })

const puppeteerFetcher = new PuppeteerFetcher()
Container.set({ id: 'puppeteer.fetcher', factory: () => puppeteerFetcher })

const s3Client = new S3Client({ region: config.aws.region })
Container.set({ id: 's3.client', factory: () => s3Client })

const sesClient = new SESClient({ region: config.aws.region })
Container.set({ id: 'ses.client', factory: () => sesClient })

const snsClient = new SNS({ region: config.aws.region })
Container.set({ id: 'sns.client', factory: () => snsClient })

const sqsClient = new SQSClient({ region: config.aws.region })
Container.set({ id: 'sqs.client', factory: () => sqsClient })

const emailService = new EmailService()
Container.set({ id: 'email.service', factory: () => emailService })

const slackService = new SlackService()
Container.set({ id: 'slack.service', factory: () => slackService })

const snsSService = new SNSService()
Container.set({ id: 'sns.service', factory: () => snsSService })

const sqsService = new SQSService()
Container.set({ id: 'sqs.service', factory: () => sqsService })

const s3Service = new S3Service()
Container.set({ id: 's3.service', factory: () => s3Service })

const stripeClient = new Stripe(config.stripe.secretKey)
Container.set({ id: 'stripe.client', factory: () => stripeClient })

const analyticsService = new Analytics()
Container.set({ id: 'analytics.service', factory: () => analyticsService })

const mediaConverter = new AWSMediaConverter.MediaConvertClient({ region: config.aws.region })
Container.set({ id: 'mediaConverter.client', factory: () => mediaConverter })

const pushNotificationService = new PushNotificationService()
Container.set({ id: 'pushNotification.service', factory: () => pushNotificationService })

const mediaConverterService = new MediaConverter()
Container.set({ id: 'mediaConverter.service', factory: () => mediaConverterService })

const epService = new EliteProspectsService()
Container.set({ id: 'ep.service', factory: () => epService })

const leijonatService = new LeijonatService()
Container.set({ id: 'leijonat.service', factory: () => leijonatService })

const scraperService = new Scraper()
Container.set({ id: 'scraper', factory: () => scraperService })

Container.set({ id: 'userStatsSyncer.service', factory: () => new UserStatsSyncerService() })

const gameSyncerService = new GameSyncerService()
Container.set({ id: 'gameSyncer.service', factory: () => gameSyncerService })

const videoSegmentationConsumer = new VideoSegmentationConsumer()
Container.set({ id: 'videoSegmentation.consumer', factory: () => videoSegmentationConsumer })

Container.set({
  id: 'streamChat.client',
  factory: () => StreamChat.getInstance(config.getstream.apiKey, config.getstream.apiSecret),
})

Container.set({
  id: 'streamFeed.client',
  factory: () =>
    streamConnect(config.getstream.apiKey, config.getstream.apiSecret, config.getstream.appId),
})
