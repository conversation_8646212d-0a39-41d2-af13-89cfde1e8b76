import 'reflect-metadata'
import './instrument'
import axios from 'axios'
import config from 'config'
import DataLoader from 'dataloader'
import Fastify, { FastifyRequest } from 'fastify'
import fingerprint from 'fastify-fingerprint'
import jwt from 'jsonwebtoken'
import { Types } from 'mongoose'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AuthenticationError,
  AuthorizationError,
  buildSchema,
  MiddlewareFn,
} from 'type-graphql'
import { Container } from 'typedi'

import {
  closeMongoDBConnection,
  connectToMongoDB,
  getMongoDBConnection,
} from './helpers/mongoose.connection'
import { ObjectIdScalar } from './helpers/object.id.scalar'
import { AdminMutationResolver, AdminResolver } from './modules/admin/admin.resolver'
import AdminUserResolver from './modules/admin/adminUser.resolver'
import { createOrganizationLoader, Organization } from './modules/stats/models/organization'
import { createTeamLoader, Team } from './modules/stats/models/team'
import { createUserInfo<PERSON>oader, UserInfo<PERSON><PERSON> } from './modules/stats/models/userInfo.dataloader'
import { ExternalConnectionResolver } from './modules/stats/resolvers/externalConnection.resolver'
import GameResolver from './modules/stats/resolvers/game.resolver'
import LeagueResolver from './modules/stats/resolvers/league.resolver'
import LeagueStatsResolver from './modules/stats/resolvers/leagueStats.resolver'
import OrganizationResolver from './modules/stats/resolvers/organization.resolver'
import PlayersStatsResolver from './modules/stats/resolvers/playersStats.resolver'
import TeamResolver from './modules/stats/resolvers/team.resolver'
import UserStatsResolver from './modules/stats/resolvers/userStats.resolver'
import StripeAccountResolver from './modules/stripe/resolvers/account.resolver'
import StripeBalanceResolver from './modules/stripe/resolvers/balance.resolver'
import { UserModel } from './modules/user/models'
import { Agency } from './modules/user/models/agency'
import { createAgencyLoader } from './modules/user/models/agency.dataloader'
import { Role, SubRole, User } from './modules/user/models/user'
import { createUserLoader } from './modules/user/models/user.dataloader'
import {
  createUserSubscriptionLoader,
  UserSubscription,
} from './modules/user/models/userSubscription'
import AgencyResolver from './modules/user/resolvers/agency.resolver'
import AnalyticsResolver from './modules/user/resolvers/analytics.resolver'
import BoostResolver from './modules/user/resolvers/boost.resolver'
import { ClientStaticConfigResolver } from './modules/user/resolvers/clientStaticConfig'
import ContactResolver from './modules/user/resolvers/contact.resolver'
import PlayerPerformanceLikeResolver from './modules/user/resolvers/player.likes.resolver'
import SuggestInvitesResolver from './modules/user/resolvers/suggestInvites.resolver'
import UserResolver from './modules/user/resolvers/user.resolver'
import { UserBioResolver } from './modules/user/resolvers/userBio.resolver'
import UserResolverFamily from './modules/user/resolvers/userFamily.resolver'
import { UserSubRoleSpecificationResolver } from './modules/user/resolvers/userSubRoleSpecification.resolver'
import { UserViewResponseResolver } from './modules/user/resolvers/userViewResponse'
import WallVideoResolver from './modules/user/resolvers/wallVideo.resolver'
import { GraetChatUser } from './modules/user/types/streamChat'
import { StreamFeedEnums } from './modules/user/types/streamFeed'
import { SystemRole, UserRole } from './types/general'
import { WebhookConnect } from './webhooks/connect'
import { WebhookGetStream } from './webhooks/getstream'
import { logger } from './container'

import * as amplitude from '@amplitude/analytics-node'
import { ApolloServer } from '@apollo/server'
import { ApolloServerPluginLandingPageDisabled } from '@apollo/server/plugin/disabled'
import cors from '@fastify/cors'
import fastifyRequestContext, { requestContext } from '@fastify/request-context'
import fastifyApollo, {
  fastifyApolloDrainPlugin,
  fastifyApolloHandler,
} from '@nitra/as-integrations-fastify'
import { createPlugin as createPromsterMetricsPlugin } from '@promster/apollo'
import { createServer } from '@promster/server'
import * as Sentry from '@sentry/node'

let apolloServer: ApolloServer<ContextType>
let server
const appState = { shuttingDown: false }

export interface ContextUser {
  id: string
  email: string
  admin?: boolean
  role: UserRole
}

export type ContextType = {
  user: ContextUser | undefined
  ipAddress: string
  userAgent: string
  sessionId: number
  operationName?: string

  mobileClientOS?: 'ios' | 'android' | 'web'
  mobileClientVersion?: number

  fingerprint: string
  loaders: {
    userSubscription: DataLoader<string, UserSubscription | null> | null
    agency: DataLoader<string, Agency | null>
    userInfo: DataLoader<UserInfoKey, string | null>
    user: DataLoader<string, User | null>
    team: DataLoader<string, Team | null>
    organization: DataLoader<string, Organization | null>
  }
}

const allowedPersonalOperations = [
  'currentUser',
  'updateUser',
  'verifyByEmail',
  'uploadAvatar',
  'googleSignIn',
  'appleSignIn',
]

export const customAuthChecker: AuthChecker<ContextType> = async (resolverData, roles) => {
  if (!resolverData.context.user && roles.length === 0) {
    throw new AuthenticationError('Authentication token is not valid.')
  }

  if (roles.length > 1 && !resolverData.context.user) {
    throw new AuthenticationError('Authentication token is not valid.')
  }

  if (roles.includes(SystemRole.ADMIN) && !resolverData.context.user?.admin) {
    throw new AuthorizationError('You are not authorized to access this resource.')
  }

  if (
    roles.includes(SystemRole.PERSONAL) &&
    !allowedPersonalOperations.includes(resolverData.info.operation.name?.value ?? '') &&
    !resolverData.context.user?.admin
  ) {
    // allow getting personal information for all subroles of SPORTS_PROFESSIONAL role
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const root = resolverData.info.operation.selectionSet.selections[0] as unknown as any

    if (root.name?.value === 'user') {
      const user = await UserModel.findOne({ slug: resolverData.info.variableValues['slug'] })
      if (
        user?.role === Role.SPORTS_PROFESSIONAL &&
        [SubRole.AGENT, SubRole.SCOUT, SubRole.COACH].includes(user?.subrole as SubRole)
      ) {
        return true
      }
    }

    throw new AuthorizationError('Personal information is not publicly accessible.')
  }

  return true
}

export const ErrorInterceptor: MiddlewareFn = async ({ context, info, root }, next) => {
  try {
    return await next()
  } catch (err) {
    // we don't want to log errors that are not user related
    if (!err.extensions?.noLog && !(context as ContextType).user?.admin) {
      logger.error(err, context, info, root)

      Sentry.captureMessage(err.toString, 'error')
    } else {
      logger.warn(err, context, info, root)
    }

    // Rethrow the error
    throw err
  }
}

const fastify = Fastify({
  // logger, # this is too noisy
  trustProxy: true,
})

Sentry.setupFastifyErrorHandler(fastify)

fastify.register(fingerprint)
fastify.register(fastifyRequestContext)

async function bootstrap() {
  const schema = await buildSchema({
    // resolvers: [ __dirname + config.resolversPath ],
    resolvers: [
      UserResolver,
      PlayerPerformanceLikeResolver,
      WallVideoResolver,
      StripeAccountResolver,
      BoostResolver,
      StripeBalanceResolver,
      LeagueResolver,
      LeagueStatsResolver,
      PlayersStatsResolver,
      OrganizationResolver,
      TeamResolver,
      UserStatsResolver,
      ContactResolver,
      AdminUserResolver,
      AgencyResolver,
      AdminResolver,
      AdminMutationResolver,
      AnalyticsResolver,
      GameResolver,
      UserViewResponseResolver,
      UserResolverFamily,
      UserBioResolver,
      UserSubRoleSpecificationResolver,
      ExternalConnectionResolver,
      SuggestInvitesResolver,
      ClientStaticConfigResolver,
    ],
    scalarsMap: [{ type: Types.ObjectId, scalar: ObjectIdScalar }],
    authChecker: customAuthChecker,
    validate: true,
    container: Container,
    orphanedTypes: [StreamFeedEnums, GraetChatUser],
    globalMiddlewares: [ErrorInterceptor],
  })

  await fastify.register(cors, {
    // put your options here
  })

  const plugins = [fastifyApolloDrainPlugin(fastify)]

  if (process.env.NODE_ENV === 'production') {
    plugins.push(ApolloServerPluginLandingPageDisabled())
  }

  plugins.push(
    createPromsterMetricsPlugin({
      options: {
        disableGcMetrics: true,
        metricPercentiles: {
          graphQlRequestDuration: [0.02, 0.04, 0.08, 0.1, 0.15, 0.2, 0.3, 0.5, 1, 2, 5, 10],
        },
      },
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    }) as any
  )

  plugins.push({
    requestDidStart: async () => ({
      willSendResponse: async (ctx) => {
        ctx.errors?.forEach((error) => {
          const excludedCodes = ['BAD_REQUEST', 'UNAUTHENTICATED', 'UNAUTHORIZED']
          if (
            (!error.extensions || !excludedCodes.includes(error.extensions.code as string)) &&
            !error.extensions?.noLog &&
            !(ctx.contextValue as ContextType).user?.admin // TODO: test this
          ) {
            // we don't need to send it as error as it is sent to sentry already
            logger.warn(`Server error: ${error.toString()} \n %o`, {
              operationName: ctx.request.operationName,
              variables: ctx.request.variables,
              code: error.extensions.code,
              context: ctx.contextValue,
            })

            Sentry.captureMessage(`Server error: ${error.toString()}`, {
              level: 'error',
              extra: {
                variables: JSON.stringify(ctx.request.variables),
              },
            })
          }
        })
      },
    }),
  })

  apolloServer = new ApolloServer<ContextType>({
    schema,
    introspection: process.env.NODE_ENV !== 'production',
    plugins,
  })

  const p = new Promise((resolve, rejects) => {
    connectToMongoDB()
      .catch((err) => {
        logger.error('DB startup error', err)
        rejects()
      })
      .then(async () => {
        const port = config.port

        const contextFunction = async (request: FastifyRequest) => {
          let user: ContextUser | undefined = undefined
          try {
            const token = request?.headers.authorization?.replace('Bearer ', '')

            if (token) {
              user = jwt.verify(token, config.jwt.secret) as ContextUser
            }
          } catch (_) {
            // jwt verification failed, we don't need to log this
          }

          const userAgent = request.headers['user-agent']
          const buildNumber = request.headers['x-build-number']
          const versionMatch = userAgent?.match(/GRAET(?:DEV)?\/(\d+)/)?.[1]
          const isIosClient = userAgent?.includes('Darwin')

          const mobileClientOS = isIosClient ? 'ios' : buildNumber ? 'android' : 'web'

          if (user) {
            // Extract only the specific fields we need, avoiding fields like 'iat'
            const contextUser: ContextUser = {
              id: user.id,
              email: user.email,
              role: user.role,
            }

            // Only add admin field if it exists
            if (user.admin) {
              contextUser.admin = user.admin
            }

            requestContext.set('user', contextUser)
          }

          const loaders = {
            userSubscription: user?.id ? createUserSubscriptionLoader(user?.id) : null,
            agency: createAgencyLoader(),
            userInfo: createUserInfoLoader(),
            user: createUserLoader(),
            team: createTeamLoader(),
            organization: createOrganizationLoader(),
          }

          const context: ContextType = {
            user,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            fingerprint: (request as any)['fingerprint'],
            ipAddress: request.ip,
            userAgent: request.headers['user-agent'] ?? '',
            sessionId: parseInt(request.headers['sessionid'] as string),
            operationName:
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              (request.body as any)?.['operationName'] ?? (request.query as any)?.['operationName'],

            mobileClientOS,
            mobileClientVersion: versionMatch ? parseInt(versionMatch) : undefined,
            loaders: loaders,
          }

          Sentry.setContext('request', context)

          return context
        }

        await apolloServer.start()
        await fastify.register(fastifyApollo(apolloServer), {
          context: contextFunction,
        })

        createServer({ port: 8888 }).then(() =>
          console.log(`@promster/server started on port 8888.`)
        )

        // TODO: define timeout on requests
        fastify.route({
          url: '/',
          method: ['POST', 'GET', 'OPTIONS'],
          handler: fastifyApolloHandler(apolloServer, {
            context: contextFunction,
          }),
        })

        const webhookConnect = new WebhookConnect()
        fastify.route({
          url: '/webhooks/connect',
          method: ['POST'],
          preParsing(request, _1, _2, done) {
            request.headers['content-type'] = 'text/plain'
            done()
          },
          handler: (request, response) => webhookConnect.handle(request, response),
        })

        const webhookGetStream = new WebhookGetStream()
        fastify.route({
          url: '/webhooks/getstream',
          method: ['POST'],
          preParsing(request, _1, _2, done) {
            request.headers['content-type'] = 'text/plain'
            done()
          },
          handler: (request, response) => webhookGetStream.handle(request, response),
        })

        fastify.route({
          url: '/health',
          method: ['GET'],
          handler: async (_, res) => {
            const readyCheck = await axios.post(
              `http://localhost:${port}/`,
              {
                query: `query {__typename}`,
              },
              {
                headers: {
                  'x-no-trace': 'true',
                },
              }
            )

            if (
              !appState.shuttingDown &&
              getMongoDBConnection().readyState === 1 &&
              readyCheck.status === 200
            ) {
              res.send({ status: 'ok' })
              return
            } else {
              logger.error('Health check failed: %o', {
                appState,
                mongodb: getMongoDBConnection().readyState,
                readyCheck: readyCheck.status,
                memory: `${(process.memoryUsage().rss / 1024 / 1024).toFixed(2)}MB`,
              })
              res.status(503).send({ status: 'Health check failed.' })
              return
            }
          },
        })

        const url = await fastify.listen({ port, host: '0.0.0.0' })

        amplitude.init(config.amplitude.apiKey)

        logger.info(`🚀  Server ready at ${url}, env: ${process.env.NODE_ENV}`)

        resolve('All good')
      })
  })

  return p
}

const boot = bootstrap()

boot.catch((err) => {
  logger.error('Server failed to start', err)
})

const gracefulShutdown = async (signal: string): Promise<void> => {
  if (appState.shuttingDown) {
    return
  }

  appState.shuttingDown = true
  logger.info(`${signal} received. Gracefully shutting down.`)

  // Wait 60 seconds for existing connections to finish
  const timeout = setTimeout(() => {
    logger.error('Forcefully shutting down after 20 seconds.')
    process.exit(1) // Exit with a non-zero code to indicate an issue on shutdown
  }, 60000)

  timeout.unref()

  logger.info('Waiting 30s before closing connections.')
  await new Promise((resolve) => setTimeout(resolve, 30000))

  // Stop accepting new connections
  logger.info('Closing connections.')
  await amplitude.flush().promise
  await apolloServer.stop()
  await fastify.close()
  await closeMongoDBConnection()

  logger.info('Closed out remaining connections.')
  clearTimeout(timeout)

  // wait a second to allow any remaining console.log statements to run
  await new Promise((resolve) => setTimeout(resolve, 1000))

  process.exit(0)
}

process.on('SIGTERM', gracefulShutdown)
// process.on('SIGINT', gracefulShutdown)

export { apolloServer, boot, server }
