import config from 'config'
import pino from 'pino'
import Container from 'typedi'

import { Organization } from '../modules/stats/models/organization'
import { Team } from '../modules/stats/models/team'
import { ExternalLinkType, Role, User } from '../modules/user/models/user'
import { ContextType } from '..'

import { Identify, identify, track } from '@amplitude/analytics-node'

export default class Analytics {
  private logger: pino.Logger

  public constructor() {
    this.logger = Container.get('app.logger')
  }

  async trackEvent(
    eventName: string,
    data: Record<string, string | number | boolean>,
    ctx: ContextType | string
  ) {
    if (typeof ctx === 'string') {
      return track(eventName, data, {
        user_id: ctx,
      })
    }

    return await track(eventName, data, {
      session_id: ctx.sessionId,
      user_id: ctx.user?.id,
    })
  }

  async updateUserAttrs(user?: User | null) {
    if (!user) {
      return
    }

    const params = new Identify()
    params.set('user_id', user._id.toString())
    params.set('name', user.name)

    if (user.firstname) {
      params.set('firstname', user.firstname)
    }

    if (user.lastname) {
      params.set('lastname', user.lastname)
    }

    if (user.country) {
      params.set('nationality', user.country)
    }

    if (user.experimentGroup) {
      params.set('experiment_group', user.experimentGroup)
    }

    if (user.dateOfBirth) {
      params.set('birthday', user.dateOfBirth?.toISOString())

      const currentYear = new Date().getFullYear()
      const birthMonth = user.dateOfBirth.getUTCMonth()
      const birthDay = user.dateOfBirth.getUTCDate()

      const birthdayWithCurrentYear = new Date(Date.UTC(currentYear, birthMonth, birthDay))

      const birthdayTimestamp = Math.floor(birthdayWithCurrentYear.getTime() / 1000)

      params.set('birthday_this_year_at', birthdayTimestamp)
    }

    if (user.dateOfBirth) {
      params.set('year', user.dateOfBirth?.getFullYear())
    }

    params.set('email', user.email)

    if (user.boost?.currency) {
      params.set('currency', user.boost?.currency)
    }

    params.set('signup_date', user.createdAt.toISOString())
    params.set('link_profile', `${config.webUrl}${user.slug}`)

    if (user.role) {
      params.set('player_role', user.role)
    }

    if (user.bio?.position) {
      params.set('player_position', user.bio.position)
    }

    if (user.bio?.playerType) {
      params.set('player_position_type', user.bio.playerType)
    }

    params.set('player_guardian_added', !!user.contact)

    if (user.userStats) {
      params.set('player_boosts_count', user.userStats.boosts)
      params.set('player_followers_count', user.userStats.followers)
      params.set('player_views_count', user.userStats.views)
    }

    if (user.dateOfBirth) {
      params.set('age', this.getAge(user.dateOfBirth))
    }

    if (user.boost?.currentAmount) {
      params.set('player_boosts_total_amount', user.boost.currentAmount / 100)
    }

    let hasUserStats = false
    if (user.externalLinks) {
      const instagram = user.externalLinks.find((link) => link.type === ExternalLinkType.INSTAGRAM)
      if (instagram) {
        params.set('link_instagram', instagram.url)
      }

      const twitter = user.externalLinks.find((link) => link.type === ExternalLinkType.X)
      if (twitter) {
        params.set('link_x', twitter.url)
      }

      const tiktok = user.externalLinks.find((link) => link.type === ExternalLinkType.TIKTOK)
      if (tiktok) {
        params.set('link_tiktok', tiktok.url)
      }

      const linkedin = user.externalLinks.find((link) => link.type === ExternalLinkType.LINKEDIN)
      if (linkedin) {
        params.set('link_linkedin', linkedin.url)
      }

      const eliteprospects = user.externalLinks.find(
        (link) => link.type === ExternalLinkType.ELITE_PROSPECTS
      )
      if (eliteprospects) {
        params.set('link_ep', eliteprospects.url)
        hasUserStats = true
      }
    }

    // team
    if (user.currentTeam) {
      const team = user.currentTeam as Team
      const organization = team.organization as Organization

      params.set('player_has_team', true)
      params.set('team_name', team.name)
      params.set('organisation_category', team.shortName)
      params.set('organisation_name', organization?.name)
    } else {
      params.set('player_has_team', false)
    }

    // calculate profile score
    let score = 0
    let score_with_clips = 0

    params.set('player_has_picture', !!user.avatars)
    if (user.avatars) {
      score += 2
    }

    if (user.currentTeam && user.bio?.position && user.bio?.handedness) {
      params.set('player_has_sport_details', true)
      score += 2
    } else {
      params.set('player_has_sport_details', false)
    }

    if (user.bio?.graduationYear) {
      params.set('player_has_academic_details', true)
      score += 1
    } else {
      params.set('player_has_academic_details', false)
    }

    if (user.country && user.dateOfBirth && user.bio?.height && user.bio?.weight) {
      params.set('player_has_personal_details', true)
      score += 2
    } else {
      params.set('player_has_personal_details', false)
    }

    if (hasUserStats) {
      params.set('player_has_stats', true)
      score += 3
    } else {
      params.set('player_has_stats', false)
    }

    if ('familyMembers' in user) {
      if (user.role === Role.PARENT) {
        params.set('child_connected_count', user.familyMembers.length)
      } else {
        params.set('parent_connected_count', user.familyMembers.length)
      }
    }

    if ('agentV2' in user) {
      params.set('has_agent_connected', !!user.agentV2?._id)
    }

    score_with_clips = score
    score_with_clips += Math.min((user.numberOfVideos ?? 0) * 3, 15)

    params.set('player_profile_score', score)
    params.set('player_profile_score_with_clips', score_with_clips)

    params.set('notifications_enabled', !!user.pushEnabled)

    if (user.role === Role.SPORTS_PROFESSIONAL) {
      if (user.subrole) {
        params.set('pro_subrole', user.subrole?.toLocaleLowerCase())
      }
    } else {
      params.set('pro_subrole', 'other')
    }

    try {
      identify(params, {
        user_id: user._id.toString(),
      }).promise.then((response) => {
        if (response.code !== 200) {
          this.logger.error('Failed to update user attributes', response)
        }
      })

      // this will update attribute in charts as amplitude is event based system
      this.trackEvent('User Updated', {}, user._id.toString())
    } catch (err) {
      this.logger.error('Failed to update user attributes', err)
    }
  }

  async updateCustomAttrs(user: User, customAttrs: Record<string, string | number | boolean>) {
    const params = new Identify()

    for (const [key, value] of Object.entries(customAttrs)) {
      params.set(key, value)
    }

    await identify(params, {
      user_id: user._id.toString(),
    })
  }

  async addToCustomAttr(user: User, key: string, value: number) {
    const params = new Identify()
    params.add(key, value)

    await identify(params, {
      user_id: user._id.toString(),
    })
  }

  private getAge(birthDate: Date) {
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const m = today.getMonth() - birthDate.getMonth()
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    return age
  }
}
