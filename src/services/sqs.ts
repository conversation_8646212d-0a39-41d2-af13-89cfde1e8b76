import pino from 'pino'
import Container, { Service } from 'typedi'

import * as AWS from '@aws-sdk/client-sqs'

@Service()
export class SQSService {
  private client: AWS.SQS

  private logger: pino.Logger

  constructor() {
    this.client = Container.get('sqs.client')
    this.logger = Container.get('app.logger')
  }

  // send aws SQS message
  public async sendMessage(
    queueUrl: string,
    message: object,
    additionalParams?: object
  ): Promise<void> {
    const params = {
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(message),
      MessageGroupId: 'video-segmentation',
      ...additionalParams,
    }

    const command = new AWS.SendMessageCommand(params)

    try {
      await this.client.send(command)
    } catch (error) {
      this.logger.error(error)
      throw error
    }
  }

  public async receiveMessages(queueUrl: string): Promise<AWS.Message[]> {
    const params = {
      QueueUrl: queueUrl,
    }

    const command = new AWS.ReceiveMessageCommand(params)

    try {
      const response = await this.client.send(command)
      return response.Messages || []
    } catch (error) {
      this.logger.error(error)
      throw error
    }
  }

  public async deleteMessage(queueUrl: string, receiptHandle: string): Promise<void> {
    const params = {
      QueueUrl: queueUrl,
      ReceiptHandle: receiptHandle,
    }

    const command = new AWS.DeleteMessageCommand(params)

    try {
      await this.client.send(command)
    } catch (error) {
      this.logger.error(error)
      throw error
    }
  }
}
