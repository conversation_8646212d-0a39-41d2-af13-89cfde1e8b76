import pino from 'pino'
import Container, { Service } from 'typedi'

import { modelFind } from '../../modules/modules.helper'
import { Role } from '../../modules/user/models/user'
import { S3Service } from '../s3'

import { Organization } from './../../modules/stats/models/organization'
import { TeamModel } from './../../modules/stats/models/team'
import { AgencyModel } from './../../modules/user/models/agency'
import { UserModel } from './../../modules/user/models/index'

const webURLTemplates = {
  player: 'https://www.graet.com/{{slug}}/',
  agency: 'https://www.graet.com/agency/{{slug}}/',
  // `/team/${team.otganization.orgSlug}/${team.shortNameSlug}
  team: 'https://www.graet.com/team/{{slug}}/',
}

// eslint-disable-next-line prettier/prettier
const siteMapTemplate = '<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">{{content}}</urlset>'

// eslint-disable-next-line prettier/prettier
const siteMapUrlTemaplate = '<url><loc>{{url}}</loc><lastmod>{{date}}</lastmod><priority>{{priority}}</priority></url>'

@Service()
export class SitemapGenerator {
  private logger: pino.Logger
  private readonly s3: S3Service

  private mapModelToMethod: Record<string, () => Promise<string[]>>

  public constructor() {
    this.logger = Container.get('app.logger')
    this.s3 = Container.get('s3.service')

    this.mapModelToMethod = {
      player: this.generatePlayersURLs,
      agency: this.generateAgenciesURLs,
      team: this.generateTeamsURLs,
    }
  }

  public async generatePlayersURLs(batchSize: number = 1000): Promise<string[]> {
    const users = await modelFind(
      UserModel,
      {
        role: { $in: [Role.PLAYER, null] },
        public: { $ne: false },
        slug: { $ne: null },
      },
      { slug: 1 },
      null,
      batchSize
    )

    return (users as { slug: string }[]).map(({ slug }) =>
      webURLTemplates.player.replace('{{slug}}', `${slug}`)
    )
  }

  public async generateAgenciesURLs(batchSize: number = 1000): Promise<string[]> {
    const agencies = await modelFind(
      AgencyModel,
      { integrated: true },
      { slug: 1 },
      null,
      batchSize
    )

    return (agencies as { slug: string }[]).map(({ slug }) =>
      webURLTemplates.agency.replace('{{slug}}', `${slug}`)
    )
  }

  public async generateTeamsURLs(batchSize: number = 1000): Promise<string[]> {
    const teams = await modelFind(
      TeamModel,
      { organization: { $ne: null } },
      { shortNameSlug: 1, organization: 1 },
      { path: 'organization' },
      batchSize
    )

    return (teams as { shortNameSlug: string; organization: Organization }[]).map(
      ({ shortNameSlug, organization }: { shortNameSlug: string; organization: Organization }) =>
        webURLTemplates.team.replace('{{slug}}', `${organization.slug}/${shortNameSlug}`)
    )
  }

  public async generateSiteMap(model: 'player' | 'agency' | 'team'): Promise<string> {
    const urls = await this.mapModelToMethod[model].call(this)

    const date = new Date().toISOString()
    const priority = '1.00'

    const fileContents = `${siteMapTemplate.replace(
      '{{content}}',
      urls
        .map((url) =>
          siteMapUrlTemaplate
            .replace('{{date}}', date)
            .replace('{{url}}', url)
            .replace('{{priority}}', priority)
        )
        .join('\n')
    )}`

    this.logger.info(`Sitemap generated for ${model}, number of urls: ${urls.length}.`)

    await this.s3.uploadFromBuffer(
      `sitemap-${model}.xml`,
      Buffer.from(fileContents, 'utf8'),
      'application/xml',
      24 * 60 * 60
    )

    return fileContents
  }
}
