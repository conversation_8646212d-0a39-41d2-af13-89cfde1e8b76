import { APIResponse, GetFollowAPIResponse, NewActivity, StreamClient } from 'getstream'
import pino from 'pino'
import Container from 'typedi'

import { Game, GameModel } from '../../modules/stats/models/game/game'
import { League, LeagueModel } from '../../modules/stats/models/league'
import { OrganizationModel } from '../../modules/stats/models/organization'
import { Team, TeamModel } from '../../modules/stats/models/team'
import { User } from '../../modules/user/models/user'
import { FEED_NAMES, FeedActivityVerb, SystemFeedIds } from '../../modules/user/types/streamFeed'

import { DocumentType } from '@typegoose/typegoose'

export { FeedActivityVerb } from '../../modules/user/types/streamFeed'

let streamFeed: StreamClient
let logger: pino.Logger

const getStreamFeed = () => {
  streamFeed = streamFeed || (Container.get('streamFeed.client') as StreamClient)

  return streamFeed
}
const getLogger = (): pino.Logger => {
  logger = logger || Container.get('app.logger')

  return logger
}

export type AddNotificationActivityParams = {
  userId: string
  // logged in user in most cases
  actor?: User | null
  verb: FeedActivityVerb
  foreignId: string
  boostAmount?: string
  teamName?: string
  object?: string
  gameId?: string
  home?: { id: string; name: string; logo: string; slug: string; score?: number }
  away?: { id: string; name: string; logo: string; slug: string; score?: number }
}

const cleanObject = (obj: Record<string, unknown>) => {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([_, value]) => value !== undefined && value !== null && value !== ''
    )
  )
}

export const addNotificationActivity = async ({
  userId,
  actor = null,
  gameId,
  verb,
  foreignId,
  boostAmount,
  teamName,
  object,
  home,
  away,
}: AddNotificationActivityParams): Promise<NewActivity | null> => {
  // Stream Feed add activity to notification feed

  try {
    const userNotificationFeed = getStreamFeed().feed(FEED_NAMES.NOTIFICATION, userId)

    return userNotificationFeed.addActivity(
      cleanObject({
        // user can be also actor, like views insights
        actor: actor?._id.toString() || userId,
        verb: verb as string,
        object: object || 'user',
        foreign_id: foreignId,
        actor_slug: actor?.slug,
        actor_name: actor?.name ?? 'Someone',
        actor_avatar_url: actor?.avatars?.small,
        actor_role: actor?.role,
        actor_sub_role: actor?.subrole,
        actor_team: teamName,
        boost_amount: boostAmount,
        game_id: gameId,
        home,
        away,
      }) as NewActivity
    )
  } catch (error) {
    getLogger().error('Error adding notification activity to feed', error)
    return null
  }
}

export const buildGameActivityPayload = async (
  game: Game,
  hometeam: DocumentType<Team>,
  awayteam: DocumentType<Team>,
  league?: DocumentType<League>
) => {
  const gameId = game._id.toString()

  if (!hometeam.logo) {
    // check if already populated, to load from db
    const homeTeamOrg =
      'logo' in hometeam.organization && hometeam.organization.logo
        ? hometeam.organization
        : await OrganizationModel.findById(hometeam.organization)
    hometeam.logo = homeTeamOrg?.logo || ''
  }

  if (!awayteam.logo) {
    // check if already populated, to load from db
    const awayteamOrg =
      'logo' in awayteam.organization && awayteam.organization.logo
        ? awayteam.organization
        : await OrganizationModel.findById(awayteam?.organization)
    awayteam.logo = awayteamOrg?.logo || ''
  }

  return {
    actor: gameId,
    object: `gameId:${gameId}`,
    date: game.date,
    ...(hometeam
      ? {
          home: {
            id: hometeam?.id,
            name: hometeam?.name,
            logo: hometeam?.logo,
            slug: hometeam?.slug,
            score: game.stats?.homeTeam?.score,
          },
        }
      : {}),
    ...(awayteam
      ? {
          away: {
            id: awayteam?.id,
            name: awayteam?.name,
            logo: awayteam?.logo,
            slug: awayteam?.slug,
            score: game.stats?.awayTeam?.score,
          },
        }
      : {}),

    ...(league
      ? {
          league: {
            id: league?.id,
            name: league?.name,
            slug: league?.slug,
            logo: league?.logo,
            country: league?.countries?.[0],
          },
        }
      : {}),
    foreign_id: `gameId:${gameId}`,
  }
}

const addGameActivity = async (
  game: Game,
  graetGameFeedId: string
): Promise<NewActivity | null> => {
  const gameFeed = getStreamFeed().feed(FEED_NAMES.GAME, graetGameFeedId)

  if (
    game.homeTeamRoster?.some(({ externalInfo }) => !!externalInfo) ||
    game.awayTeamRoster?.some(({ externalInfo }) => !!externalInfo) ||
    game.events?.some(({ externalInfo }) => !!externalInfo)
  ) {
    // Use the instance loadPlayers method for now since we have a type mismatch
    await GameModel.loadPlayersForGames([game])
  }

  const toHomePlayers =
    game.homeTeamRoster
      ?.filter(({ player }) => !!player?._id)
      ?.map(({ player }) => `user:${player?._id.toString()}`) || []
  const toAwayPlayers =
    game.awayTeamRoster
      ?.filter(({ player }) => !!player?._id)
      ?.map(({ player }) => `user:${player?._id.toString()}`) || []

  const hometeam = await TeamModel.findById(game.homeTeam)
  const awayteam = await TeamModel.findById(game.awayTeam)
  const league = await LeagueModel.findById(game.league)

  if (!hometeam || !awayteam || !league || !toHomePlayers?.length || !toAwayPlayers?.length) {
    getLogger().error(
      `Error finding teams, players or league for game activity, gameId: ${game._id.toString()}`
    )
    return null
  }

  const activityPayload = {
    ...(await buildGameActivityPayload(
      game,
      hometeam as DocumentType<Team>,
      awayteam as DocumentType<Team>,
      league as DocumentType<League>
    )),
    verb: FeedActivityVerb.IceHockeyMatch,

    to: [
      `team:${hometeam?._id.toString()}`,
      `team:${awayteam?._id.toString()}`,
      ...toHomePlayers,
      ...toAwayPlayers,
    ],
  }

  try {
    const newActivity = await gameFeed.addActivity(activityPayload)
    return newActivity
  } catch (error) {
    getLogger().error(`Error adding game activity to feed, gameId: ${game._id.toString()}`, error)
    return null
  }
}

export const addIceHockeyGameActivity = async (game: Game) =>
  addGameActivity(game, SystemFeedIds.IceHockeyGame)

const removeGameActivity = async (
  game: Game,
  graetGameFeedId: string
): Promise<APIResponse | null> => {
  const gameFeed = getStreamFeed().feed(FEED_NAMES.GAME, graetGameFeedId)
  try {
    if (game.streamFeedActivityId) {
      const response = await gameFeed.removeActivity(game.streamFeedActivityId)
      return response
    }
    return null
  } catch (error) {
    getLogger().error(`Error adding game activity to feed, gameId: ${game._id.toString()}`, error)
    return null
  }
}

export const removeIceHockeyGameActivity = async (game: Game) =>
  removeGameActivity(game, SystemFeedIds.IceHockeyGame)

/**
 *
 * Use with Discretion, deleting followers from feeds that are obsolete, like user:undefined
 *
 * @param feedGroup
 * @param id
 * @returns
 */
export const deleteFeed = async (feedGroup: FEED_NAMES, id: string) => {
  const feed = getStreamFeed().feed(feedGroup, id)
  let offset = 0
  const limit = 100
  let response: GetFollowAPIResponse | null = null

  let unfollowCount = 0
  try {
    do {
      response = await feed.followers({ limit, offset })

      for (const follower of response?.results || []) {
        if (follower?.feed_id) {
          const split = follower.feed_id.split(':')
          const followerFeed = getStreamFeed().feed(split[0], split[1])
          const unfollowResponse = await followerFeed.unfollow(feedGroup, id)
          console.log('unfollowResponse', unfollowResponse)
          unfollowCount++
        }
        console.log('follower', follower)
        if (unfollowCount > 100) {
          console.log('unfollowCount', unfollowCount)
          await new Promise((resolve) => setTimeout(resolve, 10000))
          unfollowCount = 0
        }
      }

      offset += limit
    } while (response?.results?.length)
    console.log('no more followers')
    process.exit(0)
  } catch (error) {
    getLogger().error(`Error deleting feed, feedGroup: ${feedGroup}, id: ${id}`, error)
    return null
  }
}
