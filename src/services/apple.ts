import appleSigninAuth from 'apple-signin-auth'
import jwt from 'jsonwebtoken'
import { Service } from 'typedi'

interface AppleUserInfo {
  email: string | null
  appleUserId: string
}

@Service()
export default class AppleService {
  async getUserInfo(idToken: string): Promise<AppleUserInfo> {
    const decodedIdToken = jwt.decode(idToken) as { nonce: string }
    if (!decodedIdToken) {
      throw new Error('Failed to decode idToken')
    }

    const nonce = decodedIdToken['nonce']

    const result: AppleUserInfo = {
      email: null,
      appleUserId: '',
    }

    try {
      const results = await appleSigninAuth.verifyIdToken(idToken, {
        audience: 'com.graet.mobile',
        nonce: nonce,
        ignoreExpiration: true,
      })

      result.email = results.email
      result.appleUserId = results.sub
    } catch (err) {
      if (err.message.indexOf('jwt audience invalid') !== -1) {
        const results = await appleSigninAuth.verifyIdToken(idToken, {
          audience: 'dev.graet',
          nonce: nonce,
          ignoreExpiration: true,
        })

        result.email = results.email
        result.appleUserId = results.sub
      } else {
        throw err
      }
    }

    return result
  }
}
