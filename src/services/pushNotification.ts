import config from 'config'
import { Types } from 'mongoose'
import Mustache from 'mustache'
import pino from 'pino'
import { registerEnumType } from 'type-graphql'
import Container, { Service } from 'typedi'

import { UserModel } from '../modules/user/models'
import { NotificationModel } from '../modules/user/models/notification'
import { User } from '../modules/user/models/user'

import Analytics from './analytics'
import { SNSService } from './sns'

export enum PushNotificationType {
  FOLLOW = 'FOLLOW',
  FOLLOW_OTHER = 'FOLLOW_OTHER',
  FOLLOW_AGENT = 'FOLLOW_AGENT',
  FOLLOW_SCOUT = 'FOLLOW_SCOUT',
  FOLLOW_COACH = 'FOLLOW_COACH',

  BOOST_RECEIVED = 'BOOST_RECEIVED',
  VIDEO_SEGMENTATION_FINISHED = 'VIDEO_SEGMENTATION_FINISHED',

  VIDEO_CLIP_LIKE = 'VIDEO_CLIP_LIKE',
  VIDEO_HIGHLIGHT_LIKE = 'VIDEO_HIGHLIGHT_LIKE',

  GAME_STATS_CLIP_UPLOAD = 'GAME_STATS_CLIP_UPLOAD',

  VIEW_AGENT = 'VIEW_AGENT',
  VIEW_SCOUT = 'VIEW_SCOUT',
  VIEW_COACH = 'VIEW_COACH',
  VIEW_INSIGHTS = 'VIEW_INSIGHTS',

  NEW_TEAM_MEMBER = 'NEW_TEAM_MEMBER',

  STRIPE_ACCOUNT_VERIFICATION = 'STRIPE_ACCOUNT_VERIFICATION',

  Fire = 'FIRE',
  FireAgent = 'FIRE_AGENT',
  FireScout = 'FIRE_SCOUT',
  FireCoach = 'FIRE_COACH',

  GameResult = 'GAME_RESULT',

  Marketing = 'MARKETING',
}

registerEnumType(PushNotificationType, {
  name: 'PushNotificationType',
  description: 'Enum for push notification types',
})

export interface PushParams {
  destinationPath: string
  pushType: PushNotificationType
}

type PushNotification = {
  title: string
  message: string
  type: PushNotificationType
  params?: Partial<PushParams>
  key?: string
  forcePush?: boolean
}

type SendPushNotificationResponse = {
  sent: boolean
}

@Service()
export class PushNotificationService {
  private sns: SNSService
  private logger: pino.Logger
  private analytics: Analytics

  constructor() {
    this.logger = Container.get('app.logger')
    this.sns = Container.get('sns.service')
    this.analytics = Container.get('analytics.service')
  }

  async sendPushNotification(
    user: User | null,
    args: PushNotification
  ): Promise<SendPushNotificationResponse> {
    if (!user) {
      return {
        sent: false,
      }
    }

    let shouldSend = true
    if (
      user.lastNotificationSentAt &&
      user.lastNotificationSentAt >
        new Date(Date.now() - config.pushNotifications.delayInMinutes * 60 * 1000) &&
      !args.forcePush
    ) {
      shouldSend = false
    }

    const renderedTitle = Mustache.render(args.title, {
      name: user.name,
      firstname: user.firstname,
      lastname: user.lastname,
    })

    const renderedMessage = Mustache.render(args.message, {
      name: user.name,
      firstname: user.firstname,
      lastname: user.lastname,
    })

    try {
      await NotificationModel.create({
        title: renderedTitle,
        body: renderedMessage,
        destinationPath: args.params?.destinationPath,
        user: user._id,
        type: args.type,
        sent: shouldSend,
        key: args.key ?? new Types.ObjectId(),
      })
    } catch (err) {
      if (err.name === 'MongoServerError' && err.code === 11000) {
        // Notification already sent
        return {
          sent: false,
        }
      }

      this.logger.error('Failed to create notification', err)
    }

    if (shouldSend) {
      await UserModel.updateOne({ _id: user._id }, { lastNotificationSentAt: new Date() })

      let atLeastOneMessageSent = false
      for (const device of user.registeredDevices ?? []) {
        if (!device.arn) {
          continue
        }

        const sent = await this.sns.sendEndpointNotification(
          device.arn,
          renderedTitle,
          renderedMessage,
          user._id.toString(),
          {
            pushType: args.type,
            destinationPath: args.params?.destinationPath ?? '/',
          }
        )

        if (!atLeastOneMessageSent && sent) {
          atLeastOneMessageSent = true
        }
      }

      if (atLeastOneMessageSent) {
        await this.analytics.trackEvent(
          'pushNotificationSent',
          {
            userId: user._id.toString(),
            type: args.type,
          },
          user._id.toString()
        )
      }

      return {
        sent: atLeastOneMessageSent,
      }
    }

    return {
      sent: false,
    }
  }
}
