import config from 'config'
import pino from 'pino'
import sharp from 'sharp'
import Container, { Service } from 'typedi'

import { Avatars } from '../modules/user/models/user'
import { WallVideo } from '../modules/user/models/wallVideo'

import { S3Service } from './s3'

import * as AWSMediaConverter from '@aws-sdk/client-mediaconvert'

@Service()
export class MediaConverter {
  private readonly client: AWSMediaConverter.MediaConvertClient
  private readonly s3: S3Service
  private readonly logger: pino.Logger

  public constructor() {
    this.client = Container.get('mediaConverter.client')
    this.s3 = Container.get('s3.service')
    this.logger = Container.get('app.logger')
  }

  public async createHLSVideo(video?: WallVideo | null, segmentation?: boolean) {
    if (!video || !video.width) return

    return this.client.send(
      new AWSMediaConverter.CreateJobCommand({
        Settings: {
          TimecodeConfig: {
            Source: 'ZEROBASED',
          },
          OutputGroups: this.getOutputGroups(video, segmentation ?? false),
          Inputs: [
            {
              TimecodeSource: 'ZEROBASED',
              VideoSelector: {},
              AudioSelectors: {
                'Audio Selector 1': {
                  DefaultSelection: 'DEFAULT',
                },
              },
              FileInput: `s3://${config.aws.assetsBucket}/${segmentation ? video.segmentation.videoPath : video.path}`,
            },
          ],
          FollowSource: 1,
        },
        AccelerationSettings: {
          Mode: 'DISABLED',
        },
        Priority: 0,
        HopDestinations: [],
        Role: 'arn:aws:iam::845702489680:role/service-role/MediaConvert_Default_Role',
      })
    )
  }

  public async createImageThumbnails(imageKey: string): Promise<Avatars> {
    const image = await this.s3.downloadToBuffer(imageKey)

    if (!image) {
      throw new Error('Image not found')
    }

    const [large, medium, small, tiny, og] = await Promise.all([
      await sharp(image).resize(800).jpeg().toBuffer(),
      await sharp(image).resize(400).jpeg().toBuffer(),
      await sharp(image).resize(160).jpeg().toBuffer(),
      await sharp(image).resize(40).jpeg().toBuffer(),
      await this.createOGImage(image),
    ])

    const extension = imageKey.substring(imageKey.lastIndexOf('.'))
    const largeKey = `${imageKey.replace(extension, '')}-large${extension}`
    const mediumKey = `${imageKey.replace(extension, '')}-medium${extension}`
    const smallKey = `${imageKey.replace(extension, '')}-small${extension}`
    const tinyKey = `${imageKey.replace(extension, '')}-tiny${extension}`
    const ogKey = `${imageKey.replace(extension, '')}-og${extension}`

    await Promise.all([
      this.s3.uploadFromBuffer(largeKey, large, 'image/jpeg'),
      this.s3.uploadFromBuffer(mediumKey, medium, 'image/jpeg'),
      this.s3.uploadFromBuffer(smallKey, small, 'image/jpeg'),
      this.s3.uploadFromBuffer(tinyKey, tiny, 'image/jpeg'),
      this.s3.uploadFromBuffer(ogKey, og, 'image/jpeg'),
    ])

    return {
      large: largeKey,
      medium: mediumKey,
      small: smallKey,
      tiny: tinyKey,
      og: ogKey,
    }
  }

  public async createAndUploadOGImage(imageKey: string): Promise<string> {
    const image = await this.s3.downloadToBuffer(imageKey)

    if (!image) {
      this.logger.error('Image not found', { imageKey })
      return ''
    }

    const ogImage = await this.createOGImage(image, true)

    const extension = imageKey.substring(imageKey.lastIndexOf('.'))
    const ogKey = `${imageKey.replace(extension, '')}-og${extension}`

    const imageContentType = imageKey.endsWith('.png') ? 'image/png' : 'image/jpeg'

    await this.s3.uploadFromBuffer(ogKey, ogImage, imageContentType)

    return ogKey
  }

  public async createOGImage(image: Uint8Array, isLogo: boolean = false): Promise<Buffer> {
    const templatePath = './assets/og-template.png'
    const inputImage = sharp(image)

    const targetWidth = 1200
    const targetHeight = 630
    let imageWidth = 630
    const imageHeight = 630

    if (isLogo) {
      imageWidth = 342
    }

    const resizedImage = await inputImage
      .resize(imageWidth + (isLogo ? 0 : 10), imageHeight, {
        fit: isLogo ? 'contain' : 'cover',
        background: { r: 255, g: 255, b: 255, alpha: 255 },
      })
      .toBuffer()

    const baseImage = await sharp({
      create: {
        width: targetWidth,
        height: targetHeight,
        channels: 4,
        background: { r: 255, g: 255, b: 255, alpha: 255 },
      },
    })
      .png()
      .toBuffer()

    // composite player
    const imageWithPlayer = await sharp(baseImage)
      .composite([
        {
          input: resizedImage,
          top: 0,
          left: targetWidth - imageWidth - (isLogo ? imageWidth / 2 : 40),
        },
      ])
      .toBuffer()

    const template = sharp(templatePath)

    return await sharp(imageWithPlayer)
      .composite([{ input: await template.toBuffer(), top: 0, left: 0 }])
      .png({
        quality: 80,
        compressionLevel: 9,
      })
      .toBuffer()
  }

  private getOutputGroups(
    video: WallVideo,
    segmentation: boolean
  ): AWSMediaConverter.OutputGroup[] {
    const fileSize = video.size * 8
    const duration = video.duration
    const maxBitrate1080p = fileSize / duration + 500_000

    const videoOrientation = video.width > video.height ? 'landscape' : 'portrait'
    const shorterResolutionSide = videoOrientation === 'landscape' ? 'Heigth' : 'Width'

    return [
      {
        Name: 'Apple HLS',
        Outputs: [
          {
            ContainerSettings: {
              Container: 'M3U8',
              M3u8Settings: {},
            },
            VideoDescription: {
              [shorterResolutionSide]: 1080,
              CodecSettings: {
                Codec: 'H_264',
                H264Settings: {
                  MaxBitrate: maxBitrate1080p,
                  RateControlMode: 'QVBR',
                  QvbrSettings: {
                    QvbrQualityLevel: 9,
                  },
                  SceneChangeDetect: 'TRANSITION_DETECTION',
                },
              },
            },
            AudioDescriptions: [
              {
                AudioSourceName: 'Audio Selector 1',
                CodecSettings: {
                  Codec: 'AAC',
                  AacSettings: {
                    Bitrate: 128_000,
                    CodingMode: 'CODING_MODE_2_0',
                    SampleRate: 48_000,
                  },
                },
              },
            ],
            OutputSettings: {
              HlsSettings: {},
            },
            NameModifier: '_1080',
          },
          {
            ContainerSettings: {
              Container: 'M3U8',
              M3u8Settings: {},
            },
            VideoDescription: {
              [shorterResolutionSide]: 720,
              CodecSettings: {
                Codec: 'H_264',
                H264Settings: {
                  MaxBitrate: 2_000_000,
                  RateControlMode: 'QVBR',
                  QvbrSettings: {
                    QvbrQualityLevel: 7,
                  },
                  SceneChangeDetect: 'TRANSITION_DETECTION',
                },
              },
            },
            AudioDescriptions: [
              {
                AudioSourceName: 'Audio Selector 1',
                CodecSettings: {
                  Codec: 'AAC',
                  AacSettings: {
                    Bitrate: 128_000,
                    CodingMode: 'CODING_MODE_2_0',
                    SampleRate: 48_000,
                  },
                },
              },
            ],
            OutputSettings: {
              HlsSettings: {},
            },
            NameModifier: '_720',
          },
          {
            ContainerSettings: {
              Container: 'M3U8',
              M3u8Settings: {},
            },
            VideoDescription: {
              [shorterResolutionSide]: 360,
              CodecSettings: {
                Codec: 'H_264',
                H264Settings: {
                  MaxBitrate: 1_000_000,
                  RateControlMode: 'QVBR',
                  QvbrSettings: {
                    QvbrQualityLevel: 7,
                  },
                  SceneChangeDetect: 'TRANSITION_DETECTION',
                },
              },
            },
            AudioDescriptions: [
              {
                AudioSourceName: 'Audio Selector 1',
                CodecSettings: {
                  Codec: 'AAC',
                  AacSettings: {
                    Bitrate: 128_000,
                    CodingMode: 'CODING_MODE_2_0',
                    SampleRate: 48_000,
                  },
                },
              },
            ],
            OutputSettings: {
              HlsSettings: {},
            },
            NameModifier: '_360',
          },
        ],
        OutputGroupSettings: {
          Type: 'HLS_GROUP_SETTINGS',
          HlsGroupSettings: {
            SegmentLength: 4,
            MinSegmentLength: 0,
            Destination: `s3://${config.aws.assetsBucket}/${segmentation ? video.segmentation.videoHlsPath : video.hlsPath}/`,
          },
        },
      },
    ]
  }
}
