import config from 'config'
import Stripe from 'stripe'
import Container, { Service } from 'typedi'

import { StripeAccount, StripeExternalAccount } from '../modules/stripe/models/account'
import { CreateStripeAccountInput } from '../modules/stripe/types/account'
import { User } from '../modules/user/models/user'

@Service()
export class StripeService {
  private stripe: Stripe

  public constructor() {
    this.stripe = Container.get('stripe.client')
  }

  public async createConnectAccount(
    accountInfo: CreateStripeAccountInput,
    ip: string,
    userAgent: string,
    user: User
  ): Promise<StripeAccount> {
    const account = await this.stripe.accounts.create({
      type: 'custom',
      country: accountInfo.country,
      email: accountInfo.email,
      business_type: 'individual',
      business_profile: {
        mcc: '7299',
        url: 'https://www.graet.com/' + user.slug,
        product_description: 'Graet - boost player.',
      },
      individual: {
        email: accountInfo.email,
        first_name: accountInfo.firstname,
        last_name: accountInfo.lastname,
        address: {
          country: accountInfo.country,
          city: accountInfo.address?.city,
          line1: accountInfo.address?.line1,
          postal_code: accountInfo.address?.postalCode,
          state: accountInfo.address?.state,
        },
        ...(accountInfo.dob
          ? {
              dob: {
                day: accountInfo.dob?.getDate(),
                month: accountInfo.dob?.getMonth() + 1,
                year: accountInfo.dob?.getFullYear(),
              },
            }
          : {}),
        phone: accountInfo.phone,
      },
      tos_acceptance: {
        date: Math.floor(Date.now() / 1000),
        ip,
        service_agreement: 'full',
        user_agent: userAgent,
      },
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
        // bank_transfer_payments: { requested: true }, Not available in Canada
        link_payments: { requested: true },
      },
      default_currency: accountInfo.currency,
      settings: {
        payouts: {
          schedule: {
            interval: 'weekly',
            weekly_anchor: 'monday',
          },
        },
        payments: {
          statement_descriptor: `GRAET ${user.name}`.substring(0, 22),
        },
      },
    })

    await this.stripe.paymentMethodDomains.create(
      {
        domain_name: process.env.NODE_ENV === 'production' ? 'www.graet.com' : 'www.graet.dev',
      },
      {
        stripeAccount: account.id,
      }
    )

    const dob = account.individual?.dob
    return {
      id: account.id,
      firstname: account.individual?.first_name ?? '',
      lastname: account.individual?.last_name ?? '',
      dob:
        dob && dob.year && dob.day && dob.month
          ? new Date(dob.year, dob.month, dob.day)
          : undefined,
      address: {
        city: account.individual?.address?.city ?? '',
        line1: account.individual?.address?.line1 ?? '',
        line2: account.individual?.address?.line2 ?? '',
        postalCode: account.individual?.address?.postal_code ?? '',
        state: account.individual?.address?.state ?? '',
      },
      currency: account.default_currency,
      phone: account.individual?.phone ?? '',
      email: account.individual?.email ?? '',
      country: account.country,
      payoutsEnabled: account.payouts_enabled,
    }
  }

  public async updateConnectAccount(
    accountId: string,
    accountInfo: CreateStripeAccountInput,
    user: User
  ): Promise<StripeAccount> {
    const account = await this.stripe.accounts.update(accountId, {
      individual: {
        ssn_last_4: accountInfo.ssnLast4,
        email: accountInfo.email,
        first_name: accountInfo.firstname,
        last_name: accountInfo.lastname,
        relationship: {
          title: accountInfo.jobTitle,
        },
        address: {
          city: accountInfo.address?.city,
          line1: accountInfo.address?.line1,
          postal_code: accountInfo.address?.postalCode,
          state: accountInfo.address?.state,
        },
        ...(accountInfo.dob
          ? {
              dob: {
                day: accountInfo.dob?.getDate(),
                month: accountInfo.dob?.getMonth() + 1,
                year: accountInfo.dob?.getFullYear(),
              },
            }
          : {}),
        phone: accountInfo.phone,
      },
      email: accountInfo.email,
      default_currency: accountInfo.currency,
      settings: {
        payouts: {
          schedule: {
            interval: 'weekly',
            weekly_anchor: 'monday',
          },
        },
        payments: {
          statement_descriptor: `GRAET ${user.name}`.substring(0, 22),
        },
      },
      ...(accountInfo.verification
        ? { individual: { verification: accountInfo.verification } }
        : {}),
    })

    return this.mapStripeAccount(account)
  }

  public async createExternalAccount(
    accountId: string,
    bankAccount: {
      country: string
      currency: string
      firstname: string
      lastname: string
    },
    routingNumber: string,
    accountNumber: string
  ): Promise<StripeExternalAccount> {
    const externalAccount = await this.stripe.accounts.createExternalAccount(accountId, {
      external_account: {
        object: 'bank_account',
        country: bankAccount.country,
        currency: bankAccount.currency,
        account_holder_name: `${bankAccount.firstname} ${bankAccount.lastname}`,
        account_holder_type: 'individual',
        routing_number: routingNumber,
        account_number: accountNumber,
      },
    })

    return {
      id: externalAccount.id,
      bankAccount: {
        country: externalAccount.country ?? undefined,
        currency: externalAccount.currency ?? undefined,
      },
      // routingNumber: externalAccount.routing_number,
      accountNumber: externalAccount.last4,
    }
  }

  public async getExternalAccount(
    accountId: string,
    externalAccountId: string
  ): Promise<StripeExternalAccount | null> {
    const externalAccount = await this.stripe.accounts.retrieveExternalAccount(
      accountId,
      externalAccountId
    )

    if ('routing_number' in externalAccount) {
      return {
        id: externalAccount.id,
        bankAccount: {
          country: externalAccount.country,
          currency: externalAccount.currency,
        },
        accountNumber: externalAccount.last4,
        routingNumber: externalAccount.routing_number ?? undefined,
        status: externalAccount.status,
      }
    }

    return null
  }

  public async deleteExternalAccount(accountId: string, externalAccountId: string): Promise<void> {
    await this.stripe.accounts.deleteExternalAccount(accountId, externalAccountId)
  }

  public async setDefaultExternalAccount(
    accountId: string,
    externalAccountId: string
  ): Promise<void> {
    await this.stripe.accounts.updateExternalAccount(accountId, externalAccountId, {
      default_for_currency: true,
    })
  }

  public async getConnectAccount(accountId: string): Promise<StripeAccount> {
    return this.mapStripeAccount(
      await this.stripe.accounts.retrieve(accountId, { expand: ['requirements'] })
    )
  }

  public async createPaymentSheet(amount: number, currency: string, accountId: string) {
    // TODO insert information if we know the customer
    const customer = await this.stripe.customers.create(
      {},
      {
        stripeAccount: accountId,
      }
    )

    await this.stripe.paymentMethodDomains.create(
      {
        domain_name: config.domain,
      },
      {
        stripeAccount: accountId,
      }
    )

    const ephemeralKey = await this.stripe.ephemeralKeys.create(
      { customer: customer.id },
      {
        apiVersion: '2022-11-15',
        stripeAccount: accountId,
      }
    )

    const fee = Math.round(amount * 0.06) // 6% platform fee (note: stripe fee is deducted automatically)

    const paymentIntent = await this.stripe.paymentIntents.create(
      {
        amount,
        currency,
        customer: customer.id,
        application_fee_amount: fee,
      },
      {
        stripeAccount: accountId,
      }
    )

    return {
      paymentIntentId: paymentIntent.id,
      paymentIntent: paymentIntent.client_secret,
      ephemeralKey: ephemeralKey.secret,
      customerId: customer.id,
      accountId,
      publishableKey: config.stripe.publishableKey,
    }
  }

  // TODO: refactor to specific types
  public async getAccountBalance(accountId: string) {
    const balance = await this.stripe.balance.retrieve(
      {},
      {
        stripeAccount: accountId,
      }
    )

    const payouts = await this.stripe.payouts.list(
      {
        limit: 20,
      },
      {
        stripeAccount: accountId,
      }
    )

    return {
      balance: {
        available: balance.available[0],
        pending: balance.pending[0],
      },
      transactions: payouts.data.map((payout) => {
        return {
          id: payout.id,
          payout: {
            amount: payout.amount,
            currency: payout.currency,
          },
          status: payout.status,
          description: payout.description,
          createdAt: new Date(payout.created * 1000),
        }
      }),
    }
  }

  private mapStripeAccount(account: Stripe.Account): StripeAccount {
    const dob = account.individual?.dob
    return {
      id: account.id,
      firstname: account.individual?.first_name ?? undefined,
      lastname: account.individual?.last_name ?? undefined,
      dob:
        dob && dob.year && dob.month && dob.day
          ? new Date(dob.year, dob.month - 1, dob.day, 12, 0, 0)
          : undefined,
      address: {
        city: account.individual?.address?.city ?? undefined,
        line1: account.individual?.address?.line1 ?? undefined,
        line2: account.individual?.address?.line2 ?? undefined,
        postalCode: account.individual?.address?.postal_code ?? undefined,
        state: account.individual?.address?.state ?? undefined,
      },
      ssnLast4Provided: account.individual?.ssn_last_4_provided,
      jobTitle: account.individual?.relationship?.title ?? undefined,
      currency: account.default_currency,
      phone: account.individual?.phone ?? undefined,
      email: account.individual?.email ?? undefined,
      country: account.country,
      payoutsEnabled: account.payouts_enabled,
      paymentsEnabled: account.capabilities?.card_payments === 'active',
      requirements: account.requirements,
    }
  }
}
