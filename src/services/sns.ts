import config from 'config'
import pino from 'pino'
import Container, { Service } from 'typedi'

import { UserModel } from '../modules/user/models'
import { Platform } from '../modules/user/models/user'

import { PushParams } from './pushNotification'

import * as AWS from '@aws-sdk/client-sns'

export type SNSProtocol = 'email' | 'application'

// TODO handle errors
interface RegisterDeviceResponse {
  platformArn?: string
  platformSandboxArn?: string
}

@Service()
export class SNSService {
  private client: AWS.SNS
  private logger: pino.Logger

  constructor() {
    this.client = Container.get('sns.client')
    this.logger = Container.get('app.logger')
  }

  public async registerDeviceToken(
    deviceToken: string,
    platform: Platform
  ): Promise<RegisterDeviceResponse> {
    const params = {
      PlatformApplicationArn:
        platform === Platform.IOS ? config.aws.sns.iosArn : config.aws.sns.androidArn,
      Token: deviceToken,
    }

    let platformArn = null
    let platformSandboxArn = null
    platformArn = await this.client.createPlatformEndpoint(params)
    if (
      platform === Platform.IOS &&
      (process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'dev')
    ) {
      platformSandboxArn = await this.client.createPlatformEndpoint({
        ...params,
        PlatformApplicationArn: config.aws.sns.iosSandboxArn,
      })
    }

    return {
      platformArn: platformArn.EndpointArn,
      platformSandboxArn: platformSandboxArn?.EndpointArn,
    }
  }

  async sendEndpointNotification(
    endpointArn: string,
    title: string,
    message: string,
    userId: string,
    params?: PushParams
  ) {
    try {
      await this.client.publish({
        MessageStructure: 'json',
        Message: JSON.stringify({
          default: message,
          GCM: JSON.stringify({ data: { title, message, ...params } }),
          APNS: JSON.stringify({
            aps: {
              alert: { title, body: message, ...params },
            },
          }),
          APNS_SANDBOX: JSON.stringify({
            aps: {
              alert: { title, body: message, ...params },
            },
          }),
        }),
        TargetArn: endpointArn,
      })

      return true
    } catch (err) {
      if (
        err.message === 'Endpoint is disabled'
        // TODO: enable once we are sure we want to remove this endpoint
        // ||
        // err.message ===
        //   'Invalid parameter: TargetArn Reason: No endpoint found for the target arn specified'
      ) {
        this.logger.warn('Endpoint is disabled, removing %s', endpointArn)

        // remove this arn from users registeredDevices
        await UserModel.updateOne(
          { _id: userId },
          { $pull: { registeredDevices: { arn: endpointArn } } }
        )
      } else {
        this.logger.error('Send push failed: %o', { error: err.message, endpointArn })
      }

      return false
    }
  }

  async deletePlatformEndpoint(endpointArn: string) {
    try {
      await this.client.deleteEndpoint({ EndpointArn: endpointArn })
    } catch (err) {
      this.logger.error('Failed to delete SNS ARN endpoint', err)
    }
  }
}
