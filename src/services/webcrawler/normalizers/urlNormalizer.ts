import { UrlConfig } from '../types'

/**
 * Interface for URL normalizers used by the WebCrawler
 * Defines the contract for normalizing and validating URLs
 */
export interface UrlNormalizer {
  /**
   * Normalizes a URL, removing unnecessary parts and standardizing format
   * @param href The URL to normalize
   * @param baseUrl The base URL for resolving relative URLs
   * @returns Normalized URL string
   */
  normalizeUrl(href: string, baseUrl: string): string

  /**
   * Normalizes query parameters in a URL based on configuration
   * @param url The URL to normalize
   * @param config Configuration for allowed query parameters
   * @returns URL with normalized query parameters
   */
  normalizeQueryParams(url: string, config: UrlConfig): string

  /**
   * Checks if a URL is within the specified domain
   * @param url The URL to check
   * @param baseDomain The base domain to check against
   * @returns True if the URL is within the domain
   */
  isInDomain(url: string, baseDomain: string): boolean

  /**
   * Checks if a URL matches any of the relevant patterns
   * @param url The URL to check
   * @param patterns Array of regular expressions to match against
   * @param visitedUrls Set of already visited URLs
   * @returns True if the URL is relevant
   */
  isRelevantUrl(url: string, patterns: RegExp[], visitedUrls: Set<string>): boolean
}

/**
 * Default implementation of the UrlNormalizer interface
 */
export class DefaultUrlNormalizer implements UrlNormalizer {
  /**
   * Normalizes a URL, removing unnecessary parts and standardizing format
   * @param href The URL to normalize
   * @param baseUrl The base URL for resolving relative URLs
   * @returns Normalized URL string
   */
  public normalizeUrl(href: string, baseUrl: string): string {
    try {
      const url = new URL(href, baseUrl)

      url.hostname = url.hostname.replace(/^www\./, '')

      url.hash = ''
      return url.toString()
    } catch {
      return ''
    }
  }

  /**
   * Normalizes query parameters in a URL based on configuration
   * @param url The URL to normalize
   * @param config Configuration for allowed query parameters
   * @returns URL with normalized query parameters
   */
  public normalizeQueryParams(url: string, config: UrlConfig): string {
    try {
      const urlObj = new URL(url)
      const { searchParams } = urlObj

      if (config.allowedQueryParams) {
        for (const key of Array.from(searchParams.keys())) {
          if (!config.allowedQueryParams.includes(key)) {
            searchParams.delete(key)
          }
        }
      }

      return urlObj.toString()
    } catch {
      return ''
    }
  }

  /**
   * Checks if a URL is within the specified domain
   * @param url The URL to check
   * @param baseDomain The base domain to check against
   * @returns True if the URL is within the domain
   */
  public isInDomain(url: string, baseDomain: string): boolean {
    try {
      const { hostname } = new URL(url)
      return hostname.endsWith(baseDomain)
    } catch {
      return false
    }
  }

  /**
   * Checks if a URL matches any of the relevant patterns
   * @param url The URL to check
   * @param patterns Array of regular expressions to match against
   * @param visitedUrls Set of already visited URLs
   * @returns True if the URL is relevant
   */
  public isRelevantUrl(url: string, patterns: RegExp[], visitedUrls: Set<string>): boolean {
    if (visitedUrls.has(url)) {
      return false
    }

    // Check if the URL matches any of the relevant patterns
    return patterns?.some((pattern) => pattern.test(url)) ?? true
  }
}
