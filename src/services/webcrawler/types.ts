import {
  CrawlerEntity,
  FetchType,
  ParamExtractor,
} from '../../modules/admin/models/crawlerDomainConfig'

/**
 * Configuration for a URL pattern to be crawled
 */
export interface UrlConfig {
  entity: CrawlerEntity
  initialUrls: string[]
  urlPatternRegExp: RegExp
  idPattern: RegExp | undefined
  allowedQueryParams?: string[] | undefined

  /**
   * Whether to add matching URLs to the crawl queue
   * If true, matching URLs will be added to the queue for further crawling
   * If false, matching URLs will be recorded but not crawled further
   */
  addToQueue?: boolean

  /**
   * Array of param extractors to generate additional URLs
   * Each extractor defines a selector to find elements on the page
   * and a query parameter to use in the generated URLs
   */
  paramExtractors?: ParamExtractor[]
}

/**
 * Options for configuring the web crawler
 */
export interface CrawlOptions {
  /** Limit the depth of crawling. */
  maxDepth: number
  /** Base domain to restrict crawling. */
  baseDomain: string
  /** Initial URLs to start crawling. */
  urlConfigs: UrlConfig[]
  /** Batch size for processing URLs (default: 1000) */
  batchSize?: number
  /** Callback function to process batches of URLs */
  onBatch?: (urls: MatchingURLS) => Promise<void>
  /** Array of relevant URL patterns to prioritize */
  relevantUrlPatterns?: RegExp[]
  /** Browser type to use for fetching URLs (default: puppeteer) */
  fetchType?: FetchType
  /** Number of parallel requests to make (default: 5) */
  parallelism?: number
}

/**
 * Map of matching URLs discovered during crawling
 */
export type MatchingURLS = Map<
  string,
  { url: string; entity: CrawlerEntity; externalId: string | undefined }
>

/**
 * Type of crawl to perform
 */
export enum CrawlType {
  Relevant = 'relevant',
  Full = 'full',
}

/**
 * URL pattern statistics
 */
export interface UrlPatternStats {
  urlPattern: string
  matchCount: number
}

/**
 * Statistics about the crawl
 */
export interface CrawlStats {
  totalVisitedUrls: number
  totalMatchingUrls: number
  urlPatternStats?: UrlPatternStats[]
}
