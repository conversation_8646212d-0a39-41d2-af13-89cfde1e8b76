import { UrlConfig } from '../types'

/**
 * Interface for URL extractors used by the WebCrawler
 * Defines the contract for extracting additional URLs from web pages
 */
export interface UrlExtractor {
  /**
   * Extracts additional links from a document based on URL configuration
   * @param document The parsed HTML document
   * @param url The base URL
   * @param urlConfig Configuration for URL extraction
   * @returns Array of additional URLs
   */
  extractAdditionalLinks(document: Document, url: string, urlConfig: UrlConfig): string[]
}

/**
 * Default implementation of the UrlExtractor interface
 */
export class DefaultUrlExtractor implements UrlExtractor {
  /**
   * Extracts additional links from a document based on URL configuration
   * @param document The parsed HTML document
   * @param url The base URL
   * @param urlConfig Configuration for URL extraction
   * @returns Array of additional URLs
   */
  public extractAdditionalLinks(document: Document, url: string, urlConfig: UrlConfig): string[] {
    const additionalLinks: string[] = []
    const baseUrl = new URL(url)
    const urlWithoutParams = baseUrl.origin + baseUrl.pathname

    // If no paramExtractors, return empty array
    if (!urlConfig.paramExtractors || urlConfig.paramExtractors.length === 0) {
      return additionalLinks
    }

    // Extract all possible values for each paramExtractor
    const paramValues: Map<string, string[]> = new Map()

    urlConfig.paramExtractors.forEach((extractor) => {
      const selectElement = document.querySelector(extractor.selector)
      const values: string[] = []

      if (selectElement) {
        selectElement.querySelectorAll('option').forEach((option) => {
          const value = option.getAttribute('value')

          if (value && value.length > 0) {
            values.push(value)
          }
        })
      }

      if (values.length > 0) {
        paramValues.set(extractor.queryParam, values)
      }
    })

    // If no values found, return empty array
    if (paramValues.size === 0) {
      return []
    }

    // Generate URLs with single parameters
    for (const [param, values] of paramValues.entries()) {
      for (const value of values) {
        additionalLinks.push(`${urlWithoutParams}?${param}=${value}`)
      }
    }

    // Generate URLs with combinations of parameters
    if (paramValues.size > 1) {
      this.generateParamCombinations(urlWithoutParams, paramValues, additionalLinks)
    }

    return additionalLinks
  }

  /**
   * Recursively generates all possible combinations of query parameters
   * @param baseUrl The base URL without query parameters
   * @param paramValues Map of parameter names to arrays of possible values
   * @param result Array to store the generated URLs
   * @param currentParams Current set of parameters being built
   * @param paramIndex Index of the current parameter being processed
   */
  private generateParamCombinations(
    baseUrl: string,
    paramValues: Map<string, string[]>,
    result: string[],
    currentParams: Map<string, string> = new Map(),
    paramIndex: number = 0
  ): void {
    const params = Array.from(paramValues.keys())

    // Base case: we've processed all parameters
    if (paramIndex >= params.length) {
      // Skip if we have less than 2 parameters (we already handled single param case)
      if (currentParams.size >= 2) {
        const queryString = Array.from(currentParams.entries())
          .map(([param, value]) => `${param}=${value}`)
          .join('&')
        result.push(`${baseUrl}?${queryString}`)
      }
      return
    }

    const currentParam = params[paramIndex]
    const values = paramValues.get(currentParam) || []

    // Skip this parameter
    this.generateParamCombinations(
      baseUrl,
      paramValues,
      result,
      new Map(currentParams),
      paramIndex + 1
    )

    // Include this parameter with each possible value
    for (const value of values) {
      const newParams = new Map(currentParams)
      newParams.set(currentParam, value)
      this.generateParamCombinations(baseUrl, paramValues, result, newParams, paramIndex + 1)
    }
  }
}
