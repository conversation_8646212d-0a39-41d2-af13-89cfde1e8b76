import { <PERSON><PERSON><PERSON> } from 'jsdom'
import pino from 'pino'
import Container from 'typedi'

import { FetchType } from '../../modules/admin/models/crawlerDomainConfig'

import { DefaultUrlExtractor, UrlExtractor } from './extractors/urlExtractor'
import { Fetcher } from './fetchers/fetcher'
import { DefaultUrlNormalizer, UrlNormalizer } from './normalizers/urlNormalizer'
import {
  CrawlOptions,
  CrawlStats,
  CrawlType,
  MatchingURLS,
  UrlConfig,
  UrlPatternStats,
} from './types'

class WebCrawler {
  private readonly logger: pino.Logger
  private readonly visited: Set<string> = new Set()
  private readonly ignored: Set<string> = new Set()
  private readonly queue: Map<string, () => Promise<void>> = new Map()
  private readonly matchingUrls: MatchingURLS = new Map()
  private readonly urlPatternStats: Map<string, number> = new Map()
  private processedUrls = 0
  private batchSize: number
  private lastBatchSize = 0
  private onBatch?: (urls: MatchingURLS) => Promise<void>
  private relevantUrlPatterns?: RegExp[]
  private lastLoggedCheckpoint = 0
  private fetchType: FetchType
  private crawlType: CrawlType
  private fetcher: Fetcher
  private urlExtractor: UrlExtractor
  private urlNormalizer: UrlNormalizer
  private parallelism: number

  constructor(private options: CrawlOptions) {
    this.logger = Container.get('app.logger')

    this.batchSize = options.batchSize || 1000
    this.onBatch = options.onBatch
    this.parallelism = options.parallelism || 5

    this.relevantUrlPatterns = options.relevantUrlPatterns
    this.fetchType = options.fetchType ?? FetchType.Axios

    // Create the appropriate fetcher based on the fetch type
    this.fetcher = Container.get(Fetcher)

    // Create the URL extractor and normalizer
    this.urlExtractor = new DefaultUrlExtractor()
    this.urlNormalizer = new DefaultUrlNormalizer()

    this.crawlType =
      !options.relevantUrlPatterns || options.relevantUrlPatterns.length === 0
        ? CrawlType.Full
        : CrawlType.Relevant
  }

  public async crawl(): Promise<{
    crawlStats: CrawlStats
  }> {
    for (const urlConfig of this.options.urlConfigs) {
      urlConfig.initialUrls.forEach((url) => this.queue.set(url, () => this.crawlUrl(url, 0)))
    }

    await this.processQueue()

    // Process any remaining URLs in the final batch
    if (this.onBatch && this.matchingUrls.size > this.lastBatchSize) {
      await this.processBatch()
    }

    // Convert URL pattern stats map to array
    const urlPatternStats: UrlPatternStats[] = Array.from(this.urlPatternStats.entries()).map(
      ([urlPattern, matchCount]) => ({
        urlPattern,
        matchCount,
      })
    )

    return {
      crawlStats: {
        totalVisitedUrls: this.visited.size,
        totalMatchingUrls: this.matchingUrls.size,
        urlPatternStats,
      },
    }
  }

  /**
   * Process a batch of discovered URLs by calling the onBatch callback
   * Only processes URLs that haven't been processed in previous batches
   */
  private async processBatch(): Promise<void> {
    if (!this.onBatch) return

    // Create a map of only the new URLs since the last batch
    const batchUrls: MatchingURLS = new Map()
    let count = 0

    // Get all URLs and filter only the new ones (after lastBatchSize)
    const entries = Array.from(this.matchingUrls.entries())
    const unprocessedCount = entries.length - this.lastBatchSize

    // Process either the batch size or all remaining URLs if less than batch size
    const batchCount = Math.min(unprocessedCount, this.batchSize)

    for (let i = this.lastBatchSize; i < this.lastBatchSize + batchCount; i++) {
      const [key, value] = entries[i]
      if (key.length > 0) {
        batchUrls.set(key, value)
        count++
      }
    }

    // Update the last batch size to include the newly processed URLs
    this.lastBatchSize += batchCount

    // Call the onBatch callback with the new batch
    if (count > 0) {
      this.logger.info(
        `Processing batch of ${count} new URLs (total processed: ${this.lastBatchSize}/${this.matchingUrls.size})`
      )
      await this.onBatch(batchUrls)
    }
  }

  private async processQueue(): Promise<void> {
    while (this.queue.size > 0) {
      const tasks = Array.from(this.queue.keys())
        .slice(0, this.parallelism)
        .map((key) => {
          const task = this.queue.get(key)
          this.queue.delete(key)
          return task
        })
        .filter((task): task is () => Promise<void> => task !== undefined)

      await Promise.all(tasks.map((task) => task()))

      this.processedUrls += tasks.length

      const checkpoint = Math.floor(this.processedUrls / 100) * 100
      if (checkpoint > this.lastLoggedCheckpoint) {
        this.lastLoggedCheckpoint = checkpoint
        this.logger.info(
          `Visited ${this.visited.size}, matching ${this.matchingUrls.size}, queue ${this.queue.size}`
        )
      }

      // Check if we've reached the batch size and need to process a batch
      if (this.onBatch && this.matchingUrls.size - this.lastBatchSize >= this.batchSize) {
        await this.processBatch()
      }
    }
  }

  private async crawlUrl(url: string, depth: number): Promise<void> {
    if (depth > this.options.maxDepth || this.visited.has(url)) {
      return
    }

    this.visited.add(url)

    if (!this.urlNormalizer.isInDomain(url, this.options.baseDomain)) {
      return
    }

    try {
      const result = await this.fetcher.fetch(url, { type: this.fetchType, timeout: 15_000 })
      if (!result || !result.content) {
        return
      }

      const dom = new JSDOM(result.content)
      const document = dom.window.document

      const links = Array.from(document.querySelectorAll('a')).map((el) => el.href)
      // add all options from select elements
      // for example EP has lot of links hidden in options
      // TODO: we might need to improve condition about includes http in the future, for other sites
      const options = Array.from(document.querySelectorAll('option'))
        .map((el) => el.value)
        .filter((el) => el.includes('http'))

      links.push(...options)

      // extract additional links from page
      const matchingConfig = this.getMatchingConfig(url)
      if (matchingConfig?.paramExtractors) {
        links.push(...this.urlExtractor.extractAdditionalLinks(document, url, matchingConfig))
      }

      links.forEach((link) => {
        if (link) {
          let normalizedUrl = this.urlNormalizer.normalizeUrl(link, url)
          const matchingConfig = this.getMatchingConfig(normalizedUrl)

          // We found a correct URL
          if (matchingConfig) {
            normalizedUrl = this.urlNormalizer.normalizeQueryParams(normalizedUrl, matchingConfig)

            let externalId = undefined
            if (matchingConfig.idPattern) {
              const id = normalizedUrl.match(matchingConfig.idPattern)?.[1]
              externalId = id ?? normalizedUrl
            }

            if (!this.matchingUrls.has(normalizedUrl)) {
              this.matchingUrls.set(normalizedUrl, {
                url: normalizedUrl,
                entity: matchingConfig.entity,
                externalId,
              })

              // Track URL pattern statistics
              const urlPattern = matchingConfig.urlPatternRegExp.toString()
              const currentCount = this.urlPatternStats.get(urlPattern) || 0
              this.urlPatternStats.set(urlPattern, currentCount + 1)

              // Check if we should add this URL to the queue for further crawling
              if (this.crawlType === CrawlType.Full || matchingConfig.addToQueue) {
                // if we have a match, we reset the depth, so it can find more matches
                this.queue.set(normalizedUrl, () => this.crawlUrl(normalizedUrl, 0))
              }

              return
            }
          }

          if (
            !this.visited.has(normalizedUrl) &&
            !this.ignored.has(normalizedUrl) &&
            !this.matchingUrls.has(normalizedUrl) &&
            !this.queue.has(normalizedUrl)
          ) {
            if (
              this.urlNormalizer.isInDomain(normalizedUrl, this.options.baseDomain) &&
              depth + 1 <= this.options.maxDepth
            ) {
              // If no relevant patterns are defined, all URLs are considered relevant
              if (this.crawlType === CrawlType.Full) {
                this.queue.set(normalizedUrl, () => this.crawlUrl(normalizedUrl, depth + 1))
                return
              }

              if (
                this.urlNormalizer.isRelevantUrl(
                  normalizedUrl,
                  this.relevantUrlPatterns || [],
                  this.visited
                )
              ) {
                this.queue.set(normalizedUrl, () => this.crawlUrl(normalizedUrl, 0))
              }
            }

            if (!this.urlNormalizer.isInDomain(normalizedUrl, this.options.baseDomain)) {
              this.ignored.add(normalizedUrl)
            }
          }
        }
      })
    } catch (error) {
      if (error.message === 'Request failed with status code 429') {
        this.logger.warn(`Rate limited for ${url} (waiting 5s): %s`, error.message)

        // re-add to queue
        this.queue.set(url, () => this.crawlUrl(url, 0))

        await new Promise((resolve) => setTimeout(resolve, 5000))
      } else if (
        error.message.includes('timeout of') ||
        error.message.includes('Navigation timeout of')
      ) {
        this.logger.warn(`Timeout for ${url}: %s`, error.message)

        // re-add to queue
        this.queue.set(url, () => this.crawlUrl(url, 0))
      } else {
        this.logger.warn(`Failed to crawl ${url}: %s`, error.message)
      }
    }
  }

  private getMatchingConfig(url: string): UrlConfig | undefined {
    return this.options.urlConfigs.find((config) => config.urlPatternRegExp.test(url))
  }
}

export default WebCrawler
