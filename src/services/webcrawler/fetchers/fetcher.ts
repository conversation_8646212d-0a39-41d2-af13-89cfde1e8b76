import pino from 'pino'
import Container, { Service } from 'typedi'

import { FetchType } from '../../../modules/admin/models/crawlerDomainConfig'

import { AxiosFetcher } from './axiosFetcher'
import { PuppeteerFetcher } from './puppeteerFetcher'

export interface FetcherOptions {
  type: FetchType
  timeout?: number
}

export interface FetchResult {
  content: string
  httpStatus?: number
  errorMessage?: string
}

export interface ExtractResult<T> {
  data: T
  httpStatus?: number
  errorMessage?: string
}

/**
 * Interface for URL fetchers used by the WebCrawler
 * Defines the contract for fetching content from URLs
 */
export interface IFetcher {
  /**
   * Fetches content from a URL
   * @param url The URL to fetch
   * @returns FetchResult containing the content, HTTP status, and any error message
   * @throws Error if the URL cannot be fetched
   */
  fetch(url: string, options: FetcherOptions): Promise<FetchResult>

  /**
   * Fetches content from a URL and extracts data using a provided script
   * @param url The URL to fetch
   * @param extractionScript The script to extract data from the fetched content
   * @returns ExtractResult containing the extracted data, HTTP status, and any error message
   * @throws Error if the URL cannot be fetched or if the extraction fails
   */
  fetchAndExtract<T>(
    url: string,
    extractionScript: string,
    options: FetcherOptions
  ): Promise<ExtractResult<T | null>>
}

@Service()
export class Fetcher implements IFetcher {
  private readonly logger: pino.Logger
  private readonly axiosFetcher: AxiosFetcher
  private readonly puppeteerFetcher

  constructor() {
    this.logger = Container.get('app.logger')
    this.axiosFetcher = Container.get<AxiosFetcher>('axios.fetcher')
    this.puppeteerFetcher = Container.get<PuppeteerFetcher>('puppeteer.fetcher')
  }

  async fetchAndExtract<T>(
    url: string,
    extractionScript: string,
    options: FetcherOptions
  ): Promise<ExtractResult<T | null>> {
    try {
      if (options.type === FetchType.Axios) {
        return await this.axiosFetcher.fetchAndExtract(url, extractionScript, options)
      } else if (options.type === FetchType.Puppeteer) {
        return await this.puppeteerFetcher.fetchAndExtract(url, extractionScript, options)
      } else {
        throw new Error(`Invalid fetch type: ${options.type}`)
      }
    } catch (error) {
      this.logger.error(`Fetcher failed to extract data from ${url}: ${error.message}`)
      return {
        data: null as unknown as T,
        httpStatus: error.status || 500,
        errorMessage: error.message,
      }
    }
  }

  public async fetch(url: string, options: FetcherOptions): Promise<FetchResult> {
    try {
      if (options.type === FetchType.Axios) {
        return await this.axiosFetcher.fetch(url, options)
      } else if (options.type === FetchType.Puppeteer) {
        return await this.puppeteerFetcher.fetch(url, options)
      } else {
        throw new Error(`Invalid fetch type: ${options.type}`)
      }
    } catch (error) {
      this.logger.error(`Fetcher failed to fetch ${url}: ${error.message}`)
      return {
        content: '',
        httpStatus: error.status || 500,
        errorMessage: error.message,
      }
    }
  }
}
