import config from 'config'
import pino from 'pino'
import puppeteer, { <PERSON><PERSON><PERSON>, executablePath, HTTPRequest } from 'puppeteer-core'
import Container, { Service } from 'typedi'

import { getCountries } from './countries'
import { ExtractResult, FetcherOptions, FetchResult, IFetcher } from './fetcher'

/**
 * Helper function to handle request interception for EliteProspects
 * This helps with performance as it does not download all the images and other assets
 *
 * TODO: This should be integrated into crawler domain configuration and
 * only used if the URL contains eliteprospects.com
 *
 * @param interceptedRequest The intercepted request from Puppeteer
 */
function handleEliteProspectsRequestInterception(interceptedRequest: HTTPRequest): void {
  // allow only gql requests as those are the ones which loads data (links) we need
  if (
    interceptedRequest.url().includes('gql.eliteprospects') ||
    (interceptedRequest.url().includes('cdn.eliteprospects-assets.com') &&
      interceptedRequest.url().includes('.js')) ||
    interceptedRequest.isNavigationRequest()
  ) {
    interceptedRequest.continue()
  } else {
    interceptedRequest.abort()
  }
}

/**
 * Implementation of Fetcher that uses <PERSON>uppeteer to fetch URLs
 */
@Service()
export class PuppeteerFetcher implements IFetcher {
  private readonly logger: pino.Logger
  private browser: Browser

  constructor() {
    this.logger = Container.get('app.logger')
  }

  async setup() {
    this.browser = await puppeteer.launch({
      ...(process.env.NODE_ENV !== 'dev'
        ? { executablePath: '/usr/bin/chromium-browser' }
        : {
            executablePath: process.env.CHROMIUM_PATH ?? executablePath('chrome'),
          }),
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-gpu',
        `--proxy-server=${config.oxylabs.host}:${config.oxylabs.port}`,
      ],
    })
  }

  /**
   * Fetches content from a URL using Puppeteer
   * @param url The URL to fetch
   * @returns FetchResult containing the content, HTTP status, and any error message
   */
  public async fetch(url: string, options: FetcherOptions): Promise<FetchResult> {
    let page
    try {
      if (this.browser === undefined) {
        await this.setup()
      }

      page = await this.browser.newPage()

      if (url.includes('eliteprospects.com')) {
        await page.setRequestInterception(true)
        page.on('request', (interceptedRequest) => {
          handleEliteProspectsRequestInterception(interceptedRequest)
        })
      }

      const username = config.oxylabs.username
      const password = config.oxylabs.password
      await page.authenticate({
        username: `user-${username}`,
        password: password,
      })

      const response = await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: options.timeout,
      })

      const httpStatus = response?.status() || 0

      // we ignore 400 because we try to explore data from options, which will include
      // non existing URLs in some cases
      if (httpStatus !== 200 && httpStatus !== 400) {
        this.logger.error(`Failed to get data from URL: ${url}. Status: ${httpStatus}`)
      }

      const data = await page.content()
      await page.close()

      return {
        content: data,
        httpStatus,
        errorMessage: httpStatus !== 200 ? `HTTP status: ${httpStatus}` : undefined,
      }
    } catch (error) {
      await page?.close()

      if (error.message === 'Protocol error: Connection closed.') {
        await this.browser.close()
        await this.setup()
      }

      this.logger.error(`Failed to get data from URL: ${url}. Error: ${error.message}`)

      return {
        content: '',
        httpStatus: error.status || 500,
        errorMessage: error.message,
      }
    }
  }

  public async fetchAndExtract<T>(
    url: string,
    extractionScript: string,
    options: FetcherOptions
  ): Promise<ExtractResult<T | null>> {
    let page
    try {
      if (this.browser === undefined) {
        await this.setup()
      }

      page = await this.browser.newPage()

      const username = config.oxylabs.username
      const password = config.oxylabs.password
      await page.authenticate({
        username: `user-${username}`,
        password: password,
      })

      const response = await page.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: options.timeout,
      })

      const httpStatus = response?.status() || 0

      // Get's country alpha-2 code from country name
      await page.exposeFunction('getCountryCode', (country: string) => {
        return getCountries().get(country)
      })

      const data = (await page.evaluate(extractionScript)) as T
      await page.close()

      return {
        data,
        httpStatus,
        errorMessage: httpStatus !== 200 ? `HTTP status: ${httpStatus}` : undefined,
      }
    } catch (error) {
      await page?.close()

      this.logger.error(`Failed to get data from URL: ${url}`, error)

      return {
        data: null,
        httpStatus: error.status || 500,
        errorMessage: error.message,
      }
    }
  }
}
