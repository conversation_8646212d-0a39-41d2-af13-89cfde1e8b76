import { AxiosInstance } from 'axios'
import { JSD<PERSON> } from 'jsdom'
import pino from 'pino'
import Container from 'typedi'
import vm from 'vm'

import { getAxiosClientWithProxy } from '../../../helpers/axios'

import { getCountries } from './countries'
import { ExtractResult, FetcherOptions, FetchResult, IFetcher } from './fetcher'

/**
 * Implementation of Fetcher that uses Axios to fetch URLs
 */
export class AxiosFetcher implements IFetcher {
  private readonly axiosClient: AxiosInstance
  private readonly axiosTimeout: number
  private readonly logger: pino.Logger

  constructor() {
    this.axiosClient = getAxiosClientWithProxy()
    this.logger = Container.get('app.logger')
  }

  /**
   * Fetches content from a URL and extracts data using a provided script
   * @param url The URL to fetch
   * @param extractionScript The script to extract data from the fetched content
   * @returns ExtractResult containing the extracted data, HTTP status, and any error message
   */
  async fetchAndExtract<T>(
    url: string,
    extractionScript: string,
    options: FetcherOptions
  ): Promise<ExtractResult<T | null>> {
    try {
      const response = await this.axiosClient.get(url, { timeout: options.timeout })
      const httpStatus = response.status

      if (httpStatus !== 200) {
        return {
          data: null,
          httpStatus,
          errorMessage: `HTTP status: ${httpStatus}`,
        }
      }

      try {
        const dom = new JSDOM(response.data, {
          url,
          contentType: 'text/html',
          runScripts: 'outside-only',
        })
        const { window } = dom

        // Inject the helper function
        window.getCountryCode = async function (country: string) {
          return getCountries().get(country)
        }

        const context = vm.createContext({
          window,
          document: window.document,
          location: window.location,
          getCountryCode: window.getCountryCode,
          console,
        })

        const script = new vm.Script(`${extractionScript}`)

        // Create an AbortController to handle timeout
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), options.timeout || 30000)

        try {
          const result = await Promise.race([
            script.runInContext(context),
            new Promise((_, reject) => {
              controller.signal.addEventListener('abort', () =>
                reject(new Error('Script execution timed out'))
              )
            }),
          ])
          clearTimeout(timeoutId)
          return {
            data: result,
            httpStatus,
            errorMessage: undefined,
          }
        } catch (error) {
          clearTimeout(timeoutId)
          throw error
        }
      } catch (err) {
        this.logger.error(`Failed to extract data from URL: ${url}`, err)
        return {
          data: null as unknown as T,
          httpStatus,
          errorMessage: err.message,
        }
      }
    } catch (error) {
      this.logger.error(`Failed to fetch URL: ${url}`, error)
      return {
        data: null as unknown as T,
        httpStatus: error.response?.status || 500,
        errorMessage: error.message,
      }
    }
  }

  /**
   * Fetches content from a URL using Axios
   * @param url The URL to fetch
   * @returns FetchResult containing the content, HTTP status, and any error message
   */
  public async fetch(url: string, options: FetcherOptions): Promise<FetchResult> {
    try {
      const response = await this.axiosClient.head(url, { timeout: options.timeout })
      const contentType = response.headers['content-type']

      if (contentType && !contentType.includes('text/html')) {
        return {
          content: '',
          httpStatus: response.status,
          errorMessage: 'Invalid content type',
        }
      }

      const fullResponse = await this.axiosClient.get(url, { timeout: this.axiosTimeout })
      return {
        content: fullResponse.data,
        httpStatus: fullResponse.status,
        errorMessage:
          fullResponse.status !== 200 ? `HTTP status: ${fullResponse.status}` : undefined,
      }
    } catch (error) {
      this.logger.error(`AxiosFetcher failed to fetch ${url}: ${error.message}`)
      return {
        content: '',
        httpStatus: error.response?.status || 500,
        errorMessage: error.message,
      }
    }
  }
}
