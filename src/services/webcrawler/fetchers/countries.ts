import * as isoCountries from 'i18n-iso-countries/langs/en.json'

const allCountries = isoCountries['countries']

export const getCountries = () => {
  const countries = new Map<string, string>()

  Object.entries(allCountries).forEach(([code, names]) => {
    if (Array.isArray(names)) {
      // Handle arrays of alternative names
      names.forEach((name) => {
        countries.set(name, code)
      })
    } else if (typeof names === 'string') {
      // Handle single name
      countries.set(names, code)
    }
  })

  // Add GB territories
  countries.set('Wales', 'GB')
  countries.set('Scotland', 'GB')
  countries.set('Northern Ireland', 'GB')
  countries.set('England', 'GB')

  return countries
}
