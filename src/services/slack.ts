import pino from 'pino'
import Container, { Service } from 'typedi'

import { WebClient } from '@slack/web-api'

@Service()
export class SlackService {
  private logger: pino.Logger
  private client: WebClient

  public constructor() {
    this.logger = Container.get<pino.Logger>('app.logger')
    this.client = Container.get<WebClient>('slack.client')
  }

  public async sendMessage(message: string, channel: string) {
    try {
      await this.client.chat.postMessage({
        channel,
        text: message,
      })
    } catch (err) {
      this.logger.error('Failed to send slack message', err)
    }
  }
}
