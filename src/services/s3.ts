import axios from 'axios'
import config from 'config'
import Container, { Service } from 'typedi'

import { getSignedUrl } from '../helpers/getSignedUrl'

import {
  CopyObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  GetObjectCommand,
  ListObjectsV2Command,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3'

@Service()
export class S3Service {
  private client: S3Client

  private bucket = config.aws.assetsBucket

  public constructor() {
    this.client = Container.get('s3.client')
  }

  public async createPresignedUrl(key: string, contentType: string, expiresIn: number = 300) {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      ContentType: contentType,
    })

    return getSignedUrl(this.client, command, { expiresIn })
  }

  public async remove(key?: string) {
    if (!key || key === '') return

    const command = new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: key,
    })
    return this.client.send(command)
  }

  public async emptyS3Directory(dir: string) {
    // just to be sure we don't delete everything
    if (!dir || dir === '' || dir === 'images/' || dir === 'videos/') return

    const listParams = {
      Bucket: this.bucket,
      Prefix: dir,
    }

    const listedObjects = await this.client.send(new ListObjectsV2Command(listParams))

    if (!listedObjects.Contents || listedObjects.Contents.length === 0) return

    const deleteParams: { Bucket: string; Delete: { Objects: { Key: string }[] } } = {
      Bucket: this.bucket,
      Delete: { Objects: [] },
    }

    listedObjects.Contents.forEach(({ Key }) => {
      if (!Key) return

      deleteParams.Delete.Objects.push({ Key })
    })

    await this.client.send(new DeleteObjectsCommand(deleteParams))
  }

  public async copyObject(sourceKey: string, destinationKey: string) {
    const command = new CopyObjectCommand({
      Bucket: this.bucket,
      Key: destinationKey,
      CopySource: `${this.bucket}/${sourceKey}`,
    })

    return this.client.send(command)
  }

  public async uploadFromBuffer(key: string, buffer: Buffer, contentType?: string, ttl?: number) {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: buffer,
      ...(contentType ? { ContentType: contentType } : {}),
      ...(ttl ? { CacheControl: `max-age=${ttl}` } : {}),
    })

    return this.client.send(command)
  }

  public async downloadToBuffer(key: string) {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    })

    const result = await this.client.send(command)

    return result.Body?.transformToByteArray()
  }

  public async uploadFromRemoteURL(key: string, url: string) {
    const resp = await axios.get(url, {
      decompress: false,
      responseType: 'arraybuffer',
    })

    return this.uploadFromBuffer(key, resp.data)
  }
}
