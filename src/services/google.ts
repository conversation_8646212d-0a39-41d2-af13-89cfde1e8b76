import { OAuth2Client } from 'google-auth-library'
import Container, { Service } from 'typedi'

interface GoogleUserInfo {
  id: string
  email?: string
  lastname?: string
  firstname?: string
}

@Service()
export default class GoogleService {
  client: OAuth2Client

  constructor() {
    this.client = Container.get('google.oauth2')
  }

  async getUserInfo(accessToken: string): Promise<GoogleUserInfo> {
    const ticket = await this.client.verifyIdToken({
      idToken: accessToken,
      audience: [
        // ios clients
        '869369456491-ipb9e58rab3udbbqn9kj37ors8l1eq0f.apps.googleusercontent.com', // prod
        '869369456491-bhg9evv5dso3n17ehbvtlscedrgqo7t6.apps.googleusercontent.com', // dev

        // android clients
        '869369456491-71aumm2uf6hjqepule0kfe8vdldgovh3.apps.googleusercontent.com', // dev
        '869369456491-tcc99d1f3u4r4bof5ar4ecihf1f451v6.apps.googleusercontent.com', // prod

        '869369456491-93rqjj2b2t0q3betsosu5ba9dgl3pf89.apps.googleusercontent.com', // web
      ],
    })
    const payload = ticket.getPayload()

    if (!payload) {
      throw new Error('Failed to get user info')
    }

    return {
      id: payload.sub,
      email: payload.email,
      firstname: payload.given_name,
      lastname: payload.family_name,
    }
  }
}
