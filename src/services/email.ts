import config from 'config'
import pino from 'pino'
import Container, { Service } from 'typedi'

import { SendEmailCommand, SendTemplatedEmailCommand, SESClient } from '@aws-sdk/client-ses'

export const EmailTemplate = {
  VERIFY_EMAIL: config.emailTemplates.VerifyEmail,
  CONTACT_MESSAGE: config.emailTemplates.ContactMessage,
  BOOST_PROFILE_SENDER: config.emailTemplates.BoostProfileSender,
  BOOST_PROFILE_RECEIVER: config.emailTemplates.BoostProfileReceiver,
  AGENT_INVITATION: config.emailTemplates.AgentInvitation,
} as const

type EmailTemplate = (typeof EmailTemplate)[keyof typeof EmailTemplate]

@Service()
export class EmailService {
  private sesClient: SESClient
  private logger: pino.Logger

  public constructor() {
    this.sesClient = Container.get<SESClient>('ses.client')
    this.logger = Container.get<pino.Logger>('app.logger')
  }

  public async sendEmail(
    to: string,
    template: EmailTemplate,
    data: Record<string, string>,
    replyTo?: string
  ) {
    const command = new SendTemplatedEmailCommand({
      Destination: {
        ToAddresses: [to],
      },
      ...(replyTo ? { ReplyToAddresses: [replyTo] } : {}),
      Source: config.aws.emailFrom,
      Template: template,
      TemplateData: JSON.stringify(data),
    })

    try {
      await this.sesClient.send(command)
    } catch (err) {
      this.logger.error('Failed to send email', err)
    }
  }

  public async sendSimpleEmail(to: string, subject: string, body: string, replyTo?: string) {
    const command = new SendEmailCommand({
      Destination: {
        ToAddresses: [to],
      },
      ...(replyTo ? { ReplyToAddresses: [replyTo] } : {}),
      Source: config.aws.emailFrom,
      Message: {
        Subject: {
          Data: subject,
        },
        Body: {
          Text: {
            Data: body,
          },
        },
      },
    })

    try {
      await this.sesClient.send(command)
    } catch (err) {
      this.logger.error('Failed to send email', err)
    }
  }
}
