import { SitemapGenerator } from '../services/web/sitemap.generator'

import { ActionContext } from '.'

export const generateSitemaps = async (ctx: ActionContext) => {
  const start = Date.now()

  ctx.logger.info('GenerateSitemap JOB start')
  const sitemapGenerator = new SitemapGenerator()

  try {
    await sitemapGenerator.generateSiteMap('player')
    await sitemapGenerator.generateSiteMap('agency')
    await sitemapGenerator.generateSiteMap('team')
  } catch (error) {
    ctx.logger.error(`GenerateSitemap JOB failed: ${error}`)
  }

  ctx.logger.info(`GenerateSitemap JOB ended after: ${Date.now() - start}ms`)
}
