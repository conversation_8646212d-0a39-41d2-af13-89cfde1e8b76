import config from 'config'
import Container from 'typedi'

import { DiscoveredUrl } from '../modules/admin/models/discoveredUrl'
import { Game, GameModel } from '../modules/stats/models/game/game'
import { RosterPlayer } from '../modules/stats/models/game/rosterPlayer'
import { UserModel } from '../modules/user/models'
import { Role, User } from '../modules/user/models/user'
import { addIceHockeyGameActivity } from '../services/helpers/streamFeed'
import { PushNotificationService, PushNotificationType } from '../services/pushNotification'
import { SlackService } from '../services/slack'

import { ActionContext } from '.'

// This job connects external data found by scraper with our db entities
export const syncGames = async (ctx: ActionContext) => {
  const start = Date.now()

  const games = await GameModel.find({
    syncedAt: { $exists: false },
    date: { $lte: new Date() },
    $or: [
      { 'events.0': { $exists: true } },
      { 'homeTeamRoster.0': { $exists: true } },
      { 'awayTeamRoster.0': { $exists: true } },
    ],
  }).populate('discoveredUrl')

  ctx.logger.info(`Starting game sync for ${games.length} games`)

  for (const game of games) {
    try {
      const rosterItems = [
        ...game.homeTeamRoster.filter((rosterItem) => rosterItem.externalInfo?.playerExternalId),
        ...game.awayTeamRoster.filter((rosterItem) => rosterItem.externalInfo?.playerExternalId),
      ]

      const externalIds = rosterItems.map((rosterItem) => rosterItem.externalInfo?.playerExternalId)

      const users = await UserModel.find({
        role: { $in: [Role.PLAYER, null] },
        $or: [
          {
            externalConnections: {
              $elemMatch: {
                crawlerDomainConfig: (game.discoveredUrl as DiscoveredUrl).crawlerDomainConfig,
                externalId: { $in: externalIds },
              },
            },
          },
        ],
      })

      for (const user of users) {
        const rosterItem = rosterItems.find((ei) =>
          user.externalConnections.find((ec) => ec.externalId === ei.externalInfo.playerExternalId)
        )

        if (!rosterItem) {
          continue
        }

        await sendGameNotification(rosterItem, user, game)
      }

      // add game activity to feed
      if (
        !game.streamFeedActivityId &&
        (game.homeTeamRoster?.length > 0 || game.awayTeamRoster?.length > 0) &&
        game.date.getTime() > Date.now() - 48 * 60 * 60 * 1000
      ) {
        try {
          const newGameActivity = await addIceHockeyGameActivity(game)

          if (newGameActivity?.id) {
            game.streamFeedActivityId = newGameActivity.id as string
          }
        } catch (error) {
          console.error('Error adding game activity to feed', error)
        }
      } else {
        // TODO decide when it makes sense to update existing game activity
      }

      game.syncedAt = new Date()
      await game.save()
    } catch (err) {
      ctx.logger.error(`Failed to sync stats for game ${game.id}`, err)
    }
  }

  ctx.logger.info(`Game sync finished in ${Date.now() - start}ms`)
}

const sendGameNotification = async (rosterItem: RosterPlayer, user: User, game: Game) => {
  const pushNotificationService = Container.get(PushNotificationService)
  const slackService = Container.get(SlackService)

  if (
    rosterItem.points &&
    rosterItem.points > 0 &&
    (user.role === Role.PLAYER || user.role === null)
  ) {
    let notification
    let body
    const teamAgainst = game.homeTeamRoster.find((rosterItem) =>
      user.externalConnections.find(
        (ec) => ec.externalId === rosterItem.externalInfo.playerExternalId
      )
    )
      ? game.externalInfo?.awayTeamName
      : game.externalInfo?.homeTeamName

    const goals = rosterItem.goals ?? 0
    const assists = rosterItem.assists ?? 0
    if (goals > 0) {
      notification = `🥅 Upload your Goal!`
      body = `Congrats! You scored ${rosterItem.goals} goal${rosterItem.goals! > 1 ? 's' : ''} against ${teamAgainst}. Upload a clip so everyone can see you scoring.`
    } else if (assists > 0) {
      notification = `🏒 You had ${rosterItem.assists} assist${rosterItem.assists! > 1 ? 's' : ''}, GRAET job!`
      body = `${user.firstname}, you made a difference against ${teamAgainst}. Keep it up.`
    }

    // send notification only if the game is recent
    // as we process also historical data
    if (game.date.getTime() > Date.now() - 48 * 60 * 60 * 1000 && notification && body) {
      const pushResponse = await pushNotificationService.sendPushNotification(user, {
        title: notification,
        message: body,
        params: {
          destinationPath: `/uploadWallVideo/add`,
        },
        type: PushNotificationType.GAME_STATS_CLIP_UPLOAD,
        key: `game-${game._id.toString()}-${user._id}`,
        forcePush: true,
      })

      if (pushResponse.sent) {
        // send slack notification so we can see what kind of notifications are being sent
        // can be removed after some time
        await slackService.sendMessage(
          `Sending game notification to <https://www.graet.com/${user.slug}|${user.name} at Graet> (<https://admin.graet.com/dashboard/users/form/?id=${user._id.toString()}|user-admin>) - <https://ceskyhokej.cz/game/detail/${game.externalId}|game details> (<https://admin.graet.com/dashboard/statistics/games/detail/?id=${game._id.toString()}|game admin>): ${notification} ${body}`,
          config.slack.channels.gameStatsNotifications
        )
      }
    }
  }
}
