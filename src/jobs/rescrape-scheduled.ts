import { BatchProcessor } from '../helpers/batchProcessor'
import { CrawlerEntity } from '../modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../modules/admin/models/discoveredUrl'

import { ActionContext } from '.'

export const rescrapeScheduled = async (ctx: ActionContext) => {
  const urlsToScrape = await DiscoveredUrlModel.find({
    'scraperInfo.scheduledForScrapeAt': {
      $lte: new Date(),
    },
    entity: { $in: [CrawlerEntity.GAME, CrawlerEntity.LEAGUE, CrawlerEntity.PLAYER_STATS] },
  })
    .limit(1000)
    .populate('crawlerDomainConfig')

  ctx.logger.info(`Starting rescrape for ${urlsToScrape.length} URLs`)

  const batchProcessor = new BatchProcessor()
  await batchProcessor.processUrls(ctx, urlsToScrape, {
    batchSize: 10,
  })
}
