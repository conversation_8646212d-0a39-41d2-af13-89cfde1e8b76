import Container from 'typedi'

import UserStatsSyncerService from '../modules/stats/services/userStatsSyncer.service'
import { UserModel } from '../modules/user/models'
import { ExternalLinkType } from '../modules/user/models/user'

import { ActionContext } from '.'

export const syncStats = async (ctx: ActionContext) => {
  const syncer = Container.get<UserStatsSyncerService>('userStatsSyncer.service')

  const start = Date.now()

  // get all users which have outdated stats (which means last update was done more than day ago) or stats were never synced
  const users = await UserModel.find(
    {
      $and: [
        {
          $or: [
            { statsSyncedAt: { $lt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) } },
            { statsSyncedAt: { $exists: false } },
          ],
        },
        {
          $or: [
            { 'externalLinks.type': ExternalLinkType.ELITE_PROSPECTS },
            { 'externalLinks.scrapable': true },
          ],
        },
      ],
    },
    undefined,
    { limit: 250 }
  )

  ctx.logger.info(`Starting stats sync for ${users.length} users`)

  // for each user, update stats
  for (const user of users) {
    // update stats
    try {
      await syncer.syncUserStats(user.id)
    } catch (err) {
      ctx.logger.error(`Failed to sync stats for user ${user.id}`, err)
    }
  }

  ctx.logger.info(`Stats sync finished in ${Date.now() - start}ms`)
}
