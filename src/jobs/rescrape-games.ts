import dayjs from 'dayjs'
import Container from 'typedi'

import { DiscoveredUrl } from '../modules/admin/models/discoveredUrl'
import { GameModel } from '../modules/stats/models/game/game'
import GameSyncerService from '../modules/stats/services/gameSyncer.service'

import { ActionContext } from '.'

import { DocumentType } from '@typegoose/typegoose'

const parseArgs = (args: string[]): Record<string, string> => {
  const result: Record<string, string> = {}
  args.forEach((arg) => {
    const [key, value] = arg.split('=')
    if (key && value) {
      result[key] = value
    }
  })
  return result
}

/**
 * Rescrape games within a specified date range.
 *
 * @param ctx - The action context containing logger and other utilities.
 * @param args - Command line arguments containing startDate and endDate.
 * @throws Error if startDate or endDate are not provided.
 *
 * @example How to run the script
 *
 * pn build && NODE_ENV=dev pn job:rescrape-game startDate=2025-03-01 endDate=2025-03-09 | npx pino-pretty
 */
export const rescrapeGames = async (ctx: ActionContext, ...args: string[]) => {
  const gameSyncerService = Container.get(GameSyncerService)

  const params = parseArgs(args)

  if (!params.startDate || !params.endDate) {
    throw new Error('startDate and endDate are required')
  }

  const isDev = process.env.NODE_ENV === 'dev'

  const failedToScrape = []
  const startTime = performance.now()

  const logEndTime = () => {
    const endTime = performance.now()
    const executionTime = (endTime - startTime) / 1000
    ctx.logger.info(`Script execution time: ${executionTime.toFixed(2)} seconds`)
  }

  const handleTermination = async (signal: string) => {
    ctx.logger.info(`\nReceived ${signal}. Gracefully shutting down...`)
    logEndTime()
  }

  process.on('SIGINT', () => handleTermination('SIGINT'))
  process.on('SIGTERM', () => handleTermination('SIGTERM'))

  try {
    const games = await GameModel.find({
      date: {
        $gte: dayjs(params.startDate).startOf('day'),
        $lte: dayjs(params.endDate).endOf('day'),
      },
    }).populate([{ path: 'discoveredUrl', populate: { path: 'crawlerDomainConfig' } }])

    ctx.logger.info(`Total games to rescrape: ${games.length}`)

    let index = 1
    for (const game of games) {
      try {
        if (game.discoveredUrl) {
          await gameSyncerService.syncData(game.discoveredUrl as DocumentType<DiscoveredUrl>)

          ctx.logger.info(
            `${index}/${games.length} - Game id: ${game.id} ${dayjs(game.date).format('DD.MM.YYYY HH:mm')} - externalId: ${game.externalId} - rescraped!`
          )
        } else {
          ctx.logger.error(`${index}/${games.length} - Game id: ${game._id} has no discoveredUrl`)
        }
        index++
      } catch (error) {
        failedToScrape.push(game)
        ctx.logger.error(
          `${index}/${games.length} - Failed to rescrape game: Game id: ${game.id} ${dayjs(game.date).format('DD.MM.YYYY HH:mm')} - externalId: ${game.externalId}`
        )

        if (!isDev) {
          ctx.logger.error(
            `${index}/${games.length} - error: ${game.id} ${dayjs(game.date).format('DD.MM.YYYY HH:mm')} - externalId: ${game.externalId}`,
            error
          )
        }

        index++
      }
    }

    if (failedToScrape.length > 0) {
      ctx.logger.error(`Failed to rescrape ${failedToScrape.length}/${games.length} games`)
      failedToScrape.forEach((game) => {
        ctx.logger.error(
          `Failed to rescrape game: Game id: ${game.id} ${dayjs(game.date).format('DD.MM.YYYY HH:mm')} - externalId: ${game.externalId}`
        )
      })
    }
  } catch (error) {
    ctx.logger.error(`Failed to rescrape game: `, error.message)
  } finally {
    process.off('SIGINT', () => handleTermination('SIGINT'))
    process.off('SIGTERM', () => handleTermination('SIGTERM'))

    logEndTime()
  }
}
