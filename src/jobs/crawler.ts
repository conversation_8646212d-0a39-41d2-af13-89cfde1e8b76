import {
  CrawlerDomainConfig,
  CrawlerDomainConfigModel,
} from '../modules/admin/models/crawlerDomainConfig'
import { CrawlerRunStatsModel } from '../modules/admin/models/crawlerRunStats'
import { DiscoveredUrlModel } from '../modules/admin/models/discoveredUrl'
import { MatchingURLS } from '../services/webcrawler/types'
import WebCrawler from '../services/webcrawler/webCrawler'

import { ActionContext } from '.'

/**
 * Process a batch of discovered URLs and save them to the database
 * Returns the number of newly created URLs
 */
async function processBatch(
  ctx: ActionContext,
  crawlerDomainConfig: CrawlerDomainConfig,
  batchUrls: MatchingURLS
): Promise<number> {
  try {
    // save discovered URLs to DB
    const discoveredUrlModels = []
    for (const [_, { url, entity, externalId }] of batchUrls.entries()) {
      discoveredUrlModels.push({
        updateOne: {
          filter: { url },
          update: {
            url,
            entity,
            crawlerDomainConfig: crawlerDomainConfig._id,
            externalId,
          },
          upsert: true,
        },
      })
    }

    const bulkWriteResult = await DiscoveredUrlModel.bulkWrite(discoveredUrlModels)
    const newlyCreatedUrls = bulkWriteResult.upsertedCount || 0

    ctx.logger.info(
      `Saved batch of ${batchUrls.size} URLs from domain ${crawlerDomainConfig.domain} (${newlyCreatedUrls} new)`
    )

    return newlyCreatedUrls
  } catch (err) {
    ctx.logger.error(`Failed to save batch of URLs from domain ${crawlerDomainConfig.domain}`, err)
    return 0
  }
}

export const crawler = async (ctx: ActionContext) => {
  const crawlerDomainConfigs = await CrawlerDomainConfigModel.find({ enabled: { $ne: false } })

  for (const crawlerDomainConfig of crawlerDomainConfigs) {
    ctx.logger.info(`Crawling domain ${crawlerDomainConfig.domain}`)

    // Convert string patterns to RegExp objects if provided
    const relevantUrlPatterns = crawlerDomainConfig?.relevantUrlPatterns?.map(
      (pattern) => new RegExp(pattern)
    )

    if (relevantUrlPatterns && relevantUrlPatterns.length > 0) {
      ctx.logger.info(
        `Using ${relevantUrlPatterns.length} relevant URL patterns for focused crawling`
      )
    } else {
      ctx.logger.info('No relevant URL patterns provided, crawling all URLs in domain')
    }

    // Track newly created URLs
    let totalNewlyCreatedUrls = 0

    const crawler = new WebCrawler({
      baseDomain: crawlerDomainConfig.domain,
      maxDepth: 2,
      batchSize: 1000,
      parallelism: crawlerDomainConfig.parallelism,

      onBatch: async (batchUrls) => {
        const newlyCreatedUrls = await processBatch(ctx, crawlerDomainConfig, batchUrls)
        totalNewlyCreatedUrls += newlyCreatedUrls
      },

      relevantUrlPatterns,
      fetchType: crawlerDomainConfig.fetchType,

      urlConfigs: crawlerDomainConfig.urlConfigs.map((urlConfig) => ({
        entity: urlConfig.entity,
        initialUrls: urlConfig.initialUrls,
        urlPatternRegExp: new RegExp(urlConfig.urlPatternRegExp),
        allowedQueryParams: urlConfig.allowedQueryParams,
        idPattern: urlConfig.externalIdPatternRegExp
          ? new RegExp(urlConfig.externalIdPatternRegExp)
          : undefined,
        paramExtractors: urlConfig.paramExtractors,
        addToQueue: urlConfig.addToQueue,
      })),
    })

    // start time
    const startTime = Date.now()

    const { crawlStats } = await crawler.crawl()
    const finishedAt = new Date()
    const durationMs = Date.now() - startTime

    // Log crawl statistics
    ctx.logger.info(`Crawl ${crawlerDomainConfig.domain} finished in ${durationMs / 1000} seconds`)
    ctx.logger.info(`Crawl statistics for domain ${crawlerDomainConfig.domain}:`)
    ctx.logger.info(`  - Total visited URLs: ${crawlStats.totalVisitedUrls}`)
    ctx.logger.info(`  - Total discovered URLs: ${crawlStats.totalMatchingUrls}`)
    ctx.logger.info(`  - Newly discovered URLs: ${totalNewlyCreatedUrls}`)

    // Save crawl statistics to database
    await CrawlerRunStatsModel.create({
      crawlerDomainConfig: crawlerDomainConfig._id,
      startedAt: new Date(startTime),
      finishedAt,
      durationMs,
      totalVisitedUrls: crawlStats.totalVisitedUrls,
      totalDiscoveredUrls: crawlStats.totalMatchingUrls,
      newlyDiscoveredUrls: totalNewlyCreatedUrls,
      urlPatternStats: crawlStats.urlPatternStats?.map((stat) => ({
        urlPattern: stat.urlPattern,
        matchCount: stat.matchCount,
      })),
    })

    ctx.logger.info(`Saved crawl statistics for domain ${crawlerDomainConfig.domain}`)
  }
}
