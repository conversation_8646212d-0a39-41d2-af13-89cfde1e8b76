import 'reflect-metadata'
import { Command } from 'commander'
import config from 'config'
import pino from 'pino'

import { logger } from '../container'
import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'

import { crawler } from './crawler'
import { gameSyncEntities } from './game-sync-entities'
import { generateSitemaps } from './generate-sitemaps'
import { leagueStatsSyncEntities } from './league-stats-sync-entities'
import { markE2eTesterAccountForDeletion } from './mark-e2e-tester-account-for-deletion'
import { pushNotifications } from './push-notifications'
import { removeDeletedUsers } from './remove-deleted-users'
import { rescrapeGames } from './rescrape-games'
import { rescrapeScheduled } from './rescrape-scheduled'
import { scraper } from './scraper'
import { sendBatchNotification } from './send-batch-notification'
import { syncGames } from './sync-games'
import { syncStats } from './sync-stats'

import { flush } from '@amplitude/analytics-node'
import * as amplitude from '@amplitude/analytics-node'

amplitude.init(config.amplitude.apiKey)

export type ActionContext = {
  logger: pino.Logger
}

const runAction = async (action: (ctx: ActionContext, ...args: string[]) => void) => {
  try {
    await connectToMongoDB().catch((err) => {
      logger.error('DB startup error', err)
      process.exit(1)
    })

    await action({ logger }, ...process.argv.slice(3, process.argv.length + 1))

    // flush all amplitude events
    await flush().promise
    await closeMongoDBConnection()

    process.exit(0)
  } catch (err) {
    console.log(err)
    logger.error(err, 'Failed to run action.')
    process.exit(1)
  }
}

const program = new Command()

program.version('0.1.0')

program.command('syncStats').action(() => {
  runAction(syncStats).catch((err) => logger.error(err, 'Failed to run syncStats.'))
})

program.command('syncGames').action(() => {
  runAction(syncGames).catch((err) => logger.error(err, 'Failed to run syncGame.'))
})

program.command('gameSyncEntities').action(() => {
  runAction(gameSyncEntities).catch((err) => logger.error(err, 'Failed to run gameSyncEntities.'))
})

program.command('leagueStatsSyncEntities').action(() => {
  runAction(leagueStatsSyncEntities).catch((err) =>
    logger.error(err, 'Failed to run leagueStatsSyncEntities.')
  )
})

program.command('pushNotifications').action(() => {
  runAction(pushNotifications).catch((err) => logger.error(err, 'Failed to run pushNotifications.'))
})

program.command('removeDeletedUsers').action(() => {
  runAction(removeDeletedUsers).catch((err) =>
    logger.error(err, 'Failed to run removeDeletedUsers.')
  )
})

program.command('crawler').action(() => {
  runAction(crawler).catch((err) => logger.error(err, 'Failed to run crawler.'))
})

program.command('scraper').action(() => {
  runAction(scraper).catch((err) => logger.error(err, 'Failed to run scraper.'))
})

program.command('rescrapeScheduled').action(() => {
  runAction(rescrapeScheduled).catch((err) =>
    logger.error(err, 'Failed to run scraper - rescraping scheduled.')
  )
})

program.command('rescrapeGames').action(() => {
  runAction(rescrapeGames).catch((err) =>
    logger.error(err, 'Failed to run scraper - rescraping game.')
  )
})

program.command('generateSitemaps').action(() => {
  runAction(generateSitemaps).catch((err) => logger.error(err, 'Failed to run sitemap generator.'))
})

program.command('sendBatchNotification').action(() => {
  runAction(sendBatchNotification).catch((err) =>
    logger.error(err, 'Failed to run sendBatchNotification.')
  )
})

program.command('markE2eTesterAccountForDeletion').action(() => {
  runAction(markE2eTesterAccountForDeletion).catch((err) =>
    logger.error(err, 'Failed to run markE2eTesterAccountForDeletion.')
  )
})

program.parse(process.argv)
