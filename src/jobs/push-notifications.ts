import config from 'config'
import Container from 'typedi'

import { createAggregatedNotification } from '../helpers/notifications'
import { UserModel } from '../modules/user/models'
import { NotificationModel } from '../modules/user/models/notification'
import Analytics from '../services/analytics'
import { SNSService } from '../services/sns'

import { ActionContext } from '.'

const analytics = Container.get<Analytics>('analytics.service')

export const pushNotifications = async (ctx: ActionContext) => {
  const start = Date.now()

  const sns = Container.get<SNSService>('sns.service')

  const delayPeriod = new Date(Date.now() - config.pushNotifications.delayInMinutes * 60 * 1000)

  // Get notifications which were not sent grouped by user
  const userNotifications = await NotificationModel.aggregate([
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'user1',
      },
    },
    {
      $match: {
        sent: false,
        'user1.lastNotificationSentAt': { $lt: delayPeriod },
      },
    },
    {
      $group: {
        _id: '$user1',
        notifications: {
          $push: {
            id: '$_id',
            title: '$title',
            body: '$body',
            destinationPath: '$destinationPath',
            type: '$type',
          },
        },
      },
    },
  ])

  for (const user of userNotifications) {
    const u = user._id[0]

    const notification = createAggregatedNotification(user.notifications)

    for (const device of u.registeredDevices ?? []) {
      try {
        await sns.sendEndpointNotification(
          device.arn,
          notification.title,
          notification.body,
          u._id.toString(),
          {
            pushType: notification.type,
            destinationPath: notification.destinationPath,
          }
        )

        await analytics.trackEvent(
          'pushNotificationSent',
          {
            userId: user._id.toString(),
            type: notification.type,
          },
          user._id.toString()
        )
      } catch (err) {
        // catch error and update notification as sent
        // it is better to not send notification than to send it twice
        ctx.logger.error(`Failed to send notification to device ${device.arn}`, err)
      }
    }

    await UserModel.updateOne({ _id: u._id }, { lastNotificationSentAt: new Date() })
    await NotificationModel.updateMany({ user: u._id, sent: false }, { sent: true })
  }

  ctx.logger.info(`Sent notifications to ${userNotifications.length} users`)
  ctx.logger.info(`Stats sync finished in ${Date.now() - start}ms`)
}
