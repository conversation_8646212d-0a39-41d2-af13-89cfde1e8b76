import { Types } from 'mongoose'
import Container from 'typedi'

import { BatchNotificationModel } from '../modules/admin/models/batchNotification'
import { BatchNotificationState } from '../modules/admin/types/batchNotification'
import { UserModel } from '../modules/user/models'
import { User } from '../modules/user/models/user'
import { PushNotificationService } from '../services/pushNotification'

import { ActionContext } from '.'

export const sendBatchNotification = async (ctx: ActionContext) => {
  const pushService = Container.get<PushNotificationService>('pushNotification.service')

  const start = Date.now()
  ctx.logger.info('Starting batch push notifications job')

  const batch = await BatchNotificationModel.findOne({
    state: BatchNotificationState.SCHEDULED,
    $or: [{ scheduledAt: { $exists: false } }, { scheduledAt: { $lte: new Date() } }],
  })

  if (!batch) {
    ctx.logger.info('No scheduled batch notifications found, skipping...')
    return
  }

  let sentCount = batch.numberOfSent | 0
  const pageSize = 500
  let users

  const baseConditions: Record<string, unknown> = {
    role: batch.filter.role,
    registeredDevices: { $exists: true, $ne: [] },
  }

  if (batch.filter.nationalities?.length) {
    baseConditions.country = { $in: batch.filter.nationalities }
  }

  if (batch.filter.numberOfVideos?.length) {
    const numberOfVideos = batch.filter.numberOfVideos.map((val) => Number(val))

    if (numberOfVideos.includes(8)) {
      baseConditions.numberOfVideos = { $gte: 8 }
    } else {
      baseConditions.numberOfVideos = { $in: numberOfVideos }
    }
  }

  if (batch.filter.firstNameInitials?.length) {
    const regexConditions = batch.filter.firstNameInitials.map((initial) => ({
      firstname: { $regex: `^${initial}`, $options: 'i' },
    }))

    baseConditions.$or = regexConditions
  }

  if (batch.filter.yearOfBirth?.length) {
    const yearConditions = batch.filter.yearOfBirth.map((year) => ({
      dateOfBirth: {
        $gte: new Date(`${year}-01-01`),
        $lte: new Date(`${year + 1}-01-01`),
      },
    }))

    baseConditions.$or = yearConditions
  }

  const totalUsers = await UserModel.countDocuments(baseConditions)

  const waitBetweenPushes = 600 // Maximum 1000 notifications per 10 minutes
  let lastUserId: Types.ObjectId | null = null

  try {
    await BatchNotificationModel.updateOne(
      { _id: batch._id },
      { state: BatchNotificationState.IN_PROGRESS, totalUsers }
    )

    do {
      const conditions = { ...baseConditions }
      if (lastUserId) {
        conditions._id = { $gt: lastUserId }
      }

      users = (await UserModel.find(conditions).sort({ _id: 1 }).limit(pageSize)) as User[]

      for (const user of users) {
        try {
          await pushService.sendPushNotification(user, {
            title: batch.title,
            message: batch.message,
            type: batch.type,
            forcePush: batch.forcePush,
            params: { destinationPath: batch.destinationPath || '/' },
          })

          await new Promise((resolve) => setTimeout(resolve, waitBetweenPushes))
        } catch (err) {
          ctx.logger.error(`Failed to send notification to user ${user._id}`, err)
        }
      }

      sentCount += users.length
      lastUserId = users[users.length - 1]?._id

      await BatchNotificationModel.updateOne(
        { _id: batch._id },
        { $set: { numberOfSent: sentCount } }
      )

      const currentBatch = await BatchNotificationModel.findById(batch._id)
      if (!currentBatch || currentBatch.state !== BatchNotificationState.IN_PROGRESS) {
        ctx.logger.info(`Batch notification ${batch._id} was cancelled, stopping...`)
        return
      }

      ctx.logger.info(`Sent ${sentCount} notifications.`)
    } while (users.length > 0)

    await BatchNotificationModel.updateOne(
      { _id: batch._id },
      { state: BatchNotificationState.SENT }
    )

    ctx.logger.info(`Batch notification ${batch._id} sent successfully.`)
  } catch (error) {
    ctx.logger.error(`Error processing batch notification ${batch._id}`, error)
  }

  ctx.logger.info(`Batch notifications job finished in ${Date.now() - start}ms`)
}
