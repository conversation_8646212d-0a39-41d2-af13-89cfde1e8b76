import { GameModel } from '../modules/stats/models/game/game'

import { ActionContext } from '.'

export const gameSyncEntities = async (ctx: ActionContext) => {
  const start = Date.now()

  const games = await GameModel.find({
    $or: [{ homeTeam: null }, { awayTeam: null }, { league: null }],
    ignore: { $ne: true },
  })

  ctx.logger.info(`Starting game sync entities for ${games.length} games`)

  let updatedGames = 0
  for (const game of games) {
    await game.syncEntities()

    updatedGames++
    if (updatedGames % 100 === 0) {
      ctx.logger.info(`Updated ${updatedGames} games`)
    }

    await game.save()
  }

  ctx.logger.info(`Game sync finished in ${Date.now() - start}ms`)
}
