import { LeagueStatsModel } from '../modules/stats/models/leagueStats/leagueStats'

import { ActionContext } from '.'

export const leagueStatsSyncEntities = async (ctx: ActionContext) => {
  const start = Date.now()

  const leagueStats = await LeagueStatsModel.find({
    $or: [{ team: null }, { league: null }],
  })

  ctx.logger.info(`Starting league stats sync entities for ${leagueStats.length} leagues stats`)

  let updatedLeagueStats = 0
  for (const leagueStatsItem of leagueStats) {
    await leagueStatsItem.syncEntities()

    updatedLeagueStats++
    if (updatedLeagueStats % 100 === 0) {
      ctx.logger.info(`Updated ${updatedLeagueStats} leagues`)
    }

    await leagueStatsItem.save()
  }

  ctx.logger.info(`League stats sync finished in ${Date.now() - start}ms`)
}
