import Container from 'typedi'

import { createShadowUser } from '../modules/admin/helpers/shadowUser'
import { UserModel } from '../modules/user/models'
import { CreationSource, Role } from '../modules/user/models/user'
import UserRepository from '../modules/user/repositories/user.repository'

import { ActionContext } from '.'

export const removeDeletedUsers = async (ctx: ActionContext) => {
  const userRepository = Container.get(UserRepository)

  const start = Date.now()

  const users = await UserModel.find(
    {
      markedForDeletion: true,
    },
    undefined,
    { limit: 100 }
  )

  ctx.logger.info(`Starting removing deleted users - ${users.length}`)

  // for each user, delete stats, videos, avatar etc
  for (const user of users) {
    try {
      ctx.logger.info(`Removing user ${user.id} ${user.name}`)

      await userRepository.deleteUserData(user)

      // is deleted account is type of user and having external connections
      // we need to create shadow account again
      if (user.role === Role.PLAYER && user.externalConnections.length > 0) {
        await createShadowUser({
          country: user.country!,
          ...(user.dateOfBirth ? { dateOfBirth: user.dateOfBirth.toISOString() } : {}),
          yearsOfBirth: user.possibleYearsOfBirth,
          firstname: user.firstname,
          lastname: user.lastname,
          name: user.name,
          source: CreationSource.AccountDeletion,
        })
      }
    } catch (err) {
      ctx.logger.error(`Failed to cleanup account ${user.id} ${user.name}`, err)
    }
  }

  ctx.logger.info(`Removing of accounts finished after ${Date.now() - start}ms`)
}
