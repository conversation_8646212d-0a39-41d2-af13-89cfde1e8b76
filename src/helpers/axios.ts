import axios from 'axios'
import config from 'config'
import https from 'https'

export const getAxiosClientWithProxy = () => {
  const username = config.oxylabs.username
  const country = 'US'
  const password = config.oxylabs.password

  const proxySettings = {
    protocol: 'https',
    host: config.oxylabs.host,
    port: config.oxylabs.port,
    auth: {
      username: `user-${username}-country-${country}`,
      password: password,
    },
  }

  const client = axios.create({
    httpsAgent: new https.Agent({
      rejectUnauthorized: false,
    }),
    proxy: proxySettings,
  })

  return client
}
