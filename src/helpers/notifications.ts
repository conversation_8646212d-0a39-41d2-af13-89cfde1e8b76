import { Notification } from '../modules/user/models/notification'

export const createAggregatedNotification = (notifications: Notification[]): Notification => {
  if (notifications.length === 1) {
    return notifications[0]
  }

  // get number of unique notifications by body
  const uniqueNotifications = new Set(notifications.map((n) => n.body))

  if (uniqueNotifications.size === 1) {
    return notifications[0]
  }

  const aggregatedNotification = new Notification()
  aggregatedNotification.title = 'New followers'
  aggregatedNotification.body = `You have ${uniqueNotifications.size} new followers`
  aggregatedNotification.destinationPath = '/followers'

  return aggregatedNotification
}
