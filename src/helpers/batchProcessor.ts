import Container from 'typedi'

import { ActionContext } from '../jobs'
import { CrawlerEntity } from '../modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrl } from '../modules/admin/models/discoveredUrl'
import GameSyncerService from '../modules/stats/services/gameSyncer.service'
import LeagueStatsSyncerService from '../modules/stats/services/leagueStatsSyncer.service'
import UserInfoSyncerService from '../modules/stats/services/userInfoSyncer.service'
import UserStatsSyncerService from '../modules/stats/services/userStatsSyncer.service'

import { DocumentType } from '@typegoose/typegoose'

export interface BatchProcessorOptions {
  batchSize?: number
}

/**
 * Generic batch processor for URL scraping operations
 * Handles batching and parallel processing of discovered URLs
 */
export class BatchProcessor {
  private readonly gameSyncer: GameSyncerService
  private readonly userInfoSyncer: UserInfoSyncerService
  private readonly leagueStatsSyncer: LeagueStatsSyncerService
  private readonly userStatsSyncer: UserStatsSyncerService

  constructor() {
    this.gameSyncer = Container.get(GameSyncerService)
    this.userInfoSyncer = Container.get(UserInfoSyncerService)
    this.leagueStatsSyncer = Container.get(LeagueStatsSyncerService)
    this.userStatsSyncer = Container.get(UserStatsSyncerService)
  }

  /**
   * Process a list of URLs in batches using parallel processing
   */
  async processUrls(
    ctx: ActionContext,
    urls: DocumentType<DiscoveredUrl>[],
    options: BatchProcessorOptions = {}
  ): Promise<void> {
    const { batchSize = 10 } = options

    ctx.logger.info(`Starting processing of ${urls.length} URLs with batch size ${batchSize}`)

    const totalBatches = Math.ceil(urls.length / batchSize)

    for (let i = 0; i < totalBatches; i++) {
      const start = i * batchSize
      const end = Math.min(start + batchSize, urls.length)
      const batch = urls.slice(start, end)

      ctx.logger.info(`Processing batch ${i + 1}/${totalBatches} (${batch.length} URLs)`)
      await this.processBatch(ctx, batch)
    }
  }

  /**
   * Process a batch of URLs in parallel
   */
  private async processBatch(
    ctx: ActionContext,
    batch: DocumentType<DiscoveredUrl>[]
  ): Promise<void> {
    const promises = batch.map(async (url) => {
      try {
        await this.processUrl(ctx, url)
      } catch (error) {
        ctx.logger.error(`Failed to scrape URL ${url.url}: ${error.message}`)
      }
    })

    await Promise.all(promises)
  }

  /**
   * Process a single URL based on its entity type
   */
  private async processUrl(ctx: ActionContext, url: DocumentType<DiscoveredUrl>): Promise<void> {
    ctx.logger.info(`Scraping URL ${url.url} of entity ${url.entity}`)

    switch (url.entity) {
      case CrawlerEntity.GAME:
        await this.gameSyncer.syncData(url)
        break
      case CrawlerEntity.PLAYER:
        await this.userInfoSyncer.syncData(url)
        break
      case CrawlerEntity.LEAGUE:
        await this.leagueStatsSyncer.syncData(url)
        break
      case CrawlerEntity.PLAYER_STATS:
        await this.userStatsSyncer.syncData(url)
        break
      default:
        ctx.logger.warn(`Unknown entity type: ${url.entity} for URL ${url.url}`)
        break
    }
  }
}
