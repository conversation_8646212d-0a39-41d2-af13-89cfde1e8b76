import config from 'config'
import mongoose from 'mongoose'

import { logger } from '../container'

let connected = false

/**
 * Connects to MongoDB with standard configuration
 * @returns Mongoose connection
 */
export const connectToMongoDB = async () => {
  try {
    if (connected) {
      return mongoose.connection
    }

    await mongoose.connect(process.env.MONGO_DB_URI ?? config.get('mongo.url'), {
      directConnection: process.env.NODE_ENV === 'test' ? true : false,
      readPreference: process.env.NODE_ENV === 'test' ? 'primary' : 'nearest',
      autoIndex: false,
    })

    mongoose.set('debug', process.env.DB_DEBUG === 'true')
    if (process.env.SYNC_INDEXES === 'true') {
      await mongoose.syncIndexes()
    }

    connected = true

    return mongoose.connection
  } catch (err) {
    logger.error('DB connection error', err)
    throw err
  }
}

/**
 * Closes the MongoDB connection
 */
export const closeMongoDBConnection = async () => {
  connected = false
  return mongoose.connection.close()
}

/**
 * Returns the current MongoDB connection
 */
export const getMongoDBConnection = () => {
  return mongoose.connection
}
