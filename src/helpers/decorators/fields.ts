import { GraphQLResolveInfo } from 'graphql'
import { parseResolveInfo, ResolveTree } from 'graphql-parse-resolve-info'
import { createParameterDecorator } from 'type-graphql'

export function Fields(): ParameterDecorator {
  return createParameterDecorator<{ info: GraphQLResolveInfo }>(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ({ info }: { info: any }): ResolveTree => {
      const parsedResolveInfoFragment = parseResolveInfo(info)

      if (!parsedResolveInfoFragment) {
        throw new Error('Failed to parse resolve info')
      }

      return parsedResolveInfoFragment as ResolveTree
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ) as any
}
