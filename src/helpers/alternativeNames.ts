import { AlternativeName, AlternativeNameInput } from '../modules/stats/models/alternativeName'

import { slugifyString } from './string'

export const handleAlternativeNames = (
  alternativeNames: AlternativeNameInput[] | undefined
): AlternativeName[] => {
  const seenSlugs = new Set<string>()

  return (
    alternativeNames
      ?.map((name) => {
        return {
          name: name.name,
          slug: slugifyString(name.name),
        }
      })
      .filter(({ slug }) => {
        if (seenSlugs.has(slug)) {
          return false
        }

        seenSlugs.add(slug)
        return true
      }) ?? []
  )
}
