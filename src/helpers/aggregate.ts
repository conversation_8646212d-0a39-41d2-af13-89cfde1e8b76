import { BioPosition } from '../modules/user/models/user'
import { AggregatedInfoGroup } from '../types/general'

type Item = { key: string; value: number }
type ItemInfoGroup = { name: string; items: Record<string, Item> }
type Aggrs = Record<string, ItemInfoGroup>

export type AggregatedType = {
  _id: Record<string, string>
  count: number
}

const possibleDefenderPositions = [
  BioPosition.DEFENDER,
  BioPosition.LEFT_DEFENSIVE,
  BioPosition.RIGHT_DEFENSIVE,
] as string[]

export const resolveItemNameDefenders = (name: string) => {
  if (possibleDefenderPositions.includes(name)) {
    return BioPosition.DEFENDER as string
  }
  return name
}

export const resolveAggregation = (aggregated: AggregatedType[]): AggregatedInfoGroup[] => {
  const aggrs: Aggrs = aggregated.reduce((aggr, { _id: group, count }) => {
    const entries: [string, string][] = Object.entries(group)

    for (const [groupName, itemName] of entries) {
      const group = (aggr[groupName] = aggr[groupName] || { name: groupName, items: {} })
      const resolvedItemName = resolveItemNameDefenders(itemName)
      group.items[resolvedItemName] = group.items[resolvedItemName] || {
        key: resolvedItemName,
        value: 0,
      }
      group.items[resolvedItemName].value += count
    }
    return aggr
  }, {} as Aggrs)

  const aggrInfo: AggregatedInfoGroup[] = []

  Object.entries(aggrs).forEach(([name, value]: [string, ItemInfoGroup]) => {
    aggrInfo.push({
      name,
      items: Object.values(value.items) as Item[],
    } as AggregatedInfoGroup)
  })

  return aggrInfo
}
