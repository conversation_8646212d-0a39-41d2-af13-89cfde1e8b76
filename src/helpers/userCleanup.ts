import { GraphQLError } from 'graphql'
import { Types } from 'mongoose'

import { recalculateTeamPlayersCount } from '../modules/user/helpers/currentTeam'
import { UserModel } from '../modules/user/models'
import { Role, User } from '../modules/user/models/user'
import { SNSService } from '../services/sns'

import { recalculateAgentsCount, recalculateClientsCount } from './agency'

export async function userCleanup(user: User, snsService: SNSService): Promise<User | null> {
  // Remove SNS endpoints
  if (user.registeredDevices) {
    for (const device of user.registeredDevices ?? []) {
      if (device.arn) {
        await snsService.deletePlatformEndpoint(device.arn)
      }
    }
  }

  const updatedUser = await UserModel.findOneAndUpdate(
    { _id: user._id },
    {
      email: user._id, // to make it unique (in case user wants to create new account with same email) and disable login
      markedForDeletion: true,
      markedForDeletionEmail: user.email,
      registeredDevices: [],
      currentTeam: null,
      slug: null,
    },
    { new: true }
  )

  if (!updatedUser) {
    throw new GraphQLError('Failed to find and update user', {
      extensions: { code: 'NOT_FOUND' },
    })
  }

  if (user.currentTeam) {
    await recalculateTeamPlayersCount(user)
  }

  if (user.bio?.agency) {
    if (user.role === Role.PLAYER || user.role === null) {
      await recalculateClientsCount(user.bio.agency as Types.ObjectId)
    } else if (user.role === Role.SPORTS_PROFESSIONAL) {
      await recalculateAgentsCount(user.bio.agency as Types.ObjectId)
    }
  }

  return updatedUser
}
