import { GraphQLScalarType, Kind } from 'graphql'
import { Types } from 'mongoose'

export const ObjectIdScalar = new GraphQLScalarType({
  name: 'ObjectId',
  description: 'Mongo object id scalar type',
  serialize(value: unknown): string {
    if (!(value instanceof Types.ObjectId)) {
      throw new Error('ObjectIdScalar can only serialize ObjectId values')
    }
    return value.toHexString()
  },
  parseValue(value: unknown): Types.ObjectId {
    if (typeof value !== 'string') {
      throw new Error('ObjectIdScalar can only parse string values')
    }
    return new Types.ObjectId(value)
  },
  parseLiteral(ast): Types.ObjectId {
    if (ast.kind !== Kind.STRING) {
      throw new Error('ObjectIdScalar can only parse string values')
    }
    return new Types.ObjectId(ast.value)
  },
})
