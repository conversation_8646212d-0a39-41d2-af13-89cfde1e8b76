import { Types } from 'mongoose'

import { UserModel } from '../modules/user/models'
import { Agency, AgencyModel } from '../modules/user/models/agency'
import { Role, SubRole } from '../modules/user/models/user'

import { DocumentType } from '@typegoose/typegoose'

export const recalculateClientsCount = async (
  agencyId: Types.ObjectId
): Promise<DocumentType<Agency>> => {
  const clientsCount = await UserModel.countDocuments({
    'bio.agency': agencyId,
    role: { $in: [Role.PLAYER, null] },
    markedForDeletion: { $exists: false },
  })

  const shadowClientsCount = await UserModel.countDocuments({
    'bio.agency': agencyId,
    role: Role.SHADOW_PLAYER,
    markedForDeletion: { $exists: false },
  })

  const agency = await AgencyModel.findOneAndUpdate(
    { _id: agencyId },
    {
      clientsCount,
      shadowClientsCount,
    },
    { new: true }
  )

  if (!agency) {
    throw new Error('Agency not found')
  }

  return agency
}

export const recalculateAgentsCount = async (
  agencyId: Types.ObjectId
): Promise<DocumentType<Agency>> => {
  const agentsCount = await UserModel.countDocuments({
    'bio.agency': agencyId,
    role: Role.SPORTS_PROFESSIONAL,
    subrole: SubRole.AGENT,
    markedForDeletion: { $exists: false },
  })

  const agency = await AgencyModel.findOneAndUpdate(
    {
      _id: agencyId,
    },
    {
      agentsCount,
    },
    { new: true }
  )

  if (!agency) {
    throw new Error('Agency not found')
  }

  return agency
}
