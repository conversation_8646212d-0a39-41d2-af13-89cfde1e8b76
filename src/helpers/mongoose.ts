import { Types } from 'mongoose'

/**
 * Type for any Mongoose document or document-like object
 * This handles both direct documents and populated/transformed documents
 */
type MongooseDocumentLike<T> = {
  _id?: string | Types.ObjectId | undefined
  _doc?: T
} & Partial<T>

/**
 * Gets the actual entity from a Mongoose document or document-like object
 * This handles both direct documents and populated/transformed documents
 * @param model Any Mongoose document or document-like object
 * @returns The actual entity
 */
export const getEntity = <T>(model: MongooseDocumentLike<T>): T => {
  if (model._id !== undefined) {
    return model as T
  } else {
    return model._doc as T
  }
}
