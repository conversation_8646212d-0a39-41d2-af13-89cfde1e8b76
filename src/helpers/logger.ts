import * as Sentry from '@sentry/node'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function wrapLogger(logger: any) {
  const { error, child } = logger
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function errorRearranger(this: any, ...args: any[]) {
    if (typeof args[0] === 'string' && args.length > 1) {
      for (let i = 1; i < args.length; i++) {
        const arg = args[i]
        if (arg instanceof Error) {
          const [err] = args.splice(i, 1)
          args.unshift(err)
        }
      }
    }

    Sentry.captureMessage(`Logger error: ${args[0]}`, {
      level: 'error',
      extra: {
        ...args.slice(1),
      },
    })

    return error.apply(this, args)
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function childModifier(this: any, ...args: any[]) {
    const c = child.apply(this, args)
    c.error = errorRearranger
    c.child = childModifier
    return c
  }
  logger.error = errorRearranger
  logger.child = childModifier
  return logger
}
