import { FilterQuery } from 'mongoose'

import { UserModel } from '../modules/user/models'
import { User } from '../modules/user/models/user'

import { slugifyString } from './string'

export const findUserSlug = async (input: {
  slug?: string
  nonUniqueSlug?: string
  firstname?: string
  lastname?: string
}): Promise<void> => {
  input.slug = slugifyString(input.firstname + ' ' + input.lastname)
  input.nonUniqueSlug = input.slug

  // check user with this slug does not exist, if so add random string to the end
  const existingUser = await UserModel.findOne({ slug: input.slug })
  if (existingUser) {
    input.slug =
      slugifyString(input.firstname + ' ' + input.lastname) +
      '-' +
      Math.random().toString(36).substring(7)
  }
}

/**
 * Generates a Mongoose filter to match documents based on a specified array of years.
 * Supports a special case for "2006+" which means the date can be before or in that year.
 *
 * @param {string[]} years - An array of years as strings (e.g., ["2010", "2011", "2006+"]).
 * @returns {FilterQuery<any>} - A Mongoose filter query with `$or` conditions for each year.
 *
 * @example usage:
 * ```
 * const years = ['2010', '2011', '2006+']
 * const filter = generateYearFilter(years)
 * ```
 */
export const generateYearFilter = (years: string[]): FilterQuery<User> => {
  return {
    $or: years
      .filter((year) => {
        return year.length === 4 || (year.length === 5 && year.lastIndexOf('+') === 4)
      })
      .map((year) => {
        if (year.lastIndexOf('+') === 4) {
          // Special case: Date should be before or within the a year. e.g. 2006+
          const yr = parseInt(year.substring(0, 4), 10)
          return {
            dateOfBirth: {
              $lte: new Date(`${yr}-12-31T23:59:59.999Z`),
            },
          }
        } else {
          // Standard case for a single year
          return {
            dateOfBirth: {
              $gte: new Date(`${year}-01-01`),
              $lt: new Date(`${year}-12-31T23:59:59.999Z`),
            },
          }
        }
      }),
  }
}
