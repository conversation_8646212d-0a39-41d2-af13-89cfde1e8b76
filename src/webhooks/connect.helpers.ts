import Stripe from 'stripe'

import { STRIPE_DOCUMENT_VERIFICATION_FIELDS } from './connect.constants'

export const stripeAccountNeedsDocumentVerification = (
  account: {
    id: Stripe.Account['id']
    requirements?: Stripe.Account['requirements']
  },
  includeEventuallyDue = true
): boolean => {
  const requirements = account.requirements

  if (!requirements) {
    return false
  }
  const dueFields: string[] = [
    ...(requirements.currently_due ?? []),
    ...(includeEventuallyDue ? (requirements?.eventually_due ?? []) : []),
  ]

  return STRIPE_DOCUMENT_VERIFICATION_FIELDS.some((field) => dueFields.includes(field))
}

export const isStripeAccountVerificationPending = (account: {
  id: Stripe.Account['id']
  requirements?: Stripe.Account['requirements']
}): boolean => {
  return !!account?.requirements?.pending_verification?.length
}
