import config from 'config'
import DataLoader from 'dataloader'
import mongoose, { Types } from 'mongoose'
import { Field, ID, Int, ObjectType } from 'type-graphql'

import { CacheControl } from '../../../middlewares/cacheControl'

import { AlternativeName } from './alternativeName'
import { League } from './league'
import { Organization } from './organization'

import { getModelForClass, index, modelOptions, prop, Ref, searchIndex } from '@typegoose/typegoose'

@ObjectType()
export class TeamStats {
  @prop()
  @Field((_type) => Int, { nullable: false })
  public followers: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public views: number
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ slug: 1 }, { unique: true })
@index({ organization: 1, shortNameSlug: 1, _id: 1 })
@index({ country: 1, shortName: 1 })
@index({
  country: 1,
  score: 1,
  _id: 1,
})
@index({
  leagues: 1,
  slug: 1,
})
// TODO: solve: cannot index parallel arrays if we want to have compound index on leagues and alternativeNames.slug
@index({
  leagues: 1,
})
@index({
  'alternativeNames.slug': 1,
})
@index({ abbreviation: 1 })
// @index({ shortNameSlug: 1, organization: 1 }, { unique: true }) // we handle this in the resolver as we don't know short name on team creation
@searchIndex({
  name: 'teams_search',
  definition: {
    mappings: {
      dynamic: false,
      fields: {
        name: {
          tokenization: 'edgeGram',
          minGrams: 3,
          maxGrams: 15,
          type: 'autocomplete',
        },
      },
    },
  },
})
@CacheControl({ inheritMaxAge: true })
export class Team {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop()
  @Field({ nullable: false })
  public name: string

  @prop()
  @Field({ nullable: true })
  public abbreviation?: string

  @prop()
  @Field({ nullable: false })
  public slug: string

  @prop()
  @Field({ nullable: true })
  public logo: string

  @prop()
  @Field({ nullable: true })
  public ogLogo: string

  @prop()
  public logoUnconfirmed: string

  @prop()
  @Field({ nullable: true })
  public country: string

  @prop({ type: Types.ObjectId, ref: Organization })
  @Field((_type) => Organization, { nullable: true })
  public organization: Ref<Organization>

  @prop({ type: Types.ObjectId, ref: League })
  @Field((_type) => [League], { nullable: true })
  public leagues: Ref<League>[]

  @prop()
  @Field({ nullable: true })
  public order: number

  @prop()
  @Field({ nullable: true })
  public externalId?: string

  @prop()
  @Field({ nullable: true })
  public shortName: string

  @prop()
  @Field({ nullable: true })
  public shortNameSlug: string

  @prop({ default: 0 })
  @Field({ nullable: false, defaultValue: 0 })
  public playersCount: number = 0

  @prop()
  public fromBatchImport: boolean

  @prop({ type: () => [AlternativeName], default: [] })
  @Field(() => [AlternativeName], { nullable: true })
  public alternativeNames: AlternativeName[]

  @prop()
  @Field({ nullable: true })
  public eliteProspectLink?: string

  @prop()
  @Field({ nullable: true })
  public checked?: boolean

  @prop({ type: TeamStats })
  @Field((_type) => TeamStats, { nullable: true })
  public stats?: TeamStats

  @prop({ type: [Number] })
  public vectorEmbedding?: number[]

  public getTeam(): Team {
    if ((this as Team)._id !== undefined) {
      return this as Team
    } else {
      return (this as unknown as { _doc: Team })._doc
    }
  }
}

export const TeamModel = getModelForClass(Team, {
  existingMongoose: mongoose,
  schemaOptions: {
    autoSearchIndex: config.mongo.autoSearchIndex,
  },
})
/**
 * Creates a DataLoader for Team entities
 * This batches multiple team lookups into a single database query
 */
export const createTeamLoader = () => {
  return new DataLoader<string, Team | null>((keys) => generateLoader(keys))
}

/**
 * Batch loader function for teams
 * @param keys Array of team IDs to load
 * @returns Array of teams in the same order as the keys
 */
const generateLoader = async (keys: readonly string[]) => {
  const teams = await TeamModel.find({
    _id: { $in: keys },
  })

  // TODO make generic, reusable, model<{_id: string}> to map, map to model by key
  const teamsMap = new Map<string, Team>()
  teams.forEach((team) => {
    const key = team._id.toString()

    teamsMap.set(key, team)
  })

  return keys.map((key) => teamsMap.get(key) || null)
}
