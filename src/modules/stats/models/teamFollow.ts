import mongoose, { Types } from 'mongoose'
import { ObjectType } from 'type-graphql'

import { User } from '../../user/models/user'

import { Team } from './team'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@Index({ team: 1, follower: 1 }, { unique: true })
export class TeamFollow {
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: Team })
  public team: Ref<Team>

  @prop({ type: Types.ObjectId, ref: User })
  public follower: Ref<User>
}

export const TeamFollowModel = getModelForClass(TeamFollow, {
  existingMongoose: mongoose,
})
