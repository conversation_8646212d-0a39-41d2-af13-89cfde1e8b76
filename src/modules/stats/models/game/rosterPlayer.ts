import { Types } from 'mongoose'
import { Field, ObjectType, registerEnumType } from 'type-graphql'

import { User } from '../../../user/models/user'

import { prop, Ref } from '@typegoose/typegoose'

export enum RosterPosition {
  Forward = 'Forward',
  Defenseman = 'Defenseman',
  Goaltender = 'Goaltender',
  Unknown = 'Unknown',
}

registerEnumType(RosterPosition, {
  name: 'RosterPosition',
  description: 'Enum for roster player positions',
})

@ObjectType()
export class RosterPlayerExternalInfo {
  @prop()
  @Field()
  public playerName: string

  @prop({ required: false })
  @Field({ nullable: true })
  public playerExternalId?: string

  @Field(() => User, { nullable: true })
  public player?: Ref<User> | null
}

@ObjectType()
export class RosterPlayer {
  @prop()
  @Field()
  public externalInfo: RosterPlayerExternalInfo

  @prop({ type: Types.ObjectId, ref: User, required: false })
  @Field(() => User, { nullable: true })
  public player: Ref<User> | null

  @prop({ required: false })
  @Field({ nullable: true })
  public goals?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public assists?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public points?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public pim?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public plusMinus?: number

  // goalie stats
  @prop({ required: false })
  @Field({ nullable: true })
  public saves?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public goalsAgainst?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public svp?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public jerseyNumber?: number

  @prop({ enum: RosterPosition, required: false })
  @Field(() => RosterPosition, { nullable: true })
  public position?: RosterPosition
}
