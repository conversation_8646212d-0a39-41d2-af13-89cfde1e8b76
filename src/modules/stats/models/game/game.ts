import { Types } from 'mongoose'
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql'

import { DiscoveredUrl } from '../../../admin/models/discoveredUrl'
import { UserModel } from '../../../user/models'
import { getLeagueByExternalInfo } from '../../helpers/league'
import { getTeamByExternalInfo } from '../../helpers/team'
import { League } from '../league'
import { Team } from '../team'

import { GameEvent } from './gameEvent'
import { RosterPlayer } from './rosterPlayer'

import * as Sentry from '@sentry/node'
import { getModelForClass, index, mongoose, prop, Ref } from '@typegoose/typegoose'

export enum GameScoreType {
  Regular = 'Regular',
  Overtime = 'Overtime',
  Shootout = 'Shootout',
}

registerEnumType(GameScoreType, {
  name: 'GameScoreType',
})

export enum GameResultType {
  Regular = 'Regular',
  Overtime = 'Overtime',
  Shootout = 'Shootout',
  Forfeit = 'Forfeit',
}

registerEnumType(GameResultType, {
  name: 'GameResultType',
})

@ObjectType()
export class GameScore {
  @prop({ required: false })
  @Field({ nullable: true })
  public homeTeam?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public awayTeam?: number

  @prop({ enum: GameScoreType, required: false })
  @Field(() => GameScoreType, { nullable: true })
  public type?: GameScoreType
}

@ObjectType()
export class GameExternalInfo {
  @prop({ required: true })
  @Field({ nullable: false })
  public homeTeamName: string

  @prop({ required: true })
  @Field({ nullable: false })
  public awayTeamName: string

  @prop({ required: true })
  @Field({ nullable: false })
  public leagueName: string
}

@ObjectType()
export class GameTeamStats {
  @prop({ required: false })
  @Field({ nullable: true })
  public score?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public shotsOnGoal?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public blockedShots?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public faceoffs?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public powerPlayGoals?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public shortHandedGoals?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public penalties?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public majorPenalties?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public penaltyMinutes?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public goalieSaves?: number
}

@ObjectType()
export class GameOverallStats {
  @prop({ required: false })
  @Field(() => GameTeamStats, { nullable: true })
  public homeTeam?: GameTeamStats

  @prop({ required: false })
  @Field(() => GameTeamStats, { nullable: true })
  public awayTeam?: GameTeamStats

  @prop({ required: false })
  @Field({ nullable: true })
  public periodScores?: string

  @prop({ type: GameScore, required: false })
  @Field(() => [GameScore], { nullable: true })
  public scores?: GameScore[]
}

@ObjectType('GameStats')
@index({ externalId: 1, discoveredUrl: 1 }, { unique: true })
@index({ date: 1, syncedAt: 1 })
@index({ homeTeam: 1, awayTeam: 1, league: 1 })
@index({ homeTeam: 1, awayTeam: 1 })
@index({ 'awayTeamRoster.player': 1 })
@index({ 'homeTeamRoster.player': 1 })
@index({ ignore: 1 })
@index({ season: 1 })
@index({ league: 1, season: 1 })
@index({ homeTeam: 1, season: 1, date: -1 })
@index({ awayTeam: 1, season: 1, date: -1 })
@index({ 'homeTeamRoster.externalInfo.playerExternalId': 1, season: 1, date: 1, ignore: 1 })
@index({ 'awayTeamRoster.externalInfo.playerExternalId': 1, season: 1, date: 1, ignore: 1 })
export class Game {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ required: true })
  @Field()
  public date: Date

  @prop({ required: false })
  @Field({ nullable: true })
  public season?: string

  @prop({ required: true })
  @Field()
  public externalId: string

  @prop({ required: true })
  @Field((_type) => GameExternalInfo, { nullable: false })
  public externalInfo: GameExternalInfo

  @prop({ type: Types.ObjectId, ref: DiscoveredUrl, required: true })
  @Field(() => DiscoveredUrl)
  public discoveredUrl: Ref<DiscoveredUrl>

  @prop({ type: Types.ObjectId, ref: Team, required: false })
  @Field(() => Team, { nullable: true })
  public homeTeam?: Ref<Team>

  @prop({ type: Types.ObjectId, ref: Team, required: false })
  @Field(() => Team, { nullable: true })
  public awayTeam?: Ref<Team>

  @prop({ type: Types.ObjectId, ref: League, required: false })
  @Field(() => League, { nullable: true })
  public league?: Ref<League>

  @prop({ type: GameEvent, required: false })
  @Field(() => [GameEvent], { nullable: true })
  public events: GameEvent[]

  @prop({ required: false })
  @Field(() => GameOverallStats, { nullable: true })
  public stats: GameOverallStats

  @prop({ type: RosterPlayer, required: false })
  @Field(() => [RosterPlayer], { nullable: true })
  public homeTeamRoster: RosterPlayer[]

  @prop({ type: RosterPlayer, required: false })
  @Field(() => [RosterPlayer], { nullable: true })
  public awayTeamRoster: RosterPlayer[]

  @prop({ required: false })
  @Field({ nullable: true })
  public syncedAt?: Date

  @prop({ required: false })
  @Field({ nullable: true })
  public ignore?: boolean

  @prop({ enum: GameResultType, required: false })
  @Field(() => GameResultType, { nullable: true })
  public gameResult?: GameResultType

  @prop()
  @Field({ nullable: true })
  public streamFeedActivityId?: string

  public async syncEntities(force: boolean = false): Promise<boolean> {
    if (this.league && this.homeTeam && this.awayTeam && !force) {
      return false
    }

    const league = await getLeagueByExternalInfo(
      this.league as League,
      {
        externalLeagueName: this.externalInfo?.leagueName,
        externalLeagueCountry: 'CZ', // TODO: add country to external info, for now just testing
      },
      force
    )

    const homeTeam = await getTeamByExternalInfo(
      this.homeTeam as Team,
      {
        externalTeamName: this.externalInfo?.homeTeamName,
        externalTeamCountry: 'CZ',
      },
      league,
      force
    )

    const awayTeam = await getTeamByExternalInfo(
      this.awayTeam as Team,
      {
        externalTeamName: this.externalInfo?.awayTeamName,
        externalTeamCountry: 'CZ',
      },
      league,
      force
    )

    this.homeTeam = homeTeam?._id
    this.awayTeam = awayTeam?._id
    this.league = league?._id

    if (league?.ignore) {
      this.ignore = true
    }

    return true
  }

  /**
   * Load players for multiple games with a single database query
   */
  public static async loadPlayersForGames(games: Game[]) {
    if (!games.length) return

    // Collect all external player IDs and crawlerDomainConfig IDs from all games
    const externalUserIdsMap = new Map<string, Set<string>>()

    for (const game of games) {
      const crawlerDomainConfigId = (
        game.discoveredUrl as DiscoveredUrl
      ).crawlerDomainConfig.toString()

      if (!externalUserIdsMap.has(crawlerDomainConfigId)) {
        externalUserIdsMap.set(crawlerDomainConfigId, new Set())
      }

      const externalIdsSet = externalUserIdsMap.get(crawlerDomainConfigId)!

      for (const player of game.homeTeamRoster ?? []) {
        if (player.externalInfo?.playerExternalId) {
          externalIdsSet.add(player.externalInfo.playerExternalId)
        }
      }

      for (const player of game.awayTeamRoster ?? []) {
        if (player.externalInfo?.playerExternalId) {
          externalIdsSet.add(player.externalInfo.playerExternalId)
        }
      }

      for (const event of game.events ?? []) {
        if (event.externalInfo?.playerExternalId) {
          externalIdsSet.add(event.externalInfo.playerExternalId)
        }

        for (const assist of event.assists ?? []) {
          if (assist.playerExternalId) {
            externalIdsSet.add(assist.playerExternalId)
          }
        }
      }
    }

    // Create query conditions for each crawlerDomainConfig
    const orConditions = []
    for (const [crawlerDomainConfigId, externalIds] of externalUserIdsMap.entries()) {
      if (externalIds.size > 0) {
        orConditions.push({
          externalConnections: {
            $elemMatch: {
              externalId: { $in: Array.from(externalIds) },
              crawlerDomainConfig: new Types.ObjectId(crawlerDomainConfigId),
            },
          },
        })
      }
    }

    // If no external IDs were found, return early
    if (orConditions.length === 0) return

    // Fetch all users in a single query
    const users = await UserModel.find({ $or: orConditions }).lean()

    Sentry.startSpan({ name: 'assignPlayersStart' }, (span) => {
      // Assign users to players in each game
      const userMap = new Map()

      // Create an entry in the map for each external connection of each user
      // as one user can have multiple external connections, so we need to be able to find
      // user by any of their external connections
      for (const user of users) {
        for (const connection of user.externalConnections) {
          const key = JSON.stringify([
            {
              externalId: connection.externalId,
              crawlerDomainConfig: connection.crawlerDomainConfig.toString(),
            },
          ])
          userMap.set(key, user)
        }
      }

      span.addEvent('assignPlayersMap')

      for (const game of games) {
        const crawlerDomainConfig = (
          game.discoveredUrl as DiscoveredUrl
        ).crawlerDomainConfig.toString()

        const assignPlayer = (player: RosterPlayer | GameEvent) => {
          span.addEvent('assignPlayerFunc')

          if (player.externalInfo?.playerExternalId) {
            const key = JSON.stringify([
              { externalId: player.externalInfo.playerExternalId, crawlerDomainConfig },
            ])
            player.player = userMap.get(key) ?? null
          }
        }

        game.homeTeamRoster.forEach(assignPlayer)
        game.awayTeamRoster.forEach(assignPlayer)

        for (const event of game.events) {
          assignPlayer(event)

          // Handle assists separately as they have a different structure
          event.assists?.forEach((assist) => {
            if (assist.playerExternalId) {
              const key = JSON.stringify([
                { externalId: assist.playerExternalId, crawlerDomainConfig },
              ])
              assist.player = userMap.get(key) ?? null
            }
          })
        }
      }
    })
  }
}

export const GameModel = getModelForClass(Game, {
  existingMongoose: mongoose,
  options: {
    customName: 'gameStats',
  },
})
