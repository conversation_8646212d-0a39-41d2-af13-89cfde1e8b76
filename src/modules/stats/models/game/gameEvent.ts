import { Types } from 'mongoose'
import { Field, ObjectType, registerEnumType } from 'type-graphql'

import { User } from '../../../user/models/user'

import { RosterPlayerExternalInfo } from './rosterPlayer'

import { prop, Ref } from '@typegoose/typegoose'

export enum GameEventType {
  Goal = 'Goal',
  Penalty = 'Penalty',
  GoalieEvent = 'GoalieEvent',
  Unknown = 'Unknown',
}

registerEnumType(GameEventType, {
  name: 'GameEventType',
})

@ObjectType()
class GameEventExternalInfo {
  @prop()
  @Field()
  public teamName: string

  @prop({ required: false })
  @Field({ nullable: true })
  public playerName?: string

  @prop({ required: false })
  @Field({ nullable: true })
  public playerExternalId?: string
}

@ObjectType()
export class GameEvent {
  @prop({ required: true })
  @Field()
  public time: string

  @prop({ required: false })
  @Field({ nullable: true })
  public isHomeTeam?: boolean

  @prop({ enum: GameEventType })
  @Field(() => GameEventType)
  public type: GameEventType

  @prop({ required: false })
  @Field({ nullable: true })
  public score?: string

  @prop({ required: false })
  @Field({ nullable: true })
  public penaltyTime?: string

  @prop()
  @Field()
  public externalInfo: GameEventExternalInfo

  @prop({ type: Types.ObjectId, ref: User, required: false })
  @Field(() => User, { nullable: true })
  public player: Ref<User> | null

  @prop({ required: false })
  @Field({ nullable: true })
  public description?: string

  @prop({ required: false })
  @Field({ nullable: true })
  public descriptionCode?: string

  @prop({ type: RosterPlayerExternalInfo, required: false })
  @Field(() => [RosterPlayerExternalInfo], { nullable: true })
  public assists?: RosterPlayerExternalInfo[]
}
