import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql'

import { slugifyString } from '../../../helpers/string'
import { CrawlerDomainConfig } from '../../admin/models/crawlerDomainConfig'
import { DiscoveredUrl } from '../../admin/models/discoveredUrl'
import { User } from '../../user/models/user'
import { getLeagueByExternalInfo } from '../helpers/league'
import { getTeamByExternalInfo } from '../helpers/team'

import { League, LeagueType } from './league'
import { Team } from './team'

import { getModelForClass, index, modelOptions, prop, Ref } from '@typegoose/typegoose'

export enum UserStatsPosition {
  Skater = 'Skater',
  Goalie = 'Goalie',
  Unknown = 'Unknown',
}

registerEnumType(UserStatsPosition, {
  name: 'UserStatsPosition',
  description: 'Enum for user stats player positions',
})

@ObjectType()
export class LeagueStage {
  @prop()
  @Field({ nullable: true })
  public value?: string

  @prop()
  @Field({ nullable: true })
  public label?: string

  public crawlerDomainConfig?: Ref<CrawlerDomainConfig>
}

@ObjectType()
export class LeagueStages {
  @Field((_type) => League, { nullable: true })
  public league?: Ref<League>

  @Field(() => [LeagueStage], { nullable: false })
  public stages: LeagueStage[]
}

@ObjectType()
export class ExternalInfo {
  @prop()
  @Field({ nullable: true })
  public externalTeamName?: string

  @prop()
  @Field({ nullable: true })
  public externalTeamCountry?: string

  @prop()
  @Field({ nullable: true })
  public externalLeagueName?: string

  @prop()
  @Field({ nullable: true })
  public externalLeagueCountry?: string

  @prop()
  @Field({ nullable: true })
  public externalPlayerName?: string

  @prop()
  @Field(() => LeagueStage, { nullable: true })
  public stage?: LeagueStage
}

export enum StatsSource {
  EP = 'EP',
  LEIJONAT = 'LEIJONAT',
  SCRAPER = 'SCRAPER',
  MANUAL = 'MANUAL',
}

registerEnumType(StatsSource, {
  name: 'StatsSource',
})

export enum SeasonType {
  REGULAR = 'REGULAR',
  TOURNAMENT = 'TOURNAMENT',
}

registerEnumType(SeasonType, {
  name: 'SeasonType',
})

@ObjectType()
export class PostSeasonStats {
  // Common stats
  @prop()
  @Field({ nullable: true })
  public gamesPlayed?: number

  // Player stats
  @prop()
  @Field({ nullable: true })
  public assists?: number

  @prop()
  @Field({ nullable: true })
  public goals?: number

  @prop()
  @Field({ nullable: true })
  public pim?: number

  @prop()
  @Field({ nullable: true })
  public plusMinus?: number

  // Goalie stats
  @prop()
  @Field({ nullable: true })
  public wins?: number

  @prop()
  @Field({ nullable: true })
  public losses?: number

  @prop()
  @Field({ nullable: true })
  public ties?: number

  @prop()
  @Field({ nullable: true })
  public gaa?: number

  @prop()
  @Field({ nullable: true })
  public svp?: number

  @prop()
  @Field({ nullable: true })
  public shutouts?: number
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index(
  {
    user: 1,
    team: 1,
    league: 1,
    season: 1,
    'externalInfo.externalTeamName': 1,
    'externalInfo.externalLeagueName': 1,
    'externalInfo.stage': 1,
    userExternalId: 1,
  },
  { unique: true }
)
@index({
  'externalInfo.externalLeagueCountry': 1,
  'externalInfo.externalLeagueName': 1,
  league: 1,
})
@index({
  'externalInfo.externalTeamCountry': 1,
  'externalInfo.externalTeamName': 1,
  team: 1,
})
@index({ league: 1 })
@index({ team: 1 })
@index({ externalId: 1, user: 1 })
@index({ userExternalId: 1, crawlerDomainConfig: 1 })
@index({ source: 1 })
@index({ source: 1, team: 1, league: 1 })
@index({ externalId: 1, key: 1 })
@index({ total: 1 })
@index({ key: 1 })
@index({
  source: 1,
  league: 1,
  position: 1,
  season: 1,
  total: 1,
  crawlerDomainConfig: 1,
  'externalInfo.stage.value': 1,
})
export class UserStats {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: User, required: false })
  @Field((_type) => User, { nullable: true })
  public user?: Ref<User>

  @prop({ type: Types.ObjectId, ref: Team })
  @Field((_type) => Team, { nullable: true })
  public team?: Ref<Team>

  @prop({ type: Types.ObjectId, ref: League })
  @Field((_type) => League, { nullable: true })
  public league?: Ref<League>

  @prop()
  @Field((_type) => SeasonType, { nullable: true })
  public seasonType?: SeasonType

  @prop()
  @Field({ nullable: false })
  public season: string

  @prop()
  @Field({ nullable: true })
  public gamesPlayed?: number

  @prop()
  @Field({ nullable: true })
  public assists?: number

  @prop()
  @Field({ nullable: true })
  public goals?: number

  @prop()
  @Field({ nullable: true })
  public points?: number

  @prop()
  @Field({ nullable: true })
  public pim?: number

  @prop()
  @Field({ nullable: true })
  public plusMinus?: number

  // Goalie stats
  @prop()
  @Field({ nullable: true })
  public wins?: number

  @prop()
  @Field({ nullable: true })
  public losses?: number

  @prop()
  @Field({ nullable: true })
  public ties?: number

  @prop()
  @Field({ nullable: true })
  public gaa?: number

  @prop()
  @Field({ nullable: true })
  public svp?: number

  @prop()
  @Field({ nullable: true })
  public shutouts?: number

  @prop()
  @Field({ nullable: true })
  public ga?: number

  @prop()
  @Field({ nullable: true })
  public toi?: string

  @prop()
  @Field({ nullable: true })
  public saves?: number

  @prop()
  @Field({ nullable: true })
  public orderInSeason?: number

  @prop()
  @Field((_type) => PostSeasonStats, { nullable: true })
  public postSeasonStats?: PostSeasonStats

  @prop()
  public externalId?: string

  @prop()
  @Field((_type) => ExternalInfo, { nullable: true })
  public externalInfo?: ExternalInfo

  @prop()
  @Field({ nullable: true })
  public userExternalId?: string

  @prop({ enum: StatsSource })
  @Field(() => StatsSource, { nullable: true })
  public source?: StatsSource

  @prop({ type: Types.ObjectId, ref: DiscoveredUrl, required: false })
  @Field(() => DiscoveredUrl, { nullable: true })
  public discoveredUrl?: Ref<DiscoveredUrl>

  @prop({ type: Types.ObjectId, ref: CrawlerDomainConfig, required: false })
  @Field(() => CrawlerDomainConfig, { nullable: true })
  public crawlerDomainConfig?: Ref<CrawlerDomainConfig>

  // TODO: make sure this is regenerated on update
  // mainly when team, league are assigned
  @prop()
  @Field({ nullable: true })
  public key?: string

  @prop({ enum: UserStatsPosition })
  @Field(() => UserStatsPosition, { nullable: true })
  public position?: UserStatsPosition

  @prop()
  @Field({ nullable: true })
  public total?: boolean

  /**
   * Sync team and league entities
   * @param force - ignores cache
   * @returns boolean If entities were changed.
   */
  public async syncEntities(force: boolean = false): Promise<boolean> {
    if (!this.team || !this.league) {
      const league = await getLeagueByExternalInfo(this.league as League, this.externalInfo, force)
      const team = await getTeamByExternalInfo(this.team as Team, this.externalInfo, league, force)

      if (league?.type === LeagueType.TOURNAMENT) {
        this.seasonType = SeasonType.TOURNAMENT
      }

      const changed = league !== this.league || team !== this.team

      this.league = league?._id
      this.team = team?._id

      return changed
    }

    return false
  }
}

export const generateUserStatsKey = (data: UserStats) => {
  return `${data.season}-${data.user?.id ?? data.userExternalId}-${slugifyString(data.externalInfo?.externalTeamName)}-${slugifyString(data.externalInfo?.externalLeagueName)}-${data.externalInfo?.stage?.value}`
}

export const UserStatsModel = getModelForClass(UserStats, {
  existingMongoose: mongoose,
})
