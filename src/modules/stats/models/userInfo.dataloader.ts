import DataLoader from 'dataloader'

import { UserInfoModel } from './userInfo'

export type UserInfoKey = {
  externalId: string
  crawlerDomainConfig: string
}

/**
 * Creates a DataLoader for UserInfo names
 * This batches multiple userInfo lookups into a single database query
 * and returns only the name field
 */
export const createUserInfoLoader = () => {
  return new DataLoader<UserInfoKey, string | null>((keys) => generateUserInfoLoader(keys))
}

/**
 * Batch loader function for userInfo names
 * @param keys Array of externalIds to load
 * @returns Array of userInfo names in the same order as the keys
 */
const generateUserInfoLoader = async (keys: readonly UserInfoKey[]) => {
  const userInfos = await UserInfoModel.find({
    $or: keys.map((key) => ({
      externalId: key.externalId,
      crawlerDomainConfig: key.crawlerDomainConfig,
    })),
  })

  const userInfoMap = new Map<string, string>()
  userInfos.forEach((userInfo) => {
    const key = `${userInfo.externalId}:${userInfo.crawlerDomainConfig}`
    userInfoMap.set(key, userInfo.name)
  })

  // Return userInfo names in the same order as the keys
  return keys.map((key) => userInfoMap.get(`${key.externalId}:${key.crawlerDomainConfig}`) || null)
}
