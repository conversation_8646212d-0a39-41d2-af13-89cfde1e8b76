import { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'

import { slugifyString } from '../../../../helpers/string'
import { DiscoveredUrl } from '../../../admin/models/discoveredUrl'
import { getLeagueByExternalInfo } from '../../helpers/league'
import { getTeamByExternalInfo } from '../../helpers/team'
import { League } from '../league'
import { Team } from '../team'

import { getModelForClass, Index, mongoose, prop, Ref } from '@typegoose/typegoose'

@ObjectType()
class LeagueStatsExternalInfo {
  @prop()
  @Field()
  leagueName: string

  @prop()
  @Field()
  teamName: string
}

@ObjectType()
@Index({ league: 1, standing: 1, group: 1 })
@Index({ key: 1 }, { unique: true })
@Index({ externalId: 1, 'externalInfo.teamName': 1, league: 1 }) // TODO: should be unique?
export class LeagueStats {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ required: true })
  @Field()
  public externalId: string

  @prop({ required: true })
  @Field((_type) => LeagueStatsExternalInfo)
  public externalInfo: LeagueStatsExternalInfo

  @prop({ type: Types.ObjectId, ref: DiscoveredUrl, required: true })
  @Field(() => DiscoveredUrl)
  public discoveredUrl: Ref<DiscoveredUrl>

  @prop({ type: Types.ObjectId, ref: Team, required: false })
  @Field(() => Team, { nullable: true })
  public team?: Ref<Team>

  @prop({ type: Types.ObjectId, ref: League, required: false })
  @Field(() => League, { nullable: true })
  public league?: Ref<League>

  @prop({ required: true })
  @Field({ nullable: false })
  public season: string

  @prop({ required: false })
  @Field({ nullable: true })
  public group?: string

  @prop({ required: true })
  @Field()
  public standing: number

  @prop({ required: true })
  @Field()
  public games: number

  @prop({ required: true })
  @Field()
  public wins: number

  @prop({ required: true })
  @Field()
  public overtimeWins: number

  @prop({ required: true })
  @Field()
  public losses: number

  @prop({ required: true })
  @Field()
  public overtimeLosses: number

  @prop({ required: true })
  @Field()
  public ties: number

  @prop({ required: true })
  @Field()
  public goalsAgainst: number

  @prop({ required: true })
  @Field()
  public goalsFor: number

  @prop({ required: true })
  @Field()
  public points: number

  @prop({ required: false })
  @Field({ nullable: true })
  public syncedAt?: Date

  @prop()
  public key: string

  public async syncEntities(): Promise<boolean> {
    if (this.league && this.team) {
      return false
    }

    const league = await getLeagueByExternalInfo(this.league as League, {
      externalLeagueName: this.externalInfo?.leagueName,
      externalLeagueCountry: 'CZ', // TODO: add country to external info, for now just testing
    })

    const team = await getTeamByExternalInfo(
      this.team as Team,
      {
        externalTeamName: this.externalInfo?.teamName,
        externalTeamCountry: 'CZ',
      },
      league
    )

    this.team = team?._id
    this.league = league?._id

    return true
  }
}

export const generateLeagueStatsKey = (leagueStats: LeagueStats) => {
  return `${slugifyString(leagueStats.externalInfo?.teamName)}_${slugifyString(leagueStats.externalInfo?.leagueName)}_${slugifyString(leagueStats.group)}_${leagueStats.season}`
}

export const LeagueStatsModel = getModelForClass(LeagueStats, {
  existingMongoose: mongoose,
})
