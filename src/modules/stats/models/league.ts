import mongoose, { Types } from 'mongoose'
import { Authorized, Field, ID, ObjectType, registerEnumType } from 'type-graphql'

import { SystemRole } from '../../../types/general'

import { AlternativeName } from './alternativeName'

import { getModelForClass, index, modelOptions, prop } from '@typegoose/typegoose'

export enum LeagueGenderCategory {
  MEN = 'MEN',
  WOMEN = 'WOMEN',
  MIXED = 'MIXED',
}

registerEnumType(LeagueGenderCategory, {
  name: 'LeagueGenderCategory',
})

export enum LeagueLevel {
  PROFESSIONAL = 'PROFESSIONAL',
  SEMI_PROFESSIONAL = 'SEMI_PROFESSIONAL',
  COLLEGE = 'COLLEGE',
  JUNIOR = 'JUNIOR',
  YOUTH = 'YOUTH',
}

registerEnumType(LeagueLevel, {
  name: 'LeagueLevel',
})

export enum LeagueType {
  LEAGUE = 'LEAGUE',
  TOURNAMENT = 'TOURNAMENT',
}

registerEnumType(LeagueType, {
  name: 'LeagueType',
})

export enum StatusType {
  TRUE = 'TRUE',
  FALSE = 'FALSE',
  NOT_SET = 'UNDEFINED',
}

registerEnumType(StatusType, {
  name: 'StatusType',
})

/** These are rules to be able to automatically connect league to team */
@ObjectType()
class TeamAutoConnectRules {
  @Field()
  @prop()
  country: string

  @Field()
  @prop()
  shortName: string

  @Field()
  @prop()
  shortNameSlug: string

  @Field()
  @prop()
  shortNameInclude: boolean
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ name: 'text' })
@index({ slug: 1, country: 1 }, { unique: true })
@index({ country: 1 })
@index({ genderCategory: 1 })
@index({ level: 1 })
export class League {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ required: true })
  @Field({ nullable: false })
  public name: string

  @prop({ required: true })
  @Field({ nullable: false })
  public slug: string

  @prop()
  @Field({ nullable: true })
  public logo: string

  @prop()
  @Field({ nullable: true })
  public ogLogo: string

  @prop()
  public logoUnconfirmed: string

  @prop()
  @Field({ nullable: true, deprecationReason: 'Use `countries` instead' })
  public country: string

  @prop({ type: () => [String], default: [] })
  @Field((_) => [String], { nullable: true })
  public countries: string[]

  @prop()
  public externalId?: string

  @prop({ type: () => [AlternativeName], default: [] })
  @Field(() => [AlternativeName], { nullable: true })
  public alternativeNames: AlternativeName[]

  @prop()
  @Field({ nullable: true })
  public publicAlternativeName?: string

  @prop({ type: String, enum: LeagueGenderCategory })
  @Field((_) => LeagueGenderCategory, { nullable: true })
  public genderCategory?: LeagueGenderCategory

  @prop()
  @Field({ nullable: true })
  public website?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public instagramUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public facebookUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public twitterUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public tiktokUrl?: string

  @prop({ type: String, enum: LeagueLevel })
  @Field((_) => LeagueLevel, { nullable: true })
  public level?: LeagueLevel

  @prop()
  @Field({ nullable: true })
  @Authorized(SystemRole.ADMIN)
  public scraped: boolean

  @prop({ type: String, enum: LeagueType })
  @Field((_) => LeagueType, { nullable: true })
  public type: LeagueType

  @prop()
  @Field({ nullable: true })
  public sourceUrl?: string

  @prop()
  @Field({ nullable: true })
  public eliteProspectSlug?: string

  @prop()
  @Field({ nullable: true })
  public checked?: boolean

  @prop()
  @Field(() => TeamAutoConnectRules, { nullable: true })
  teamAutoConnectRules?: TeamAutoConnectRules

  @prop()
  @Field({ nullable: true })
  public ignore?: boolean

  @prop({ type: [Number] })
  public vectorEmbedding?: number[]

  public getLeague(): League {
    if ((this as League)._id !== undefined) {
      return this as League
    } else {
      return (this as unknown as { _doc: League })._doc
    }
  }
}

export const LeagueModel = getModelForClass(League, {
  existingMongoose: mongoose,
})
