import mongoose, { Types } from 'mongoose'

import { Team } from './team'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@Index({ createdAt: 1 })
@Index({ team: 1, fingerprint: 1, createdAt: 1 })
export class TeamView {
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: Team })
  public team: Ref<Team>

  @prop({ required: false })
  public country?: string

  @prop({ required: false })
  public ipAddress?: string

  @prop({ required: false })
  public fingerprint: string

  public totalViews: number
}

export const TeamViewModel = getModelForClass(TeamView, {
  existingMongoose: mongoose,
})
