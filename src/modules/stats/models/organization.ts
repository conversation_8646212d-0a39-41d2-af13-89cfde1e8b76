import DataLoader from 'dataloader'
import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'

import { AlternativeName } from './alternativeName'

import { getModelForClass, index, modelOptions, prop, searchIndex } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ name: 1 }, { unique: true })
@index({ slug: 1 }, { unique: true })
@searchIndex({
  name: 'organizations_search',
  definition: {
    mappings: {
      dynamic: false,
      fields: {
        name: {
          tokenization: 'edgeGram',
          minGrams: 3,
          maxGrams: 15,
          type: 'autocomplete',
        },
      },
    },
  },
})
@index({ 'alternativeNames.slug': 1 })
@index({ abbreviation: 1 })
export class Organization {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop()
  @Field({ nullable: false })
  public name: string

  @prop()
  @Field({ nullable: true })
  public abbreviation?: string

  @prop()
  @Field({ nullable: false })
  public slug: string

  @prop()
  @Field({ nullable: true })
  public country: string

  @prop()
  @Field({ nullable: true })
  public logo: string

  @prop()
  @Field({ nullable: true })
  public ogLogo: string

  @prop()
  public logoUnconfirmed: string

  @prop()
  @Field({ nullable: true })
  public state?: string

  @prop()
  @Field({ nullable: true })
  public province?: string

  @prop({ type: () => [AlternativeName], default: [] })
  @Field(() => [AlternativeName], { nullable: true })
  public alternativeNames: AlternativeName[]

  @prop()
  @Field({ nullable: true })
  public website?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public instagramUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public facebookUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public twitterUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public tiktokUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public arenaName?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public arenaAddress?: string

  @prop()
  @Field({ nullable: true })
  public checked?: boolean
}

export const OrganizationModel = getModelForClass(Organization, {
  existingMongoose: mongoose,
})
/**
 * Creates a DataLoader for Organization entities
 * This batches multiple organization lookups into a single database query
 */
export const createOrganizationLoader = () => {
  return new DataLoader<string, Organization | null>((keys) => generateLoader(keys))
}

/**
 * Batch loader function for organizations
 * @param keys Array of organization IDs to load
 * @returns Array of organizations in the same order as the keys
 */
const generateLoader = async (keys: readonly string[]) => {
  const organizations = await OrganizationModel.find({
    _id: { $in: keys },
  })

  // TODO make generic, reusable, model<{_id: string}> to map, map to model by key
  const organizationsMap = new Map<string, Organization>()
  organizations.forEach((organization) => {
    const key = organization._id.toString()

    organizationsMap.set(key, organization)
  })

  return keys.map((key) => organizationsMap.get(key) || null)
}
