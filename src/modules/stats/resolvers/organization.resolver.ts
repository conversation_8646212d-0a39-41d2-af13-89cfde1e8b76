import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Authorized, Info, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { handleAlternativeNames } from '../../../helpers/alternativeNames'
import { slugifyString } from '../../../helpers/string'
import { MediaConverter } from '../../../services/mediaConverter'
import { S3Service } from '../../../services/s3'
import { FailedToCreatePresignedUrl, Pagination, SystemRole } from '../../../types/general'
import { createStatusFilter } from '../helpers/league'
import { Organization, OrganizationModel } from '../models/organization'
import { TeamModel } from '../models/team'
import OrganizationRepository from '../repositories/organization.repository'
import {
  CreateOrganizationAlreadyExistsError,
  CreateOrganizationInput,
  CreateOrganizationResponse,
  OrganizationsFilter,
  OrganizationsResponse,
  UpdateOrganizationInput,
  UpdateOrganizationNotFoundError,
  UpdateOrganizationResponse,
  UploadOrganizationLogoInput,
  UploadOrganizationLogoResponse,
} from '../types/organization'

@Resolver((_of) => Organization)
@Service()
export default class OrganizationResolver {
  @Inject('app.logger')
  private readonly logger: pino.Logger

  @Inject()
  private readonly s3: S3Service

  @Inject()
  private readonly organizationRepository: OrganizationRepository

  @Inject()
  private readonly mediaConverter: MediaConverter

  @Mutation(() => CreateOrganizationResponse)
  @Authorized(SystemRole.ADMIN)
  public async createOrganization(
    @Arg('input') input: CreateOrganizationInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<CreateOrganizationResponse> {
    const response = new CreateOrganizationResponse()

    try {
      response.organization = await OrganizationModel.create({
        ...input,
        slug: slugifyString(input.name),
        alternativeNames: handleAlternativeNames(input.alternativeNames),
      })
    } catch (err) {
      if (err.name !== 'MongoServerError' || err.code !== 11000) {
        this.logger.error('Failed to create a organization.', err)
      }

      response.errors = [
        new CreateOrganizationAlreadyExistsError(
          'Organization already exists',
          info.path.key.toString()
        ),
      ]
    }

    return response
  }

  @Mutation(() => UpdateOrganizationResponse)
  @Authorized(SystemRole.ADMIN)
  public async updateOrganization(
    @Arg('id') id: Types.ObjectId,
    @Arg('input') input: UpdateOrganizationInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<UpdateOrganizationResponse> {
    const response = new UpdateOrganizationResponse()

    try {
      response.organization = await OrganizationModel.findByIdAndUpdate(
        id,
        {
          ...input,
          ...(input.name ? { slug: slugifyString(input.name) } : {}),
          alternativeNames: handleAlternativeNames(input.alternativeNames),
        },
        { new: true }
      )
    } catch (err) {
      if (err.name !== 'MongoServerError' || err.code !== 11000) {
        this.logger.error('Failed to update a organization.', err)
      }

      response.errors = [
        new UpdateOrganizationNotFoundError(
          'Failed to update organization',
          info.path.key.toString()
        ),
      ]
    }

    return response
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  public async deleteOrganization(@Arg('id') id: Types.ObjectId): Promise<boolean> {
    try {
      // check that organization is not used in any team
      const orgTeams = await TeamModel.find({ organization: id }).countDocuments()

      if (orgTeams > 0) {
        throw new GraphQLError('Organization is in use and cannot be deleted.', {
          extensions: { code: 'ORGANIZATION_IN_USE', noLog: true },
        })
      }

      const org = await OrganizationModel.findByIdAndDelete(id)
      await this.s3.remove(org?.logo)
      return true
    } catch (err) {
      if (err.extensions?.code === 'ORGANIZATION_IN_USE') {
        throw err
      }

      this.logger.error('Failed to delete organization.', err)
      return false
    }
  }

  @Query(() => Organization, { nullable: true })
  public async organization(
    @Arg('id', { nullable: true }) id: Types.ObjectId,
    @Arg('slug', { nullable: true }) slug: string
  ): Promise<Organization | null> {
    try {
      if (id) {
        return await OrganizationModel.findById(id)
      } else {
        return await OrganizationModel.findOne({ slug })
      }
    } catch (err) {
      this.logger.error('Failed to find organization.', err)
      return null
    }
  }

  @Query(() => OrganizationsResponse)
  async organizations(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter: OrganizationsFilter
  ): Promise<OrganizationsResponse> {
    const response = new OrganizationsResponse()
    response.edges = []

    const matchPipeline = {
      ...(filter?.country ? { country: filter.country } : {}),
      ...(filter?.checked ? createStatusFilter(filter.checked, 'checked') : {}),
      ...(filter?.withoutLogo
        ? { $or: [{ logo: { $exists: false } }, { logo: null }, { logo: '' }] }
        : {}),
    }

    const organizations = await this.organizationRepository.search(
      filter?.searchQuery,
      pagination,
      matchPipeline
    )

    for (const organization of organizations.nodes) {
      response.edges.push({
        node: organization,
      })
    }

    response.pageInfo = organizations.pageInfo

    return response
  }

  @Mutation(() => UploadOrganizationLogoResponse)
  @Authorized(SystemRole.ADMIN)
  async uploadOrganizationLogo(
    @Arg('input', { nullable: true }) input: UploadOrganizationLogoInput,
    @Arg('id', (_type) => Types.ObjectId) id: Types.ObjectId,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadOrganizationLogoResponse> {
    const response = new UploadOrganizationLogoResponse()

    if (input) {
      if (input.uploadSuccessful) {
        response.organization = await OrganizationModel.findById(id)

        if (response.organization?.logoUnconfirmed) {
          // remove old logo if exists
          if (response.organization.logo) {
            this.s3
              .remove(response.organization.logo)
              .catch((err) => this.logger.error('Failed to remove old organization logo', err))
          }

          const ogImageKey = await this.mediaConverter.createAndUploadOGImage(
            response.organization.logoUnconfirmed
          )

          response.organization = await OrganizationModel.findOneAndUpdate(
            { _id: id },
            {
              logo: response.organization.logoUnconfirmed,
              logoUnconfirmed: null,
              ogLogo: ogImageKey,
            }
          )
        }

        return response
      } else {
        // TODO: maybe also delete the file from S3 if it exists
        response.organization = await OrganizationModel.findOneAndUpdate(
          { _id: id },
          { logoUnconfirmed: null }
        )

        return response
      }
    }

    try {
      const logoKey = `images/organizations/${id}/logo/${new Date().getTime()}.png`
      response.preSignedUrl = await this.s3.createPresignedUrl(logoKey, 'image/png')

      // save the key to the user
      response.organization = await OrganizationModel.findOneAndUpdate(
        { _id: id },
        { logoUnconfirmed: logoKey }
      )
    } catch (err) {
      this.logger.error("Failed to create preSignedUrl for organization's logo:", err)
      response.errors = [
        new FailedToCreatePresignedUrl(`Failed to create preSignedUrl`, info.path.key.toString()),
      ]
    }

    return response
  }
}
