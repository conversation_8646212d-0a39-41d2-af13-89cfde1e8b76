import { <PERSON>t<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'type-graphql'
import { Service } from 'typedi'

import { ContextType } from '../../..'
import { getEntity } from '../../../helpers/mongoose'
import { ExternalConnection } from '../types/shared'

@Service()
@Resolver(() => ExternalConnection)
export class ExternalConnectionResolver {
  @FieldResolver(() => String, { nullable: true })
  async name(@Root() root: ExternalConnection, @Ctx() ctx: ContextType): Promise<string | null> {
    const externalConnection = getEntity(root)

    if (!externalConnection.externalId || !externalConnection.crawlerDomainConfig) {
      return null
    }

    return ctx.loaders.userInfo.load({
      externalId: externalConnection.externalId,
      crawlerDomainConfig: externalConnection.crawlerDomainConfig.toString(),
    })
  }
}
