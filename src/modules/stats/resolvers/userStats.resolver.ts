import { GraphQLResolveInfo } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { Pagination, SortOrder, SystemRole } from '../../../types/general'
import { UserModel } from '../../user/models'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { aggregatedUserStats, saveAggregatedStats } from '../helpers/userStats'
import { StatsSource, UserStats, UserStatsModel } from '../models/userStats'
import UserStatsSyncerService from '../services/userStatsSyncer.service'
import {
  CreateUserStatsAlreadyExistsError,
  CreateUserStatsInput,
  CreateUserStatsResponse,
  UpdateUserStatsInput,
  UpdateUserStatsNotFoundError,
  UpdateUserStatsResponse,
  UserStatsFilter,
  UserStatsResponse,
} from '../types/userStats'

@Resolver((_of) => UserStats)
@Service()
export default class UserStatsResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private userStatsSyncerService: UserStatsSyncerService

  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  @Mutation(() => CreateUserStatsResponse)
  @Authorized(SystemRole.ADMIN)
  public async createUserStats(
    @Arg('input') input: CreateUserStatsInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<CreateUserStatsResponse> {
    const response = new CreateUserStatsResponse()

    try {
      response.userStats = await (
        await UserStatsModel.create({ source: StatsSource.MANUAL, ...input })
      ).populate([{ path: 'team' }, { path: 'league' }])
    } catch (err) {
      this.logger.error('Failed to create a userStats.', err)
      response.errors = [
        new CreateUserStatsAlreadyExistsError('UserStats already exists', info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => UpdateUserStatsResponse)
  @Authorized(SystemRole.ADMIN)
  public async updateUserStats(
    @Arg('id') id: Types.ObjectId,
    @Arg('input') input: UpdateUserStatsInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<UpdateUserStatsResponse> {
    const response = new UpdateUserStatsResponse()

    try {
      response.userStats = await UserStatsModel.findByIdAndUpdate(id, input, {
        new: true,
      }).populate([{ path: 'team' }, { path: 'league' }])

      // if there are other user stats with team or league null and same external name, update them
      if (input.team && response.userStats) {
        await UserStatsModel.updateMany(
          {
            team: null,
            'externalInfo.externalTeamName': response.userStats.externalInfo?.externalTeamName,
            'externalInfo.externalTeamCountry':
              response.userStats.externalInfo?.externalTeamCountry,
          },
          {
            team: input.team,
          }
        )
      }

      if (input.league && response.userStats) {
        await UserStatsModel.updateMany(
          {
            league: null,
            'externalInfo.externalLeagueName': response.userStats.externalInfo?.externalLeagueName,
            'externalInfo.externalLeagueCountry':
              response.userStats.externalInfo?.externalLeagueCountry,
          },
          {
            league: input.league,
          }
        )
      }
    } catch (err) {
      this.logger.error('Failed to update a userStats.', err)
      response.errors = [
        new UpdateUserStatsNotFoundError('UserStats not found', info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  public async deleteUserStats(@Arg('id') id: Types.ObjectId): Promise<boolean> {
    try {
      await UserStatsModel.findByIdAndDelete(id)
      return true
    } catch (err) {
      this.logger.error('Failed to delete userStats.', err)
      return false
    }
  }

  @Query(() => UserStats, { nullable: true })
  @Authorized(SystemRole.ADMIN)
  public async adminUserStats(@Arg('id') id: Types.ObjectId): Promise<UserStats | null> {
    try {
      const userStats = await UserStatsModel.findById(id).populate([
        { path: 'team', populate: { path: 'organization' } },
        { path: 'league' },
      ])

      return userStats
    } catch (err) {
      this.logger.error('Failed to find userStats.', err)
      return null
    }
  }

  @Query(() => UserStatsResponse)
  @Authorized(SystemRole.ADMIN)
  async searchUserStats(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter') filter: UserStatsFilter
  ): Promise<UserStatsResponse> {
    const response = new UserStatsResponse()
    response.edges = []

    const userStats = await this.paginationRepository.find(
      { _id: new Types.ObjectId() },
      filter,
      UserStatsModel,
      { fields: [{ field: '_id', order: SortOrder.DESC }] },
      this.paginationRepository.mapPagination(pagination),
      ['team', 'league']
    )

    for (const userStat of userStats.nodes) {
      response.edges.push({
        node: userStat,
      })
    }

    response.pageInfo = userStats.pageInfo

    return response
  }

  @Query(() => UserStatsResponse)
  async userStats(
    @Ctx() ctx: ContextType,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter?: UserStatsFilter
  ): Promise<UserStatsResponse> {
    const response = new UserStatsResponse()
    response.edges = []

    const user = await UserModel.findOne(
      filter?.slug ? { slug: filter.slug } : { _id: filter?.user ?? ctx.user!.id }
    )

    const finalPagination = {
      first: pagination?.first ?? 80,
      after: pagination?.after,
      before: pagination?.before,
    }

    const userStatss = await this.paginationRepository.find(
      { _id: new Types.ObjectId(), orderInSeason: 0 },
      {
        $or: [
          { user: user?.id }, // TODO: once we figure out EP and leijonat (how to move to scraper approach), we can remove this
          ...(user?.externalConnections?.map((ec) => ({
            userExternalId: ec.externalId,
            crawlerDomainConfig: ec.crawlerDomainConfig._id,
          })) || []),
        ],
      },
      UserStatsModel,
      {
        fields: [{ field: 'orderInSeason', order: SortOrder.ASC }],
      },
      this.paginationRepository.mapPagination(finalPagination),
      [{ path: 'team', populate: { path: 'organization' } }, 'league']
    )

    const stats = aggregatedUserStats(userStatss.nodes)
    for (const userStats of stats) {
      response.edges.push({
        node: userStats,
      })
    }

    await saveAggregatedStats(stats, user!).catch((err) => {
      this.logger.error('Failed to save aggregated stats.', err)
    })

    response.pageInfo = userStatss.pageInfo

    return response
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  async syncUserStats(@Arg('userId') userId: string): Promise<boolean> {
    try {
      await this.userStatsSyncerService.syncUserStats(userId)
    } catch (err) {
      this.logger.error('Failed to sync user stats.', err)
      return false
    }

    return true
  }
}
