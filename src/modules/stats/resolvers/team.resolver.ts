import { StreamClient } from 'getstream'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { ResolveTree } from 'graphql-parse-resolve-info'
import { Types } from 'mongoose'
import pino from 'pino'
import {
  Arg,
  Authorized,
  Ctx,
  FieldResolver,
  Info,
  Int,
  Mutation,
  Query,
  Resolver,
  Root,
} from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { handleAlternativeNames } from '../../../helpers/alternativeNames'
import { Fields } from '../../../helpers/decorators/fields'
import { CURRENT_SEASON } from '../../../helpers/league'
import { slugifyString } from '../../../helpers/string'
import EliteProspectsService from '../../../modules/stats/services/eliteprospects/ep'
import { EliteProspectTeam } from '../../../modules/stats/types/ep'
import Analytics from '../../../services/analytics'
import { MediaConverter } from '../../../services/mediaConverter'
import { S3Service } from '../../../services/s3'
import {
  EntityNotFoundError,
  FailedToCreatePresignedUrl,
  Pagination,
  SortOrder,
  SystemRole,
} from '../../../types/general'
import { createTeamEmbedding } from '../../admin/helpers/team'
import { DiscoveredUrl } from '../../admin/models/discoveredUrl'
import TeamRepository from '../../stats/repositories/team.repository'
import { UserModel } from '../../user/models'
import { Role, User } from '../../user/models/user'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { FEED_NAMES } from '../../user/types/streamFeed'
import { UsersResponse } from '../../user/types/user'
import { assignLeaguesToTeam, createStatusFilter } from '../helpers/league'
import { GameModel } from '../models/game/game'
import { League } from '../models/league'
import { Organization, OrganizationModel } from '../models/organization'
import { Team, TeamModel } from '../models/team'
import { TeamFollowModel } from '../models/teamFollow'
import { TeamViewModel } from '../models/teamView'
import { UserStatsModel } from '../models/userStats'
import {
  CreateTeamAlreadyExistsError,
  CreateTeamInput,
  CreateTeamResponse,
  FollowTeamResponse,
  TeamDetailsResponse,
  TeamNotFoundError,
  TeamsFilter,
  TeamsFollowingResponse,
  TeamsResponse,
  UnfollowTeamResponse,
  UpdateTeamInput,
  UpdateTeamNotFoundError,
  UpdateTeamResponse,
  UploadTeamLogoInput,
  UploadTeamLogoResponse,
  VisitTeamResponse,
} from '../types/team'

@Resolver((_of) => Team)
@Service()
export default class TeamResolver {
  @Inject('app.logger')
  private readonly logger: pino.Logger

  @Inject('s3.service')
  private readonly s3: S3Service

  @Inject()
  private readonly teamRepository: TeamRepository

  @Inject()
  private readonly epService: EliteProspectsService

  @Inject()
  private readonly mediaConverter: MediaConverter

  @Inject('analytics.service')
  private readonly analytics: Analytics

  @Inject('streamFeed.client')
  private streamFeed: StreamClient

  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  @Mutation(() => CreateTeamResponse)
  @Authorized(SystemRole.ADMIN)
  public async createTeam(
    @Arg('input') input: CreateTeamInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<CreateTeamResponse> {
    const response = new CreateTeamResponse()

    try {
      if (input.organization) {
        const shortSlug = slugifyString(input.shortName)
        const teamExists = await TeamModel.findOne({
          shortNameSlug: shortSlug,
          organization: input.organization,
        })

        if (teamExists) {
          throw new GraphQLError('Team with same short name already exists in this organization.', {
            extensions: { code: 'TEAM_ALREADY_EXISTS' },
          })
        }
      }

      response.team = await (
        await TeamModel.create({
          ...input,
          slug: slugifyString(input.name),
          shortNameSlug: slugifyString(input.shortName),
          alternativeNames: handleAlternativeNames(input.alternativeNames),
        })
      ).populate([{ path: 'organization' }, { path: 'leagues' }])

      await assignLeaguesToTeam(response.team)

      await TeamModel.updateOne(
        { _id: response.team._id },
        { vectorEmbedding: await createTeamEmbedding(response.team._id) }
      )
    } catch (err) {
      if (
        !(err instanceof GraphQLError) &&
        (err.name !== 'MongoServerError' || err.code !== 11000)
      ) {
        this.logger.error('Failed to create a team.', err)
      }

      response.errors = [
        new CreateTeamAlreadyExistsError('Team already exists', info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => UpdateTeamResponse)
  @Authorized(SystemRole.ADMIN)
  public async updateTeam(
    @Arg('id') id: Types.ObjectId,
    @Arg('input') input: UpdateTeamInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<UpdateTeamResponse> {
    const response = new UpdateTeamResponse()

    try {
      // check that team with the same shortNameSlug and organization does not exist
      if (input.organization) {
        const team = await TeamModel.findById(id)

        if (!team) {
          throw new GraphQLError('Team not found.', {
            extensions: { code: 'TEAM_NOT_FOUND' },
          })
        }

        const shortNameSlug = slugifyString(input.shortName)

        const teamExists = await TeamModel.findOne({
          _id: { $ne: id },
          shortNameSlug,
          organization: input.organization,
        })

        if (teamExists) {
          throw new GraphQLError('Team with same short name already exists in this organization.', {
            extensions: { code: 'TEAM_ALREADY_EXISTS' },
          })
        }
      }

      response.team = await TeamModel.findByIdAndUpdate(
        id,
        {
          ...input,
          ...(input.name ? { slug: slugifyString(input.name) } : {}),
          ...(input.shortName ? { shortNameSlug: slugifyString(input.shortName) } : {}),
          alternativeNames: handleAlternativeNames(input.alternativeNames),
        },
        { new: true }
      ).populate([{ path: 'organization' }, { path: 'leagues' }])

      if (response.team) {
        await assignLeaguesToTeam(response.team)

        await TeamModel.updateOne(
          { _id: response.team._id },
          { vectorEmbedding: await createTeamEmbedding(response.team._id) }
        )
      }

      // if setting organization and organization does not have logo, set it from team
      if (input.organization) {
        if (
          response.team?.organization &&
          !(response.team.organization as Organization).logo &&
          response.team.logo
        ) {
          const logoKey = `images/organizations/${response.team.organization.id}/logo/${new Date().getTime()}.png`

          await this.s3.copyObject(response.team.logo, logoKey)

          await OrganizationModel.updateOne({ _id: input.organization }, { logo: logoKey })
        }
      }
    } catch (err) {
      // instance of GraphQLError
      if (err instanceof GraphQLError) {
        throw err
      }

      if (err.name !== 'MongoServerError' || err.code !== 11000) {
        this.logger.error('Failed to update a team.', err)
      }

      response.errors = [
        new UpdateTeamNotFoundError('Failed to update team', info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  public async deleteTeam(@Arg('id') id: Types.ObjectId): Promise<boolean> {
    try {
      // check that team is not used by any user
      const usersWithTeam = await UserModel.find({ currentTeam: id }, {}, { limit: 1 })

      if (usersWithTeam.length > 0) {
        throw new GraphQLError('Team is in use and cannot be deleted.', {
          extensions: { code: 'TEAM_IN_USE' },
        })
      }

      await Promise.all([
        GameModel.updateMany({ homeTeam: id }, { homeTeam: null }),
        GameModel.updateMany({ awayTeam: id }, { awayTeam: null }),
        UserStatsModel.updateMany({ team: id }, { team: null }),
        TeamModel.findByIdAndDelete(id),
      ])

      return true
    } catch (err) {
      if (err.extensions?.code === 'TEAM_IN_USE') {
        throw err
      }

      this.logger.error('Failed to delete team.', err)
      return false
    }
  }

  @Query(() => TeamsResponse)
  async teams(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter?: TeamsFilter,
    @Ctx() ctx?: ContextType
  ): Promise<TeamsResponse> {
    const response = new TeamsResponse()
    response.edges = []

    const matchPipeline = {
      ...(filter?.hasOrganization === false
        ? { $or: [{ organization: { $exists: false } }, { organization: { $eq: null } }] }
        : {}),
      ...(filter?.country ? { country: filter.country } : {}),
      ...(filter?.shortName ? { shortName: filter.shortName } : {}),
      ...(filter?.hasPlayers ? { playersCount: { $gt: 0 } } : {}),
      ...(filter?.playersCount ? { playersCount: { $lte: filter.playersCount } } : {}),
      ...(filter?.checked ? createStatusFilter(filter.checked, 'checked') : {}),
      ...(filter?.leagues ? { leagues: { $in: filter.leagues } } : {}),
    }

    const teams = await this.teamRepository.search(
      filter?.searchQuery,
      pagination,
      matchPipeline,
      !!ctx?.user?.admin
    )

    if (ctx?.user?.admin) {
      // TODO: populate only if client asks for leagues field
      await TeamModel.populate(teams.nodes, [{ path: 'leagues' }])
    }

    for (const team of teams.nodes) {
      response.edges.push({
        node: team,
        playersCount: team.playersCount,
      })
    }

    response.pageInfo = teams.pageInfo

    return response
  }

  @Query(() => TeamDetailsResponse)
  async team(
    @Arg('id', (_type) => Types.ObjectId, { nullable: true }) id: Types.ObjectId,
    @Arg('slug', { nullable: true }) slug: string,
    @Arg('orgSlug', { nullable: true }) orgSlug: string,
    @Arg('includeShadowPlayers', { nullable: true }) includeShadowPlayers: boolean,
    @Arg('season', { nullable: true }) season: string,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Fields() fields: ResolveTree,
    @Info() info: GraphQLResolveInfo
  ): Promise<TeamDetailsResponse> {
    const response = new TeamDetailsResponse()

    if (id) {
      response.team = await TeamModel.findById(id).populate([
        { path: 'organization' },
        { path: 'leagues' },
      ])
    } else {
      const organization = await OrganizationModel.findOne({ slug: orgSlug })

      if (!organization) {
        throw new GraphQLError('Organization not found.', {
          extensions: { code: 'ORGANIZATION_NOT_FOUND', noLog: true },
        })
      }

      response.team = await TeamModel.findOne({
        shortNameSlug: slug,
        organization: organization.id,
      }).populate([{ path: 'organization' }, { path: 'leagues' }])
    }

    const isUsersFieldRequested = !!fields.fieldsByTypeName.TeamDetailsResponse?.users

    if (!response.team) {
      response.errors = [new TeamNotFoundError('Team not found', info.path.key.toString())]
      return response
    }

    if (isUsersFieldRequested) {
      response.users = new UsersResponse()
      response.users.edges = []

      const filter = {
        currentTeam: response.team._id,
        public: { $ne: false },
        role: { $in: [Role.PLAYER, null] },
      }

      let shadowPlayersPromise: Promise<User[]> = new Promise((resolve) => resolve([]))
      if (includeShadowPlayers) {
        const games = await GameModel.find({
          $or: [{ homeTeam: response.team._id }, { awayTeam: response.team._id }],
          ...(season ? { season: season } : {}),
          date: { $lt: new Date() },
        })
          .sort({ date: -1 })
          .limit(20)
          .populate('discoveredUrl')

        const externalUserIdsMap = new Map<string, Set<string>>()
        for (const game of games) {
          const crawlerDomainConfig = (
            game.discoveredUrl as DiscoveredUrl
          ).crawlerDomainConfig.toString()

          if (!crawlerDomainConfig) {
            continue
          }

          if (!externalUserIdsMap.has(crawlerDomainConfig.toString())) {
            externalUserIdsMap.set(crawlerDomainConfig.toString(), new Set())
          }

          const players =
            game.homeTeam?.toString() === response.team?._id?.toString()
              ? game.homeTeamRoster
              : (game.awayTeamRoster ?? [])

          for (const player of players) {
            if (!player.externalInfo.playerExternalId) {
              continue
            }

            externalUserIdsMap
              .get(crawlerDomainConfig.toString())!
              .add(player.externalInfo.playerExternalId)
          }
        }

        const orConditions = []
        for (const [crawlerDomainConfigId, externalIds] of externalUserIdsMap.entries()) {
          if (externalIds.size > 0) {
            orConditions.push({
              externalConnections: {
                $elemMatch: {
                  externalId: { $in: Array.from(externalIds) },
                  crawlerDomainConfig: new Types.ObjectId(crawlerDomainConfigId),
                },
              },
            })
          }
        }

        if (orConditions.length > 0) {
          shadowPlayersPromise = UserModel.find({
            $or: orConditions,
            role: Role.SHADOW_PLAYER,
          })
            .limit(20)
            .lean()
        }
      }

      const usersPromise = await this.paginationRepository.find(
        { createdAt: new Date() },
        filter,
        UserModel,
        { fields: [{ field: 'createdAt', order: SortOrder.DESC }] },
        this.paginationRepository.mapPagination(pagination),
        [{ path: 'currentTeam', populate: { path: 'organization' } }]
      )

      const [users, shadowPlayersByGameStats] = await Promise.all([
        usersPromise,
        shadowPlayersPromise,
      ])

      for (const user of users.nodes) {
        response.users.edges.push({
          node: user as User,
        })
      }

      for (const shadowPlayer of shadowPlayersByGameStats) {
        response.users.edges.push({
          node: shadowPlayer,
        })
      }

      response.users.pageInfo = users.pageInfo
    } else {
      response.users = null
    }

    return response
  }

  @Query(() => TeamsResponse)
  async organizationTeams(
    @Arg('organizationId', (_type) => Types.ObjectId, { nullable: true })
    organizationId: Types.ObjectId,
    @Arg('slug', { nullable: true }) slug: string,
    @Arg('pagination', { nullable: true }) pagination: Pagination
  ): Promise<TeamsResponse> {
    const response = new TeamsResponse()
    response.edges = []

    if (slug) {
      const organization = await OrganizationModel.findOne({ slug })

      if (!organization) {
        throw new GraphQLError('Organization not found.', {
          extensions: { code: 'ORGANIZATION_NOT_FOUND', noLog: true },
        })
      }

      organizationId = organization._id
    }

    const filter = { organization: organizationId }

    const finalPagination = {
      first: pagination?.first ?? 25,
      after: pagination?.after,
      before: pagination?.before,
    }

    const teams = await this.paginationRepository.find(
      { _id: new Types.ObjectId(), order: 0 },
      filter,
      TeamModel,
      { fields: [{ field: 'order', order: SortOrder.ASC }] },
      this.paginationRepository.mapPagination(finalPagination),
      ['organization']
    )

    for (const team of teams.nodes) {
      response.edges.push({
        node: team,
        playersCount: team.playersCount,
      })
    }

    response.pageInfo = teams.pageInfo

    return response
  }

  @Mutation(() => FollowTeamResponse)
  @Authorized()
  async followTeam(
    @Arg('id') id: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<FollowTeamResponse> {
    try {
      let team = await TeamModel.findById(id)

      if (!team) {
        return {
          team,
          errors: [new EntityNotFoundError('Team does not exist.', 'followTeam')],
        }
      }

      await TeamFollowModel.create({
        follower: ctx.user?.id,
        team: team._id,
      })

      if (!team.stats) {
        team.stats = {
          followers: 1,
          views: 0,
        }

        team = await TeamModel.findOneAndUpdate({ _id: id }, { stats: team.stats }, { new: true })
      } else {
        team = await TeamModel.findOneAndUpdate(
          { _id: id },
          { $inc: { 'stats.followers': 1 } },
          { new: true }
        )
      }

      // Stream Feed follow
      const myTimeLinefeed = this.streamFeed.feed(FEED_NAMES.TIMELINE, ctx.user!.id)
      await myTimeLinefeed.follow(FEED_NAMES.TEAM, id.toString())

      this.analytics.trackEvent('newTeamFollow', {}, id.toString())

      return {
        team,
        errors: [],
      }
    } catch (err) {
      if (err.name === 'MongoServerError' && err.code === 11000) {
        // duplicate key error, we don't mind this
      } else {
        this.logger.error('DB error:', err)
      }

      throw new GraphQLError('Failed to follow team', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => UnfollowTeamResponse)
  @Authorized()
  async unfollowTeam(
    @Arg('id') id: Types.ObjectId,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<UnfollowTeamResponse> {
    try {
      const teamToUnfollow = await TeamModel.findById(id)
      if (!teamToUnfollow) {
        return {
          team: null,
          errors: [new EntityNotFoundError(`Team does not exist.`, info.path.key.toString())],
        }
      }

      const follow = await TeamFollowModel.findOne({
        team: id,
        follower: ctx.user!.id,
      })

      if (follow) {
        await follow.deleteOne()

        teamToUnfollow.stats!.followers -= 1
        await teamToUnfollow.save()

        // Stream Feed unfollow
        const myTimeLinefeed = this.streamFeed.feed(FEED_NAMES.TIMELINE, ctx.user!.id)
        await myTimeLinefeed.unfollow(FEED_NAMES.TEAM, id.toString())

        return {
          team: teamToUnfollow,
          errors: [],
        }
      }

      return {
        team: teamToUnfollow,
        errors: [new EntityNotFoundError(`Team follow does not exist.`, info.path.key.toString())],
      }
    } catch (err) {
      this.logger.error('Failed to unfollow team:', err)

      throw new GraphQLError('Failed to unfollow team', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Query(() => TeamsFollowingResponse)
  @Authorized()
  async myFollowingTeams(
    @Ctx() ctx: ContextType,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string
  ): Promise<TeamsFollowingResponse> {
    const response = new TeamsFollowingResponse()
    response.edges = []

    const mergedPagination = {
      first: first ?? pagination?.first ?? 10,
      after: after ?? pagination?.after,
      before: before ?? pagination?.before,
    }

    const teams = await this.paginationRepository.find(
      { _id: new Types.ObjectId() },
      { follower: ctx.user!.id },
      TeamFollowModel,
      { fields: [{ field: '_id', order: SortOrder.DESC }] },
      this.paginationRepository.mapPagination(mergedPagination),
      [{ path: 'team', populate: { path: 'organization' } }]
    )

    for (const team of teams.nodes) {
      response.edges.push({
        node: team.team as Team,
      })
    }

    response.pageInfo = teams.pageInfo

    return response
  }

  @Query(() => Boolean)
  @Authorized()
  async isFollowingTeam(
    @Arg('id') teamId: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<boolean> {
    const follow = await TeamFollowModel.findOne({
      team: teamId,
      follower: ctx.user!.id,
    })

    return !!follow
  }

  @Mutation(() => VisitTeamResponse)
  async visitTeam(
    @Arg('id') id: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<VisitTeamResponse> {
    try {
      const team = await TeamModel.findById(id)

      if (!team) {
        return {
          team: null,
          errors: [new EntityNotFoundError('Team does not exist.', 'visitTeam')],
        }
      }

      const viewExists = await TeamViewModel.findOne({
        team: id,
        fingerprint: ctx.fingerprint,
        createdAt: { $gt: new Date(Date.now() - 1000 * 60 * 60 * 4) },
      })

      if (viewExists) {
        return {
          team,
          errors: [],
        }
      }

      let teamUpdated
      if (!team.stats) {
        team.stats = {
          followers: 0,
          views: 1,
        }

        teamUpdated = await TeamModel.findOneAndUpdate(
          { _id: id },
          { stats: team.stats },
          { new: true }
        )
      } else {
        teamUpdated = await TeamModel.findOneAndUpdate(
          { _id: id },
          { $inc: { 'stats.views': 1 } },
          { new: true }
        )
      }

      await TeamViewModel.create({
        team: id,
        ipAddress: ctx.ipAddress,
        fingerprint: ctx.fingerprint,
      })

      this.analytics.trackEvent('newTeamView', {}, id.toString())

      return {
        team: teamUpdated,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to visit team:', err)

      throw new GraphQLError('Failed to visit team', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => UploadTeamLogoResponse)
  @Authorized(SystemRole.ADMIN)
  async uploadTeamLogo(
    @Arg('input', { nullable: true }) input: UploadTeamLogoInput,
    @Arg('id', (_type) => Types.ObjectId) id: Types.ObjectId,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadTeamLogoResponse> {
    const response = new UploadTeamLogoResponse()

    if (input) {
      if (input.uploadSuccessful) {
        response.team = await TeamModel.findById(id)

        if (response.team?.logoUnconfirmed) {
          if (response.team.logo) {
            this.s3
              .remove(response.team.logo)
              .catch((err) => this.logger.error('Failed to remove old team logo', err))
          }

          const ogImageKey = await this.mediaConverter.createAndUploadOGImage(
            response.team.logoUnconfirmed
          )

          response.team = await TeamModel.findOneAndUpdate(
            { _id: id },
            { logo: response.team.logoUnconfirmed, logoUnconfirmed: null, ogLogo: ogImageKey }
          )
        }

        return response
      } else {
        // TODO: maybe also delete the file from S3 if it exists
        response.team = await TeamModel.findOneAndUpdate({ _id: id }, { logoUnconfirmed: null })

        return response
      }
    }

    try {
      const logoKey = `images/teams/${id}/logo/${new Date().getTime()}.png`
      response.preSignedUrl = await this.s3.createPresignedUrl(logoKey, 'image/png')

      // save the key to the team
      response.team = await TeamModel.findOneAndUpdate({ _id: id }, { logoUnconfirmed: logoKey })
    } catch (err) {
      this.logger.error("Failed to create preSignedUrl for team's logo:", err)
      response.errors = [
        new FailedToCreatePresignedUrl(`Failed to create preSignedUrl`, info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  async changeTeamOrderUp(
    @Arg('id', (_type) => Types.ObjectId) id: Types.ObjectId
  ): Promise<boolean> {
    const team = await TeamModel.findById(id)

    if (!team) {
      return false
    }

    if (team.order === undefined) {
      return this.putTeamToTheEnd(team)
    }

    const previousTeam = await TeamModel.findOne({
      organization: team.organization,
      order: team.order - 1,
    })
    if (!previousTeam) {
      return false
    }

    await TeamModel.updateOne({ _id: team._id }, { order: team.order - 1 })
    await TeamModel.updateOne({ _id: previousTeam._id }, { order: previousTeam.order + 1 })

    return true
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  async changeTeamOrderDown(
    @Arg('id', (_type) => Types.ObjectId) id: Types.ObjectId
  ): Promise<boolean> {
    const team = await TeamModel.findById(id)

    if (!team) {
      return false
    }

    if (team.order === undefined) {
      return this.putTeamToTheEnd(team)
    }

    const nextTeam = await TeamModel.findOne({
      organization: team.organization,
      order: team.order + 1,
    })
    if (!nextTeam) {
      return this.putTeamToTheEnd(team)
    }

    await TeamModel.updateOne({ _id: team._id }, { order: team.order + 1 })
    await TeamModel.updateOne({ _id: nextTeam._id }, { order: nextTeam.order - 1 })

    return true
  }

  private async putTeamToTheEnd(team: Team): Promise<boolean> {
    const higherOrderTeam = await TeamModel.findOne({
      organization: team.organization,
      order: { $exists: true },
    }).sort({ order: -1 })

    if (!higherOrderTeam) {
      await TeamModel.updateOne({ _id: team._id }, { order: 1 })
      return true
    }

    await TeamModel.updateOne({ _id: team._id }, { order: higherOrderTeam.order + 1 })

    return true
  }

  @Query(() => EliteProspectTeam, { nullable: true })
  @Authorized(SystemRole.ADMIN)
  public async epTeam(@Arg('id') id: string): Promise<EliteProspectTeam | null> {
    try {
      const team = await this.epService.getTeam(id)

      return team
    } catch (err) {
      this.logger.error('Failed to get EP team data.', err)
      return null
    }
  }

  @Query(() => [EliteProspectTeam])
  @Authorized(SystemRole.ADMIN)
  public async epTeams(@Arg('searchQuery') searchQuery: string): Promise<EliteProspectTeam[]> {
    try {
      const teams = await this.epService.searchTeams(searchQuery)

      return teams ?? []
    } catch (err) {
      this.logger.warn('Failed to search EP teams.', err)
      return []
    }
  }

  @FieldResolver(() => Boolean, { nullable: true })
  async hasGames(@Root() root: Team): Promise<boolean | null> {
    const team = root.getTeam()

    const hasGames = await GameModel.exists({
      season: CURRENT_SEASON,
      $or: [{ homeTeam: team._id }, { awayTeam: team._id }],
    })

    return !!hasGames
  }

  @FieldResolver(() => League)
  async leagues(@Ctx() ctx: ContextType, @Root() root: Team): Promise<League[]> {
    if (ctx.user?.admin) {
      return root.getTeam().leagues as League[]
    }

    return this.teamRepository.getTeamLeagues(root.getTeam()._id)
  }

  @FieldResolver(() => Organization, { nullable: true })
  async organization(@Ctx() ctx: ContextType, @Root() root: Team): Promise<Organization | null> {
    const team = root.getTeam()

    if (!team.organization) {
      return null
    }

    // TODO: move this to loader - pass populated object and cache it
    if (!(team.organization instanceof Types.ObjectId) && team.organization._id) {
      return team.organization
    }

    return ctx.loaders.organization.load(team.organization.toString())
  }
}
