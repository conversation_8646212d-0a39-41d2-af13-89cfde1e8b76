import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Authorized, FieldResolver, Info, Mutation, Query, Resolver, Root } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { handleAlternativeNames } from '../../../helpers/alternativeNames'
import { CURRENT_SEASON } from '../../../helpers/league'
import { slugifyString } from '../../../helpers/string'
import { Pagination, SortOrder, SystemRole } from '../../../types/general'
import { createLeagueEmbedding } from '../../admin/helpers/league'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { assignLeagueToTeams } from '../helpers/league'
import { GameModel } from '../models/game/game'
import { League, LeagueModel } from '../models/league'
import { UserStatsModel } from '../models/userStats'
import {
  CreateLeagueAlreadyExistsError,
  CreateLeagueInput,
  CreateLeagueResponse,
  LeagueAlreadyExistsError,
  LeaguesFilter,
  LeaguesResponse,
  UpdateLeagueInput,
  UpdateLeagueNotFoundError,
  UpdateLeagueResponse,
} from '../types/league'

@Resolver((_of) => League)
@Service()
export default class LeagueResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  @Mutation(() => CreateLeagueResponse)
  @Authorized(SystemRole.ADMIN)
  public async createLeague(
    @Arg('input') input: CreateLeagueInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<CreateLeagueResponse> {
    const response = new CreateLeagueResponse()

    try {
      if (input.teamAutoConnectRules?.shortName) {
        input.teamAutoConnectRules.shortNameSlug = slugifyString(
          input.teamAutoConnectRules.shortName
        )
      }

      response.league = await LeagueModel.create({
        ...input,
        slug: slugifyString(input.name),
        alternativeNames: handleAlternativeNames(input.alternativeNames),
        vectorEmbedding: await createLeagueEmbedding(input),
      })

      if (response.league.teamAutoConnectRules) {
        await assignLeagueToTeams(response.league)
      }
    } catch (err) {
      this.logger.error('Failed to create a league.', err)
      response.errors = [
        new CreateLeagueAlreadyExistsError('League already exists', info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => UpdateLeagueResponse)
  @Authorized(SystemRole.ADMIN)
  public async updateLeague(
    @Arg('id') id: Types.ObjectId,
    @Arg('input') input: UpdateLeagueInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<UpdateLeagueResponse> {
    const response = new UpdateLeagueResponse()

    try {
      if (input.teamAutoConnectRules?.shortName) {
        input.teamAutoConnectRules.shortNameSlug = slugifyString(
          input.teamAutoConnectRules.shortName
        )
      }

      response.league = await LeagueModel.findByIdAndUpdate(
        id,
        {
          ...input,
          ...(input.name ? { slug: slugifyString(input.name) } : {}),
          ...(input.alternativeNames
            ? { alternativeNames: handleAlternativeNames(input.alternativeNames) }
            : {}),
          vectorEmbedding: await createLeagueEmbedding(input),
        },
        { new: true }
      )

      if (response.league?.teamAutoConnectRules) {
        await assignLeagueToTeams(response.league)
      }
    } catch (err) {
      if (err.code === 11000) {
        response.errors = [
          new LeagueAlreadyExistsError(
            `League with slug ${slugifyString(input.name)} already exists`,
            info.path.key.toString()
          ),
        ]
      } else {
        response.errors = [
          new UpdateLeagueNotFoundError('League not found', info.path.key.toString()),
        ]
      }
    }

    return response
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  public async deleteLeague(@Arg('id') id: Types.ObjectId): Promise<boolean> {
    try {
      // check that league is not used in any userStats
      const leagueUserStats = await UserStatsModel.find({ league: id }, undefined, { limit: 1 })

      if (leagueUserStats.length > 0) {
        throw new GraphQLError('League is in use and cannot be deleted.', {
          extensions: { code: 'LEAGUE_IN_USE' },
        })
      }

      await LeagueModel.findByIdAndDelete(id)

      return true
    } catch (err) {
      if (err.extensions?.code === 'LEAGUE_IN_USE') {
        throw err
      }

      this.logger.error('Failed to delete league.', err)
      return false
    }
  }

  @Query(() => League, { nullable: true })
  public async league(@Arg('slug', { nullable: true }) slug: string): Promise<League | null> {
    try {
      return await LeagueModel.findOne({ slug })
    } catch (err) {
      this.logger.error('Failed to find league.', err)
      return null
    }
  }

  @Query(() => LeaguesResponse)
  async leagues(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter: LeaguesFilter
  ): Promise<LeaguesResponse> {
    const { first = 10, after, before } = pagination || {}

    // filter by name, alternativeNames, and publicAlternativeName
    const searchFilter = filter?.searchQuery
      ? {
          $or: [
            { name: { $regex: new RegExp(`${filter.searchQuery}.*`, 'i') } },
            { 'alternativeNames.name': { $regex: new RegExp(`${filter.searchQuery}.*`, 'i') } },
            { publicAlternativeName: { $regex: new RegExp(`${filter.searchQuery}.*`, 'i') } },
          ],
        }
      : {}

    const leagues = await this.paginationRepository.find(
      { _id: new Types.ObjectId() },
      searchFilter,
      LeagueModel,
      { fields: [{ field: '_id', order: SortOrder.DESC }] },
      this.paginationRepository.mapPagination({ before, first, after })
    )

    return {
      edges: leagues.nodes.map((league) => ({ node: league })),
      pageInfo: leagues.pageInfo,
    }
  }

  @FieldResolver(() => Boolean, { nullable: true })
  async hasGames(@Root() root: League): Promise<boolean | null> {
    try {
      const league = root.getLeague()

      const hasGames = await GameModel.exists({
        league: league._id,
        season: CURRENT_SEASON,
      })

      return !!hasGames
    } catch (err) {
      this.logger.error('Failed to check if league has games.', err)
      return null
    }
  }
}
