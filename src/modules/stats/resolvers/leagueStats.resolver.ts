import { Types } from 'mongoose'
import { Arg, Query, Resolver } from 'type-graphql'
import { Service } from 'typedi'

import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { League } from '../models/league'
import { LeagueStats } from '../models/leagueStats/leagueStats'
import { LeagueStatsModel } from '../models/leagueStats/leagueStats'
import { LeagueStandingsResponse, LeagueStatsGroup } from '../types/leagueStats'

@Resolver((_of) => LeagueStats)
@Service()
export default class LeagueStatsResolver {
  private readonly pagination = new PaginationRepository()

  @Query(() => [LeagueStandingsResponse])
  async leagueStandings(
    @Arg('leagueId', { nullable: true }) leagueId: Types.ObjectId,
    @Arg('teamId', { nullable: true }) teamId: Types.ObjectId,
    @Arg('season', { nullable: true }) season: string
  ): Promise<LeagueStandingsResponse[]> {
    const response: LeagueStandingsResponse[] = []

    if (!leagueId && !teamId) {
      throw new Error('Either leagueId or teamId must be provided')
    }

    let leagueStats = []

    if (teamId) {
      leagueStats = await LeagueStatsModel.aggregate([
        {
          $match: {
            team: teamId,
            ...(season ? { season: season } : {}),
          },
        },
        {
          $lookup: {
            from: 'leagues',
            localField: 'league',
            foreignField: '_id',
            as: 'leagueData',
          },
        },
        {
          $addFields: {
            league: { $arrayElemAt: ['$leagueData', 0] },
          },
        },
        {
          $lookup: {
            from: 'leaguestats',
            // group by group and season
            let: { teamGroup: '$group', teamSeason: '$season' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$group', '$$teamGroup'] },
                      { $eq: ['$season', '$$teamSeason'] },
                    ],
                  },
                },
              },
              // load all team data
              {
                $lookup: {
                  from: 'teams',
                  localField: 'team',
                  foreignField: '_id',
                  as: 'teamData',
                },
              },
              {
                $lookup: {
                  from: 'organizations',
                  localField: 'teamData.organization',
                  foreignField: '_id',
                  as: 'organizationData',
                },
              },
              // merge them from teamData to team property
              {
                $addFields: {
                  team: {
                    $mergeObjects: [
                      { $arrayElemAt: ['$teamData', 0] },
                      { organization: { $arrayElemAt: ['$organizationData', 0] } },
                    ],
                  },
                },
              },
              { $sort: { standing: 1 } },
            ],
            as: 'standings',
          },
        },
      ])

      // group final result by league - we already have groups from aggregation
      const groupedByLeague = new Map<string, LeagueStatsGroup>()
      leagueStats.forEach((leagueStat) => {
        if (!groupedByLeague.has(leagueStat.league?._id.toString())) {
          groupedByLeague.set(leagueStat.league?._id.toString(), {
            league: leagueStat.league,
            groups: [],
          })
        }
        groupedByLeague.get(leagueStat.league?._id.toString())?.groups.push({
          group: leagueStat.group,
          standings: leagueStat.standings.map((standing: LeagueStats) => ({
            ...standing,
            league: leagueStat.league,
          })),
        })
      })

      // for team ID league stats we could have team in different leagues
      for (const groupedLeague of groupedByLeague.values()) {
        response.push({
          league: groupedLeague.league,
          groups: groupedLeague.groups,
        })
      }
    } else if (leagueId) {
      const rawLeagueStats = await LeagueStatsModel.find({
        league: leagueId,
        ...(season ? { season: season } : {}),
      })
        .populate([
          'league',
          {
            path: 'team',
            populate: { path: 'organization' },
          },
        ])
        .lean()

      // group league statsby group
      const groupedLeagueStatsByGroups = new Map<string, LeagueStats[]>()
      rawLeagueStats.forEach((leagueStat) => {
        if (leagueStat && leagueStat.group) {
          if (!groupedLeagueStatsByGroups.has(leagueStat.group)) {
            groupedLeagueStatsByGroups.set(leagueStat.group, [])
          }
          groupedLeagueStatsByGroups.get(leagueStat.group)?.push(leagueStat)
        }
      })

      const groups = []
      for (const group of groupedLeagueStatsByGroups.keys()) {
        groups.push({
          group,
          standings:
            groupedLeagueStatsByGroups.get(group)?.sort((a, b) => a.standing - b.standing) || [],
        })
      }

      // for league ID league stats we have get one league and it's groups
      response.push({
        league: rawLeagueStats[0].league as League,
        groups,
      })
    }
    return response
  }
}
