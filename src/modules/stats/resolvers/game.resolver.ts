import { ResolveTree } from 'graphql-parse-resolve-info'
import { PipelineStage, Types } from 'mongoose'
import pino from 'pino'
import { Arg, FieldResolver, Int, Query, Resolver, Root } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { Fields } from '../../../helpers/decorators/fields'
import { getEntity } from '../../../helpers/mongoose'
import { SortInput, SortOrder } from '../../../types/general'
import { UserModel } from '../../user/models'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { eventsMapper } from '../helpers/game'
import { Game, GameModel } from '../models/game/game'
import { GameEvent } from '../models/game/gameEvent'
import {
  CurrentUserTeam,
  GamesFilter,
  GamesPeriod,
  GamesPlayersInput,
  GamesPlayersResponse,
  GamesResponse,
} from '../types/game'

import { DocumentType } from '@typegoose/typegoose'

@Resolver((_of) => Game)
@Service()
export default class GameResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Query(() => GamesResponse)
  async games(
    @Arg('filter', { nullable: true }) filter?: GamesFilter,
    @Arg('sort', { nullable: true }) sort?: SortInput,
    @Arg('first', () => Int, { nullable: true }) first?: number,
    @Arg('after', { nullable: true }) after?: string,
    @Arg('before', { nullable: true }) before?: string
  ): Promise<GamesResponse> {
    try {
      const response = new GamesResponse()
      response.edges = []

      const pipelineStages: PipelineStage[] = []
      const matchStage: PipelineStage.Match['$match'] = {}

      matchStage.date = { $lt: new Date() }

      if (filter?.leagueId) {
        matchStage.league = new Types.ObjectId(filter.leagueId)
      }

      if (filter?.season) {
        matchStage.season = filter.season
      }

      matchStage.ignore = { $ne: true }
      if (filter?.userId) {
        const user = await UserModel.findById(filter.userId)

        matchStage.$or = [
          {
            'homeTeamRoster.externalInfo.playerExternalId': {
              $in: user?.externalConnections.map((ec) => ec.externalId),
            },
          },
          {
            'awayTeamRoster.externalInfo.playerExternalId': {
              $in: user?.externalConnections.map((ec) => ec.externalId),
            },
          },
        ]
      }

      if (filter?.teamId) {
        matchStage.$or = [
          ...(matchStage.$or || []),
          { homeTeam: new Types.ObjectId(filter.teamId) },
          { awayTeam: new Types.ObjectId(filter.teamId) },
        ]
      }

      if (filter?.period) {
        const now = new Date()

        if (filter.period === GamesPeriod.HISTORY) {
          matchStage.date = { $lt: now }
        } else if (filter.period === GamesPeriod.FUTURE) {
          matchStage.date = { $gt: now }
        }
      }

      if (Object.keys(matchStage).length > 0) {
        pipelineStages.unshift({ $match: matchStage })
      }

      const paginationRepository = new PaginationRepository()

      const gameStats = await paginationRepository.aggregate({
        templateSortObject: {
          _id: new Types.ObjectId(),
          date: new Date(),
          league: new Types.ObjectId(),
          homeTeam: new Types.ObjectId(),
          awayTeam: new Types.ObjectId(),
        },
        pipeline: pipelineStages,
        model: GameModel,
        sorting: sort ?? { fields: [{ field: 'date', order: SortOrder.DESC }] },
        pagination: paginationRepository.mapPagination({ before, first, after }),
      })

      await GameModel.populate(gameStats.nodes, [
        { path: 'homeTeam', populate: { path: 'organization' } },
        { path: 'awayTeam', populate: { path: 'organization' } },
        { path: 'league' },
      ])

      const user = await UserModel.findById(filter?.userId)

      const externalUserIdSet = new Set(user?.externalConnections.map((ec) => ec.externalId))

      response.edges = gameStats.nodes.map((node) => {
        let currentUserTeam = CurrentUserTeam.UNKNOWN
        let currentUserPlayer = null

        const findPlayer = (
          roster: Game['homeTeamRoster'] | Game['awayTeamRoster'],
          team: CurrentUserTeam
        ) => {
          for (const player of roster || []) {
            const playerId = player.externalInfo.playerExternalId
            if (playerId && externalUserIdSet.has(playerId)) {
              currentUserTeam = team
              return player
            }
          }
          return null
        }

        currentUserPlayer =
          findPlayer(node.homeTeamRoster, CurrentUserTeam.HOME_TEAM) ||
          findPlayer(node.awayTeamRoster, CurrentUserTeam.AWAY_TEAM)

        if (currentUserPlayer) {
          currentUserPlayer.player = user
        }

        return { node, currentUserPlayer, currentUserTeam }
      })

      response.pageInfo = gameStats.pageInfo
      return response
    } catch (err) {
      this.logger.error('Failed to fetch game stats:', err)
      const emptyResponse = new GamesResponse()
      emptyResponse.edges = []
      emptyResponse.pageInfo = {
        hasNextPage: false,
        endCursor: '',
        hasPreviousPage: false,
        startCursor: '',
        totalCount: 0,
      }
      return emptyResponse
    }
  }

  @Query(() => Game, { nullable: true })
  async game(@Arg('id') id: Types.ObjectId): Promise<Game | null> {
    try {
      const game = await GameModel.findById(id)
        .populate([
          { path: 'discoveredUrl' },
          { path: 'league' },
          { path: 'homeTeam', populate: { path: 'organization' } },
          { path: 'awayTeam', populate: { path: 'organization' } },
        ])
        .lean()

      if (game) {
        await Game.loadPlayersForGames([game])
      }

      return game
    } catch (err) {
      this.logger.error('Failed to find game stats:', err)
      return null
    }
  }

  @FieldResolver(() => [[GameEvent]], { nullable: true })
  async eventsByPeriod(@Root() root: DocumentType<Game>): Promise<GameEvent[][] | null> {
    const game = getEntity(root)
    const sortedEvents = game ? eventsMapper(root.events as DocumentType<GameEvent>[]) : []
    return sortedEvents
  }

  @Query(() => GamesPlayersResponse, { nullable: true })
  async gamesPlayers(
    @Arg('input') input: GamesPlayersInput,
    @Fields() fields: ResolveTree
  ): Promise<GamesPlayersResponse | null> {
    try {
      const { gameIds, addGamePerformanceLikes } = input

      const populateHomeTeam =
        !!fields.fieldsByTypeName.GamesPlayersResponse.games.fieldsByTypeName.GameStats.homeTeam
      const populateAwayTeam =
        !!fields.fieldsByTypeName.GamesPlayersResponse.games.fieldsByTypeName.GameStats.awayTeam

      const populate = [
        { path: 'discoveredUrl' },
        { path: 'league' },
        ...(populateHomeTeam ? [{ path: 'homeTeam', populate: { path: 'organization' } }] : []),
        ...(populateAwayTeam ? [{ path: 'awayTeam', populate: { path: 'organization' } }] : []),
      ]

      // TODO remove league poplation after few weeks, the league logo shoudl be on recent activites by then
      const games = await GameModel.find({ _id: { $in: gameIds } })
        .populate(populate)
        .lean()

      await Game.loadPlayersForGames(games)

      // TODO later combine with users likes of playesr in game(s)
      if (addGamePerformanceLikes) {
        // call userLikesPlayersGamesPerformance
      }

      return { games }
    } catch (err) {
      this.logger.error('Failed to find games players:', err)
      return null
    }
  }
}
