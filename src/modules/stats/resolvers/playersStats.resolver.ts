import { PipelineStage, Types } from 'mongoose'
import pino from 'pino'
import { Arg, Int, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { CURRENT_SEASON } from '../../../helpers/league'
import { SortInput, SortOrder } from '../../../types/general'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { populateUsersByExternalIds } from '../helpers/userStats'
import { LeagueStages, StatsSource, UserStats, UserStatsModel } from '../models/userStats'
import { PlayersStatsFilter, PlayersStatsResponse } from '../types/userStats'

@Resolver((_of) => UserStats)
@Service()
export default class PlayersStatsResolver {
  @Inject('app.logger')
  private readonly logger: pino.Logger

  private readonly paginationRepository = new PaginationRepository()

  @Query(() => PlayersStatsResponse)
  async playersStatsList(
    @Arg('filter') filter: PlayersStatsFilter,
    @Arg('sort', { nullable: true }) sort?: SortInput,
    @Arg('first', () => Int, { nullable: true }) first?: number,
    @Arg('after', { nullable: true }) after?: string,
    @Arg('before', { nullable: true }) before?: string
  ): Promise<PlayersStatsResponse> {
    const response = new PlayersStatsResponse()
    response.edges = []

    const pipelineStages: PipelineStage[] = []
    const matchStage: PipelineStage.Match['$match'] = {}

    matchStage.source = StatsSource.SCRAPER

    matchStage.league = filter.leagueId

    if (filter?.teamId) {
      matchStage.team = filter.teamId
    }
    if (filter?.position) {
      matchStage.position = filter.position
    }

    matchStage.season = filter.season ?? CURRENT_SEASON

    if (!filter?.stage || filter?.stage === 'all') {
      matchStage.$or = matchStage.$or || []

      matchStage.$or.push(
        ...[
          { ['externalInfo.stage']: null },
          { ['externalInfo.stage.value']: null },
          { ['externalInfo.stage.value']: '' },
          { ['externalInfo.stage']: { $exists: false } },
          { ['externalInfo.stage.value']: { $exists: false } },
        ]
      )
    } else if (filter?.stage) {
      matchStage['externalInfo.stage.value'] = filter?.stage
    }

    if (!filter?.teamId) {
      matchStage.total = { $ne: false }
    }

    let crawlerDomainConfig: Types.ObjectId | undefined
    //aggregate league(s)stages
    try {
      const stages = await UserStatsModel.aggregate<LeagueStages>([
        {
          $match: {
            ...(matchStage.league ? { league: matchStage.league } : {}),
            ...(matchStage.team ? { team: matchStage.team } : {}),
            ...(matchStage.season ? { season: matchStage.season } : {}),
          },
        },
        {
          $group: {
            _id: {
              league: '$league',
              stageValue: '$externalInfo.stage.value',
              stageLabel: '$externalInfo.stage.label',
              crawlerDomainConfig: '$crawlerDomainConfig',
            },
          },
        },
        {
          $group: {
            _id: '$_id.league',
            stages: {
              $addToSet: {
                value: '$_id.stageValue',
                label: '$_id.stageLabel',
                crawlerDomainConfig: '$_id.crawlerDomainConfig',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            league: '$_id',
            stages: 1,
          },
        },
      ])

      await UserStatsModel.populate(stages, [{ path: 'league' }])

      // We need to filter stages from multiple scraper sources or we would show duplicated data
      // include only stages from single crawler domain config (first one we find - at least for now)
      stages.forEach((stage) => {
        const firstCrawlerDomainConfig = stage.stages[0]?.crawlerDomainConfig
        crawlerDomainConfig = firstCrawlerDomainConfig?._id
        stage.stages = stage.stages.filter(
          (stage) => stage.crawlerDomainConfig?.toString() === firstCrawlerDomainConfig?.toString()
        )
      })

      if (crawlerDomainConfig) {
        matchStage.crawlerDomainConfig = crawlerDomainConfig
      }

      response.leagueStages = stages
    } catch (err) {
      this.logger.error('Failed to fetch league stages.', err)
    }

    if (Object.keys(matchStage).length > 0) {
      const { $or, ...matches } = matchStage
      pipelineStages.unshift({
        $match: $or?.length
          ? {
              $and: [matches, { $or }],
            }
          : matches,
      })
    }

    const userStats = await this.paginationRepository.aggregate({
      templateSortObject: {
        _id: new Types.ObjectId(),
        league: new Types.ObjectId(),
        team: new Types.ObjectId(),
        goals: 0,
        assists: 0,
        points: 0,
        pim: 0,

        gamesPlayed: 0,
        svp: 0,
        gaa: 0,
        shutouts: 0,

        toi: '00:00',
        saves: 0,
        ga: 0,
      },
      pipeline: pipelineStages,
      model: UserStatsModel,
      sorting: sort ?? { fields: [{ field: 'points', order: SortOrder.DESC }] },
      pagination: this.paginationRepository.mapPagination({ before, first, after }),
    })

    // populates users on userStats by UserModel's externalConnections and userExtenralid
    await populateUsersByExternalIds(userStats.nodes, (userStat) => {
      response.edges.push({
        node: userStat,
      })
    })

    // TODO we need only Team logo, and shortname and org slug
    await UserStatsModel.populate(userStats.nodes, [
      { path: 'league' },
      { path: 'team', populate: { path: 'organization' } },
    ])

    response.pageInfo = userStats.pageInfo

    return response
  }
}
