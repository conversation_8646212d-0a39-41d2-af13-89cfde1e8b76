import { Types } from 'mongoose'
import { Field, InputType, ObjectType, registerEnumType } from 'type-graphql'

import { PageInfo } from '../../../types/general'
import { Game } from '../models/game/game'
import { RosterPlayer } from '../models/game/rosterPlayer'

export enum CurrentUserTeam {
  HOME_TEAM = 'HOME_TEAM',
  AWAY_TEAM = 'AWAY_TEAM',
  UNKNOWN = 'UNKNOWN',
}

registerEnumType(CurrentUserTeam, {
  name: 'CurrentUserTeam',
  description: 'Indicates which team the current user is on in a game',
})

@ObjectType()
export class GamesEdge {
  @Field(() => Game)
  public node: Game

  @Field(() => RosterPlayer, { nullable: true })
  public currentUserPlayer?: RosterPlayer | null

  @Field(() => CurrentUserTeam, { nullable: true })
  public currentUserTeam?: CurrentUserTeam | null
}

@ObjectType()
export class GamesResponse {
  @Field(() => [GamesEdge])
  public edges: Array<GamesEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

@InputType()
export class GamesPlayersInput {
  @Field(() => [Types.ObjectId], { nullable: false })
  public gameIds?: Types.ObjectId[]

  @Field({ defaultValue: false })
  public addGamePerformanceLikes?: boolean
}

@ObjectType()
export class GamesPlayersResponse {
  @Field(() => [Game], { nullable: true })
  public games: Game[] | null
}

export enum GamesPeriod {
  ALL = 'ALL',
  HISTORY = 'HISTORY',
  FUTURE = 'FUTURE',
}

registerEnumType(GamesPeriod, {
  name: 'GamesPeriod',
  description: 'The period filter for games (ALL, HISTORY, FUTURE)',
})

@InputType()
export class GamesFilter {
  @Field({ nullable: true })
  public leagueId?: Types.ObjectId

  @Field({ nullable: true })
  public userId: Types.ObjectId

  @Field({ nullable: true })
  public teamId?: Types.ObjectId

  @Field(() => GamesPeriod, { nullable: true })
  public period?: GamesPeriod

  @Field({ nullable: true })
  public season?: string
}
