import { Types } from 'mongoose'
import { Field, ObjectType } from 'type-graphql'

import { CrawlerDomainConfig } from '../../admin/models/crawlerDomainConfig'

import { prop, Ref } from '@typegoose/typegoose'

@ObjectType()
export class ExternalConnection {
  @Field(() => CrawlerDomainConfig)
  @prop({ required: true, type: Types.ObjectId, ref: CrawlerDomainConfig })
  public crawlerDomainConfig: Ref<CrawlerDomainConfig>

  @prop({ required: true })
  @Field(() => String)
  public externalId: string
}
