import { Field, InputType, ObjectType } from 'type-graphql'

import { PageInfo } from '../../../types/general'
import { UserInfo } from '../models/userInfo'

@InputType()
export class UserInfoFilter {
  @Field()
  searchQuery: string
}

@ObjectType()
class UserInfoEdge {
  @Field(() => UserInfo)
  node: UserInfo
}

@ObjectType()
export class UserInfoResponse {
  @Field(() => [UserInfoEdge])
  edges: UserInfoEdge[]

  @Field(() => PageInfo)
  pageInfo: PageInfo
}
