import { Field, ObjectType } from 'type-graphql'

export class EliteProspectsSeason {
  id: string | undefined
  postseasonType: string | undefined
  leagueType: string | undefined
  league:
    | {
        id: string
        slug: string
        name: string
        country: {
          iso_3166_1_alpha_2: string
        }
      }
    | undefined
  team:
    | {
        id: string
        slug: string
        name: string
        country: {
          iso_3166_1_alpha_2: string
        }
        logo: {
          large: string
        }
      }
    | undefined
  season:
    | {
        slug: string
        startYear: number
        endYear: number
      }
    | undefined
  regularStats:
    | {
        GP: number
        G: number
        A: number
        PIM: number
        PM: number

        W: number
        L: number
        T: number
        GAA: number
        SVP: number
        SO: number
      }
    | undefined
  postseasonStats:
    | {
        GP: number
        G: number
        A: number
        PIM: number
        PM: number

        W: number
        L: number
        T: number
        GAA: number
        SVP: number
        SO: number
      }
    | undefined
}

@ObjectType()
export class EliteProspectCountry {
  @Field({ nullable: true })
  public iso_3166_1_alpha_2?: string
}

@ObjectType()
export class EliteProspectLogo {
  @Field()
  public large?: string
}

@ObjectType()
export class EliteProspectTeam {
  @Field()
  public id: string

  @Field()
  public slug: string

  @Field()
  public name: string

  @Field((_) => EliteProspectCountry)
  public country: EliteProspectCountry

  @Field({ nullable: true })
  public logo?: EliteProspectLogo
}
