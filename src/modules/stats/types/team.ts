import { IsISO31661Alpha2, ValidateIf } from 'class-validator'
import { Types } from 'mongoose'
import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import {
  EntityNotFoundError,
  FailedToCreatePresignedUrl,
  IUserError,
  PageInfo,
} from '../../../types/general'
import { UsersResponse } from '../../user/types/user'
import { AlternativeNameInput } from '../models/alternativeName'
import { StatusType } from '../models/league'
import { Team } from '../models/team'

// create team

@ObjectType()
export class CreateTeamResponse {
  @Field(() => Team, { nullable: true })
  public team?: Team

  @Field((_type) => [CreateTeamError], { nullable: true })
  public errors: Array<typeof CreateTeamError>
}

const CreateTeamError = createUnionType({
  name: 'CreateTeamError',
  types: () => [CreateTeamAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class CreateTeamAlreadyExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class CreateTeamInput {
  @Field()
  public name: string

  @Field()
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country: string

  @Field({ nullable: true })
  public shortName?: string

  @Field({ nullable: true })
  public organization?: string

  @Field({ nullable: true })
  public externalId?: string

  @Field(() => [AlternativeNameInput], { nullable: true })
  public alternativeNames?: AlternativeNameInput[]

  @Field(() => [Types.ObjectId], { nullable: true })
  public leagues?: Types.ObjectId[]

  @Field({ nullable: true })
  public eliteProspectLink?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field({ nullable: true })
  public abbreviation?: string
}

// update team

@ObjectType()
export class UpdateTeamResponse {
  @Field(() => Team, { nullable: true })
  public team?: Team | null

  @Field((_type) => [UpdateTeamError], { nullable: true })
  public errors: Array<typeof UpdateTeamError>
}

const UpdateTeamError = createUnionType({
  name: 'UpdateTeamError',
  types: () => [UpdateTeamNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class UpdateTeamNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class UpdateTeamInput {
  @Field({ nullable: true })
  public name?: string

  @Field({ nullable: true })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country?: string

  @Field({ nullable: true })
  public shortName?: string

  @Field({ nullable: true })
  public organization?: string

  @Field(() => [AlternativeNameInput], { nullable: true })
  public alternativeNames?: AlternativeNameInput[]

  @Field(() => [Types.ObjectId], { nullable: true })
  public leagues?: Types.ObjectId[]

  @Field({ nullable: true })
  public eliteProspectLink?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field({ nullable: true })
  public abbreviation?: string
}

// delete team

@ObjectType()
export class DeleteTeamResponse {
  @Field(() => Team, { nullable: true })
  public team?: Team

  @Field((_type) => [DeleteTeamError], { nullable: true })
  public errors: Array<typeof DeleteTeamError>
}

const DeleteTeamError = createUnionType({
  name: 'DeleteTeamError',
  types: () => [DeleteTeamNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class DeleteTeamNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

// teams search

@InputType()
export class TeamsFilter {
  @Field({ nullable: true })
  public searchQuery?: string

  @Field({ nullable: true })
  public hasOrganization?: boolean

  @Field({ nullable: true })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country?: string

  @Field({ nullable: true })
  public shortName?: string

  @Field({ nullable: true })
  public hasPlayers?: boolean

  @Field({ nullable: true })
  public playersCount?: number

  @Field((_) => StatusType, { nullable: true })
  public checked?: StatusType

  @Field(() => [Types.ObjectId], { nullable: true })
  public leagues?: Types.ObjectId[]
}

@ObjectType()
export class TeamEdge {
  @Field((_type) => Team, { nullable: false })
  public node: Team

  @Field({ nullable: false, deprecationReason: 'Use team.playersCount instead' })
  public playersCount: number
}

@ObjectType()
export class TeamFollowingEdge {
  @Field((_type) => Team, { nullable: false })
  public node: Team
}

@ObjectType()
export class TeamsResponse {
  @Field((_type) => [TeamEdge], { nullable: false })
  public edges: Array<TeamEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

@ObjectType()
export class TeamsFollowingResponse {
  @Field((_type) => [TeamFollowingEdge], { nullable: false })
  public edges: Array<TeamFollowingEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

@ObjectType()
export class TeamDetailsResponse {
  @Field(() => Team, { nullable: true })
  public team?: Team | null

  @Field((_type) => UsersResponse, { nullable: true })
  public users: UsersResponse | null

  @Field((_type) => [TeamDetailsError], { nullable: true })
  public errors: Array<typeof TeamDetailsError>
}

const TeamDetailsError = createUnionType({
  name: 'TeamDetailsError',
  types: () => [TeamNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class TeamNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

// logo upload

@InputType()
export class UploadTeamLogoInput {
  @Field({ nullable: false })
  public uploadSuccessful: boolean
}

@ObjectType()
export class UploadTeamLogoResponse {
  @Field(() => String, { nullable: true })
  public preSignedUrl: string | null

  @Field(() => Team, { nullable: true })
  public team?: Team | null

  @Field((_type) => [UploadTeamLogoError], { nullable: true })
  public errors: Array<typeof UploadTeamLogoError>
}

const UploadTeamLogoError = createUnionType({
  name: 'UploadTeamLogoError',
  types: () => [FailedToCreatePresignedUrl] as const,
})

// FollowTeam

@ObjectType()
export class FollowTeamResponse {
  @Field(() => Team, { nullable: true })
  public team?: Team | null

  @Field((_type) => [FollowTeamError], { nullable: true })
  public errors: Array<typeof FollowTeamError>
}

const FollowTeamError = createUnionType({
  name: 'FollowTeamError',
  types: () => [EntityNotFoundError] as const,
})

// UnfollowTeam

@ObjectType()
export class UnfollowTeamResponse {
  @Field(() => Team, { nullable: true })
  public team?: Team | null

  @Field((_type) => [UnfollowTeamError], { nullable: true })
  public errors: Array<typeof UnfollowTeamError>
}

const UnfollowTeamError = createUnionType({
  name: 'UnfollowTeamError',
  types: () => [EntityNotFoundError] as const,
})

// VisitTeamResponse

@ObjectType()
export class VisitTeamResponse {
  @Field(() => Team, { nullable: true })
  public team?: Team | null

  @Field((_type) => [VisitTeamError], { nullable: true })
  public errors: Array<typeof VisitTeamError>
}

const VisitTeamError = createUnionType({
  name: 'VisitTeamError',
  types: () => [EntityNotFoundError] as const,
})
