import { Types } from 'mongoose'
import { createUnionType, Field, Float, InputType, Int, ObjectType } from 'type-graphql'

import { IUserError, PageInfo } from '../../../types/general'
import { LeagueStages, UserStats, UserStatsPosition } from '../models/userStats'

// create userStats

@ObjectType()
export class CreateUserStatsResponse {
  @Field(() => UserStats, { nullable: true })
  public userStats?: UserStats

  @Field((_type) => [CreateUserStatsError], { nullable: true })
  public errors: Array<typeof CreateUserStatsError>
}

const CreateUserStatsError = createUnionType({
  name: 'CreateUserStatsError',
  types: () => [CreateUserStatsAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class CreateUserStatsAlreadyExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class CreateUserStatsInput {
  @Field()
  public user: Types.ObjectId

  @Field({ nullable: true })
  public team: Types.ObjectId

  @Field({ nullable: true })
  public league: Types.ObjectId

  @Field((_type) => Int, { nullable: true })
  public gamesPlayed: number

  @Field((_type) => Int, { nullable: true })
  public assists: number

  @Field((_type) => Int, { nullable: true })
  public goals: number

  @Field((_type) => Int, { nullable: true })
  public wins: number

  @Field((_type) => Int, { nullable: true })
  public losses: number

  @Field((_type) => Int, { nullable: true })
  public ties: number

  @Field((_type) => Float, { nullable: true })
  public gaa: number

  @Field((_type) => Float, { nullable: true })
  public svp: number

  @Field((_type) => Int, { nullable: true })
  public shutouts: number

  @Field((_type) => Int, { nullable: true })
  public orderInSeason: number

  @Field()
  public season: string
}

// update userStats

@ObjectType()
export class UpdateUserStatsResponse {
  @Field(() => UserStats, { nullable: true })
  public userStats?: UserStats | null

  @Field((_type) => [UpdateUserStatsError], { nullable: true })
  public errors: Array<typeof UpdateUserStatsError>
}

const UpdateUserStatsError = createUnionType({
  name: 'UpdateUserStatsError',
  types: () => [UpdateUserStatsNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class UpdateUserStatsNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class UpdateUserStatsInput {
  @Field({ nullable: true })
  public user?: Types.ObjectId

  @Field({ nullable: true })
  public team?: Types.ObjectId

  @Field({ nullable: true })
  public league?: Types.ObjectId

  @Field((_type) => Int, { nullable: true })
  public gamesPlayed?: number

  @Field((_type) => Int, { nullable: true })
  public assists?: number

  @Field((_type) => Int, { nullable: true })
  public goals?: number

  @Field((_type) => Int, { nullable: true })
  public wins?: number

  @Field((_type) => Int, { nullable: true })
  public losses?: number

  @Field((_type) => Int, { nullable: true })
  public ties?: number

  @Field((_type) => Float, { nullable: true })
  public gaa?: number

  @Field((_type) => Float, { nullable: true })
  public svp?: number

  @Field((_type) => Int, { nullable: true })
  public shutouts?: number

  @Field((_type) => Int, { nullable: true })
  public orderInSeason?: number

  @Field({ nullable: true })
  public season?: string
}

// delete userStats

@ObjectType()
export class DeleteUserStatsResponse {
  @Field(() => UserStats, { nullable: true })
  public userStats?: UserStats

  @Field((_type) => [DeleteUserStatsError], { nullable: true })
  public errors: Array<typeof DeleteUserStatsError>
}

const DeleteUserStatsError = createUnionType({
  name: 'DeleteUserStatsError',
  types: () => [DeleteUserStatsNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class DeleteUserStatsNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

// userStats search

@InputType()
export class UserStatsFilter {
  @Field({ nullable: true })
  public user?: Types.ObjectId

  @Field({ nullable: true })
  public slug?: string
}

@ObjectType()
export class UserStatsEdge {
  @Field((_type) => UserStats, { nullable: false })
  public node: UserStats
}

@ObjectType()
export class UserStatsResponse {
  @Field((_type) => [UserStatsEdge], { nullable: false })
  public edges: Array<UserStatsEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

@InputType()
export class PlayersStatsFilter {
  @Field({ nullable: true })
  public teamId?: Types.ObjectId

  @Field({ nullable: false })
  public leagueId: Types.ObjectId

  @Field({ nullable: true })
  public season?: string

  @Field({ nullable: true })
  public stage?: string | 'all'

  @Field({ nullable: true })
  public position?: UserStatsPosition
}

@ObjectType()
export class PlayersStatsResponse {
  @Field((_type) => [UserStatsEdge], { nullable: false })
  public edges: Array<UserStatsEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo

  @Field((_type) => [LeagueStages], { nullable: true })
  public leagueStages?: LeagueStages[]
}
