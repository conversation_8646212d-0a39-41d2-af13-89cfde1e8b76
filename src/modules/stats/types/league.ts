import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { IUserError, PageInfo } from '../../../types/general'
import { AlternativeNameInput } from '../models/alternativeName'
import { League, LeagueGenderCategory, LeagueLevel, LeagueType } from '../models/league'

// create league

@ObjectType()
export class CreateLeagueResponse {
  @Field(() => League, { nullable: true })
  public league?: League

  @Field((_type) => [CreateLeagueError], { nullable: true })
  public errors: Array<typeof CreateLeagueError>
}

const CreateLeagueError = createUnionType({
  name: 'CreateLeagueError',
  types: () => [CreateLeagueAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class CreateLeagueAlreadyExistsError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class LeagueAlreadyExistsError extends IUserError implements IUserError {}

@InputType()
class TeamAutoConnectRulesInput {
  @Field()
  country: string

  @Field()
  shortName: string

  shortNameSlug: string

  @Field()
  shortNameInclude: boolean
}

@InputType()
export class CreateLeagueInput {
  @Field()
  public name: string

  @Field((_) => [String])
  public countries: string[]

  @Field(() => [AlternativeNameInput], { nullable: true })
  public alternativeNames?: AlternativeNameInput[]

  @Field({ nullable: true })
  public publicAlternativeName?: string

  @Field({ nullable: true })
  public website?: string

  @Field({ nullable: true })
  public instagramUrl?: string

  @Field({ nullable: true })
  public logo?: string

  @Field({ nullable: true })
  public facebookUrl?: string

  @Field({ nullable: true })
  public twitterUrl?: string

  @Field({ nullable: true })
  public tiktokUrl?: string

  @Field((_) => LeagueGenderCategory, { nullable: true })
  public genderCategory?: LeagueGenderCategory

  @Field((_type) => LeagueLevel, { nullable: true })
  public level?: LeagueLevel

  @Field({ nullable: true })
  public scraped: boolean

  @Field((_type) => LeagueType, { nullable: true })
  public type: LeagueType

  @Field({ nullable: true })
  public sourceUrl?: string

  @Field({ nullable: true })
  public eliteProspectSlug?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field({ nullable: true })
  public ignore?: boolean

  @Field(() => TeamAutoConnectRulesInput, { nullable: true })
  public teamAutoConnectRules?: TeamAutoConnectRulesInput
}

// update league

@ObjectType()
export class UpdateLeagueResponse {
  @Field(() => League, { nullable: true })
  public league?: League | null

  @Field((_type) => [UpdateLeagueError], { nullable: true })
  public errors: Array<typeof UpdateLeagueError>
}

const UpdateLeagueError = createUnionType({
  name: 'UpdateLeagueError',
  types: () => [UpdateLeagueNotFoundError, LeagueAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class UpdateLeagueNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class UpdateLeagueInput {
  @Field({ nullable: true })
  public name?: string

  @Field((_) => [String], { nullable: true })
  // @IsISO31661Alpha2(), we need to allow special values like INT for international leagues
  public countries?: string[]

  @Field(() => [AlternativeNameInput], { nullable: true })
  public alternativeNames?: AlternativeNameInput[]

  @Field({ nullable: true })
  public publicAlternativeName?: string

  @Field({ nullable: true })
  public website?: string

  @Field({ nullable: true })
  public instagramUrl?: string

  @Field({ nullable: true })
  public facebookUrl?: string

  @Field({ nullable: true })
  public logo?: string

  @Field({ nullable: true })
  public twitterUrl?: string

  @Field({ nullable: true })
  public tiktokUrl?: string

  @Field((_) => LeagueGenderCategory, { nullable: true })
  public genderCategory?: LeagueGenderCategory

  @Field((_type) => LeagueLevel, { nullable: true })
  public level?: LeagueLevel

  @Field({ nullable: true })
  public scraped?: boolean

  @Field((_type) => LeagueType, { nullable: true })
  public type?: LeagueType

  @Field({ nullable: true })
  public sourceUrl?: string

  @Field({ nullable: true })
  public eliteProspectSlug?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field(() => TeamAutoConnectRulesInput, { nullable: true })
  public teamAutoConnectRules?: TeamAutoConnectRulesInput

  @Field({ nullable: true })
  public ignore?: boolean
}

// delete league

@ObjectType()
export class DeleteLeagueResponse {
  @Field(() => League, { nullable: true })
  public league?: League

  @Field((_type) => [DeleteLeagueError], { nullable: true })
  public errors: Array<typeof DeleteLeagueError>
}

const DeleteLeagueError = createUnionType({
  name: 'DeleteLeagueError',
  types: () => [DeleteLeagueNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class DeleteLeagueNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

// leagues search

@InputType()
export class LeaguesFilter {
  @Field({ nullable: true })
  public searchQuery?: string
}

@ObjectType()
export class LeagueEdge {
  @Field((_type) => League, { nullable: false })
  public node: League
}

@ObjectType()
export class LeaguesResponse {
  @Field((_type) => [LeagueEdge], { nullable: false })
  public edges: Array<LeagueEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}
