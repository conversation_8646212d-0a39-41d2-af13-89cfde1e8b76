import { IsISO31661Alpha2, ValidateIf } from 'class-validator'
import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { FailedToCreatePresignedUrl, IUserError, PageInfo } from '../../../types/general'
import { AlternativeNameInput } from '../models/alternativeName'
import { StatusType } from '../models/league'
import { Organization } from '../models/organization'

// create Organization

@ObjectType()
export class CreateOrganizationResponse {
  @Field(() => Organization, { nullable: true })
  public organization?: Organization

  @Field((_type) => [CreateOrganizationError], { nullable: true })
  public errors: Array<typeof CreateOrganizationError>
}

const CreateOrganizationError = createUnionType({
  name: 'CreateOrganizationError',
  types: () => [CreateOrganizationAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class CreateOrganizationAlreadyExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class CreateOrganizationInput {
  @Field()
  public name: string

  @Field()
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country: string

  @Field({ nullable: true })
  public state?: string

  @Field({ nullable: true })
  public province?: string

  @Field(() => [AlternativeNameInput], { nullable: true })
  public alternativeNames?: AlternativeNameInput[]

  @Field({ nullable: true })
  public website?: string

  @Field({ nullable: true })
  public instagramUrl?: string

  @Field({ nullable: true })
  public facebookUrl?: string

  @Field({ nullable: true })
  public twitterUrl?: string

  @Field({ nullable: true })
  public tiktokUrl?: string

  @Field({ nullable: true })
  public arenaName?: string

  @Field({ nullable: true })
  public arenaAddress?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field({ nullable: true })
  public abbreviation?: string
}

// update Organization

@ObjectType()
export class UpdateOrganizationResponse {
  @Field(() => Organization, { nullable: true })
  public organization?: Organization | null

  @Field((_type) => [UpdateOrganizationError], { nullable: true })
  public errors: Array<typeof UpdateOrganizationError>
}

const UpdateOrganizationError = createUnionType({
  name: 'UpdateOrganizationError',
  types: () => [UpdateOrganizationNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class UpdateOrganizationNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class UpdateOrganizationInput {
  @Field({ nullable: true })
  public name?: string

  @Field({ nullable: true })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country?: string

  @Field({ nullable: true })
  public state?: string

  @Field({ nullable: true })
  public province?: string

  @Field(() => [AlternativeNameInput], { nullable: true })
  public alternativeNames?: AlternativeNameInput[]

  @Field({ nullable: true })
  public website?: string

  @Field({ nullable: true })
  public instagramUrl?: string

  @Field({ nullable: true })
  public facebookUrl?: string

  @Field({ nullable: true })
  public twitterUrl?: string

  @Field({ nullable: true })
  public tiktokUrl?: string

  @Field({ nullable: true })
  public arenaName?: string

  @Field({ nullable: true })
  public arenaAddress?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field({ nullable: true })
  public abbreviation?: string
}

// delete Organization

@ObjectType()
export class DeleteOrganizationResponse {
  @Field(() => Organization, { nullable: true })
  public organization?: Organization

  @Field((_type) => [DeleteOrganizationError], { nullable: true })
  public errors: Array<typeof DeleteOrganizationError>
}

const DeleteOrganizationError = createUnionType({
  name: 'DeleteOrganizationError',
  types: () => [DeleteOrganizationNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class DeleteOrganizationNotFoundError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

// Organizations search

@InputType()
export class OrganizationsFilter {
  @Field({ nullable: true })
  public searchQuery?: string

  @Field({ nullable: true })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country?: string

  @Field((_) => StatusType, { nullable: true })
  public checked?: StatusType

  @Field({ nullable: true, defaultValue: false })
  public withoutLogo?: boolean
}

@ObjectType()
export class OrganizationEdge {
  @Field((_type) => Organization, { nullable: false })
  public node: Organization
}

@ObjectType()
export class OrganizationsResponse {
  @Field((_type) => [OrganizationEdge], { nullable: false })
  public edges: Array<OrganizationEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

// logo upload

@InputType()
export class UploadOrganizationLogoInput {
  @Field({ nullable: false })
  public uploadSuccessful: boolean
}

@ObjectType()
export class UploadOrganizationLogoResponse {
  @Field({ nullable: true })
  public preSignedUrl: string

  @Field(() => Organization, { nullable: true })
  public organization?: Organization | null

  @Field((_type) => [UploadOrganizationLogoError], { nullable: true })
  public errors: Array<typeof UploadOrganizationLogoError>
}

const UploadOrganizationLogoError = createUnionType({
  name: 'UploadOrganizationLogoError',
  types: () => [FailedToCreatePresignedUrl] as const,
})
