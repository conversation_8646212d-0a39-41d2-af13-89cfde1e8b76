import { Field, ObjectType } from 'type-graphql'

import { League } from '../models/league'
import { LeagueStats } from '../models/leagueStats/leagueStats'

export type LeagueStatsGroup = {
  league: League
  groups: { group: string; standings: LeagueStats[] }[]
}

@ObjectType()
export class LeagueStandingsResponse {
  @Field(() => League)
  public league: League

  @Field(() => [LeagueStandingsGroup])
  public groups: Array<LeagueStandingsGroup>
}

@ObjectType()
export class LeagueStandingsGroup {
  @Field(() => String)
  public group: string

  @Field(() => [LeagueStats])
  public standings: Array<LeagueStats>
}
