import { Service } from 'typedi'

import { CrawlerDomainConfig, CrawlerEntity } from '../../admin/models/crawlerDomainConfig'
import { DiscoveredUrl } from '../../admin/models/discoveredUrl'
import { GameModel } from '../models/game/game'

import { BaseSyncer, IBaseSyncer, MAX_SCRAPES } from './baseSyncer'

import { DocumentType } from '@typegoose/typegoose'

@Service()
export default class GameSyncerService extends BaseSyncer implements IBaseSyncer {
  async syncData(url: DocumentType<DiscoveredUrl>): Promise<void> {
    const urlConfig = (url.crawlerDomainConfig as CrawlerDomainConfig)?.urlConfigs?.find(
      (urlConfig) => urlConfig.entity === CrawlerEntity.GAME
    )

    if (!urlConfig?.extractionScript) {
      this.logger.error(`No extraction script found for ${url.url}`)
      return
    }

    const result = await this.scraper.getGameStats(
      url.url,
      urlConfig.extractionScript,
      urlConfig.fetchType
    )

    if (!this.handleScrapeResult(url, result)) {
      await url.save()

      throw new Error('Failed to get game stats')
    }

    // At this point, we know result.data is not null
    const data = result.data!

    data.externalId = url.externalId ?? url.url

    let game = await GameModel.findOne({
      externalId: data.externalId,
    })

    if (game) {
      if (game.discoveredUrl.toString() !== url._id.toString()) {
        this.logger.error(
          `Game with externalId: ${data.externalId} already exists with different discoveredUrl. Old: ${game.discoveredUrl} New: ${url._id}`
        )
        return
      }

      game = await GameModel.findOneAndUpdate(
        {
          externalId: data.externalId,
        },
        data,
        { new: true }
      )
    } else {
      data.discoveredUrl = url._id

      game = await GameModel.create(data)
    }

    // sync entities, we do this for both create/update as we might have new match in the future
    const changed = await game?.syncEntities()
    if (changed) {
      await game?.save()
    }

    this.initScraperInfo(url)

    let scheduledForScrapeAt: Date | null = null

    if (
      data.date.getTime() > new Date().getTime() + 4 * 60 * 60 * 1000 ||
      (data.stats?.homeTeam?.score === undefined &&
        data.stats?.awayTeam?.score === undefined &&
        (url.scraperInfo?.scrapeAttempts ?? 0) < MAX_SCRAPES)
    ) {
      scheduledForScrapeAt = new Date(
        data.date.getTime() + (url.scraperInfo?.scrapeAttempts ?? 1) * 4 * 60 * 60 * 1000
      )
    }

    if ((url.scraperInfo?.scrapeAttempts ?? 0) >= MAX_SCRAPES) {
      scheduledForScrapeAt = null
    }

    if (
      data.events.length > 0 ||
      data.awayTeamRoster.length > 0 ||
      data.homeTeamRoster.length > 0
    ) {
      this.markScrapeAsSuccessful(url, null)
    } else if ((url.scraperInfo?.scrapeAttempts ?? 0) >= MAX_SCRAPES) {
      this.markScrapeAsUnsuccessful(url, null)
    } else {
      this.markScrapeAsSuccessful(url, scheduledForScrapeAt)
    }

    await url.save()
  }
}
