import pino from 'pino'
import Container from 'typedi'

import { DiscoveredUrl, DiscoveredUrlScraperInfo } from '../../admin/models/discoveredUrl'

import { Scraper } from './scraper/scraper'

import { DocumentType } from '@typegoose/typegoose'

export interface IBaseSyncer {
  syncData(url: DocumentType<DiscoveredUrl>): Promise<void>
}

export interface ScraperResult<T> {
  data: T | null
  httpStatus?: number
  errorMessage?: string
}

/**
 * HTTP status codes that should be retried
 * These are typically temporary errors like timeouts, rate limits, and server errors
 */
const RETRYABLE_HTTP_STATUS_CODES = [408, 429, 500, 502, 503, 504]

/**
 * Default delay in milliseconds for retrying a scrape (24 hours)
 */
const DEFAULT_RETRY_DELAY_MS = 24 * 60 * 60 * 1000

/**
 * Maximum number of unsuccessful scrape attempts before giving up
 */
const MAX_UNSUCCESSFUL_SCRAPES = 8

/**
 * Maximum number of scrape attempts in total
 */
export const MAX_SCRAPES = 8

export abstract class BaseSyncer {
  protected readonly scraper: Scraper
  protected readonly logger: pino.Logger

  constructor() {
    this.logger = Container.get('app.logger')
    this.scraper = Container.get('scraper')
  }

  /**
   * Initialize scraperInfo if it doesn't exist
   */
  protected initScraperInfo(url: DocumentType<DiscoveredUrl>): void {
    if (!url.scraperInfo) {
      url.scraperInfo = new DiscoveredUrlScraperInfo()
    }
  }

  /**
   * Determines if an HTTP status code is retryable
   * @param httpStatus - The HTTP status code to check
   * @returns true if the status code is retryable, false otherwise
   */
  protected isRetryableHttpStatus(httpStatus?: number): boolean {
    if (!httpStatus) {
      return false
    }

    return RETRYABLE_HTTP_STATUS_CODES.includes(httpStatus)
  }

  /**
   * Update common scraperInfo fields with result data
   */
  protected updateScraperInfoWithResult<T>(
    url: DocumentType<DiscoveredUrl>,
    result: ScraperResult<T>
  ): void {
    this.initScraperInfo(url)

    // After initScraperInfo, we can be sure that scraperInfo exists
    url.scraperInfo!.lastScrapedAt = new Date()
    url.scraperInfo!.scrapeAttempts = (url.scraperInfo!.scrapeAttempts ?? 0) + 1
    url.scraperInfo!.httpStatus = result.httpStatus
    url.scraperInfo!.errorMessage = result.errorMessage
  }

  /**
   * Mark scrape as unsuccessful
   */
  protected markScrapeAsUnsuccessful(
    url: DocumentType<DiscoveredUrl>,
    scheduledForScrapeAt: Date | null = null
  ): void {
    this.initScraperInfo(url)

    // After initScraperInfo, we can be sure that scraperInfo exists
    url.scraperInfo!.scrapeSuccessful = false
    url.scraperInfo!.unsuccessfulScrapes = (url.scraperInfo!.unsuccessfulScrapes ?? 0) + 1
    url.scraperInfo!.scheduledForScrapeAt = scheduledForScrapeAt
  }

  /**
   * Mark scrape as successful
   */
  protected markScrapeAsSuccessful(
    url: DocumentType<DiscoveredUrl>,
    scheduledForScrapeAt: Date | null = null
  ): void {
    this.initScraperInfo(url)

    // After initScraperInfo, we can be sure that scraperInfo exists
    url.scraperInfo!.scrapeSuccessful = true
    url.scraperInfo!.scheduledForScrapeAt = scheduledForScrapeAt
  }

  /**
   * Schedule next scrape
   */
  protected scheduleNextScrape(url: DocumentType<DiscoveredUrl>, delayInMs: number): void {
    this.initScraperInfo(url)

    // After initScraperInfo, we can be sure that scraperInfo exists
    url.scraperInfo!.scheduledForScrapeAt = new Date(Date.now() + delayInMs)
  }

  /**
   * Handle scrape result with retryable error logic
   * If the result has a retryable HTTP status code, it will schedule a retry
   * @param url - The URL being scraped
   * @param result - The scraper result
   * @returns true if the scrape should continue, false if it should stop
   */
  protected handleScrapeResult<T>(
    url: DocumentType<DiscoveredUrl>,
    result: ScraperResult<T>
  ): boolean {
    // Update scraper info to increment scrapeAttempts
    this.updateScraperInfoWithResult(url, result)

    if (!result.data || (Array.isArray(result.data) && result.data.length === 0)) {
      if (
        this.isRetryableHttpStatus(result.httpStatus) &&
        (url.scraperInfo?.unsuccessfulScrapes ?? 1) < MAX_UNSUCCESSFUL_SCRAPES
      ) {
        // Retryable HTTP status, schedule a retry
        this.markScrapeAsUnsuccessful(url, new Date(Date.now() + DEFAULT_RETRY_DELAY_MS))
      } else {
        // Non-retryable error, don't schedule a retry
        this.markScrapeAsUnsuccessful(url, null)
      }

      return false
    }

    return true
  }
}
