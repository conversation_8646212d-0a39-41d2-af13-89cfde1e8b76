import { Service } from 'typedi'

import { CrawlerDomainConfig, CrawlerEntity } from '../../admin/models/crawlerDomainConfig'
import { DiscoveredUrl } from '../../admin/models/discoveredUrl'
import { isCurrentOrFutureSeason } from '../helpers/general'
import {
  generateLeagueStatsKey,
  LeagueStats,
  LeagueStatsModel,
} from '../models/leagueStats/leagueStats'

import { BaseSyncer, IBaseSyncer } from './baseSyncer'

import { DocumentType } from '@typegoose/typegoose'

@Service()
export default class LeagueStatsSyncerService extends BaseSyncer implements IBaseSyncer {
  async syncData(url: DocumentType<DiscoveredUrl>): Promise<void> {
    const urlConfig = (url.crawlerDomainConfig as CrawlerDomainConfig)?.urlConfigs?.find(
      (urlConfig) => urlConfig.entity === CrawlerEntity.LEAGUE
    )

    if (!urlConfig?.extractionScript) {
      this.logger.error(`No extraction script found for ${url.url}`)
      return
    }

    const result = await this.scraper.getLeagueStats(
      url.url,
      urlConfig.extractionScript,
      urlConfig.fetchType
    )

    if (!this.handleScrapeResult(url, result)) {
      await url.save()

      throw new Error('Failed to get league stats')
    }

    // At this point, we know result.data is not null and not empty
    const stats = result.data!

    for (const leagueStat of stats) {
      const data: LeagueStats = leagueStat as LeagueStats

      data.key = generateLeagueStatsKey(data)
      let leagueStats = await LeagueStatsModel.findOne({
        key: data.key,
      })

      if (leagueStats) {
        await LeagueStatsModel.updateOne(
          {
            externalId: data.externalId,
            discoveredUrl: url._id,
            key: data.key,
          },
          data
        )
      } else {
        data.discoveredUrl = url._id

        leagueStats = await LeagueStatsModel.create(data)
      }

      const changed = await leagueStats?.syncEntities()
      if (changed) {
        await leagueStats?.save()
      }
    }

    // rescrape every day if the stats are from current season
    if (isCurrentOrFutureSeason(stats[0].season)) {
      this.markScrapeAsSuccessful(url, new Date(Date.now() + 24 * 60 * 60 * 1000))
    } else {
      this.markScrapeAsSuccessful(url)
    }

    await url.save()
  }
}
