import pino from 'pino'
import Container, { Service } from 'typedi'

import { Fetcher } from '../../../../services/webcrawler/fetchers/fetcher'
import { FetchType } from '../../../admin/models/crawlerDomainConfig'
import { Game, GameExternalInfo, GameOverallStats } from '../../models/game/game'
import { GameEvent, GameEventType } from '../../models/game/gameEvent'
import {
  RosterPlayer,
  RosterPlayerExternalInfo,
  RosterPosition,
} from '../../models/game/rosterPlayer'
import { LeagueStats } from '../../models/leagueStats/leagueStats'
import { UserInfo } from '../../models/userInfo'
import { SeasonType, StatsSource, UserStats } from '../../models/userStats'

import {
  GameStatsResponseFormat,
  LeagueStatsResponseFormat,
  rosterFormat,
  UserInfoResponseFormat,
  UserStatsResponseFormat,
} from './response'

type ScrapedLeague = Omit<
  LeagueStats,
  'discoveredUrl' | 'externalId' | '_id' | 'syncEntities' | 'key' | 'generateKey'
>

// TODO: Refactor that every crawler entity has its own scraper
@Service()
export class Scraper {
  private readonly logger: pino.Logger
  private readonly fetcher

  constructor() {
    this.logger = Container.get('app.logger')
    this.fetcher = new Fetcher()
  }

  public async getUserStats(
    url: string,
    extractionScript: string,
    fetchType?: FetchType
  ): Promise<{ data: UserStats[]; httpStatus?: number; errorMessage?: string }> {
    const type = UserStatsResponseFormat._type

    let scrapedStats
    try {
      const result = await this.fetcher.fetchAndExtract<typeof type>(url, extractionScript, {
        type: fetchType || FetchType.Puppeteer,
        timeout: 15_000,
      })

      try {
        scrapedStats = UserStatsResponseFormat.parse(result.data)
      } catch (err) {
        this.logger.error(`Failed to parse user stats: ${url}`, err)
        return {
          data: [],
          httpStatus: result.httpStatus,
          errorMessage: `Failed to parse data: ${err.message}`,
        }
      }

      if (!scrapedStats) {
        return {
          data: [],
          httpStatus: result.httpStatus,
          errorMessage: 'No data returned from extraction script',
        }
      }

      const mappedStats = scrapedStats.userStats.map((statData) => {
        const userStats = new UserStats()
        userStats.source = StatsSource.SCRAPER

        const formatSeason = (season: string) => {
          if (season.length === 4) {
            return `${Number(season) - 1}-${season}`
          }

          return season
        }

        const season = formatSeason(statData.season)
        userStats.season = season
        userStats.seasonType =
          statData.seasonType === 'REGULAR' ? SeasonType.REGULAR : SeasonType.TOURNAMENT

        userStats.externalId = statData.externalId
        userStats.userExternalId = statData.userExternalId
        userStats.externalInfo = {
          externalTeamName: statData.team,
          externalTeamCountry: statData.teamCountry,
          externalLeagueName: statData.league,
          externalLeagueCountry: statData.leagueCountry,
          externalPlayerName: statData.externalPlayerName,
          stage: statData.stage,
        }

        userStats.gamesPlayed = statData.gamesPlayed
        userStats.goals = statData.goals
        userStats.assists = statData.assists
        userStats.points = statData.points
        userStats.pim = statData.pim
        userStats.plusMinus = statData.plusMinus

        userStats.wins = statData.wins
        userStats.losses = statData.losses
        userStats.ties = statData.ties
        userStats.gaa = statData.gaa
        userStats.svp = statData.svp
        userStats.ga = statData.ga
        userStats.toi = statData.toi
        userStats.saves = statData.saves
        userStats.shutouts = statData.shutouts

        userStats.postSeasonStats = {
          gamesPlayed: statData.postSeasonStats?.gamesPlayed,
          goals: statData.postSeasonStats?.goals,
          assists: statData.postSeasonStats?.assists,
          pim: statData.postSeasonStats?.pim,
          plusMinus: statData.postSeasonStats?.plusMinus,
          wins: statData.postSeasonStats?.wins,
          losses: statData.postSeasonStats?.losses,
          ties: statData.postSeasonStats?.ties,
          gaa: statData.postSeasonStats?.gaa,
          svp: statData.postSeasonStats?.svp,
          shutouts: statData.postSeasonStats?.shutouts,
        }

        userStats.position = statData.position
        userStats.total = statData.total

        return userStats
      })

      return {
        data: mappedStats,
        httpStatus: result.httpStatus,
        errorMessage: result.errorMessage,
      }
    } catch (err) {
      this.logger.error(`Failed to get user stats: ${url}`, err)
      return {
        data: [],
        httpStatus: err.status || 500,
        errorMessage: err.message,
      }
    }
  }

  public async getGameStats(
    url: string,
    extractionScript: string,
    fetchType?: FetchType
  ): Promise<{ data: Game | null; httpStatus?: number; errorMessage?: string }> {
    const type = GameStatsResponseFormat._type

    let result
    try {
      const fetchResult = await this.fetcher.fetchAndExtract<typeof type>(url, extractionScript, {
        type: fetchType || FetchType.Puppeteer,
        timeout: 15_000,
      })

      try {
        result = GameStatsResponseFormat.parse(fetchResult.data)
      } catch (err) {
        this.logger.error(`Failed to parse game stats: ${url}`, err)
        return {
          data: null,
          httpStatus: fetchResult.httpStatus,
          errorMessage: `Failed to parse data: ${err.message}`,
        }
      }

      if (!result) {
        return {
          data: null,
          httpStatus: fetchResult.httpStatus,
          errorMessage: 'No data returned from extraction script',
        }
      }

      const data = result.gameStats

      // map data to game model
      const gameStats = new Game()

      gameStats.date = new Date(data.date)
      gameStats.season = data.season

      gameStats.externalInfo = new GameExternalInfo()
      gameStats.externalInfo.homeTeamName = data.homeTeamName
      gameStats.externalInfo.awayTeamName = data.awayTeamName
      gameStats.externalInfo.leagueName = data.leagueName

      gameStats.stats = new GameOverallStats()
      gameStats.stats.homeTeam = data.homeTeamStats
      gameStats.stats.awayTeam = data.awayTeamStats
      gameStats.stats.periodScores = data.periodScores
      gameStats.gameResult = data.gameResult
      gameStats.stats.scores = data.scores?.map((score) => ({
        homeTeam: score.homeTeam,
        awayTeam: score.awayTeam,
        type: score.type,
      }))

      gameStats.events = data.events?.map((event) => {
        const gameEvent = new GameEvent()
        gameEvent.type = event.type || GameEventType.Unknown
        gameEvent.isHomeTeam = event.isHomeTeam
        gameEvent.time = event.time
        gameEvent.score = event.score
        gameEvent.penaltyTime = event.penaltyTime
        gameEvent.description = event.description
        gameEvent.descriptionCode = event.descriptionCode

        gameEvent.externalInfo = {
          teamName: event.teamName,
          playerName: event.playerName,
          playerExternalId: event.playerExternalId,
        }

        if (event.assists) {
          gameEvent.assists = event.assists.map((assist) => {
            const assistInfo = new RosterPlayerExternalInfo()
            assistInfo.playerName = assist.playerName
            assistInfo.playerExternalId = assist.playerExternalId
            return assistInfo
          })
        }

        gameEvent.player = null

        return gameEvent
      })

      const mapRosterPlayer = (player: typeof rosterFormat._type): RosterPlayer => {
        const rosterPlayer = new RosterPlayer()

        rosterPlayer.externalInfo = new RosterPlayerExternalInfo()
        rosterPlayer.externalInfo.playerName = player.playerName
        rosterPlayer.externalInfo.playerExternalId = player.playerExternalId

        rosterPlayer.player = null
        rosterPlayer.goals = player.goals
        rosterPlayer.assists = player.assists
        rosterPlayer.points = player.points
        rosterPlayer.saves = player.saves
        rosterPlayer.goalsAgainst = player.goalsAgainst
        rosterPlayer.svp = player.svp
        rosterPlayer.pim = player.pim
        rosterPlayer.plusMinus = player.plusMinus
        rosterPlayer.jerseyNumber = player.jerseyNumber
        rosterPlayer.position = player.position || RosterPosition.Unknown

        return rosterPlayer
      }

      gameStats.homeTeamRoster = data.homeTeamRoster.map(mapRosterPlayer)
      gameStats.awayTeamRoster = data.awayTeamRoster.map(mapRosterPlayer)

      return {
        data: gameStats,
        httpStatus: fetchResult.httpStatus,
        errorMessage: fetchResult.errorMessage,
      }
    } catch (err) {
      this.logger.error(`Failed to get game stats: ${url}`, err)
      return {
        data: null,
        httpStatus: err.status || 500,
        errorMessage: err.message,
      }
    }
  }

  public async getUserInfo(
    url: string,
    extractionScript: string,
    fetchType?: FetchType
  ): Promise<{ data: UserInfo | null; httpStatus?: number; errorMessage?: string }> {
    const type = UserInfoResponseFormat._type

    let result
    try {
      const fetchResult = await this.fetcher.fetchAndExtract<typeof type>(url, extractionScript, {
        type: fetchType || FetchType.Puppeteer,
        timeout: 15_000,
      })

      try {
        result = UserInfoResponseFormat.parse(fetchResult.data)
      } catch (err) {
        this.logger.error(`Failed to parse user info: ${url}`, err)
        return {
          data: null,
          httpStatus: fetchResult.httpStatus,
          errorMessage: `Failed to parse data: ${err.message}`,
        }
      }

      if (!result?.userInfo) {
        return {
          data: null,
          httpStatus: fetchResult.httpStatus,
          errorMessage: 'No data returned from extraction script',
        }
      }

      const data = result.userInfo
      const userInfo = new UserInfo()

      userInfo.name = data.name
      userInfo.country = data.country
      userInfo.age = data.age
      userInfo.possibleYearsOfBirth = userInfo.getPossibleYearsOfBirth(data.yearOfBirth)
      userInfo.dateOfBirth = data.dateOfBirth ? new Date(data.dateOfBirth) : undefined

      return {
        data: userInfo,
        httpStatus: fetchResult.httpStatus,
        errorMessage: fetchResult.errorMessage,
      }
    } catch (err) {
      this.logger.error(`Failed to get user info: ${url}`, err)
      return {
        data: null,
        httpStatus: err.status || 500,
        errorMessage: err.message,
      }
    }
  }

  public async getLeagueStats(
    url: string,
    extractionScript: string,
    fetchType?: FetchType
  ): Promise<{ data: ScrapedLeague[] | null; httpStatus?: number; errorMessage?: string }> {
    const type = LeagueStatsResponseFormat._type

    let result
    try {
      const fetchResult = await this.fetcher.fetchAndExtract<typeof type>(url, extractionScript, {
        type: fetchType || FetchType.Puppeteer,
        timeout: 15_000,
      })

      try {
        result = LeagueStatsResponseFormat.parse(fetchResult.data)
      } catch (err) {
        this.logger.error(`Failed to parse league stats: ${url}`, err)
        return {
          data: null,
          httpStatus: fetchResult.httpStatus,
          errorMessage: `Failed to parse data: ${err.message}`,
        }
      }

      if (!result?.leagueStats || result.leagueStats.length === 0) {
        return {
          data: null,
          httpStatus: fetchResult.httpStatus,
          errorMessage: 'No data returned from extraction script',
        }
      }

      return {
        data: result.leagueStats,
        httpStatus: fetchResult.httpStatus,
        errorMessage: fetchResult.errorMessage,
      }
    } catch (err) {
      this.logger.error(`Failed to get league stats: ${url}`, err)
      return {
        data: null,
        httpStatus: err.status || 500,
        errorMessage: err.message,
      }
    }
  }
}
