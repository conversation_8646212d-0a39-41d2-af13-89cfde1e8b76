import { z } from 'zod'

import { GameResultType, GameScoreType } from '../../models/game/game'
import { GameEventType } from '../../models/game/gameEvent'
import { RosterPosition } from '../../models/game/rosterPlayer'
import { UserStatsPosition } from '../../models/userStats'

export const UserStatsResponseFormat = z.object({
  userStats: z
    .object({
      externalId: z.string().optional(),
      userExternalId: z.string().optional(),
      externalPlayerName: z.string().optional(),
      team: z.string(),
      teamCountry: z.string().optional(),
      league: z.string(),
      leagueCountry: z.string().optional(),
      stage: z.object({ value: z.string().optional(), label: z.string().optional() }).optional(),
      seasonType: z.enum(['REGULAR', 'TOURNAMENT']).optional(),
      position: z
        .enum([UserStatsPosition.Goalie, UserStatsPosition.Skater, UserStatsPosition.Unknown])
        .optional(),
      season: z.string(),
      gamesPlayed: z.number().optional(),
      assists: z.number().optional(),
      goals: z.number().optional(),
      points: z.number().optional(),
      pim: z.number().optional(),
      plusMinus: z.number().optional(),
      wins: z.number().optional(),
      losses: z.number().optional(),
      ties: z.number().optional(),
      gaa: z.number().optional(),
      svp: z.number().optional(),
      ga: z.number().optional(),
      toi: z.string().optional(),
      saves: z.number().optional(),
      shutouts: z.number().optional(),
      orderInSeason: z.number().optional(),
      postSeasonStats: z
        .object({
          gamesPlayed: z.number().optional(),
          assists: z.number().optional(),
          goals: z.number().optional(),
          pim: z.number().optional(),
          plusMinus: z.number().optional(),
          wins: z.number().optional(),
          losses: z.number().optional(),
          ties: z.number().optional(),
          gaa: z.number().optional(),
          svp: z.number().optional(),
          shutouts: z.number().optional(),
        })
        .optional(),
      total: z
        .boolean({
          description:
            'Set to false if this is not total sum row (player played for more teams in league)',
        })
        .optional(),
    })
    .array(),
})

const playerExternalIdDescription = {
  description:
    'This is usually hidden in link href value, extract only numeric id (like https://www.domain/player/1234)',
}

const teamNameDescription = {
  description:
    'This will be usually shortname, but full name will be on the page, so include full name',
}

const penaltyTimeDescription = {
  description: 'Time in format like 2 (whole minutes)',
}

const gameDescription = {
  description: `Description of the game, for Penalty it is description of event "Boarding, Clipping, etc",
    for Goal it is player state or event like Penalty shot "4/5, PS, etc"`,
}

export const rosterFormat = z.object({
  playerName: z.string(),
  playerExternalId: z.string(playerExternalIdDescription).optional(),

  goals: z.number().optional(),
  assists: z.number().optional(),
  points: z.number().optional(),
  pim: z.number().optional(),
  plusMinus: z.number().optional(),
  saves: z.number().optional(),
  goalsAgainst: z.number().optional(),
  svp: z.number().optional(),
  jerseyNumber: z.number().optional(),
  position: z.nativeEnum(RosterPosition).optional(),
  teamName: z.string(teamNameDescription).optional(),
})
const gameEventFormat = z.object({
  type: z.nativeEnum(GameEventType).optional(),
  isHomeTeam: z.boolean().optional(),
  time: z.string({ description: 'Time in format like 12:34' }),
  score: z.string().optional(),
  penaltyTime: z.string(penaltyTimeDescription).optional(),
  playerName: z.string().optional(),
  playerExternalId: z.string(playerExternalIdDescription).optional(),
  teamName: z.string(teamNameDescription),
  description: z.string(gameDescription).optional(),
  descriptionCode: z.string().optional(),
  assists: z.array(
    z.object({
      playerName: z.string(),
      playerExternalId: z.string(playerExternalIdDescription).optional(),
    })
  ),
})

const gameTeamStatsFormat = z.object({
  score: z.number().optional(),
  shotsOnGoal: z.number().optional(),
  blockedShots: z.number().optional(),
  faceoffs: z.number().optional(),
  powerPlayGoals: z.number().optional(),
  shortHandedGoals: z.number().optional(),
  penalties: z.number().optional(),
  majorPenalties: z.number().optional(),
  penaltyMinutes: z.number().optional(),
  goalieSaves: z.number().optional(),
})

export const GameStatsResponseFormat = z.object({
  gameStats: z.object({
    date: z.string({ description: 'Full date with time in ISO UTC format' }),
    season: z.string().optional(),
    homeTeamName: z.string(),
    awayTeamName: z.string(),
    leagueName: z.string(),

    homeTeamRoster: z.array(rosterFormat),
    awayTeamRoster: z.array(rosterFormat),

    homeTeamStats: gameTeamStatsFormat.optional(),
    awayTeamStats: gameTeamStatsFormat.optional(),

    events: z.array(gameEventFormat),
    gameResult: z.nativeEnum(GameResultType).optional(),

    periodScores: z
      .string({
        description: 'Comma separated list of goals for each period, like: 1:0, 2:1, 3:2',
      })
      .optional(),
    scores: z
      .object({
        homeTeam: z.number(),
        awayTeam: z.number(),
        type: z.nativeEnum(GameScoreType).optional(),
      })
      .array(),
  }),
})

export const UserInfoResponseFormat = z.object({
  userInfo: z
    .object({
      id: z.string(),
      name: z.string(),
      yearOfBirth: z.number().optional(),
      dateOfBirth: z.number().optional(),
      age: z.number().optional(),
      country: z.string(),
    })
    .nullable(),
})

export const LeagueStatsResponseFormat = z.object({
  leagueStats: z.array(
    z.object({
      externalInfo: z.object({
        leagueName: z.string(),
        teamName: z.string(),
      }),
      externalId: z.string({
        description: 'Team id from external source',
      }),
      season: z.string(),
      group: z.string(),
      standing: z.number(),
      games: z.number(),
      wins: z.number(),
      overtimeWins: z.number(),
      losses: z.number(),
      overtimeLosses: z.number(),
      ties: z.number(),
      goalsFor: z.number(),
      goalsAgainst: z.number(),
      points: z.number(),
    })
  ),
})
