export const playersQuery = () => {
  return `
 query Player($firstName: String, $lastName: String, $yearOfBirth: String, $nationality: String) {
    players(firstName: $firstName, lastName: $lastName, yearOfBirth: $yearOfBirth, nationality: $nationality) {
      edges {
        height {
          imperial
          metrics
        }
        weight {
          metrics
          imperial
        }
        links {
          eliteprospectsUrl
        }
        shoots
        position
      }
    }
  }
  `
}

export const playerQuery = () => {
  return `
          query Player($id: ID!) {
            player(id: $id) {
              id
              views
              firstName
              lastName
              position
              name
              dateOfBirth
              yearOfBirth
              nationality {                
                iso_3166_1_alpha_2
              }
              height {
                metrics
                imperial
              }
              weight {
                metrics
                imperial
              }
              shoots
              links {
                eliteprospectsUrl
              }
            }
          }
        `
}

export type EPPlayer = {
  id: number
  firstName?: string
  lastName?: string
  name?: string
  yearOfBirth?: number
  dateOfBirth?: string
  imageUrl?: string
  playerType?: 'GOALTENDER' | 'SKATER'
  nationality?: {
    iso_3166_1_alpha_2: string
  }
  position?: 'G' | 'D' | 'F'

  height: {
    imperial: string
    metrics: number
  }
  weight: {
    metrics: number
    imperial: number
  }
  links: {
    eliteprospectsUrl: string
  }
  shoots: string
  views?: number
}

export const playerStatsQuery = (playerId: string) => {
  return `
  query playerStats {
    playerStats (player: "${playerId}", sort: "season") {
      edges {
        id
        leagueName
        leagueType
        postseasonType
        league {
            slug
            name
            country {
                iso_3166_1_alpha_2
            }
        }
        team {
            slug
            name
            country {
                iso_3166_1_alpha_2
            }
            logo {
                large
            }
        }
        season {
            slug
            startYear
            endYear
        }
        regularStats {
            GP
            G
            A
            PIM
            PM

            W
            L
            T
            GAA
            SVP
            SO
        }
        postseasonStats {
          GP
          G
          A
          PIM
          PM

          W
          L
          T
          GAA
          SVP
          SO
        }
      }
    }
  }
  `
}

export type EPPlayerStats = {
  id: string
  leagueName: string
  leagueType: string
  postseasonType: string
  league: {
    slug: string
    name: string
    country: {
      iso_3166_1_alpha_2: string
    }
  }
  team: {
    slug: string
    name: string
    country: {
      iso_3166_1_alpha_2: string
    }
    logo: {
      large: string
    }
  }
  season: {
    slug: string
    startYear: number
    endYear: number
  }
  regularStats?: {
    GP: number
    G: number
    A: number
    PIM: number | null
    PM: number | null

    W: number | null
    L: number | null
    T: number | null
    GAA: number | null
    SVP: number | null
    SO: number | null
  }
  postseasonStats?: {
    GP: number
    G: number
    A: number
    PIM: number
    PM: number

    W: number
    L: number
    T: number
    GAA: number
    SVP: number
    SO: number
  }
}
