import axios from 'axios'
import { plainToClass } from 'class-transformer'
import config from 'config'
import pino from 'pino'
import Container, { Service } from 'typedi'

import { resolveSeasonType } from '../../../../helpers/ep'
import { StatsSource, UserStats, UserStatsModel } from '../../models/userStats'
import { EliteProspectsSeason, EliteProspectTeam } from '../../types/ep'
import { IStatsSource } from '../../types/sources'

import { EPPlayer, EPPlayerStats, playerQuery, playersQuery, playerStatsQuery } from './graphql'

@Service()
export default class EliteProspectsService implements IStatsSource {
  private logger: pino.Logger

  constructor() {
    this.logger = Container.get('app.logger')
  }

  async getAllSeasons(playerId: string): Promise<EliteProspectsSeason[]> {
    const userStats = await axios.post<{ data: { playerStats: { edges: EPPlayerStats[] } } }>(
      'https://gql.eliteprospects.com',
      {
        query: playerStatsQuery(playerId),
        variables: {
          playerId,
        },
      }
    )

    if (!userStats.data.data.playerStats?.edges) {
      this.logger.error('Failed to get EP data for player with id: ' + playerId)
      return []
    }

    return userStats.data.data.playerStats.edges.map((edge) =>
      plainToClass(EliteProspectsSeason, edge)
    )
  }

  async getEPPlayer(user: {
    yearOfBirth: number
    firstname: string
    lastname: string
    country: string
  }): Promise<EPPlayer | null> {
    const response = await axios.post<{
      data: { players: { edges: EPPlayer[] } }
    }>('https://gql.eliteprospects.com', {
      query: playersQuery(),
      variables: {
        firstName: user.firstname,
        lastName: user.lastname,
        yearOfBirth: user.yearOfBirth.toString(),
        nationality: user.country,
      },
    })

    const players = response.data.data.players.edges

    if (players && players.length > 0) {
      const player = players[0]
      return player
    }

    return null
  }

  async getUserData(user: {
    possibleYearsOfBirth: number[]
    firstname: string
    lastname: string
    country: string
  }): Promise<EPPlayer | null> {
    if (user && user.possibleYearsOfBirth.length > 0) {
      try {
        const player = await this.getEPPlayer({
          ...user,
          yearOfBirth: user.possibleYearsOfBirth[0],
        })

        if (!player && user.possibleYearsOfBirth.length > 1) {
          const playerWithSecondYearOfBirth = await this.getEPPlayer({
            ...user,
            yearOfBirth: user.possibleYearsOfBirth[1],
          })

          return playerWithSecondYearOfBirth
        } else {
          return player
        }
      } catch (err) {
        this.logger.error('Failed to get EP data', err)
        return null
      }
    }

    return null
  }

  async getUserStats(input: { eliteProspectId: string }): Promise<UserStats[]> {
    const seasons = (await this.getAllSeasons(input.eliteProspectId)).reverse()

    const userStats: UserStats[] = []

    for (const season of seasons) {
      const userSeasonStat = new UserStatsModel({
        source: StatsSource.EP,
        gamesPlayed: season.regularStats?.GP,
        goals: season.regularStats?.G,
        assists: season.regularStats?.A,
        pim: season.regularStats?.PIM,
        plusMinus: season.regularStats?.PM,

        wins: season.regularStats?.W,
        losses: season.regularStats?.L,
        ties: season.regularStats?.T,
        gaa: season.regularStats?.GAA,
        svp: season.regularStats?.SVP,
        shutouts: season.regularStats?.SO,

        season: `${season.season?.startYear}-${season.season?.endYear}`,
        seasonType: resolveSeasonType(season),
        orderInSeason: 0,
        postSeasonStats: season.postseasonStats
          ? {
              gamesPlayed: season.postseasonStats.GP,
              goals: season.postseasonStats.G,
              assists: season.postseasonStats.A,
              pim: season.postseasonStats.PIM,
              plusMinus: season.postseasonStats.PM,

              wins: season.postseasonStats?.W,
              losses: season.postseasonStats?.L,
              ties: season.postseasonStats?.T,
              gaa: season.postseasonStats?.GAA,
              svp: season.postseasonStats?.SVP,
              shutouts: season.postseasonStats?.SO,
            }
          : undefined,

        externalId: season.id,
        userExternalId: input.eliteProspectId,
        externalInfo: {
          externalTeamName: season.team?.name,
          externalTeamCountry: season.team?.country?.iso_3166_1_alpha_2,
          externalLeagueName: season.league?.name,
          externalLeagueCountry: season.league?.country?.iso_3166_1_alpha_2,
        },
      })

      userStats.push(userSeasonStat)
    }

    return userStats
  }

  async getTeam(teamId: string): Promise<EliteProspectTeam | null> {
    const response = await axios.get<{ data: EliteProspectTeam }>(
      'https://api.eliteprospects.com/v1/teams/' +
        teamId +
        '?&fields=id,name,slug,logo.*,country.*&apiKey=' +
        config.ep.apiKey
    )

    const team = response.data.data

    if (team) {
      return team
    } else {
      this.logger.warn(`Failed to find match for team with id: ${teamId}`)
    }

    return null
  }

  async searchTeams(search: string): Promise<EliteProspectTeam[] | null> {
    try {
      const response = await axios.get<{ data: EliteProspectTeam[] }>(
        'https://api.eliteprospects.com/v1/teams?q=' +
          search +
          '&fields=id,name,slug,logo.*,country.*&offset=0&limit=20&apiKey=' +
          config.ep.apiKey
      )

      const teams = response.data.data
      if (teams) {
        return teams
      } else {
        this.logger.warn(`Failed to find match for team with name: ${search}`)
      }
    } catch (err) {
      this.logger.warn('Failed to get EP data', err)
      throw err
    }

    return null
  }

  async getAgencyClients(agencyId: number): Promise<EPPlayer[]> {
    let offset = 0
    let hasNextPage = true
    let clients: EPPlayer[] = []

    // just putting some safe limit here
    while (hasNextPage && offset < 1000) {
      const { data, hasNextPage: nextPage } = await this.getAgencyClientsPaged(agencyId, offset)
      clients = clients.concat(data)
      hasNextPage = nextPage
      offset += 100
    }

    return clients
  }

  private async getAgencyClientsPaged(
    agencyId: number,
    offset: number
  ): Promise<{ data: EPPlayer[]; hasNextPage: boolean }> {
    try {
      const response = await axios.get<{
        data: EPPlayer[]
        _links: { next: string }
      }>(
        `https://api.eliteprospects.com/v1/agencies/${agencyId}/clients?offset=${offset}&limit=100&fields=views,firstName,position,lastName,name,imageUrl,dateOfBirth,yearOfBirth,nationality.*,height,weight,shoots,links&apiKey=${config.ep.apiKey}`
      )

      return { data: response.data.data, hasNextPage: response.data._links.next ? true : false }
    } catch (err) {
      this.logger.error('EP: failed to fetch agency clients', err)

      return { data: [], hasNextPage: false }
    }
  }

  async getPlayer(userId: string): Promise<EPPlayer | null> {
    try {
      const response = await axios.post<{
        data: { player: EPPlayer }
      }>('https://gql.eliteprospects.com/graphql', {
        query: playerQuery(),
        variables: {
          id: userId,
        },
      })

      const player = response.data.data?.player

      if (player) {
        return player
      } else {
        this.logger.warn(`No player found with ID: ${userId}`)
        return null
      }
    } catch (err) {
      this.logger.error(`EP: failed to fetch player with ID: ${userId}`, err)
      return null
    }
  }
}
