type ImperialHeight = {
  feet: number
  inches: number
}

// parse imperial height string (6'2") and returns object with feet and inches
export const parseImperialHeight = (height?: string | null): ImperialHeight | null => {
  if (!height) {
    return null
  }

  const cleanHeight = height.replace(/"/g, '')

  const match = cleanHeight.match(/(\d{1,3})'(\d{1,2})?/)

  if (!match) {
    throw new Error('Invalid height format')
  }

  const feet = parseInt(match[1], 10)
  const inches = match[2] ? parseInt(match[2], 10) : 0

  return { feet, inches }
}
