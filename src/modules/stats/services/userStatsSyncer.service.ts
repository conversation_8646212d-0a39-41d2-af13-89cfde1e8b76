import { isEqual, pick } from 'lodash'
import Container, { Service } from 'typedi'
import { URL } from 'url'

import { CrawlerDomainConfig, CrawlerEntity } from '../../admin/models/crawlerDomainConfig'
import { DiscoveredUrl } from '../../admin/models/discoveredUrl'
import { UserModel } from '../../user/models'
import { ExternalLinkType, User } from '../../user/models/user'
import { isCurrentOrFutureSeason } from '../helpers/general'
import { syncUserStats } from '../helpers/userStats'
import { generateUserStatsKey, UserStats, UserStatsModel } from '../models/userStats'

import EliteProspectsService from './eliteprospects/ep'
import LeijonatService from './leijonat/leijonat'
import { BaseSyncer, IBaseSyncer } from './baseSyncer'

import { DocumentType } from '@typegoose/typegoose'

@Service()
export default class UserStatsSyncerService extends BaseSyncer implements IBaseSyncer {
  private epService: EliteProspectsService
  private leijonatService: LeijonatService

  constructor() {
    super()

    this.epService = Container.get('ep.service')
    this.leijonatService = Container.get('leijonat.service')
  }

  async syncData(url: DocumentType<DiscoveredUrl>) {
    const urlConfig = (url.crawlerDomainConfig as CrawlerDomainConfig)?.urlConfigs?.find(
      (urlConfig) => urlConfig.entity === CrawlerEntity.PLAYER_STATS
    )

    if (!urlConfig?.extractionScript) {
      this.logger.error(`No extraction script found for ${url.url}`)
      return
    }

    const result = await this.scraper.getUserStats(
      url.url,
      urlConfig.extractionScript,
      urlConfig.fetchType
    )

    if (!this.handleScrapeResult(url, result)) {
      await url.save()

      throw new Error('Failed to get user stats')
    }

    const scrapedStats = result.data

    // Generate keys for all scraped stats
    const keys = scrapedStats.map((data) => generateUserStatsKey(data))

    // Fetch all existing stats in a single query
    const existingStatsArray = await UserStatsModel.find({
      $or: [{ key: { $in: keys } }],
    })

    // Create a map for faster lookups
    const existingStatsMap = new Map()
    existingStatsArray.forEach((stat) => {
      existingStatsMap.set(stat.key, stat)
    })

    // Prepare bulk operations
    const bulkOperations = []

    // Process each scraped stat
    for (const data of scrapedStats) {
      const key = generateUserStatsKey(data)
      const existingStats = existingStatsMap.get(key)

      // Sync entities for the data
      const entitiesChanged = await data.syncEntities()

      if (existingStats) {
        // Check if data has actually changed before updating
        const hasChanged = this.hasStatsChanged(existingStats, data)

        if (hasChanged || entitiesChanged) {
          bulkOperations.push({
            updateOne: {
              filter: { _id: existingStats._id },
              update: { $set: data },
            },
          })
        }
      } else {
        data.discoveredUrl = url._id
        data.crawlerDomainConfig = url.crawlerDomainConfig._id

        bulkOperations.push({
          insertOne: {
            document: { ...data, key },
          },
        })
      }
    }

    // Execute bulk operations if there are any
    if (bulkOperations.length > 0) {
      await UserStatsModel.bulkWrite(bulkOperations)
    }

    // rescrape every day if stats are from current season
    if (isCurrentOrFutureSeason(scrapedStats[0].season)) {
      this.markScrapeAsSuccessful(url, new Date(Date.now() + 24 * 60 * 60 * 1000))
    } else {
      this.markScrapeAsSuccessful(url)
    }

    await url.save()
  }

  async syncUserStats(userId: string): Promise<void> {
    const user = await UserModel.findOne({ _id: userId })

    if (!user) {
      throw new Error('User not found')
    }

    const stats: UserStats[] = []
    const eliteProspectId = this.getEliteProspectId(user)
    if (eliteProspectId) {
      stats.push(...(await this.epService.getUserStats({ eliteProspectId })))
    }

    // TODO: add player stats data from scraper
    // maybe based on externalId of player, get items from userStats without user assigned
    // and then assign user to them based on externalId - we need to create connection

    const linksToScrape = this.getLinksToScrape(user)
    for (const link of linksToScrape) {
      // TODO: move leijonat to crawler and scraper, this is exception
      if (link.includes('leijonat.fi')) {
        const leUserId = new URL(link).searchParams.get('lkq')

        if (!leUserId) {
          this.logger.error(`Failed to get user id from leijonat link: ${link} userId: ${userId}`)
          continue
        }

        stats.push(...(await this.leijonatService.getUserStats({ userId: leUserId })))

        continue
      }
    }

    await syncUserStats(this.mergeStats(stats), user)
  }

  private getEliteProspectId(user: DocumentType<User>): string | null {
    const epLink = user.externalLinks?.find(
      (link) => link.type === ExternalLinkType.ELITE_PROSPECTS
    )?.url

    if (!epLink?.includes('eliteprospects.com/player/')) {
      return null
    }

    return epLink ? epLink.split('/').slice(-2, -1)[0] : null
  }

  private getLinksToScrape(user: DocumentType<User>): string[] {
    return user.externalLinks?.filter((link) => link.scrapable).map((link) => link.url) ?? []
  }

  private readonly mergeStats = (stats: UserStats[]): UserStats[] => {
    const map = new Map<string, UserStats>()

    for (const stat of stats) {
      const key = generateUserStatsKey(stat)

      const existingStat = map.get(key)
      if (existingStat) {
        if (!stat?.gamesPlayed) {
          continue
        }

        // pick the one with more games played
        if (stat.gamesPlayed > (existingStat.gamesPlayed ?? 0)) {
          map.set(key, stat)
        }
      } else {
        map.set(key, stat)
      }
    }

    return Array.from(map.values())
  }

  /**
   * Checks if two user stats objects have meaningful differences
   * @param existingStat - The existing stat in the database
   * @param newStat - The new stat to compare against
   * @returns true if stats are different, false if they're the same
   */
  private hasStatsChanged(existingStat: UserStats, newStat: UserStats): boolean {
    const mainFields = [
      'gamesPlayed',
      'assists',
      'goals',
      'points',
      'pim',
      'plusMinus',
      'wins',
      'losses',
      'ties',
      'gaa',
      'svp',
      'shutouts',
      'ga',
      'toi',
      'saves',
    ]

    // Check main fields
    const existingPicked = pick(existingStat, mainFields)
    const newPicked = pick(newStat, mainFields)
    const mainFieldsEqual = isEqual(existingPicked, newPicked)

    if (!mainFieldsEqual) {
      return true
    }

    // Check postSeasonStats
    if (!isEqual(existingStat.postSeasonStats, newStat.postSeasonStats)) {
      return true
    }

    return false
  }
}
