import { Service } from 'typedi'

import { slugifyString } from '../../../helpers/string'
import { addExternalConnections, createShadowUser } from '../../admin/helpers/shadowUser'
import { CrawlerDomainConfig, CrawlerEntity } from '../../admin/models/crawlerDomainConfig'
import { DiscoveredUrl } from '../../admin/models/discoveredUrl'
import { CreationSource } from '../../user/models/user'
import { findAlreadyRegisteredUser } from '../helpers/user'
import { UserInfoModel } from '../models/userInfo'

import { BaseSyncer, IBaseSyncer } from './baseSyncer'

import { DocumentType } from '@typegoose/typegoose'

@Service()
export default class UserInfoSyncerService extends BaseSyncer implements IBaseSyncer {
  async syncData(url: DocumentType<DiscoveredUrl>): Promise<void> {
    const urlConfig = (url.crawlerDomainConfig as CrawlerDomainConfig)?.urlConfigs?.find(
      (urlConfig) => urlConfig.entity === CrawlerEntity.PLAYER
    )

    if (!urlConfig?.extractionScript) {
      this.logger.error(`No extraction script found for ${url.url}`)
      return
    }

    const result = await this.scraper.getUserInfo(
      url.url,
      urlConfig.extractionScript,
      urlConfig.fetchType
    )

    // For UserInfoSyncer, we don't throw an error if the scrape fails
    // We just mark it as unsuccessful and continue
    if (!this.handleScrapeResult(url, result)) {
      await url.save()
      return
    }

    // At this point, we know result.data is not null
    const data = result.data!

    data.externalId = url.externalId ?? url.url
    data.slug = slugifyString(data.name)

    const userInfo = await UserInfoModel.findOne({
      externalId: data.externalId,
      crawlerDomainConfig: url.crawlerDomainConfig,
    })

    if (userInfo) {
      await UserInfoModel.updateOne(
        {
          externalId: data.externalId,
          crawlerDomainConfig: url.crawlerDomainConfig,
        },
        data
      )
    } else {
      data.discoveredUrl = url._id
      data.crawlerDomainConfig = url.crawlerDomainConfig

      const userInfo = await UserInfoModel.create(data)

      // find already registered users or shadow profiles
      const user = data.slug ? await findAlreadyRegisteredUser(data) : null

      if (user) {
        await addExternalConnections(user, [userInfo])
        await user.save()
      } else {
        await createShadowUser({
          name: data.name,
          country: data.country,
          yearsOfBirth: data.possibleYearsOfBirth,
          ...(data.dateOfBirth ? { dateOfBirth: data.dateOfBirth.toISOString() } : {}),
          source: CreationSource.Scraper,
        })
      }
    }

    this.markScrapeAsSuccessful(url)
    await url.save()
  }
}
