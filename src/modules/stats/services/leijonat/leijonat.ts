import axios from 'axios'
import qs from 'qs'
import { Service } from 'typedi'

import { slugifyString } from '../../../../helpers/string'
import { StatsSource, UserStats } from '../../models/userStats'
import { IStatsSource } from '../../types/sources'

import { GoalieLevel, PlayerStatsResponse, SeasonsResponse, SkaterLevel } from './types'

@Service()
export default class LeijonatService implements IStatsSource {
  private readonly baseUrl = 'https://www.leijonat.fi/modules'

  public async getUserStats(input: { userId: string }): Promise<UserStats[]> {
    const allStats = await axios.post<{ IsGoalieStats: string }>(
      `${this.baseUrl}/mod_playercardallstats/helper/getplayerallstats5.php?lkq=${input.userId}&season=2025`
    )
    const isGoalie = allStats.data.IsGoalieStats === '1'

    const seasons = await axios.post<SeasonsResponse>(
      `${this.baseUrl}/mod_playercardmain/helper/getseasonstatsdata.php`,
      qs.stringify({
        lkq: input.userId,
        'filters[All]': 1,
        'filters[Series]': 0,
        'filters[Games]': 0,
        'filters[SeasonNumber]': 0,
      })
    )

    const stats: UserStats[] = []

    const uniqueSeasons = [...new Set(seasons.data.Skater?.map((season) => season.SeasonNumber))]

    const userSeasons = uniqueSeasons
    for (const season of userSeasons) {
      const seasonStats = await this.getSeasonStats(input.userId, parseInt(season), isGoalie)
      if (seasonStats) {
        stats.push(...seasonStats)
      }
    }

    return stats
  }

  private async getSeasonStats(
    userId: string,
    year: number,
    isGoalie: boolean
  ): Promise<UserStats[]> {
    const response = await axios.post<PlayerStatsResponse>(
      `${this.baseUrl}/mod_playercardseriestats/helper/getplayerseriestats5.php?lkq=${userId}&season=${year}&isgoalie=${isGoalie ? 1 : 0}&isskater=${isGoalie ? 0 : 1}`
    )

    const data = response.data
    const seasonStats: UserStats[] = []

    const levels = isGoalie ? data.GoalieLevels : data.SkaterLevels
    for (const season of levels) {
      if (!season.LevelTeams || season.LevelTeams.length === 0 || !season.LevelTeams[0].TeamName) {
        continue
      }

      const leagueName = season.LevelName
      const leagueSlug = slugifyString(leagueName)
      const teamName = season.LevelTeams[0].TeamName
      const teamSlug = slugifyString(teamName)

      const stats = new UserStats()
      stats.source = StatsSource.LEIJONAT
      stats.season = `${year - 1}-${year}`
      stats.externalId = `leijonat-${userId}-${year}-${teamSlug}-${leagueSlug}`

      if ((season as SkaterLevel).LevelGoals) {
        const level = season as SkaterLevel

        stats.gamesPlayed = this.parseNumber(level?.PlayedLevelGames)
        stats.goals = this.parseNumber(level?.LevelGoals)
        stats.assists = this.parseNumber(level?.LevelAssists)
        stats.pim = this.parseNumber(level?.LevelPenMinutes)
      } else {
        const level = season as GoalieLevel

        stats.gamesPlayed = this.parseNumber(level.InLevelPlayedGames)
        stats.gaa = parseFloat(
          (this.parseNumber(level.LevelGoalsAgainst) / (stats.gamesPlayed || 1)).toFixed(2)
        )
        stats.svp = this.parseNumber(level.LevelSavePerc, true) / 100
      }

      stats.userExternalId = userId
      stats.externalInfo = {}
      stats.externalInfo.externalTeamName = teamName
      stats.externalInfo.externalTeamCountry = 'FI' // TODO: get country team (could not found it in API anywhere)
      stats.externalInfo.externalLeagueName = leagueName
      stats.externalInfo.externalLeagueCountry = 'FI'

      seasonStats.push(stats)
    }

    return seasonStats
  }

  private parseNumber(value: string | undefined, float: boolean = false): number {
    if (!value) {
      return 0
    }

    return float ? parseFloat(value) : parseInt(value)
  }
}
