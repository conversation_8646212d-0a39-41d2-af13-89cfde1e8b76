export type SeasonsResponse = {
  Skater: {
    SeasonNumber: string
  }[]
  Goalie: {
    SeasonNumber: string
  }[]
}

export type SkaterLevel = {
  LevelName: string
  LevelID: string

  LevelGoals: string
  LevelAssists: string
  LevelPenMinutes: string
  PlayedLevelGames: string

  LevelTeams: { TeamName: string }[]
}

export type GoalieLevel = {
  LevelName: string
  LevelID: string

  InLevelPlayedGames: string
  LevelGoalsAgainst: string
  LevelSavePerc: string

  LevelTeams: { TeamName: string }[]
}

export type PlayerStatsResponse = {
  SkaterLevels: SkaterLevel[]
  GoalieLevels: GoalieLevel[]
}
