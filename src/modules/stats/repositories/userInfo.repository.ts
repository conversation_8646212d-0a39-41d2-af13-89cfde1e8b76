import { Types } from 'mongoose'
import { Service } from 'typedi'

import { Pagination, SortOrder } from '../../../types/general'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { UserInfoModel } from '../models/userInfo'

@Service()
export default class UserInfoRepository {
  private readonly pagination: PaginationRepository = new PaginationRepository()

  async search(query: string, pagination: Pagination) {
    const pipeline = []

    const templateSortObject = { score: 1, _id: new Types.ObjectId() }
    pipeline.push(
      {
        $search: {
          index: 'userInfo_search',
          autocomplete: {
            query,
            path: 'name',
          },
        },
      },
      { $addFields: { score: { $meta: 'searchScore' } } }
    )

    const results = await this.pagination.aggregate({
      templateSortObject,
      pipeline,
      model: UserInfoModel,
      sorting: {
        fields: [{ field: 'score', order: SortOrder.DESC }],
      },
      pagination: this.pagination.mapPagination(pagination),
    })

    await UserInfoModel.populate(results.nodes, [{ path: 'crawlerDomainConfig' }])

    return results
  }
}
