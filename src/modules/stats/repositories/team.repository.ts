import { PipelineStage, Types } from 'mongoose'
import { Service } from 'typedi'

import { Pagination, SortOrder } from '../../../types/general'
import { Organization, OrganizationModel } from '../../stats/models/organization'
import { Team, TeamModel } from '../../stats/models/team'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { GameModel } from '../models/game/game'
import { League, LeagueModel } from '../models/league'

import { Cache } from '@t.voslar/ts-cache-decorator'
import { Ref } from '@typegoose/typegoose'

@Service()
export default class TeamRepository {
  private readonly pagination: PaginationRepository = new PaginationRepository()

  @Cache({ type: 'normal', cacheKey: 'getTeamByExternalInfo', ttl: 300 })
  async getByExternalInfo(league: League, slug: string): Promise<Team | null> {
    const teams = await TeamModel.aggregate([
      {
        $lookup: {
          from: 'organizations',
          localField: 'organization',
          foreignField: '_id',
          as: 'org',
        },
      },
      {
        $match: {
          leagues: { $in: [new Types.ObjectId(league?._id), league?._id] },
          $or: [
            {
              slug: slug,
            },
            {
              'alternativeNames.slug': slug,
            },
            {
              abbreviation: slug,
            },
            {
              'org.slug': slug,
            },
            {
              'org.alternativeNames.slug': slug,
            },
            {
              'org.abbreviation': slug,
            },
          ],
        },
      },
    ])

    return teams[0]
  }

  async search(
    query: string | null = null,
    pagination: Pagination,
    matchPipeline: Partial<Record<keyof Team, unknown>> = {},
    isAdmin = false
  ) {
    const pipeline: PipelineStage[] = []

    if (query && query?.length > 0) {
      pipeline.push({
        $search: {
          index: 'teams_search',
          autocomplete: {
            query: query,
            path: 'name',
          },
        },
      })

      pipeline.push({ $addFields: { score: { $meta: 'searchScore' } } })
    }

    if (!isAdmin) {
      matchPipeline['organization'] = { $exists: true }
      matchPipeline['shortNameSlug'] = { $exists: true }
      matchPipeline['playersCount'] = { $gt: 0 }
    }

    pipeline.push({ $match: { ...matchPipeline } })

    const sorting = { fields: [{ field: query ? 'score' : '_id', order: SortOrder.DESC }] }

    const templateSortObject = {
      _id: new Types.ObjectId(),
      score: 0,
    }

    const teams = await this.pagination.aggregate({
      templateSortObject: templateSortObject,
      pipeline: pipeline,
      model: TeamModel,
      sorting: sorting,
      pagination: this.pagination.mapPagination(pagination),
    })

    // fetch organizations for all teams with simple find
    const organizations = await OrganizationModel.find({
      _id: { $in: teams.nodes.map((team) => team.organization) },
    })

    teams.nodes.forEach((team) => {
      team.organization = organizations.find((organization) =>
        organization._id.equals(team.organization as unknown as string)
      ) as Ref<Organization>
    })

    return teams
  }

  @Cache({ type: 'normal', cacheKey: 'getTeamLeagues', ttl: 60 * 60 * 24 })
  async getTeamLeagues(teamId: Types.ObjectId): Promise<League[]> {
    const games = await GameModel.find({
      $or: [{ homeTeam: teamId }, { awayTeam: teamId }],
      league: { $exists: true },
    }).distinct('league')

    const leagues = await LeagueModel.find({
      _id: { $in: games },
    })

    return leagues
  }
}
