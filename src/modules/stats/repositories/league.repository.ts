import { Service } from 'typedi'

import { League, LeagueModel } from '../models/league'

import { Cache } from '@t.voslar/ts-cache-decorator'

@Service()
export default class LeagueRepository {
  @Cache({ type: 'normal', cacheKey: 'getLeagueByExternalInfo', ttl: 300 })
  async getByExternalInfo(slug: string, country: string | undefined): Promise<League | undefined> {
    const league = await LeagueModel.findOne({
      $or: [
        {
          slug: slug,
          ...(country ? { countries: { $in: country } } : {}),
        },
        {
          'alternativeNames.slug': slug,
          ...(country ? { countries: { $in: country } } : {}),
        },
      ],
    })

    return league ?? undefined
  }

  @Cache({ type: 'normal', cacheKey: 'getLeagueByExternalInfo', ttl: 300 })
  async getById(id: string | undefined): Promise<League | undefined> {
    if (!id) {
      return undefined
    }

    const league = await LeagueModel.findById(id)

    return league ?? undefined
  }
}
