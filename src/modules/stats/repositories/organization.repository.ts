import { PipelineStage, Types } from 'mongoose'
import { Service } from 'typedi'

import { Pagination, SortOrder } from '../../../types/general'
import { OrganizationModel } from '../../stats/models/organization'
import { PaginationRepository } from '../../user/repositories/pagination.repository'

@Service()
export default class OrganizationRepository {
  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  async search(query: string | null = null, pagination: Pagination, matchPipeline: object = {}) {
    const pipeline: PipelineStage[] = []

    if (query && query?.length > 0) {
      pipeline.push({
        $search: {
          index: 'organizations_search',
          compound: {
            should: [
              {
                autocomplete: {
                  query,
                  path: 'name',
                },
              },
            ],
            minimumShouldMatch: 1,
          },
        },
      })

      pipeline.push({ $addFields: { score: { $meta: 'searchScore' } } })
    }

    pipeline.push({ $match: { ...matchPipeline } })

    const templateSortObject = { _id: new Types.ObjectId(), score: 0 }

    const sorting = query
      ? { fields: [{ field: 'score', order: SortOrder.DESC }] }
      : { fields: [{ field: '_id', order: SortOrder.DESC }] }

    const organizations = await this.paginationRepository.aggregate({
      templateSortObject,
      pipeline,
      model: OrganizationModel,
      sorting,
      pagination: this.paginationRepository.mapPagination(pagination),
    })

    return organizations
  }
}
