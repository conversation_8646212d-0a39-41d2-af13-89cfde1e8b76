import { Types } from 'mongoose'
import { Service } from 'typedi'

import { collectExternalUserIdsFromGame } from '../helpers/shadowPlayers'
import { GameModel } from '../models/game/game'

@Service()
export default class GameRepository {
  /**
   * Collects external user IDs from games for a team
   *
   * @param teamId The ID of the team to collect players for (if null, collects from both teams)
   * @returns A map of crawler domain config IDs to sets of external player IDs
   */
  async collectExternalUserIdsFromTeamGames(
    teamId: Types.ObjectId | null = null
  ): Promise<Map<string, Set<string>>> {
    const externalUserIdsMap = new Map<string, Set<string>>()

    // Build the query based on whether we're filtering by team
    const query: Record<string, unknown> = {
      date: { $lt: new Date() },
    }

    // If teamId is provided, only get games for that team
    if (teamId) {
      query.$or = [{ homeTeam: teamId }, { awayTeam: teamId }]
    }

    // Find recent games
    const games = await GameModel.find(query).sort({ date: -1 }).limit(20).populate('discoveredUrl')

    // Collect external user IDs from games
    for (const game of games) {
      collectExternalUserIdsFromGame(game, teamId, externalUserIdsMap)
    }

    return externalUserIdsMap
  }

  /**
   * Collects external user IDs from games for multiple teams in a single query
   *
   * @param teamIds Array of team IDs to collect players for
   * @returns A map of crawler domain config IDs to sets of external player IDs
   */
  async collectExternalUserIdsFromMultipleTeams(
    teamIds: Types.ObjectId[]
  ): Promise<Map<string, Set<string>>> {
    if (!teamIds.length) {
      return new Map<string, Set<string>>()
    }

    const externalUserIdsMap = new Map<string, Set<string>>()

    // Build a query to find games for any of the teams
    const query = {
      $or: [{ homeTeam: { $in: teamIds } }, { awayTeam: { $in: teamIds } }],
      date: { $lt: new Date() },
    }

    // Find recent games for all teams in a single query
    const games = await GameModel.find(query)
      .sort({ date: -1 })
      .limit(50) // Increased limit since we're querying for multiple teams
      .populate('discoveredUrl')

    // Process each game
    for (const game of games) {
      // Determine which team from our list is involved in this game
      const relevantTeamId = teamIds.find(
        (id) =>
          game.homeTeam?.toString() === id.toString() || game.awayTeam?.toString() === id.toString()
      )

      if (relevantTeamId) {
        // Collect external user IDs only for the relevant team
        collectExternalUserIdsFromGame(game, relevantTeamId, externalUserIdsMap)
      }
    }

    return externalUserIdsMap
  }
}
