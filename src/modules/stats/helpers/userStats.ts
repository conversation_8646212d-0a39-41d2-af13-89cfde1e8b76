import { Types } from 'mongoose'
import Container from 'typedi'

import { logger } from '../../../container'
import { slugifyString } from '../../../helpers/string'
import { LeagueType } from '../../../modules/stats/models/league'
import { TeamModel } from '../../../modules/stats/models/team'
import {
  SeasonType,
  StatsSource,
  UserStats,
  UserStatsModel,
} from '../../../modules/stats/models/userStats'
import { User } from '../../../modules/user/models/user'
import { UserModel } from '../../user/models'
import { AggregatedStats } from '../../user/models/user/aggregatedStats'
import LeagueRepository from '../repositories/league.repository'

import { DocumentType } from '@typegoose/typegoose'

export const syncUserStats = async (stats: UserStats[], user: DocumentType<User>) => {
  const allFoundUserStats = await UserStatsModel.find({
    user,
  })

  const statsToSave = []
  for (const stat of stats) {
    let userStats = allFoundUserStats.find(
      (userStat) =>
        userStat.externalId === stat.externalId ||
        // in some cases EP changes the externalId (no idea why),
        // so we need to check by season, team and league to not have duplicate errors
        (userStat.season === stat.season &&
          userStat.externalInfo?.externalTeamName === stat.externalInfo?.externalTeamName &&
          userStat.externalInfo?.externalLeagueName === stat.externalInfo?.externalLeagueName)
    )

    if (!stat.season) {
      logger.error(`User ${user._id} has a stat without a season: ${JSON.stringify(stat)}`)

      continue
    }

    if (userStats) {
      // just update statistics like goals, assists, etc.
      userStats.gamesPlayed = stat.gamesPlayed
      userStats.goals = stat.goals
      userStats.assists = stat.assists
      userStats.pim = stat.pim
      userStats.plusMinus = stat.plusMinus
      userStats.wins = stat.wins
      userStats.losses = stat.losses
      userStats.ties = stat.ties
      userStats.gaa = stat.gaa
      userStats.svp = stat.svp
      userStats.shutouts = stat.shutouts
      userStats.postSeasonStats = stat.postSeasonStats
      userStats.externalInfo = stat.externalInfo
    } else {
      // if there is no team we don't want to create user stats
      if (!stat.externalInfo?.externalTeamName) {
        continue
      }

      userStats = new UserStatsModel(stat)
      userStats.user = user._id
    }

    // TODO: probably we should connect external data with our data somewhere else
    // to offload db queries
    await userStats.syncEntities()

    statsToSave.push(userStats)
  }

  // sync manually added stats, to have seasonType update based on league.type
  for (const userStat of allFoundUserStats.filter((stat) => stat.source === StatsSource.MANUAL)) {
    if (!statsToSave.find((stat) => stat.id === userStat.id)) {
      const leagueRepository = Container.get(LeagueRepository)
      const league = await leagueRepository.getById(userStat.league?.toString())

      if (league?.type === LeagueType.TOURNAMENT) {
        userStat.seasonType = SeasonType.TOURNAMENT
      } else {
        userStat.seasonType = SeasonType.REGULAR
      }
    }

    statsToSave.push(userStat)
  }

  const mergedStats = new Map<string, DocumentType<UserStats>>()
  for (const statToSave of statsToSave) {
    const key = `${statToSave.season}-${statToSave.team ?? statToSave.externalInfo?.externalTeamName}-${statToSave.league ?? statToSave.externalInfo?.externalLeagueName}`

    mergedStats.set(key, statToSave)
  }

  await UserStatsModel.bulkSave([...mergedStats.values()])

  await saveLatestEPTeam(statsToSave, user)

  user.statsSyncedAt = new Date()
  await user.save()
}

const saveLatestEPTeam = async (userStats: UserStats[], user: DocumentType<User>) => {
  if (userStats.length > 0) {
    const lastTeam = userStats[0].externalInfo?.externalTeamName
    const epTeam = await TeamModel.findOne({ slug: slugifyString(lastTeam) })

    user.epCurrentTeam = epTeam?.id
  }
}

export const saveAggregatedStats = async (userStats: UserStats[], user: DocumentType<User>) => {
  const aggregatedStats = new AggregatedStats()

  aggregatedStats.career = calculateStats(userStats)
  aggregatedStats.season = calculateStats(
    userStats.filter((season) => season.season === '2024-2025') // TODO: calculate current season by date
  )

  const previousStats = { ...user.stats }

  // save only if stats differs from user.stats
  if (JSON.stringify(previousStats) !== JSON.stringify(aggregatedStats)) {
    user.stats = aggregatedStats
    await user.save()
  }
}

const calculateStats = (seasons: UserStats[]) => {
  return seasons?.reduce(
    (total, season) => {
      if (season.gamesPlayed === 0) {
        return total
      }

      const gamesPlayed = total.gamesPlayed + (season.gamesPlayed ?? 0)
      const goals = total.goals + (season.goals ?? 0)
      const assists = total.assists + (season.assists ?? 0)
      const shutouts = total.shutouts + (season.shutouts ?? 0)
      const points = total.points + (season.goals ?? 0) + (season.assists ?? 0)
      const wins = total.wins + (season.wins ?? 0)
      const losses = total.losses + (season.losses ?? 0)
      const ties = total.ties + (season.ties ?? 0)

      // calculate weighted average for gaa
      const gaa =
        season.gaa !== null
          ? (total.gaa * total.gamesPlayed + (season.gaa ?? 0) * (season.gamesPlayed ?? 0)) /
            gamesPlayed
          : total.gaa

      // calculate weighted average for SVP, ignoring values that are 0 or invalid
      let updatedSvp = total.svp
      let updatedSvpGamesCount = total.svpGamesCount
      if (
        !isEmpty(season.svp) &&
        (season.svp ?? 0) > 0 &&
        !(season.svp === 1 && (season.gaa ?? 0) > 0)
      ) {
        updatedSvp =
          (total.svp * total.svpGamesCount + (season.svp ?? 0) * (season.gamesPlayed ?? 0)) /
          (total.svpGamesCount + (season.gamesPlayed ?? 0))
        updatedSvpGamesCount += season.gamesPlayed ?? 0
      }

      const roundedPpg = gamesPlayed > 0 ? Math.round((points / gamesPlayed) * 100) / 100 : 0
      const roundedGaa = Math.round(gaa * 100) / 100
      const roundedSvp = Math.round(updatedSvp * 1000) / 1000
      return {
        gamesPlayed,
        goals,
        assists,
        points,
        pointsPerGame: isNaN(roundedPpg) ? 0 : roundedPpg,
        wins,
        losses,
        ties,
        shutouts,
        gaa: isNaN(roundedGaa) ? 0 : roundedGaa,
        svp: isNaN(roundedSvp) ? 0 : roundedSvp,
        svpGamesCount: updatedSvpGamesCount,
        gaaGamesCount: total.gaaGamesCount,
      }
    },
    {
      gamesPlayed: 0,
      goals: 0,
      assists: 0,
      shutouts: 0,
      points: 0,
      pointsPerGame: 0,
      wins: 0,
      losses: 0,
      ties: 0,
      gaa: 0,
      svp: 0,
      gaaGamesCount: 0,
      svpGamesCount: 0,
    }
  )
}

export const isEmpty = (value: unknown): boolean => {
  return (
    value === undefined ||
    value === null ||
    (typeof value === 'object' && Object.keys(value).length === 0) ||
    (typeof value === 'string' && value.trim().length === 0)
  )
}

export const mergeStats = (stat1: UserStats, stat2: UserStats): UserStats => {
  // Create a new object with all properties from stat1
  const merged = { ...stat1 }

  // Keep the original _id and id
  merged._id = stat1._id

  // Merge numeric stats by adding values together
  merged.gamesPlayed = (stat1.gamesPlayed ?? 0) + (stat2.gamesPlayed ?? 0)
  merged.goals = (stat1.goals ?? 0) + (stat2.goals ?? 0)
  merged.assists = (stat1.assists ?? 0) + (stat2.assists ?? 0)
  merged.pim = (stat1.pim ?? 0) + (stat2.pim ?? 0)
  merged.plusMinus = (stat1.plusMinus ?? 0) + (stat2.plusMinus ?? 0)

  // Merge goalie stats
  merged.wins = (stat1.wins ?? 0) + (stat2.wins ?? 0)
  merged.losses = (stat1.losses ?? 0) + (stat2.losses ?? 0)
  merged.ties = (stat1.ties ?? 0) + (stat2.ties ?? 0)
  merged.shutouts = (stat1.shutouts ?? 0) + (stat2.shutouts ?? 0)

  // Calculate weighted averages for goalie stats based on games played
  const totalGames = merged.gamesPlayed ?? 0
  if (totalGames > 0) {
    // Calculate weighted average for GAA (Goals Against Average)
    merged.gaa =
      totalGames > 0
        ? ((stat1.gaa ?? 0) * (stat1.gamesPlayed ?? 0) +
            (stat2.gaa ?? 0) * (stat2.gamesPlayed ?? 0)) /
          totalGames
        : 0

    // Calculate weighted average for SVP (Save Percentage)
    merged.svp =
      totalGames > 0
        ? ((stat1.svp ?? 0) * (stat1.gamesPlayed ?? 0) +
            (stat2.svp ?? 0) * (stat2.gamesPlayed ?? 0)) /
          totalGames
        : 0
  }

  // Preserve metadata from the primary stat
  merged.user = stat1.user
  merged.team = stat1.team
  merged.league = stat1.league
  merged.season = stat1.season
  merged.orderInSeason = stat1.orderInSeason

  // Copy external info but remove the stage since this is an aggregated stat
  merged.externalInfo = { ...stat1.externalInfo, stage: undefined }

  return merged as UserStats
}

/**
 * Aggregates an array of UserStats objects based on season, team, and league.
 *
 * This function processes a collection of user statistics and aggregates them according to
 * specific rules:
 * 1. If SCRAPER source stats exist, EP source stats are ignored for the same season
 * 2. Stats are grouped by season, team, and league
 * 3. Stats without stages are prioritized over stats with stages
 * 4. Stats with the same season, team, and league but different stages are merged
 */
export const aggregatedUserStats = (userStats: UserStats[]) => {
  // Group stats by season, team, and league using a Map
  const statsMap = new Map<string, UserStats>()

  // Process stats, excluding EP source if SCRAPER exists
  for (const stat of userStats) {
    // Skip EP stats only if SCRAPER stats exist for the same season
    const seasonKey = stat.season
    const hasScraperStatsForSeason = userStats.some(
      (s) => s.season === seasonKey && s.source === StatsSource.SCRAPER
    )
    if (hasScraperStatsForSeason && stat.source === StatsSource.EP) {
      continue
    }

    // Create a unique key for each season-team-league combination
    const key = `${stat.season}-${stat.team?.toString()}-${stat.league?.toString()}`

    // If we find a stat without stage, use it directly (preferred)
    if (!stat.externalInfo?.stage?.value) {
      statsMap.set(key, stat)
      continue
    }

    // If we already have a non-stage stat for this combination, skip
    // (non-stage stats take precedence over stage stats)
    if (statsMap.has(key) && !statsMap.get(key)!.externalInfo?.stage?.value) {
      continue
    }

    // Sum stats with stages or add new stat
    if (statsMap.has(key)) {
      const existingStat = statsMap.get(key)!
      // Merge the existing stat with the current one
      statsMap.set(key, mergeStats(existingStat, stat))
    } else {
      // First occurrence of this season-team-league combination
      statsMap.set(key, stat)
    }
  }

  // Convert the Map values back to an array
  return Array.from(statsMap.values())
}

export const populateUsersByExternalIds = async (
  userStats: UserStats[],
  mappingCallback?: (userStat: UserStats) => void
): Promise<void> => {
  // gather users by userExternalIds
  const externalUserIdsMap = new Map<string, Set<string>>()
  for (const userStat of userStats) {
    if (!userStat.crawlerDomainConfig || !userStat.userExternalId) {
      continue
    }

    if (!externalUserIdsMap.has(userStat.crawlerDomainConfig.toString())) {
      externalUserIdsMap.set(userStat.crawlerDomainConfig.toString(), new Set())
    }

    externalUserIdsMap.get(userStat.crawlerDomainConfig.toString())!.add(userStat.userExternalId)
  }

  const orConditions = []
  for (const [crawlerDomainConfigId, externalIds] of externalUserIdsMap.entries()) {
    if (externalIds.size > 0) {
      orConditions.push({
        externalConnections: {
          $elemMatch: {
            externalId: { $in: Array.from(externalIds) },
            crawlerDomainConfig: new Types.ObjectId(crawlerDomainConfigId),
          },
        },
      })
    }
  }

  let users: User[] = []
  if (orConditions.length > 0) {
    users = await UserModel.find({ $or: orConditions }).lean()
  }

  for (const userStat of userStats) {
    const user = users.find((user) => {
      return user.externalConnections.some((ec) => {
        if (!userStat.crawlerDomainConfig) {
          return false
        }

        return (
          ec.externalId === userStat.userExternalId &&
          ec.crawlerDomainConfig.toString() === userStat.crawlerDomainConfig.toString()
        )
      })
    })

    if (user) {
      ;(userStat.user as User) = user
    }

    mappingCallback?.(userStat)
  }
}
