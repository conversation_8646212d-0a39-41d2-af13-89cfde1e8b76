import { Types } from 'mongoose'

import { DiscoveredUrl } from '../../admin/models/discoveredUrl'
import { UserModel } from '../../user/models'
import { Role, User } from '../../user/models/user'
import { Game } from '../models/game/game'

/**
 * Collects external user IDs from a game
 *
 * @param game The game to collect player IDs from
 * @param teamId Optional team ID to filter players (if null, collects from both teams)
 * @param externalUserIdsMap Map to store the collected external IDs
 */
export const collectExternalUserIdsFromGame = (
  game: Game,
  teamId: Types.ObjectId | null,
  externalUserIdsMap: Map<string, Set<string>>
): void => {
  const crawlerDomainConfigId = (game.discoveredUrl as DiscoveredUrl).crawlerDomainConfig.toString()

  if (!externalUserIdsMap.has(crawlerDomainConfigId)) {
    externalUserIdsMap.set(crawlerDomainConfigId, new Set())
  }

  const externalIdsSet = externalUserIdsMap.get(crawlerDomainConfigId)!

  // Determine which teams to collect from
  const collectFromHomeTeam = teamId ? game.homeTeam?.toString() === teamId.toString() : true
  const collectFromAwayTeam = teamId ? game.awayTeam?.toString() === teamId.toString() : true

  // Helper function to collect player IDs from a roster
  const collectFromRoster = (roster: Array<{ externalInfo?: { playerExternalId?: string } }>) => {
    for (const player of roster ?? []) {
      if (player.externalInfo?.playerExternalId) {
        externalIdsSet.add(player.externalInfo.playerExternalId)
      }
    }
  }

  // Helper function to collect player IDs from events
  const collectFromEvents = (isHome: boolean) => {
    for (const event of game.events ?? []) {
      // Check if the event is for the team we're interested in
      if (event.isHomeTeam === isHome && event.externalInfo?.playerExternalId) {
        externalIdsSet.add(event.externalInfo.playerExternalId)
      }

      // Collect assists for the team
      if (event.isHomeTeam === isHome) {
        for (const assist of event.assists ?? []) {
          if (assist.playerExternalId) {
            externalIdsSet.add(assist.playerExternalId)
          }
        }
      }
    }
  }

  // Collect from home team if needed
  if (collectFromHomeTeam) {
    collectFromRoster(game.homeTeamRoster ?? [])
    collectFromEvents(true)
  }

  // Collect from away team if needed
  if (collectFromAwayTeam) {
    collectFromRoster(game.awayTeamRoster ?? [])
    collectFromEvents(false)
  }
}

/**
 * Creates MongoDB query conditions from external user IDs map
 */
export const createQueryConditions = (
  externalUserIdsMap: Map<string, Set<string>>
): Array<Record<string, unknown>> => {
  const orConditions = []
  for (const [crawlerDomainConfigId, externalIds] of externalUserIdsMap.entries()) {
    if (externalIds.size > 0) {
      orConditions.push({
        externalConnections: {
          $elemMatch: {
            externalId: { $in: Array.from(externalIds) },
            crawlerDomainConfig: new Types.ObjectId(crawlerDomainConfigId),
          },
        },
      })
    }
  }
  return orConditions
}

/**
 * Finds shadow players based on query conditions
 */
export const findShadowPlayers = async (
  orConditions: Array<Record<string, unknown>>,
  excludeIds: Types.ObjectId[] = []
): Promise<User[]> => {
  if (orConditions.length === 0) {
    return []
  }

  const query: Record<string, unknown> = {
    $or: orConditions,
    role: Role.SHADOW_PLAYER,
  }

  if (excludeIds.length > 0) {
    query._id = { $nin: excludeIds }
  }

  return UserModel.find(query).limit(20).lean()
}
