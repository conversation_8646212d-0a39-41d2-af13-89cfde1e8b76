/**
 * Determines if the given season is the current season.
 *
 * @param season - A string representing the season in the format "YYYY-YYYY"
 * @returns A boolean indicating whether the given season is the current season
 * @throws {Error} If the season format is invalid or the years are not consecutive
 */
export const isCurrentOrFutureSeason = (season: string): boolean => {
  // Validate season format (YYYY-YYYY)
  const seasonRegex = /^\d{4}-\d{4}$/
  if (!seasonRegex.test(season)) {
    return false
  }

  const [startYear, endYear] = season.split('-').map(Number)
  if (endYear !== startYear + 1) {
    return false
  }

  const now = new Date()
  const currentYear = now.getFullYear()
  const isAfterMidYear = now.getMonth() >= 5 // June (0-based month index)

  // or if this is future season we return also true
  if (endYear > currentYear) {
    return true
  }

  // If we're after June, we're in the startYear part of startYear-endYear season
  // If we're before June, we're in the endYear part of (startYear-1)-startYear season
  return isAfterMidYear ? currentYear === startYear : currentYear === endYear
}
