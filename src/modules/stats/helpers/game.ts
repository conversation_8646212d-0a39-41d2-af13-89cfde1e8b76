import { GameEvent } from '../models/game/gameEvent'

// TODO for future sports change this to the actual game length, 2000 => 20:00
const PERIOD_LENGTH = 2000

export const eventsMapper = (events: GameEvent[]) => {
  let previousPeriod = 0

  return events
    .sort((a, b) => {
      const timeToNumber = (time: string | undefined) => {
        if (!time) return 0
        return parseInt(time.replace(':', ''), 10)
      }

      return timeToNumber(a?.time) - timeToNumber(b?.time)
    })
    .map((event) => {
      const eventTime = parseInt(event?.time.replace(':', ''), 10)
      const period = eventTime / PERIOD_LENGTH

      return {
        ...event,
        period: period === 0 || period % 1 !== 0 ? Math.round(Math.floor(period)) : period - 1,
      }
    })
    .reduce((acc: GameEvent[][], event) => {
      const { period, ...rest } = event

      // if we don't have any events in between some periods (ie. 2nd is empty), add empty array
      if (period - previousPeriod > 1) {
        for (let missingPeriod = previousPeriod + 1; missingPeriod < period; missingPeriod++) {
          acc[missingPeriod] = []
        }
      }

      previousPeriod = period

      if (!acc[period]) {
        acc[period] = []
      }

      acc[period].push(rest)

      return acc
    }, [])
}
