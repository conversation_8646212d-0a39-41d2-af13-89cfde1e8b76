import Container from 'typedi'

import { slugifyString } from '../../../helpers/string'
import { League } from '../models/league'
import { Team } from '../models/team'
import { ExternalInfo } from '../models/userStats'
import TeamRepository from '../repositories/team.repository'

import { invalidateCache } from '@t.voslar/ts-cache-decorator'

export const getTeamByExternalInfo = async (
  team?: Team,
  externalInfo?: ExternalInfo,
  league?: League,
  force: boolean = false
): Promise<Team | undefined> => {
  if (externalInfo?.externalTeamName) {
    const slug = slugifyString(externalInfo?.externalTeamName)

    let team: Team | null = null
    if (league) {
      const teamRepository = Container.get(TeamRepository)

      if (force) {
        await invalidateCache('getTeamByExternalInfo', [league, slug])
      }

      team = await teamRepository.getByExternalInfo(league, slug)
    }

    return team ?? undefined
  }

  return team as Team
}
