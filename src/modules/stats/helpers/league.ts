import { FilterQuery } from 'mongoose'
import Container from 'typedi'

import { slugifyString } from '../../../helpers/string'
import { League, LeagueModel, StatusType } from '../models/league'
import { Team, TeamModel } from '../models/team'
import { ExternalInfo } from '../models/userStats'
import LeagueRepository from '../repositories/league.repository'

import { invalidateCache } from '@t.voslar/ts-cache-decorator'

export const assignLeagueToTeams = async (league: League) => {
  if (league.teamAutoConnectRules) {
    // find all teams which matches auto connect rules
    const query: FilterQuery<Team> = {
      country: league.teamAutoConnectRules.country,
      leagues: { $nin: [league._id] },
      shortNameSlug: league.teamAutoConnectRules.shortNameSlug,
    }

    if (league.teamAutoConnectRules.shortNameInclude) {
      query.shortNameSlug = {
        $regex: `.*${league.teamAutoConnectRules.shortNameSlug}.*`,
        $options: 'i',
      }
    }

    await TeamModel.updateMany(query, { $push: { leagues: league._id } })
  }
}

export const assignLeaguesToTeam = async (team: Team) => {
  // find leagues with auto connect rules that matches team
  const leagues = await LeagueModel.find({
    'teamAutoConnectRules.country': { $in: team.country },
    $or: [
      {
        'teamAutoConnectRules.shortNameInclude': false,
        'teamAutoConnectRules.shortNameSlug': team.shortNameSlug,
      },
      {
        'teamAutoConnectRules.shortNameInclude': true,
        $expr: {
          $regexMatch: {
            input: team.shortNameSlug,
            regex: {
              $concat: ['.*', '$teamAutoConnectRules.shortNameSlug', '.*'],
            },
            options: 'i',
          },
        },
      },
    ],
  })

  // assign leagues to team and merge with existing leagues
  const mergedLeagues = Array.from(
    new Set([...team.leagues.map((l) => l.id.toString()), ...leagues.map((l) => l._id.toString())])
  )

  await TeamModel.updateOne({ _id: team._id }, { leagues: mergedLeagues })
}

export const createStatusFilter = (filter: StatusType | undefined, key: string) => {
  if (filter === StatusType.TRUE) {
    return { [key]: true }
  } else if (filter === StatusType.FALSE) {
    return { [key]: false }
  } else if (filter === StatusType.NOT_SET) {
    return { [key]: { $exists: false } }
  } else {
    return {}
  }
}

export const getLeagueByExternalInfo = async (
  league?: League,
  externalInfo?: ExternalInfo,
  force: boolean = false
): Promise<League | undefined> => {
  if (externalInfo?.externalLeagueName) {
    const slug = slugifyString(externalInfo?.externalLeagueName)
    const country = externalInfo?.externalLeagueCountry

    const leagueRepository = Container.get(LeagueRepository)

    if (force) {
      await invalidateCache('getLeagueByExternalInfo', [slug, country])
    }

    return await leagueRepository.getByExternalInfo(slug, country)
  }

  return league as League
}
