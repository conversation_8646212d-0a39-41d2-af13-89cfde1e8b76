import { Model, PopulateOptions } from 'mongoose'

export const modelFind = async <T>(
  model: Model<T>,
  filter = {},
  selection = {},
  populate: PopulateOptions | null,
  batchSize: number = 1000
): Promise<T[]> => {
  let allObjects: T[] = []
  let skip = 0
  let objects: T[]
  do {
    objects = (await model
      .find(filter)
      .lean()
      .limit(batchSize)
      .skip(skip)
      .select(selection)
      .populate(populate || [])) as T[]

    skip += batchSize
    allObjects = allObjects.concat(objects)
  } while (objects?.length)

  return allObjects
}
