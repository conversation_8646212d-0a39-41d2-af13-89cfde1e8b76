import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'

import { CrawlerDomainConfig } from './crawlerDomainConfig'

import { getModelForClass, index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@ObjectType()
export class DomainUrlStats {
  @prop({ required: true })
  @Field()
  public urlPattern: string

  @prop({ required: true })
  @Field()
  public matchCount: number
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ startedAt: -1 })
@index({ crawlerDomainConfig: 1, startedAt: -1 })
export class CrawlerRunStats {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: CrawlerDomainConfig, autopopulate: true })
  @Field((_type) => CrawlerDomainConfig, { nullable: true })
  public crawlerDomainConfig: Ref<CrawlerDomainConfig>

  @prop({ required: true })
  @Field()
  public startedAt: Date

  @prop({ required: true })
  @Field()
  public finishedAt: Date

  @prop({ required: true })
  @Field()
  public durationMs: number

  @prop({ required: true })
  @Field()
  public totalVisitedUrls: number

  @prop({ required: true })
  @Field()
  public totalDiscoveredUrls: number

  @prop({ required: true })
  @Field()
  public newlyDiscoveredUrls: number

  @prop({ type: [DomainUrlStats], required: false })
  @Field(() => [DomainUrlStats], { nullable: true })
  public urlPatternStats?: DomainUrlStats[]
}

export const CrawlerRunStatsModel = getModelForClass(CrawlerRunStats, {
  existingMongoose: mongoose,
})
