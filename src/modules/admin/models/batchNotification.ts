import { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'

import { PushNotificationType } from '../../../services/pushNotification'
import { Role } from '../../user/models/user'

import { BatchNotificationState } from './../types/batchNotification'

import { getModelForClass, Index, modelOptions, prop } from '@typegoose/typegoose'

@ObjectType()
export class BatchNotificationFilter {
  @prop()
  @Field(() => Role)
  public role: Role

  @prop({ type: () => [String], default: undefined })
  @Field((_) => [String], { nullable: true })
  public nationalities?: string[]

  @prop({ type: () => [Number], default: undefined })
  @Field(() => [Number], { nullable: true })
  public numberOfVideos?: number[]

  @prop({ type: () => [String], default: undefined })
  @Field(() => [String], { nullable: true })
  public firstNameInitials?: string[]

  @prop({ type: () => [Number], default: undefined })
  @Field(() => [Number], { nullable: true })
  public yearOfBirth?: number[]
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@Index({ state: 1 })
export class BatchNotification {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ enum: BatchNotificationState, default: BatchNotificationState.SCHEDULED })
  @Field()
  public state: BatchNotificationState

  @prop({ required: true })
  @Field()
  public title: string

  @prop({ required: true })
  @Field()
  public message: string

  @prop({ required: true })
  @Field()
  public type: PushNotificationType

  @prop({ default: false })
  @Field({ nullable: true })
  public forcePush: boolean

  @prop()
  @Field({ nullable: true })
  public destinationPath: string

  @prop({ default: 0 })
  @Field()
  public numberOfSent: number = 0

  @prop({ default: 0 })
  @Field()
  public totalUsers: number = 0

  @Field(() => BatchNotificationFilter)
  @prop({ required: true })
  public filter: BatchNotificationFilter

  @prop()
  @Field({ nullable: true })
  public scheduledAt?: Date
}

export const BatchNotificationModel = getModelForClass(BatchNotification)
