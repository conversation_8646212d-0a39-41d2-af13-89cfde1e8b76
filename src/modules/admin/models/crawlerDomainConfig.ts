import mongoose, { Types } from 'mongoose'
import { patchHistoryPlugin } from 'ts-patch-mongoose'
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql'

import { requestContext } from '@fastify/request-context'
import { getModelForClass, index, modelOptions, plugin, prop } from '@typegoose/typegoose'

export enum CrawlerEntity {
  PLAYER = 'player',
  GAME = 'game',
  LEAGUE = 'league',
  PLAYER_STATS = 'playerStats',
}

registerEnumType(CrawlerEntity, {
  name: 'CrawlerEntity',
})

export enum FetchType {
  Axios = 'axios',
  Puppeteer = 'puppeteer',
}

registerEnumType(FetchType, {
  name: 'FetchType',
})

@ObjectType()
export class ParamExtractor {
  @prop({ required: true })
  @Field({ nullable: false })
  selector: string

  @prop({ required: true })
  @Field({ nullable: false })
  queryParam: string
}

@ObjectType()
// idea is to have multiple configs per domain as we can crawl multiple entities
export class CrawlerUrlConfig {
  @prop({ required: true })
  @Field()
  // RegExp pattern for url - /ceskyhokej\.cz\/game\/detail\/.*/
  public urlPatternRegExp: string

  @prop({ type: String, enum: CrawlerEntity, required: true })
  @Field((_type) => CrawlerEntity)
  public entity: CrawlerEntity

  @prop({ type: [String], required: true })
  @Field(() => [String])
  public initialUrls: string[]

  @prop({ required: false })
  @Field({ nullable: true })
  public externalIdPatternRegExp?: string

  @prop({ type: [ParamExtractor], required: false })
  @Field(() => [ParamExtractor], { nullable: true })
  public paramExtractors?: ParamExtractor[]

  @prop({ type: [String], required: false })
  @Field(() => [String], { nullable: true })
  public allowedQueryParams?: string[]

  @prop({ required: false, default: false })
  @Field({ nullable: true, defaultValue: false })
  public addToQueue?: boolean

  @prop({ required: false })
  @Field({ nullable: true })
  public extractionScript?: string

  @prop({ type: String, enum: FetchType, required: false })
  @Field(() => FetchType, { nullable: true })
  public fetchType?: FetchType
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@plugin(patchHistoryPlugin, {
  getUser: () => {
    return requestContext.get('user')
  },
  omit: ['createdAt', 'updatedAt', '_id'],
})
@ObjectType()
@index({ domain: 1 }, { unique: true })
export class CrawlerDomainConfig {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ required: true })
  @Field()
  public domain: string

  @prop({ type: [CrawlerUrlConfig], required: true })
  @Field((_type) => [CrawlerUrlConfig])
  public urlConfigs: CrawlerUrlConfig[]

  @prop({ type: [String], required: false })
  @Field(() => [String], { nullable: true })
  public relevantUrlPatterns?: string[]

  @prop({ type: String, enum: FetchType, required: false })
  @Field(() => FetchType, { nullable: true })
  public fetchType: FetchType

  @prop({ required: false, default: true })
  @Field({ nullable: false, defaultValue: true })
  public enabled: boolean

  @prop({ required: false, default: 5 })
  @Field({ nullable: true, defaultValue: 5 })
  public parallelism?: number
}

export const CrawlerDomainConfigModel = getModelForClass(CrawlerDomainConfig, {
  existingMongoose: mongoose,
})
