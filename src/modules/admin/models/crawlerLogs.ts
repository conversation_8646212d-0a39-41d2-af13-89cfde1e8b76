import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'
import { GraphQLJSONObject } from 'graphql-scalars'

import { getModelForClass, index, modelOptions, prop } from '@typegoose/typegoose'

@ObjectType()
export class CrawlerLogsPatchOperation {
  @prop({ required: true })
  @Field()
  public op: string

  @prop({ required: true })
  @Field()
  public path: string

  @prop({ required: false })
  @Field(() => GraphQLJSONObject, { nullable: true })
  public value?: any
}

@ObjectType()
export class CrawlerLogsUser {
  @prop({ required: true })
  @Field()
  public id: string

  @prop({ required: true })
  @Field()
  public email: string

  @prop({ required: true })
  @Field()
  public admin: boolean

  @prop({ required: true })
  @Field()
  public role: string

  @prop({ required: true })
  @Field()
  public iat: number

  @prop({ required: true })
  @Field()
  public exp: number
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
    collection: 'history',
  },
})
@ObjectType()
@index({ collectionId: 1, createdAt: -1 })
@index({ modelName: 1, createdAt: -1 })
export class CrawlerLogs {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ required: true })
  @Field()
  public op: string

  @prop({ required: true })
  @Field()
  public modelName: string

  @prop({ required: true })
  @Field()
  public collectionName: string

  @prop({ type: Types.ObjectId, required: true })
  @Field()
  public collectionId: Types.ObjectId

  @prop({ type: [CrawlerLogsPatchOperation], required: true })
  @Field(() => [CrawlerLogsPatchOperation])
  public patch: CrawlerLogsPatchOperation[]

  @prop({ type: CrawlerLogsUser, required: true })
  @Field(() => CrawlerLogsUser)
  public user: CrawlerLogsUser

  @prop({ required: true })
  @Field()
  public version: number

  @Field()
  public createdAt: Date

  @Field()
  public updatedAt: Date
}

export const CrawlerLogsModel = getModelForClass(CrawlerLogs, {
  existingMongoose: mongoose,
})
