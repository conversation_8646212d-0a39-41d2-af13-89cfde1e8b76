import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'

import { CrawlerDomainConfig, CrawlerEntity } from './crawlerDomainConfig'

import { getModelForClass, index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@ObjectType()
export class DiscoveredUrlScraperInfo {
  @prop()
  @Field()
  public lastScrapedAt: Date

  @prop({ type: Date, required: false })
  @Field(() => Date, { nullable: true })
  public scheduledForScrapeAt?: Date | null

  @prop({ required: false })
  @Field({ nullable: true })
  public scrapeAttempts?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public unsuccessfulScrapes?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public scrapeSuccessful?: boolean

  @prop({ required: false })
  @Field({ nullable: true })
  public httpStatus?: number

  @prop({ required: false })
  @Field({ nullable: true })
  public errorMessage?: string
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ url: 1, crawlerDomainConfig: 1, entity: 1 }, { unique: true })
@index({ 'scraperInfo.lastScrapedAt': 1, entity: 1 })
@index({ 'scraperInfo.scheduledForScrapeAt': 1, entity: 1 })
@index({ entity: 1, scraperInfo: 1 })
export class DiscoveredUrl {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ required: true })
  @Field()
  public url: string

  @prop({ type: String, enum: CrawlerEntity, required: true })
  @Field((_type) => CrawlerEntity)
  public entity: CrawlerEntity

  @prop({ type: Types.ObjectId, ref: CrawlerDomainConfig, autopopulate: true })
  @Field((_type) => CrawlerDomainConfig, { nullable: true })
  public crawlerDomainConfig: Ref<CrawlerDomainConfig>

  @prop({ required: false })
  @Field({ nullable: true })
  public externalId?: string

  @prop({ required: false })
  @Field(() => DiscoveredUrlScraperInfo, { nullable: true })
  scraperInfo?: DiscoveredUrlScraperInfo
}

export const DiscoveredUrlModel = getModelForClass(DiscoveredUrl, {
  existingMongoose: mongoose,
})
