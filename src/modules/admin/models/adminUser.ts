import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'

import { VerificationCode } from '../../user/models/user'

import { getModelForClass, modelOptions, prop } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
export class AdminUser {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop()
  @Field({ nullable: true })
  public firstname?: string

  @prop()
  @Field({ nullable: true })
  public lastname?: string

  @prop()
  @Field({ nullable: false })
  public email: string

  @prop()
  public verificationCode?: VerificationCode
}

export const AdminUserModel = getModelForClass(AdminUser, {
  existingMongoose: mongoose,
})
