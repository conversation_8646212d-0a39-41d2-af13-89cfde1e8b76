import { Types } from 'mongoose'
import { Arg, Authorized, Field, Int, Mutation, ObjectType, Query, Resolver } from 'type-graphql'
import { Service } from 'typedi'

import { targets } from '../../helpers/countryTargets'
import { SystemRole } from '../../types/general'
import { UserModel } from '../user/models'
import { Role, User } from '../user/models/user'

import {
  AdminBatchNotificationMutations,
  AdminBatchNotificationQueries,
} from './resolvers/batchNotification.resolver'
import { AdminBoostMutations, AdminBoostQueries } from './resolvers/boost.resolver'
import {
  CrawlerDomainConfigMutations,
  CrawlerDomainConfigQueries,
} from './resolvers/crawlerDomainConfig.resolver'
import { AdminCrawlerDomainConfigHistoryQueries } from './resolvers/crawlerDomainConfigHistory.resolver'
import { AdminCrawlerRunStatsQueries } from './resolvers/crawlerRunStats.resolver'
import {
  AdminDiscoveredUrlMutations,
  AdminDiscoveredUrlQueries,
} from './resolvers/discoveredUrl.resolver'
import { AdminGameMutations, AdminGameQueries } from './resolvers/game.resolver'
import { AdminLeagueMutations, AdminLeagueQueries } from './resolvers/league.resolver'
import { AdminLeagueStatsQueries } from './resolvers/leagueStats.resolver'
import {
  AdminNotificationsMutations,
  AdminNotificationsQueries,
} from './resolvers/notifications.resolver'
import { AdminOrganizationMutations } from './resolvers/organization.resolver'
import { AdminTeamMutations, AdminTeamQueries } from './resolvers/team.resolver'
import { AdminUserMutations } from './resolvers/user.resolver'
import { AdminUserInfoQueries } from './resolvers/userInfo.resolver'
import { AdminUserStatsMutations, AdminUserStatsQueries } from './resolvers/userStats.resolver'
import { AdminWallVideosMutations, AdminWallVideosQueries } from './resolvers/wallVideo.resolver'
import { UserCount, UserStatisticsByYearAndCountry } from './types/admin'
import { AdminAgencyMutations } from './agency.resolver'

@ObjectType()
export class AdminMutation {
  @Field(() => AdminWallVideosMutations)
  async wallVideo(): Promise<AdminWallVideosMutations> {
    return new AdminWallVideosMutations()
  }

  @Field(() => AdminAgencyMutations)
  async agency(): Promise<AdminAgencyMutations> {
    return new AdminAgencyMutations()
  }

  @Field(() => AdminUserStatsMutations)
  async userStats(): Promise<AdminUserStatsMutations> {
    return new AdminUserStatsMutations()
  }

  @Field(() => AdminUserMutations)
  async user(): Promise<AdminUserMutations> {
    return new AdminUserMutations()
  }

  @Field(() => AdminLeagueMutations)
  async league(): Promise<AdminLeagueMutations> {
    return new AdminLeagueMutations()
  }

  @Field(() => AdminBoostMutations)
  async boost(): Promise<AdminBoostMutations> {
    return new AdminBoostMutations()
  }

  @Field(() => CrawlerDomainConfigMutations)
  async crawlerDomainConfig(): Promise<CrawlerDomainConfigMutations> {
    return new CrawlerDomainConfigMutations()
  }

  @Field(() => AdminGameMutations)
  async game(): Promise<AdminGameMutations> {
    return new AdminGameMutations()
  }

  @Field(() => AdminNotificationsMutations)
  async notifications(): Promise<AdminNotificationsMutations> {
    return new AdminNotificationsMutations()
  }

  @Field(() => AdminBatchNotificationMutations)
  async batchNotification(): Promise<AdminBatchNotificationMutations> {
    return new AdminBatchNotificationMutations()
  }

  @Field(() => AdminTeamMutations)
  async team(): Promise<AdminTeamMutations> {
    return new AdminTeamMutations()
  }

  @Field(() => AdminOrganizationMutations)
  async organization(): Promise<AdminOrganizationMutations> {
    return new AdminOrganizationMutations()
  }

  @Field(() => AdminDiscoveredUrlMutations)
  async discoveredUrl(): Promise<AdminDiscoveredUrlMutations> {
    return new AdminDiscoveredUrlMutations()
  }
}

@ObjectType()
export class AdminQuery {
  @Field(() => CrawlerDomainConfigQueries)
  async crawlerDomainConfig(): Promise<CrawlerDomainConfigQueries> {
    return new CrawlerDomainConfigQueries()
  }

  @Field(() => AdminWallVideosQueries)
  async wallVideo(): Promise<AdminWallVideosQueries> {
    return new AdminWallVideosQueries()
  }

  @Field(() => AdminUserStatsQueries)
  async userStats(): Promise<AdminUserStatsQueries> {
    return new AdminUserStatsQueries()
  }

  @Field(() => AdminGameQueries)
  async game(): Promise<AdminGameQueries> {
    return new AdminGameQueries()
  }

  @Field(() => AdminNotificationsQueries)
  async notifications(): Promise<AdminNotificationsQueries> {
    return new AdminNotificationsQueries()
  }

  @Field(() => AdminBatchNotificationQueries)
  async batchNotification(): Promise<AdminBatchNotificationQueries> {
    return new AdminBatchNotificationQueries()
  }

  @Field(() => AdminLeagueStatsQueries)
  async leagueStats(): Promise<AdminLeagueStatsQueries> {
    return new AdminLeagueStatsQueries()
  }

  @Field(() => AdminLeagueQueries)
  async league(): Promise<AdminLeagueQueries> {
    return new AdminLeagueQueries()
  }

  @Field(() => AdminTeamQueries)
  async team(): Promise<AdminTeamQueries> {
    return new AdminTeamQueries()
  }

  @Field(() => AdminUserInfoQueries)
  async userInfo(): Promise<AdminUserInfoQueries> {
    return new AdminUserInfoQueries()
  }

  @Field(() => User, { nullable: true })
  async user(@Arg('id') id: Types.ObjectId): Promise<User | null> {
    return await UserModel.findById(id).populate([
      { path: 'currentTeam', populate: { path: 'organization' } },
      { path: 'agentV2' },
    ])
  }

  @Field(() => UserCount, { nullable: true })
  async userCount(@Arg('days', () => Int, { nullable: true }) days?: number): Promise<UserCount> {
    let filter = {}

    if (days) {
      const dayFrom = new Date()
      dayFrom.setDate(dayFrom.getDate() - days)
      filter = { createdAt: { $gte: dayFrom } }
    }

    const playersCount = await UserModel.countDocuments({ role: 'PLAYER', ...filter })
    const sportsProfsCount = await UserModel.countDocuments({
      role: 'SPORTS_PROFESSIONAL',
      ...filter,
    })
    const parentsCount = await UserModel.countDocuments({ role: 'PARENT', ...filter })
    const sportsFansCount = await UserModel.countDocuments({ role: 'SPORTS_FAN', ...filter })
    const shadowPlayersCount = await UserModel.countDocuments({ role: 'SHADOW_PLAYER', ...filter })

    return {
      playersCount,
      sportsProfsCount,
      parentsCount,
      sportsFansCount,
      shadowPlayersCount,
    }
  }

  @Field(() => UserStatisticsByYearAndCountry)
  async userStatisticsByYearAndCountry(): Promise<UserStatisticsByYearAndCountry> {
    const totalUsers = await UserModel.countDocuments({ role: 'PLAYER' })

    const selectedCountries = ['US', 'CA', 'SE', 'FI', 'RU', 'CZ', 'SK', 'DE', 'CH', 'FR', 'AT']

    const usersByCountryAndYear = await UserModel.aggregate([
      {
        $match: {
          country: { $exists: true, $ne: '' },
          dateOfBirth: { $exists: true, $ne: null },
          role: { $in: [Role.PLAYER, null] },
        },
      },
      {
        $group: {
          _id: {
            country: {
              $cond: {
                if: { $in: ['$country', selectedCountries] },
                then: '$country',
                else: 'Other',
              },
            },
            year: {
              $cond: {
                if: { $lte: [{ $year: '$dateOfBirth' }, 2006] },
                then: '2006+',
                else: { $year: '$dateOfBirth' },
              },
            },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': -1 } },
    ])

    const usersCountWithTargets = usersByCountryAndYear.map((userDoc) => {
      const country = userDoc._id.country
      const year = userDoc._id.year
      const target = targets[country]?.[year] ?? 0
      const percentageOfTarget =
        target > 0 ? parseFloat(((userDoc.count / target) * 100).toFixed(2)) : 0

      return {
        country: userDoc._id.country,
        year: userDoc._id.year,
        count: userDoc.count,
        target: target,
        percentageOfTarget: percentageOfTarget,
      }
    })

    return {
      totalUsers,
      usersByCountryAndYear: usersCountWithTargets,
    }
  }

  @Field(() => AdminBoostQueries)
  async boost(): Promise<AdminBoostQueries> {
    return new AdminBoostQueries()
  }

  @Field(() => AdminDiscoveredUrlQueries)
  async discoveredUrl(): Promise<AdminDiscoveredUrlQueries> {
    return new AdminDiscoveredUrlQueries()
  }

  @Field(() => AdminCrawlerRunStatsQueries)
  async crawlerRunStats(): Promise<AdminCrawlerRunStatsQueries> {
    return new AdminCrawlerRunStatsQueries()
  }

  @Field(() => AdminCrawlerDomainConfigHistoryQueries)
  async crawlerDomainConfigHistory(): Promise<AdminCrawlerDomainConfigHistoryQueries> {
    return new AdminCrawlerDomainConfigHistoryQueries()
  }
}

@Resolver()
@Service()
export class AdminResolver {
  @Query(() => AdminQuery)
  @Authorized(SystemRole.ADMIN)
  admin() {
    return {}
  }
}

@Resolver()
@Service()
export class AdminMutationResolver {
  @Mutation(() => AdminMutation)
  @Authorized(SystemRole.ADMIN)
  admin() {
    return {}
  }
}
