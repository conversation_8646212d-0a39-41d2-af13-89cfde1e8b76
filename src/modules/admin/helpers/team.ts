import config from 'config'
import { Types } from 'mongoose'

import { League } from '../../stats/models/league'
import { Organization } from '../../stats/models/organization'
import { TeamModel } from '../../stats/models/team'

import { OpenAIEmbeddings } from '@langchain/openai'

export const createTeamEmbedding = async (id: Types.ObjectId) => {
  const openAIEmbeddings = new OpenAIEmbeddings({
    openAIApiKey: config.openai.apiKey,
    modelName: 'text-embedding-ada-002',
  })

  const team = await TeamModel.findById(id)

  await TeamModel.populate(team, [{ path: 'leagues' }, { path: 'organization' }])

  if (!team) {
    return null
  }

  const names = [
    team.name,
    ...(team.alternativeNames ? team.alternativeNames.map((an) => an.name) : []),
  ]

  const embedding = `${names.join(', ')} country: ${team.country} leagues: ${(team.leagues as League[]).map((l) => l.name).join(', ')} ${(team.organization as Organization)?.name ? `organization: ${(team.organization as Organization)?.name}` : ''}`

  const vector = await openAIEmbeddings.embedQuery(embedding)

  return vector
}
