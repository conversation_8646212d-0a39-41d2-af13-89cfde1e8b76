import config from 'config'

import { CreateLeagueInput, UpdateLeagueInput } from '../../stats/types/league'

import { OpenAIEmbeddings } from '@langchain/openai'

export const createLeagueEmbedding = async (input: CreateLeagueInput | UpdateLeagueInput) => {
  const embeddings = new OpenAIEmbeddings({
    openAIApiKey: config.openai.apiKey,
    modelName: 'text-embedding-ada-002',
  })

  const names = [
    input.name,
    ...(input.alternativeNames ? input.alternativeNames.map((an) => an.name) : []),
  ]
  const countries = input.countries?.join(', ')
  const age = input.level

  const vector = await embeddings.embedQuery(
    `${names.join(', ')} countries: ${countries}, age: ${age}`
  )

  return vector
}
