import { Types } from 'mongoose'
import Container from 'typedi'

import { slugifyString } from '../../../helpers/string'
import { findUserSlug } from '../../../helpers/user'
import { UserInfo, UserInfoModel } from '../../stats/models/userInfo'
import EliteProspectsService from '../../stats/services/eliteprospects/ep'
import { EPPlayer } from '../../stats/services/eliteprospects/graphql'
import { parseImperialHeight } from '../../stats/services/eliteprospects/helpers'
import { UserModel } from '../../user/models'
import {
  BioHandedness,
  BioPosition,
  CreationSource,
  ExternalLinkType,
  Role,
  User,
} from '../../user/models/user'

type ShadowUserInput = {
  // is nullable because we either already fetched the data or we will fetch it later
  epData?: EPPlayer | null
  country: string
  dateOfBirth?: string
  yearsOfBirth?: number[]
  agency?: Types.ObjectId
  source?: CreationSource
} & (
  | { firstname: string; lastname: string; name?: string }
  | { firstname?: string; lastname?: string; name: string }
)

export const createShadowUser = async (input: ShadowUserInput) => {
  if (!input.firstname || !input.lastname) {
    const nameParts = input.name?.split(' ') ?? []
    input.firstname = nameParts.slice(0, -1).join(' ') // Combine all but the last part as firstname
    input.lastname = nameParts.slice(-1).join(' ') // Use the last part as lastname
  }

  if (!input.epData) {
    const yearOfBirth = input.dateOfBirth ? new Date(input.dateOfBirth).getFullYear() : null
    let possibleYearsOfBirth = input.yearsOfBirth

    if (possibleYearsOfBirth?.length === 0 && yearOfBirth) {
      possibleYearsOfBirth = [yearOfBirth]
    }

    if (!possibleYearsOfBirth) {
      possibleYearsOfBirth = []
    }

    input.epData = await Container.get<EliteProspectsService>('ep.service').getUserData({
      firstname: input.firstname,
      lastname: input.lastname,
      country: input.country,
      possibleYearsOfBirth,
    })
  }

  const shadowUser = new User()
  shadowUser.role = Role.SHADOW_PLAYER
  shadowUser.firstname = input.firstname
  shadowUser.lastname = input.lastname
  shadowUser.epViews = input.epData?.views
  shadowUser.creationSource = input.source

  await findUserSlug(shadowUser)

  // we need to save non unique slug, to be able to find user by name
  shadowUser.shadowSlug = slugifyString(input.name)

  if (input.dateOfBirth && !isNaN(new Date(input.dateOfBirth).getTime())) {
    shadowUser.dateOfBirth = new Date(input.dateOfBirth)
  } else {
    shadowUser.possibleYearsOfBirth = input.yearsOfBirth
  }

  if (!input.dateOfBirth && input.epData?.dateOfBirth) {
    shadowUser.dateOfBirth = new Date(input.epData.dateOfBirth)
  }

  shadowUser.country = input.country
  // we need something random for email
  shadowUser.email = `${new Types.ObjectId().toHexString()}@shadow-import.com`

  const height = parseImperialHeight(input.epData?.height?.imperial)
  shadowUser.bio = {
    agency: input.agency,
    position:
      input.epData?.position === 'G'
        ? BioPosition.GOALTENDER
        : input.epData?.position === 'D'
          ? BioPosition.RIGHT_DEFENSIVE
          : BioPosition.CENTER,
    ...(height
      ? {
          height: {
            inches: height.inches ?? 0,
            feet: height.feet ?? 0,
            centimeters: input.epData?.height?.metrics ?? 0,
          },
        }
      : {}),
    ...(input.epData?.weight
      ? {
          weight: {
            pounds: input.epData?.weight.imperial ?? 0,
            kilograms: input.epData?.weight.metrics ?? 0,
          },
        }
      : {}),
    handedness: input.epData?.shoots === 'L' ? BioHandedness.LEFT : BioHandedness.RIGHT,
  }

  if (input.epData) {
    shadowUser.externalLinks = [
      {
        name: 'Elite Prospects',
        type: ExternalLinkType.ELITE_PROSPECTS,
        url: input.epData?.links.eliteprospectsUrl,
      },
    ]
  }

  await addExternalConnections(shadowUser)

  return await UserModel.create(shadowUser)
}

export const addExternalConnections = async (user: User, userInfo?: UserInfo[]) => {
  let userInfos =
    userInfo ??
    (await UserInfoModel.find({
      slug: user.nonUniqueSlug,
      $or: [
        {
          possibleYearsOfBirth: {
            $in: user.dateOfBirth ? [user.dateOfBirth.getFullYear()] : user.possibleYearsOfBirth,
          },
        },
        {
          dateOfBirth: user.dateOfBirth,
        },
      ],
      country: user.country,
    }))

  // if we didn't find any user info, try to find it without country
  if (userInfos.length === 0) {
    userInfos = await UserInfoModel.find({
      slug: user.nonUniqueSlug,
      $or: [
        {
          possibleYearsOfBirth: {
            $in: user.dateOfBirth ? [user.dateOfBirth.getFullYear()] : user.possibleYearsOfBirth,
          },
        },
        {
          dateOfBirth: user.dateOfBirth,
        },
      ],
    })
  }

  const existingConnections = new Set(
    (user.externalConnections || []).map((connection) => connection.externalId)
  )

  user.externalConnections = [
    ...(user.externalConnections || []),
    ...userInfos
      .filter((userInfo) => !existingConnections.has(userInfo.externalId))
      .map((userInfo) => ({
        crawlerDomainConfig: userInfo.crawlerDomainConfig,
        externalId: userInfo.externalId,
      })),
  ]
}
