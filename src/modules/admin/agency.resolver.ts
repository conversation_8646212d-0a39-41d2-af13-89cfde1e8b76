import { GraphQLError } from 'graphql'
import { Types } from 'mongoose'
import { Arg, Field, ObjectType } from 'type-graphql'

import { recalculateAgentsCount, recalculateClientsCount } from '../../helpers/agency'
import { UserModel } from '../user/models'
import { Agency, AgencyModel } from '../user/models/agency'
import { Role, SubRole, User } from '../user/models/user'

import {
  AddAgencyUserAlreadyBelongsToAgencyError,
  AddAgencyUserResponse,
  RemoveAgencyUserDoesNotBelongToAgencyError,
  RemoveAgencyUserResponse,
} from './types/agency'
import { AdminMutation } from '.'

import { DocumentType } from '@typegoose/typegoose'

@ObjectType()
export class AdminAgencyMutations extends AdminMutation {
  @Field(() => AddAgencyUserResponse)
  async addUser(
    @Arg('userId') userId: Types.ObjectId,
    @Arg('agencyId') agencyId: Types.ObjectId
  ): Promise<AddAgencyUserResponse> {
    const user = await UserModel.findById(userId)
    let agency = (await AgencyModel.findById(agencyId)) as DocumentType<Agency>

    if (!user || !agency) {
      throw new GraphQLError('User or agency not found')
    }

    if (user.bio?.agency?._id.toString() === agencyId.toString()) {
      return {
        agency: null,
        errors: [
          new AddAgencyUserAlreadyBelongsToAgencyError(
            'User already belongs to this agency',
            'agencyId'
          ),
        ],
      }
    }

    const oldAgency = await AgencyModel.findById(user.bio?.agency)

    if (user.bio) {
      user.bio.agency = agency
    } else {
      user.bio = {
        agency,
      }
    }

    if (user.role === Role.SPORTS_PROFESSIONAL && !user.subrole) {
      user.subrole = SubRole.AGENT
    }

    await user.save()

    agency = await updateAgencyCounts(user, agency, oldAgency)

    return {
      agency,
      errors: [],
    }
  }
  @Field(() => RemoveAgencyUserResponse)
  async removeAgencyUser(
    @Arg('userId') userId: Types.ObjectId,
    @Arg('agencyId') agencyId: Types.ObjectId
  ): Promise<RemoveAgencyUserResponse> {
    const user = await UserModel.findById(userId)
    let agency = (await AgencyModel.findById(agencyId)) as DocumentType<Agency>

    if (!user || !agency) {
      throw new GraphQLError('User or agency not found')
    }

    if (user.bio?.agency?._id.toString() !== agencyId.toString()) {
      return {
        agency: null,
        errors: [
          new RemoveAgencyUserDoesNotBelongToAgencyError(
            'User does not belong belong to this agency',
            'agencyId'
          ),
        ],
      }
    }

    if (user.bio) {
      user.bio.agency = undefined
    }

    if (user.subrole === SubRole.AGENT) {
      user.subrole = undefined
    }

    if (user.agentV2) {
      user.agentV2 = undefined
    }

    await user.save()

    if (user.role === Role.SPORTS_PROFESSIONAL) {
      agency = await recalculateAgentsCount(agency._id)
    } else {
      agency = await recalculateClientsCount(agency._id)
    }

    return {
      agency,
      errors: [],
    }
  }
}

export async function updateAgencyCounts(
  user: DocumentType<User>,
  agency: DocumentType<Agency>,
  oldAgency?: DocumentType<Agency> | null
) {
  let updatedAgency: DocumentType<Agency>
  // increment new agency user count
  if (user.role === Role.SPORTS_PROFESSIONAL) {
    updatedAgency = await recalculateAgentsCount(agency._id)
  } else {
    updatedAgency = await recalculateClientsCount(agency._id)
  }

  // decrement old agency user count
  if (oldAgency) {
    if (user.role === Role.SPORTS_PROFESSIONAL) {
      await recalculateAgentsCount(oldAgency._id)
    } else {
      await recalculateClientsCount(oldAgency._id)
    }
  }

  return updatedAgency
}
