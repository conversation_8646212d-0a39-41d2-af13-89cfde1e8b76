import { GraphQLError } from 'graphql'
import { flatten } from 'mongo-dot-notation'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { Pagination, SortOrder } from '../../../types/general'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { CrawlerDomainConfig, CrawlerDomainConfigModel } from '../models/crawlerDomainConfig'
import {
  CrawlerDomainConfigCreateResponse,
  CrawlerDomainConfigListResponse,
} from '../types/crawlerDomainConfig'
import {
  CrawlerDomainConfigCreateInput,
  CrawlerDomainConfigUpdateInput,
  CrawlerDomainConfigUpdateResponse,
} from '../types/crawlerDomainConfig'

@ObjectType()
export class CrawlerDomainConfigQueries {
  private readonly logger: pino.Logger

  private readonly pagination = new PaginationRepository()

  constructor() {
    this.logger = Container.get('app.logger')
  }

  @Field(() => CrawlerDomainConfig, { nullable: true })
  async get(@Arg('id') id: Types.ObjectId): Promise<CrawlerDomainConfig | null> {
    return CrawlerDomainConfigModel.findById(id)
  }

  @Field(() => CrawlerDomainConfigListResponse)
  async list(
    @Arg('pagination', { nullable: true }) pagination: Pagination
  ): Promise<CrawlerDomainConfigListResponse> {
    try {
      const response = new CrawlerDomainConfigListResponse()
      response.edges = []

      const result = await this.pagination.find(
        {
          _id: new Types.ObjectId(),
        },
        {},
        CrawlerDomainConfigModel,
        {
          fields: [{ field: '_id', order: SortOrder.DESC }],
        },
        this.pagination.mapPagination(pagination)
      )

      for (const config of result.nodes) {
        response.edges.push({
          node: config,
        })
      }
      response.pageInfo = result.pageInfo

      return response
    } catch (err) {
      this.logger.error('Failed to fetch all crawler domain configs.', err)
      throw new GraphQLError('Failed to fetch all crawler domain configs.')
    }
  }
}

@ObjectType()
export class CrawlerDomainConfigMutations {
  private readonly logger: pino.Logger

  constructor() {
    this.logger = Container.get('app.logger')
  }

  @Field(() => CrawlerDomainConfigCreateResponse)
  public async create(
    @Arg('input') input: CrawlerDomainConfigCreateInput
  ): Promise<CrawlerDomainConfigCreateResponse> {
    const response = new CrawlerDomainConfigCreateResponse()

    response.config = await CrawlerDomainConfigModel.create(input)

    return response
  }

  @Field(() => CrawlerDomainConfigUpdateResponse)
  public async update(
    @Arg('id') id: Types.ObjectId,
    @Arg('input') input: CrawlerDomainConfigUpdateInput
  ): Promise<CrawlerDomainConfigUpdateResponse> {
    const response = new CrawlerDomainConfigUpdateResponse()

    const data = flatten(input)
    const config = await CrawlerDomainConfigModel.findByIdAndUpdate(id, data, {
      new: true,
    })

    if (!config) {
      throw new GraphQLError('Failed to update crawler domain config.')
    }

    response.config = config

    return response
  }
}
