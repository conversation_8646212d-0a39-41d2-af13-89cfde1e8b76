import config from 'config'
import { GraphQLError } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, ObjectType } from 'type-graphql'
import Container from 'typedi'
import { z } from 'zod'

import { S3Service } from '../../../services/s3'
import { AlternativeName } from '../../stats/models/alternativeName'
import { League } from '../../stats/models/league'
import { TeamModel } from '../../stats/models/team'
import { TeamNotFoundError } from '../../stats/types/team'
import { RemoveTeamLogoResponse, SuggestedTeamResponse } from '../types/team'
import { ModelType } from '../types/userStats'

import { HumanMessage } from '@langchain/core/messages'
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai'

@ObjectType()
export class AdminTeamQueries {
  @Field(() => SuggestedTeamResponse)
  public async suggestTeams(
    @Arg('name') name: string,
    @Arg('league', { nullable: true }) league: string,
    @Arg('country', { nullable: true }) country: string,
    @Arg('exact', { nullable: true }) exact: boolean = false
  ): Promise<SuggestedTeamResponse> {
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: config.openai.apiKey,
      modelName: 'text-embedding-ada-002',
    })

    const inputEmbedding = await embeddings.embedQuery(
      `${name} ${league ? `League: ${league}` : ''} | ${country ? `Country: ${country}` : ''}`
    )

    let exactMatch: { exact: boolean; numCandidates?: number } = {
      exact: false,
      numCandidates: 100,
    }

    if (exact) {
      exactMatch = {
        exact: true,
      }
    }

    // FIXME: for some reason mongoose does not know about $vectorSearch
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline: any = [
      {
        $vectorSearch: {
          ...exactMatch,

          filter: { country },
          queryVector: inputEmbedding,
          path: 'vectorEmbedding',
          limit: 20,
          index: 'team_vector_index',
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          abbreviation: 1,
          countries: 1,
          level: 1,
          alternativeNames: 1,
          leagues: 1,
          organization: 1,
          score: {
            $meta: 'vectorSearchScore',
          },
        },
      },
    ]

    const results = await TeamModel.aggregate(pipeline)

    await TeamModel.populate(results, [{ path: 'leagues' }, { path: 'organization' }])

    const model = new ChatOpenAI({
      apiKey: config.openai.apiKey,
      modelName: ModelType.GPT4_O,
      temperature: 0,
    })

    const suggestedTeamFormat = z.object({
      match: z
        .object({
          _id: z.string(),
        })
        .nullable(),
    })

    const prompt = `We are looking for the best possible match for the team "${name}" League: ${league} in our database.
Below is a list of teams currently in our database, including their name, alternative names, country:

${results.map((result) => `${result.name} | Id: ${result._id} | Alternative Names: ${result.alternativeNames?.map((an: AlternativeName) => an.name).join(', ')} | Leagues: ${result.leagues?.map((l: League) => l.name).join(', ')} | Country: ${result.country}`).join('\n')}

Based on the provided information, determine the best possible match for "${name}" from our database.
Consider age category, most of the time the team is part of a league.

Only select a match if you are confident it corresponds to the given team.
If no suitable match exists and you are certain the team is missing from our database, return null.
`

    const response = await model
      .withStructuredOutput(suggestedTeamFormat)
      .invoke([new HumanMessage(prompt)])

    return {
      bestMatch: results.find((r) => r._id.toString() === response.match?._id) ?? null,
      suggestions: results,
    }
  }
}

@ObjectType()
export class AdminTeamMutations {
  private readonly logger: pino.Logger
  private readonly s3: S3Service

  constructor() {
    this.logger = Container.get('app.logger')
    this.s3 = Container.get(S3Service)
  }

  @Field(() => RemoveTeamLogoResponse)
  public async removeTeamLogo(
    @Arg('id', () => Types.ObjectId) id: Types.ObjectId
  ): Promise<RemoveTeamLogoResponse> {
    const response = new RemoveTeamLogoResponse()
    response.errors = []

    try {
      const team = await TeamModel.findById(id)
      if (!team) {
        response.success = false
        response.errors = [new TeamNotFoundError('Team not found', 'removeLogo')]
        return response
      }

      if (team.logo) {
        this.s3
          .remove(team.logo)
          .catch((err) => this.logger.error('Failed to remove team logo', err))
      }

      if (team.ogLogo) {
        this.s3
          .remove(team.ogLogo)
          .catch((err) => this.logger.error('Failed to remove OG team logo', err))
      }

      if (team.logoUnconfirmed) {
        this.s3
          .remove(team.logoUnconfirmed)
          .catch((err) => this.logger.error('Failed to remove unconfirmed team logo', err))
      }

      await TeamModel.updateOne(
        { _id: id },
        {
          $set: {
            logo: null,
            ogLogo: null,
            logoUnconfirmed: null,
          },
        }
      )
      response.success = true
    } catch (err) {
      this.logger.error('Unexpected error while removing team logo', err)
      throw new GraphQLError('Failed to remove team logo')
    }

    return response
  }
}
