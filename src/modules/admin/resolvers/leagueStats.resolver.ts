import { Arg, Field, ObjectType } from 'type-graphql'

import { LeagueStatsModel } from '../../stats/models/leagueStats/leagueStats'
import { AdminLeagueStatsFilter, AdminLeagueStatsResponse } from '../types/leagueStats'

@ObjectType()
export class AdminLeagueStatsQueries {
  @Field(() => AdminLeagueStatsResponse)
  public async list(
    @Arg('filter', { nullable: false }) filter: AdminLeagueStatsFilter
  ): Promise<AdminLeagueStatsResponse> {
    const response = new AdminLeagueStatsResponse()
    response.edges = []

    const leagueStats = await LeagueStatsModel.find({
      league: filter.leagueId,
    })
      .sort({ group: 1, standing: 1 })
      .populate(['team', 'league'])

    for (const node of leagueStats) {
      response.edges.push({
        node,
      })
    }

    return response
  }
}
