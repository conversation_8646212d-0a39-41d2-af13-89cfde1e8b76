import { GraphQLError } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, Int, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { SortOrder } from '../../../types/general'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { CrawlerRunStats, CrawlerRunStatsModel } from '../models/crawlerRunStats'
import { CrawlerRunStatsFilter, CrawlerRunStatsResponse } from '../types/crawlerRunStats'

@ObjectType()
export class AdminCrawlerRunStatsQueries {
  private readonly logger: pino.Logger
  private readonly pagination = new PaginationRepository()

  constructor() {
    this.logger = Container.get('app.logger')
  }

  @Field(() => CrawlerRunStatsResponse)
  async list(
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string,
    @Arg('filter', { nullable: true }) filter?: CrawlerRunStatsFilter
  ): Promise<CrawlerRunStatsResponse> {
    try {
      const response = new CrawlerRunStatsResponse()
      response.edges = []

      const query: Record<string, unknown> = {}

      if (filter?.crawlerDomainConfig) {
        query.crawlerDomainConfig = filter.crawlerDomainConfig
      }

      const stats = await this.pagination.find(
        {
          _id: new Types.ObjectId(),
          startedAt: new Date(),
        },
        query,
        CrawlerRunStatsModel,
        {
          fields: [{ field: 'startedAt', order: SortOrder.DESC }],
        },
        this.pagination.mapPagination({ first, after, before }),
        ['crawlerDomainConfig']
      )

      for (const stat of stats.nodes) {
        response.edges.push({
          node: stat,
        })
      }

      response.pageInfo = stats.pageInfo

      return response
    } catch (err) {
      this.logger.error('Failed to fetch crawler run stats.', err)
      throw new GraphQLError('Failed to fetch crawler run stats.')
    }
  }

  @Field(() => CrawlerRunStats, { nullable: true })
  async get(@Arg('id') id: string): Promise<CrawlerRunStats | null> {
    try {
      return CrawlerRunStatsModel.findById(id).lean().populate('crawlerDomainConfig')
    } catch (err) {
      this.logger.error(`Failed to fetch crawler run stat with ID ${id}.`, err)
      throw new GraphQLError('Failed to fetch crawler run stat.')
    }
  }
}
