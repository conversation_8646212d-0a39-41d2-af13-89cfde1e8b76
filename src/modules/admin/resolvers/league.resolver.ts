import config from 'config'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { PipelineStage, Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, Info, ObjectType, Resolver } from 'type-graphql'
import { Container } from 'typedi'
import { z } from 'zod'

import { MediaConverter } from '../../../services/mediaConverter'
import { S3Service } from '../../../services/s3'
import { FailedToCreatePresignedUrl, Pagination, SortOrder } from '../../../types/general'
import { createStatusFilter } from '../../stats/helpers/league'
import { AlternativeName } from '../../stats/models/alternativeName'
import { League, LeagueModel } from '../../stats/models/league'
import { UserStatsModel } from '../../stats/models/userStats'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import {
  AdminLeaguesFilter,
  AdminLeaguesResponse,
  LeagueNotFoundError,
  RemoveLeagueLogoResponse,
  SuggestedLeagueResponse,
  UploadLeagueLogoInput,
  UploadLeagueLogoResponse,
} from '../types/league'
import { ModelType } from '../types/userStats'

import { HumanMessage } from '@langchain/core/messages'
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai'

@ObjectType()
export class AdminLeagueQueries {
  private readonly logger: pino.Logger

  constructor() {
    this.logger = Container.get('app.logger')
  }

  private readonly paginationRepository = new PaginationRepository()

  @Field(() => League, { nullable: true })
  public async get(@Arg('id') id: Types.ObjectId): Promise<League | null> {
    try {
      return await LeagueModel.findById(id)
    } catch (error) {
      this.logger.error('Failed to find league:', error.message)
      return null
    }
  }

  @Field(() => AdminLeaguesResponse)
  public async list(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter: AdminLeaguesFilter
  ): Promise<AdminLeaguesResponse> {
    const response = new AdminLeaguesResponse()
    response.edges = []

    const pipeline: PipelineStage[] = []
    const matchStage: PipelineStage.Match['$match'] = {}

    if (filter?.searchQuery) {
      matchStage.name = { $regex: new RegExp(`${filter.searchQuery}.*`, 'i') }
    }

    if (filter?.country) {
      matchStage.countries = { $in: [filter.country] }
    }

    if (filter?.genderCategory) {
      matchStage.genderCategory = filter.genderCategory
    }

    if (filter?.level) {
      matchStage.level = filter.level
    }

    if (filter?.type) {
      matchStage.type = filter.type
    }

    if (filter?.checked !== undefined) {
      Object.assign(matchStage, createStatusFilter(filter.checked, 'checked'))
    }

    if (filter?.scraped !== undefined) {
      Object.assign(matchStage, createStatusFilter(filter.scraped, 'scraped'))
    }

    if (Object.keys(matchStage).length > 0) {
      pipeline.push({ $match: matchStage })
    }

    const leagues = await this.paginationRepository.aggregate({
      templateSortObject: { _id: new Types.ObjectId() },
      pipeline,
      model: LeagueModel,
      sorting: {
        fields: [{ field: '_id', order: SortOrder.DESC }],
      },
      pagination: this.paginationRepository.mapPagination(pagination),
    })

    const leagueIds = leagues.nodes.map((league) => league._id)
    const userStatsCount = await UserStatsModel.aggregate([
      {
        $match: {
          league: { $in: leagueIds },
        },
      },
      { $group: { _id: '$league', count: { $sum: 1 } } },
    ])

    for (const league of leagues.nodes) {
      const teamStats = userStatsCount.find((count) => count._id.equals(league._id))
      response.edges.push({
        node: league,
        used: teamStats ? teamStats.count > 0 : false,
      })
    }

    response.pageInfo = leagues?.pageInfo

    return response
  }

  @Field(() => SuggestedLeagueResponse)
  public async suggestLeagues(
    @Arg('name') name: string,
    @Arg('country', { nullable: true }) country: string,
    @Arg('exact', { nullable: true }) exact: boolean = false
  ): Promise<SuggestedLeagueResponse> {
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: config.openai.apiKey,
      modelName: 'text-embedding-ada-002',
    })

    const inputEmbedding = await embeddings.embedQuery(
      `${name} ${country ? `Country: ${country}` : ''}`
    )

    let exactMatch: { exact: boolean; numCandidates?: number } = {
      exact: false,
      numCandidates: 100,
    }

    if (exact) {
      exactMatch = {
        exact: true,
      }
    }

    // FIXME: for some reason mongoose does not know about $vectorSearch
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline: any = [
      {
        $vectorSearch: {
          ...exactMatch,

          filter: { countries: { $in: [country] } },
          queryVector: inputEmbedding,
          path: 'vectorEmbedding',
          limit: 20,
          index: 'league_vector_index',
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          countries: 1,
          level: 1,
          alternativeNames: 1,
          score: {
            $meta: 'vectorSearchScore',
          },
        },
      },
    ]
    const results = await LeagueModel.aggregate(pipeline)

    const model = new ChatOpenAI({
      apiKey: config.openai.apiKey,
      modelName: ModelType.GPT4_O,
      temperature: 0,
    })

    const suggestedLeagueFormat = z.object({
      match: z
        .object({
          _id: z.string(),
        })
        .nullable(),
    })

    const prompt = `We are looking for the best possible match for the league "${name}" in our database.
Below is a list of leagues currently in our database, including their name, alternative names, country, category, and gender:

${results.map((result) => `${result.name} | Id: ${result._id} | Alternative Names: ${result.alternativeNames.map((an: AlternativeName) => an.name).join(', ')} | Country: ${result.countries.join(', ')} | Level: ${result.level} | Gender category: ${result.LeagueGenderCategory}`).join('\n')}

Age groups:

U20	17–20 years	Juniori
U18	15–18 years	Starsi dorostenci
U16	14–16 years	Mladsi dorostenci
U15	13–15 years	Previously known as "9. třída" (9th Grade); note that the U15 category was discontinued after the 2010–11 season but was reinstated in the 2021–22 season
U14	12–14 years	Previously known as "8. třída" (8th Grade)
U13	11–13 years	Previously known as "7. třída" (7th Grade); this category has not been contested since the 2010–11 season
U12	10–12 years	Previously known as "6. třída" (6th Grade)
U11	9–11 years	Previously known as "5. třída" (5th Grade); the national championship for this category was abolished after the 2008–09 season
U10	8–10 years	Previously known as "4. třída" (4th Grade)

Extraliga is highest league usually without any number after group like U20
Liga usually means second highest league like U20 2
Regionalni liga usually means lowest league like U20 3

Based on the provided information, determine the best possible match for "${name}" from our database.

Pay attention to gender differences in league names (e.g., leagues for males vs. females).
Only select a match if you are confident it corresponds to the given league.
If no suitable match exists and you are certain the league is missing from our database, return null.
      `

    const response = await model
      .withStructuredOutput(suggestedLeagueFormat)
      .invoke([new HumanMessage(prompt)])

    return {
      bestMatch: results.find((r) => r._id.toString() === response.match?._id) ?? null,
      suggestions: results,
    }
  }
}

@Resolver()
@ObjectType()
export class AdminLeagueMutations {
  private readonly logger: pino.Logger
  private readonly s3: S3Service
  private readonly mediaConverter: MediaConverter

  constructor() {
    this.s3 = Container.get(S3Service)
    this.logger = Container.get('app.logger')
    this.mediaConverter = Container.get(MediaConverter)
  }

  @Field(() => UploadLeagueLogoResponse)
  async uploadLeagueLogo(
    @Arg('input', { nullable: true }) input: UploadLeagueLogoInput,
    @Arg('id', (_type) => Types.ObjectId) id: Types.ObjectId,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadLeagueLogoResponse> {
    const response = new UploadLeagueLogoResponse()

    if (input) {
      if (input.uploadSuccessful) {
        response.league = await LeagueModel.findById(id)

        if (response.league?.logoUnconfirmed) {
          if (response.league.logo) {
            this.s3
              .remove(response.league.logo)
              .catch((err) => this.logger.error('Failed to remove old league logo', err))
          }

          const ogImageKey = await this.mediaConverter.createAndUploadOGImage(
            response.league.logoUnconfirmed
          )

          response.league = await LeagueModel.findOneAndUpdate(
            { _id: id },
            { logo: response.league.logoUnconfirmed, logoUnconfirmed: null, ogLogo: ogImageKey }
          )
        }

        return response
      } else {
        response.league = await LeagueModel.findOneAndUpdate({ _id: id }, { logoUnconfirmed: null })
        return response
      }
    }

    try {
      const logoKey = `images/leagues/${id}/logo/${new Date().getTime()}.png`

      response.preSignedUrl = await this.s3.createPresignedUrl(logoKey, 'image/png')

      response.league = await LeagueModel.findOneAndUpdate(
        { _id: id },
        { logoUnconfirmed: logoKey }
      )
    } catch (err) {
      this.logger.error("Failed to create preSignedUrl for league's logo:", err)
      response.errors = [
        new FailedToCreatePresignedUrl(`Failed to create preSignedUrl`, info.path.key.toString()),
      ]
    }

    return response
  }

  @Field(() => RemoveLeagueLogoResponse)
  public async removeLeagueLogo(
    @Arg('id', () => Types.ObjectId) id: Types.ObjectId
  ): Promise<RemoveLeagueLogoResponse> {
    const response = new RemoveLeagueLogoResponse()
    response.errors = []

    try {
      const league = await LeagueModel.findById(id)
      if (!league) {
        response.success = false
        response.errors = [new LeagueNotFoundError('League not found', 'removeLogo')]
        return response
      }

      if (league.logo) {
        this.s3
          .remove(league.logo)
          .catch((err) => this.logger.error('Failed to remove league logo', err))
      }

      if (league.ogLogo) {
        this.s3
          .remove(league.ogLogo)
          .catch((err) => this.logger.error('Failed to remove OG league logo', err))
      }

      if (league.logoUnconfirmed) {
        this.s3
          .remove(league.logoUnconfirmed)
          .catch((err) => this.logger.error('Failed to remove unconfirmed league logo', err))
      }

      await LeagueModel.updateOne(
        { _id: id },
        {
          $set: {
            logo: null,
            ogLogo: null,
            logoUnconfirmed: null,
          },
        }
      )
      response.success = true
    } catch (err) {
      this.logger.error('Unexpected error while removing league logo', err)
      throw new GraphQLError('Failed to remove league logo')
    }

    return response
  }
}
