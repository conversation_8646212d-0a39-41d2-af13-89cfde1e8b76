import { GraphQLError } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { S3Service } from '../../../services/s3'
import { OrganizationModel } from '../../stats/models/organization'
import { OrganizationNotFoundError, RemoveOrganizationLogoResponse } from '../types/organization'

@ObjectType()
export class AdminOrganizationMutations {
  private readonly logger: pino.Logger
  private readonly s3: S3Service

  constructor() {
    this.logger = Container.get('app.logger')
    this.s3 = Container.get(S3Service)
  }

  @Field(() => RemoveOrganizationLogoResponse)
  public async removeOrganizationLogo(
    @Arg('id', () => Types.ObjectId) id: Types.ObjectId
  ): Promise<RemoveOrganizationLogoResponse> {
    const response = new RemoveOrganizationLogoResponse()
    response.errors = []

    try {
      const organization = await OrganizationModel.findById(id)
      if (!organization) {
        response.success = false
        response.errors = [new OrganizationNotFoundError('Organization not found', 'removeLogo')]
        return response
      }

      if (organization.logo) {
        this.s3
          .remove(organization.logo)
          .catch((err) => this.logger.error('Failed to remove organization logo', err))
      }

      if (organization.ogLogo) {
        this.s3
          .remove(organization.ogLogo)
          .catch((err) => this.logger.error('Failed to remove OG organization logo', err))
      }

      if (organization.logoUnconfirmed) {
        this.s3
          .remove(organization.logoUnconfirmed)
          .catch((err) => this.logger.error('Failed to remove unconfirmed organization logo', err))
      }

      await OrganizationModel.updateOne(
        { _id: id },
        {
          $set: {
            logo: null,
            ogLogo: null,
            logoUnconfirmed: null,
          },
        }
      )
      response.success = true
    } catch (err) {
      this.logger.error('Unexpected error while removing organization logo', err)
      throw new GraphQLError('Failed to remove organization logo')
    }

    return response
  }
}
