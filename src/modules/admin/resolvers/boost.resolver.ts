import { GraphQLError } from 'graphql'
import { FilterQuery, PipelineStage, Types } from 'mongoose'
import pino from 'pino'
import Stripe from 'stripe'
import { Arg, Field, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { StripeService } from '../../../services/stripe'
import { Pagination, SortOrder } from '../../../types/general'
import {
  isStripeAccountVerificationPending,
  stripeAccountNeedsDocumentVerification,
} from '../../../webhooks/connect.helpers'
import { UserModel } from '../../user/models'
import { Boost, BoostModel } from '../../user/models/boost'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { BoostMessageResponse, BoostNotFoundError, BoostsResponse } from '../../user/types/boost'
import { AdminBoostFilter, SyncStripeAccountResponse } from '../types/boost'

@ObjectType()
export class AdminBoostQueries {
  private readonly logger: pino.Logger

  constructor() {
    this.logger = Container.get('app.logger')
  }

  private readonly paginationRepository = new PaginationRepository()

  @Field(() => BoostsResponse)
  async list(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter?: AdminBoostFilter
  ) {
    try {
      const response = new BoostsResponse()
      response.edges = []

      const pipeline: PipelineStage[] = []
      const queryFilter: FilterQuery<Boost> = {}

      if (filter?.currency) {
        queryFilter.currency = filter.currency
      }

      if (filter?.state) {
        queryFilter.state = filter.state
      }

      if (filter?.user) {
        queryFilter.user = filter.user
      }

      pipeline.push({ $match: queryFilter })

      const boosts = await this.paginationRepository.aggregate({
        templateSortObject: { _id: new Types.ObjectId() },
        pipeline,
        model: BoostModel,
        sorting: { fields: [{ field: '_id', order: SortOrder.DESC }] },
        pagination: this.paginationRepository.mapPagination(pagination),
      })

      await BoostModel.populate(boosts.nodes, { path: 'user' })

      for (const boost of boosts.nodes) {
        response.edges.push({
          node: boost,
        })
      }

      response.pageInfo = boosts.pageInfo

      return response
    } catch (err) {
      this.logger.error('Failed to fetch all boosts.', err)
      throw new GraphQLError('Failed to fetch all boosts. Please try again later.')
    }
  }
}

@ObjectType()
export class AdminBoostMutations {
  private readonly logger: pino.Logger
  private readonly stripe: StripeService

  constructor() {
    this.logger = Container.get('app.logger')
    this.stripe = Container.get(StripeService)
  }

  @Field(() => BoostMessageResponse)
  public async updateBoostMessage(
    @Arg('boostId') boostId: Types.ObjectId,
    @Arg('message') message: string
  ): Promise<BoostMessageResponse> {
    try {
      let boost = await BoostModel.findById(boostId)

      if (!boost) {
        this.logger.error(`Boost with ID ${boostId} not found`)
        return {
          boost: null,
          errors: [new BoostNotFoundError('Boost not found', 'boostId')],
        }
      }

      boost.message = message
      boost = await boost.save()

      return {
        boost,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to update boost message.', err)
      throw new GraphQLError('Failed to update boost message. Please try again later.')
    }
  }

  @Field(() => SyncStripeAccountResponse)
  public async syncAccount(@Arg('accountId') accountId: string) {
    const account = await this.stripe.getConnectAccount(accountId)

    const verificationNeeded = stripeAccountNeedsDocumentVerification(account)
    const verificationPending = isStripeAccountVerificationPending(account)
    const accountUpdateErrors = account.requirements?.errors as Stripe.Account.Requirements.Error[]

    // TODO send push notification to user
    try {
      await UserModel.updateOne(
        { stripeAccountId: account.id },
        { verificationNeeded, verificationPending, accountUpdateErrors }
      )

      return {
        success: true,
      }
    } catch (err) {
      this.logger.error('Failed to update user account.', err)

      return {
        success: false,
        errors: [{ message: 'Failed to update user account.' }],
      }
    }
  }
}
