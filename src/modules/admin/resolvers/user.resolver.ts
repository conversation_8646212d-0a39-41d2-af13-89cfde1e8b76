import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, Info, Int, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { recalculateClientsCount } from '../../../helpers/agency'
import { slugifyString } from '../../../helpers/string'
import { userCleanup } from '../../../helpers/userCleanup'
import { MediaConverter } from '../../../services/mediaConverter'
import { S3Service } from '../../../services/s3'
import { SNSService } from '../../../services/sns'
import { FailedToCreatePresignedUrl } from '../../../types/general'
import { UserInfoModel } from '../../stats/models/userInfo'
import EliteProspectsService from '../../stats/services/eliteprospects/ep'
import { EPPlayer } from '../../stats/services/eliteprospects/graphql'
import { UserModel } from '../../user/models'
import { Agency, AgencyModel } from '../../user/models/agency'
import { CreationSource, Role } from '../../user/models/user'
import { UploadAvatarInput, UploadAvatarResponse } from '../../user/types/avatar'
import { UserNotFoundError } from '../../user/types/user'
import { addExternalConnections, createShadowUser } from '../helpers/shadowUser'
import {
  AddExternalConnectionResponse,
  AgencyNotFoundError,
  EPUserNotFoundError,
  InvalidEPPlayerDataError,
  MarkUserAsDeletedResponse,
  RemoveExternalConnectionResponse,
  SyncAgencyClientsResponse,
  UserAlreadyMarkedForDeletionError,
  UserInfoNotFoundError,
} from '../types/user'

@ObjectType()
export class AdminUserMutations {
  private readonly epService: EliteProspectsService
  private readonly logger: pino.Logger
  private readonly s3Service: S3Service
  private readonly mediaConverter: MediaConverter
  private readonly sns: SNSService

  constructor() {
    this.epService = Container.get('ep.service')
    this.logger = Container.get('app.logger')
    this.s3Service = Container.get('s3.service')
    this.mediaConverter = Container.get(MediaConverter)
    this.sns = Container.get(SNSService)
  }

  @Field(() => SyncAgencyClientsResponse)
  async syncAgencyClients(
    @Arg('epAgencyId', (_type) => Int) epAgencyId: number,
    @Arg('agencyId') agencyId: Types.ObjectId
  ): Promise<SyncAgencyClientsResponse> {
    const response = new SyncAgencyClientsResponse()
    response.errors = []

    const agency = await AgencyModel.findById(agencyId)

    if (!agency) {
      response.errors = [new AgencyNotFoundError('Agency not found', 'syncAgencyClients')]

      return response
    }

    const epClients = await this.epService.getAgencyClients(epAgencyId)

    for (const epClient of epClients) {
      await this.syncAgencyPlayer(epClient, agency, response)
    }

    // recalculate clientsCount
    const counts = await recalculateClientsCount(agencyId)

    agency.shadowClientsCount = counts.shadowClientsCount
    agency.clientsCount = counts.clientsCount

    response.agency = agency

    return response
  }

  @Field(() => SyncAgencyClientsResponse)
  async syncAgencyClient(
    @Arg('epLink', () => String) epLink: string,
    @Arg('agencyId') agencyId: Types.ObjectId
  ): Promise<SyncAgencyClientsResponse> {
    const response = new SyncAgencyClientsResponse()
    response.errors = []

    const agency = await AgencyModel.findById(agencyId)

    if (!agency) {
      response.errors = [new AgencyNotFoundError('Agency not found', 'syncAgencyClients')]

      return response
    }

    const match = epLink.match(/(?:eliteprospects\.com\/player\/)(\d+)(?:\/|$)/)
    const extractedId = match?.[1]

    if (!extractedId) {
      response.errors.push(
        new InvalidEPPlayerDataError(`Invalid epLink format: ${epLink}`, 'syncAgencyClients')
      )
      return response
    }

    const user = await this.epService.getPlayer(extractedId)

    if (user) {
      await this.syncAgencyPlayer(user, agency, response)
    } else {
      response.errors = [
        new EPUserNotFoundError(`User with epLink ${epLink} not found`, 'syncAgencyClients'),
      ]

      return response
    }

    const counts = await recalculateClientsCount(agencyId)

    agency.shadowClientsCount = counts.shadowClientsCount
    agency.clientsCount = counts.clientsCount

    response.agency = agency
    return response
  }

  private async syncAgencyPlayer(
    playerData: EPPlayer,
    agency: Agency,
    response: SyncAgencyClientsResponse
  ): Promise<void> {
    if (
      !playerData.name ||
      !playerData.firstName ||
      !playerData.lastName ||
      !playerData.nationality?.iso_3166_1_alpha_2 ||
      !playerData.yearOfBirth
    ) {
      this.logger.warn(`Invalid playerData data: ${JSON.stringify(playerData)}`)

      response.errors?.push(
        new InvalidEPPlayerDataError(
          `Player ${playerData.name} (${playerData.id}) has invalid data, (name, nationality or yearOfBirth missing)`,
          'syncAgencyClients'
        )
      )

      return
    }

    const existingUser = await UserModel.findOne({
      slug: slugifyString(playerData.name),
      country: playerData.nationality.iso_3166_1_alpha_2,
      $expr: {
        $eq: [{ $year: '$dateOfBirth' }, playerData.yearOfBirth],
      },
    })

    if (existingUser) {
      if (existingUser.public === false || existingUser.markedForDeletion) {
        return
      }

      if (!existingUser.bio) {
        existingUser.bio = {}
      }

      if (existingUser.role === Role.SHADOW_PLAYER) {
        existingUser.epViews = playerData.views
      }

      existingUser.bio.agency = agency._id
      await existingUser.save()
    } else {
      await createShadowUser({
        epData: playerData,
        firstname: playerData.firstName,
        lastname: playerData.lastName,
        name: playerData.name,
        country: playerData.nationality.iso_3166_1_alpha_2,
        dateOfBirth: playerData.dateOfBirth,
        yearsOfBirth: [playerData.yearOfBirth],
        agency: agency._id,
        source: CreationSource.AdminSyncAgency,
      })
    }
    return
  }

  @Field(() => MarkUserAsDeletedResponse)
  async markUserAsDeleted(
    @Arg('userId') userId: Types.ObjectId
  ): Promise<MarkUserAsDeletedResponse> {
    const response = new MarkUserAsDeletedResponse()
    response.errors = []

    try {
      const user = await UserModel.findById(userId)

      if (!user) {
        response.errors = [new UserNotFoundError('User not found', 'markUserAsDeleted')]
        response.success = false
        return response
      }

      if (user.markedForDeletion) {
        response.errors = [
          new UserAlreadyMarkedForDeletionError(
            'User is already marked for deletion',
            'markUserAsDeleted'
          ),
        ]
        response.success = false
        return response
      }

      const updatedUser = await userCleanup(user, this.sns)

      if (!updatedUser) {
        throw new GraphQLError('Failed to update and retrieve user', {
          extensions: { code: 'NOT_FOUND' },
        })
      }

      response.success = true
      response.user = updatedUser
    } catch (error) {
      this.logger.error('Failed to mark user as deleted.', error)
      throw new GraphQLError('Failed to mark user as deleted.')
    }

    return response
  }

  @Field(() => AddExternalConnectionResponse)
  async addExternalConnection(
    @Arg('userId') userId: Types.ObjectId,
    @Arg('externalId') externalId: string,
    @Arg('crawlerDomainConfigId') crawlerDomainConfigId: Types.ObjectId
  ): Promise<AddExternalConnectionResponse> {
    const response = new AddExternalConnectionResponse()
    response.errors = []

    try {
      const user = await UserModel.findById(userId)
      if (!user) {
        response.errors = [new UserNotFoundError('User not found', 'addExternalConnection')]
        response.success = false
        return response
      }

      const userInfo = await UserInfoModel.findOne({
        externalId,
        crawlerDomainConfig: crawlerDomainConfigId,
      })

      if (!userInfo) {
        response.errors = [new UserInfoNotFoundError('UserInfo not found', 'addExternalConnection')]
        response.success = false
        return response
      }

      await addExternalConnections(user, [userInfo])
      await user.save()

      response.success = true
    } catch (error) {
      this.logger.error('Failed to add external connection.', error)
      throw new GraphQLError('Failed to add external connection.')
    }

    return response
  }

  @Field(() => RemoveExternalConnectionResponse)
  async removeExternalConnection(
    @Arg('userId') userId: Types.ObjectId,
    @Arg('externalId') externalId: string,
    @Arg('crawlerDomainConfigId') crawlerDomainConfigId: Types.ObjectId
  ): Promise<RemoveExternalConnectionResponse> {
    const response = new RemoveExternalConnectionResponse()
    response.errors = []

    try {
      const user = await UserModel.findById(userId)
      if (!user) {
        response.errors = [new UserNotFoundError('User not found', 'removeExternalConnection')]
        response.success = false
        return response
      }

      const userInfo = await UserInfoModel.findOne({
        externalId,
        crawlerDomainConfig: crawlerDomainConfigId,
      })
      if (!userInfo) {
        response.errors = [
          new UserInfoNotFoundError('UserInfo not found', 'removeExternalConnection'),
        ]
        response.success = false
        return response
      }

      user.externalConnections = user.externalConnections.filter(
        (ec) =>
          !(
            ec.externalId === externalId &&
            ec.crawlerDomainConfig.toString() === crawlerDomainConfigId.toString()
          )
      )

      await user.save()

      response.success = true
    } catch (error) {
      this.logger.error('Failed to remove external connection.', error)
      throw new GraphQLError('Failed to remove external connection.')
    }

    return response
  }

  @Field(() => UploadAvatarResponse)
  async uploadAvatar(
    @Arg('input', { nullable: true }) input: UploadAvatarInput,
    @Arg('userId') userId: Types.ObjectId,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadAvatarResponse> {
    const response = new UploadAvatarResponse()

    if (input) {
      if (input.uploadSuccessful) {
        response.user = await UserModel.findById(userId._id)

        if (response.user?.avatarUnconfirmed) {
          if (response.user.avatar) {
            // we don't need to await this
            Promise.all([
              this.s3Service.remove(response.user.avatar),
              this.s3Service.remove(response.user.avatars.tiny),
              this.s3Service.remove(response.user.avatars.small),
              this.s3Service.remove(response.user.avatars.medium),
              this.s3Service.remove(response.user.avatars.large),
              this.s3Service.remove(response.user.avatars.og),
            ]).catch((err) => {
              this.logger.error('Failed to remove old avatars: ', err)
            })
          }

          const avatars = await this.mediaConverter.createImageThumbnails(
            response.user.avatarUnconfirmed
          )

          response.user = await UserModel.findOneAndUpdate(
            { _id: userId._id },
            {
              avatar: response.user.avatarUnconfirmed,
              avatarUnconfirmed: null,
              avatars,
            },
            { new: true }
          )
        }
      } else {
        // TODO: maybe also delete the file from S3 if it exists
        response.user = await UserModel.findOneAndUpdate(
          { _id: userId._id },
          { avatarUnconfirmed: null }
        )

        return response
      }
    }

    try {
      const avatarKey = `images/avatars/${userId._id}-${new Date().getTime()}.jpg`
      response.preSignedUrl = await this.s3Service.createPresignedUrl(avatarKey, 'image/jpeg')

      // save the key to the user
      response.user = await UserModel.findOneAndUpdate(
        { _id: userId._id },
        { avatarUnconfirmed: avatarKey }
      ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])
    } catch (err) {
      this.logger.error("Failed to create preSignedUrl for users' avatar:", err)
      response.errors = [
        new FailedToCreatePresignedUrl(`Failed to create preSignedUrl`, info.path.key.toString()),
      ]
    }

    return response
  }
}
