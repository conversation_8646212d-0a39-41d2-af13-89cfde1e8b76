import pino from 'pino'
import { Arg, Field, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { Pagination } from '../../../types/general'
import UserInfoRepository from '../../stats/repositories/userInfo.repository'
import { UserInfoFilter, UserInfoResponse } from '../../stats/types/userInfo'

@ObjectType()
export class AdminUserInfoQueries {
  private readonly logger: pino.Logger
  private readonly userInfoRepository: UserInfoRepository

  constructor() {
    this.logger = Container.get('app.logger')
    this.userInfoRepository = Container.get(UserInfoRepository)
  }

  @Field(() => UserInfoResponse)
  async userInfos(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter') filter: UserInfoFilter
  ): Promise<UserInfoResponse> {
    const response = new UserInfoResponse()
    response.edges = []

    const userInfos = await this.userInfoRepository.search(filter.searchQuery, pagination)

    for (const userInfo of userInfos.nodes) {
      response.edges.push({
        node: userInfo,
      })
    }

    response.pageInfo = userInfos.pageInfo

    return response
  }
}
