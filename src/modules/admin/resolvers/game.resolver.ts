import { GraphQLError } from 'graphql'
import { PipelineStage, Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { EntityNotFoundError, Pagination, SortInput, SortOrder } from '../../../types/general'
import { Game, GameModel } from '../../stats/models/game/game'
import { UserModel } from '../../user/models'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { AdminGameFilter, GameResponse, SyncSingleGameResponse } from '../types/game'

@ObjectType()
export class AdminGameQueries {
  private paginationRepository = new PaginationRepository()

  @Field(() => GameResponse)
  public async list(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter?: AdminGameFilter,
    @Arg('sort', { nullable: true }) sort?: SortInput
  ): Promise<GameResponse> {
    const response = new GameResponse()
    response.edges = []

    const pipelineStages: PipelineStage[] = []
    const matchStage: PipelineStage.Match['$match'] = {}

    // Add conditions based on `filter` properties
    if (filter?.leagueMissing) {
      matchStage.$or = [...(matchStage.$or || []), { league: null }]

      // add group by league
      pipelineStages.push({
        $group: {
          _id: '$externalInfo.leagueName',
          game: {
            $first: '$$ROOT',
          },
        },
      })

      pipelineStages.push({
        $replaceRoot: {
          newRoot: '$game',
        },
      })
    } else if (filter?.teamMissing) {
      pipelineStages.push(
        ...[
          {
            $match: {
              $or: [{ homeTeam: null }, { awayTeam: null }],
            },
          },
          {
            $project: {
              teamNames: [
                { name: '$externalInfo.homeTeamName', game: '$$ROOT' },
                { name: '$externalInfo.awayTeamName', game: '$$ROOT' },
              ],
            },
          },
          {
            $unwind: '$teamNames',
          },
          {
            $group: {
              _id: '$teamNames.name',
              game: { $first: '$teamNames.game' },
            },
          },
          {
            $replaceRoot: { newRoot: '$game' },
          },
          {
            $group: {
              _id: '$_id',
              game: { $first: '$$ROOT' },
            },
          },
          {
            $replaceRoot: { newRoot: '$game' },
          },
        ]
      )
    }

    if (filter?.hideIgnored === true) {
      matchStage.ignore = { $ne: true }
    }

    if (filter?.team) {
      matchStage.$or = [
        ...(matchStage.$or || []),
        { homeTeam: new Types.ObjectId(filter.team) },
        { awayTeam: new Types.ObjectId(filter.team) },
      ]
    }

    if (filter?.league) {
      matchStage.league = new Types.ObjectId(filter.league)
    }

    if (filter?.user) {
      const user = await UserModel.findById(filter.user)

      matchStage.$or = [
        ...(matchStage.$or || []),
        {
          'homeTeamRoster.externalInfo.playerExternalId': {
            $in: user?.externalConnections.map((ec) => ec.externalId),
          },
        },
        {
          'awayTeamRoster.externalInfo.playerExternalId': {
            $in: user?.externalConnections.map((ec) => ec.externalId),
          },
        },
      ]
    }

    if (filter?.period) {
      const now = new Date()
      if (filter.period === 'HISTORY') {
        matchStage.date = { $lt: now }
      } else if (filter.period === 'FUTURE') {
        matchStage.date = { $gt: now }
      }
    }

    // Add `$match` stage if there are conditions
    if (Object.keys(matchStage).length > 0) {
      pipelineStages.unshift({ $match: matchStage })
    }

    const games = await this.paginationRepository.aggregate({
      templateSortObject: {
        _id: new Types.ObjectId(),
        date: new Date(),
        league: new Types.ObjectId(),
        homeTeam: new Types.ObjectId(),
        awayTeam: new Types.ObjectId(),
      },
      pipeline: pipelineStages,
      model: GameModel,
      sorting: sort ?? {
        fields: [
          {
            field: 'date',
            order: SortOrder.DESC,
          },
        ],
      },
      pagination: this.paginationRepository.mapPagination(pagination),
    })

    await GameModel.populate(games.nodes, [
      { path: 'homeTeam' },
      { path: 'awayTeam' },
      { path: 'league' },
      { path: 'discoveredUrl' },
    ])

    for (const node of games.nodes) {
      response.edges.push({
        node,
      })
    }

    response.pageInfo = games.pageInfo

    return response
  }

  @Field(() => Game)
  public async get(@Arg('id') id: Types.ObjectId): Promise<Game> {
    const game = await GameModel.findById(id)
      .populate([
        { path: 'discoveredUrl' },
        { path: 'league' },
        { path: 'homeTeam' },
        { path: 'awayTeam' },
      ])
      .lean()

    if (!game) {
      throw new GraphQLError('Game not found')
    }

    await Game.loadPlayersForGames([game])

    return game
  }
}

@ObjectType()
export class AdminGameMutations {
  private logger: pino.Logger

  constructor() {
    this.logger = Container.get('app.logger')
  }

  @Field(() => Game)
  public async sync(@Arg('id') id: Types.ObjectId): Promise<Game> {
    const baseGame = await GameModel.findById(id)

    if (!baseGame) {
      throw new GraphQLError('Game not found')
    }

    await baseGame.syncEntities(true)

    if (baseGame?.externalInfo?.leagueName && baseGame.league && baseGame.homeTeam) {
      await this.syncGame(
        baseGame?.externalInfo?.leagueName,
        baseGame?.externalInfo?.homeTeamName,
        baseGame.league.toString(),
        baseGame.homeTeam.toString()
      )
    }

    if (baseGame?.externalInfo?.leagueName && baseGame.league && baseGame.awayTeam) {
      await this.syncGame(
        baseGame?.externalInfo?.leagueName,
        baseGame?.externalInfo?.awayTeamName,
        baseGame.league.toString(),
        baseGame.awayTeam.toString()
      )
    }

    await GameModel.populate(baseGame, [
      { path: 'discoveredUrl' },
      { path: 'homeTeam' },
      { path: 'awayTeam' },
      { path: 'league' },
    ])

    return baseGame
  }

  private async syncGame(
    externalLeagueName: string,
    externalTeamName: string,
    league: string,
    team: string
  ): Promise<void> {
    await GameModel.updateMany(
      {
        'externalInfo.leagueName': externalLeagueName,
        'externalInfo.homeTeamName': externalTeamName,
      },
      {
        league: league,
        homeTeam: team,
      }
    )

    await GameModel.updateMany(
      {
        'externalInfo.leagueName': externalLeagueName,
        'externalInfo.awayTeamName': externalTeamName,
      },
      {
        league: league,
        awayTeam: team,
      }
    )
  }

  @Field(() => SyncSingleGameResponse)
  public async syncSingle(@Arg('id') id: Types.ObjectId): Promise<SyncSingleGameResponse> {
    const game = await GameModel.findById(id)

    if (!game) {
      return {
        game: null,
        errors: [new EntityNotFoundError('Game not found', 'syncSingle')],
      }
    }

    await game.syncEntities(true)
    await game.save()

    await GameModel.populate(game, [
      { path: 'discoveredUrl' },
      { path: 'homeTeam' },
      { path: 'awayTeam' },
      { path: 'league' },
    ])

    return {
      game,
      errors: [],
    }
  }
}
