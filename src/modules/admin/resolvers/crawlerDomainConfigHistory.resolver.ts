import { GraphQLError } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Field, Int, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { SortOrder } from '../../../types/general'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import {
  CrawlerDomainConfigHistory,
  CrawlerDomainConfigHistoryModel,
} from '../models/crawlerDomainConfigHistory'
import {
  CrawlerDomainConfigHistoryFilter,
  CrawlerDomainConfigHistoryResponse,
} from '../types/crawlerDomainConfigHistory'

@ObjectType()
export class AdminCrawlerDomainConfigHistoryQueries {
  private readonly logger: pino.Logger
  private readonly pagination = new PaginationRepository()

  constructor() {
    this.logger = Container.get('app.logger')
  }

  @Field(() => CrawlerDomainConfigHistoryResponse)
  async list(
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string,
    @Arg('filter', { nullable: true }) filter?: CrawlerDomainConfigHistoryFilter
  ): Promise<CrawlerDomainConfigHistoryResponse> {
    try {
      const response = new CrawlerDomainConfigHistoryResponse()
      response.edges = []

      const query: Record<string, unknown> = {
        modelName: 'CrawlerDomainConfig',
      }

      if (filter?.collectionId) {
        query.collectionId = filter.collectionId
      }

      const history = await this.pagination.find(
        {
          _id: new Types.ObjectId(),
          createdAt: new Date(),
        },
        query,
        CrawlerDomainConfigHistoryModel,
        {
          fields: [{ field: 'createdAt', order: SortOrder.DESC }],
        },
        this.pagination.mapPagination({ first, after, before })
      )

      for (const historyItem of history.nodes) {
        response.edges.push({
          node: historyItem,
        })
      }

      response.pageInfo = history.pageInfo

      return response
    } catch (err) {
      this.logger.error('Failed to fetch crawler domain config history.', err)
      throw new GraphQLError('Failed to fetch crawler domain config history.')
    }
  }

  @Field(() => CrawlerDomainConfigHistory, { nullable: true })
  async get(@Arg('id') id: string): Promise<CrawlerDomainConfigHistory | null> {
    try {
      return CrawlerDomainConfigHistoryModel.findById(id).lean()
    } catch (err) {
      this.logger.error(`Failed to fetch crawler domain config history with ID ${id}.`, err)
      throw new GraphQLError('Failed to fetch crawler domain config history.')
    }
  }
}
