import { GraphQLResolveInfo } from 'graphql'
import { FilterQuery, PipelineStage, Types, UpdateQuery } from 'mongoose'
import pino from 'pino'
import { Arg, Field, Info, ObjectType } from 'type-graphql'
import Container from 'typedi'

import { Pagination } from '../../../types/general'
import { LeagueModel } from '../../stats/models/league'
import { UserStats, UserStatsModel } from '../../stats/models/userStats'
import { UserStatsResponse } from '../../stats/types/userStats'
import { UserModel } from '../../user/models'
import { User } from '../../user/models/user'
import { PaginationRepository } from '../../user/repositories/pagination.repository'
import { UserNotFoundError } from '../../user/types/user'
import {
  AdminUserStatsFilter,
  DeleteUserStatsResponse,
  FailedToUpdateStats,
  MigrateUserStatsInput,
  MigrateUserStatsResponse,
  MigrateUserStatsType,
  UserStatsNotFoundError,
  UserStatsSyncResponse,
} from '../types/userStats'

@ObjectType()
export class AdminUserStatsQueries {
  private readonly paginationRepository = new PaginationRepository()

  @Field(() => UserStatsResponse)
  public async list(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter', { nullable: true }) filter?: AdminUserStatsFilter
  ): Promise<UserStatsResponse> {
    const response = new UserStatsResponse()
    response.edges = []

    const queryFilter: FilterQuery<UserStats> = {}
    const pipeline: PipelineStage[] = []

    if (filter?.source) {
      queryFilter.source = filter.source
    }

    if (filter?.teamMissing) {
      queryFilter.team = null

      const ignoreLeagues = await LeagueModel.find({ ignore: true }).lean()
      if (ignoreLeagues.length > 0) {
        queryFilter.league = { $nin: [...ignoreLeagues.map((league) => league._id), null] }
      }

      // add group by team
      pipeline.push({
        $group: {
          _id: {
            externalTeamName: '$externalInfo.externalTeamName',
            externalLeagueName: '$externalInfo.externalLeagueName',
          },
          userStats: {
            $first: '$$ROOT',
          },
        },
      })

      pipeline.push({
        $replaceRoot: {
          newRoot: '$userStats',
        },
      })
    }

    if (filter?.leagueMissing) {
      queryFilter.league = null

      // add group by league
      pipeline.push({
        $group: {
          _id: '$externalInfo.externalLeagueName',
          userStats: {
            $first: '$$ROOT',
          },
        },
      })

      pipeline.push({
        $replaceRoot: {
          newRoot: '$userStats',
        },
      })
    }

    if (filter?.team) {
      queryFilter.team = filter.team
    }

    if (filter?.league) {
      queryFilter.league = filter.league
    }

    if (filter?.user) {
      const user = await UserModel.findById(filter.user)

      if (queryFilter.$or) {
        const or = queryFilter.$or
        delete queryFilter.$or

        queryFilter.$and = [
          {
            $or: [...or],
          },
          {
            $or: [
              { user: new Types.ObjectId(user?.id) },
              ...(user?.externalConnections?.map((ec) => ({
                userExternalId: ec.externalId,
                crawlerDomainConfig: ec.crawlerDomainConfig._id,
              })) || []),
            ],
          },
        ]
      } else {
        queryFilter.$or = [
          { user: new Types.ObjectId(user?.id) },
          ...(user?.externalConnections?.map((ec) => ({
            userExternalId: ec.externalId,
            crawlerDomainConfig: ec.crawlerDomainConfig._id,
          })) || []),
        ]
      }
    }

    if (filter?.country) {
      if (queryFilter.$or) {
        const or = queryFilter.$or
        delete queryFilter.$or

        queryFilter.$and = [
          {
            $or: [...or],
          },
          {
            $or: [
              { 'externalInfo.externalTeamCountry': filter.country },
              { 'externalInfo.externalLeagueCountry': filter.country },
            ],
          },
        ]
      } else {
        queryFilter.$or = [
          { 'externalInfo.externalTeamCountry': filter.country },
          { 'externalInfo.externalLeagueCountry': filter.country },
        ]
      }
    }

    pipeline.push({
      $match: queryFilter,
    })

    const userStats = await this.paginationRepository.aggregate({
      pipeline,
      model: UserStatsModel,
      pagination: this.paginationRepository.mapPagination(pagination),
    })

    const externalUserIdsMap = new Map<string, Set<string>>()
    for (const userStat of userStats.nodes) {
      if (!userStat.crawlerDomainConfig || !userStat.userExternalId) {
        continue
      }

      if (!externalUserIdsMap.has(userStat.crawlerDomainConfig.toString())) {
        externalUserIdsMap.set(userStat.crawlerDomainConfig.toString(), new Set())
      }

      externalUserIdsMap.get(userStat.crawlerDomainConfig.toString())!.add(userStat.userExternalId)
    }

    const orConditions = []
    for (const [crawlerDomainConfigId, externalIds] of externalUserIdsMap.entries()) {
      if (externalIds.size > 0) {
        orConditions.push({
          externalConnections: {
            $elemMatch: {
              externalId: { $in: Array.from(externalIds) },
              crawlerDomainConfig: new Types.ObjectId(crawlerDomainConfigId),
            },
          },
        })
      }
    }

    let users: User[] = []
    if (orConditions.length > 0) {
      users = await UserModel.find({ $or: orConditions }).lean()
    }

    await UserStatsModel.populate(userStats.nodes, [
      { path: 'team', populate: { path: 'organization' } },
      { path: 'league' },
      { path: 'user' },
    ])

    for (const userStat of userStats.nodes) {
      const user = users.find((user) => {
        return user.externalConnections.some((ec) => {
          if (!userStat.crawlerDomainConfig) {
            return false
          }

          return (
            ec.externalId === userStat.userExternalId &&
            ec.crawlerDomainConfig.toString() === userStat.crawlerDomainConfig.toString()
          )
        })
      })

      if (user) {
        ;(userStat.user as User) = user
      }

      response.edges.push({
        node: userStat,
      })
    }

    response.pageInfo = userStats.pageInfo

    return response
  }
}

@ObjectType()
export class AdminUserStatsMutations {
  private readonly logger: pino.Logger

  constructor() {
    this.logger = Container.get('app.logger')
  }

  @Field(() => DeleteUserStatsResponse)
  public async deleteAllUserStats(
    @Arg('userId') userId: Types.ObjectId
  ): Promise<DeleteUserStatsResponse> {
    const response = new DeleteUserStatsResponse()
    response.success = true

    const user = await UserModel.findById(userId)

    if (!user) {
      response.success = false
      response.errors = [new UserNotFoundError('User not found', 'deleteUserStats')]
      return response
    }

    await UserStatsModel.deleteMany({ user: user._id })

    return response
  }

  @Field(() => MigrateUserStatsResponse)
  public async migrate(
    @Arg('input') input: MigrateUserStatsInput
  ): Promise<MigrateUserStatsResponse> {
    const response = new MigrateUserStatsResponse()
    response.success = true

    const filter: FilterQuery<UserStats> = {}
    const update: UpdateQuery<UserStats> = {}
    let nameToMigrate = { name: '', slug: '' }

    switch (input.type) {
      case MigrateUserStatsType.LEAGUE:
        filter.league = input.from
        update.league = input.to

        const fromLeague = await LeagueModel.findById(input.from)
        if (fromLeague) {
          nameToMigrate = { name: fromLeague?.name, slug: fromLeague?.slug }
        }

        break
      case MigrateUserStatsType.TEAM:
        filter.team = input.from
        update.team = input.to

        if (input.league) {
          filter.league = input.league
        }

        break
    }

    try {
      const result = await UserStatsModel.updateMany(filter, update)

      response.success = true
      response.modifiedCount = result.modifiedCount

      if (result.modifiedCount > 0 && nameToMigrate.name) {
        if (input.type === MigrateUserStatsType.LEAGUE) {
          const league = await LeagueModel.findById(input.to)

          if (!league?.alternativeNames.some((item) => item.slug === nameToMigrate.slug)) {
            await LeagueModel.findByIdAndUpdate(input.to, {
              $addToSet: { alternativeNames: nameToMigrate },
            })
          }
        }
      }
    } catch (err) {
      this.logger.error('Failed to migrate user stats', err)

      response.success = false
      response.errors = [new FailedToUpdateStats('Failed to update stats', 'migrateUserStats')]
    }

    return response
  }

  @Field(() => UserStatsSyncResponse)
  public async sync(
    @Arg('id') id: string,
    @Info() info: GraphQLResolveInfo
  ): Promise<UserStatsSyncResponse> {
    const response = new UserStatsSyncResponse()

    const base = await UserStatsModel.findById(id)

    if (!base) {
      response.errors = [
        new UserStatsNotFoundError('User stats not found', info.path.key.toString()),
      ]
      return response
    }

    try {
      await base.syncEntities(true)

      // Update the base with the populated entities
      await base.save()
    } catch (err) {
      this.logger.error('Failed to sync entities', err)
    }

    await UserStatsModel.updateMany(
      {
        'externalInfo.externalLeagueName': base.externalInfo?.externalLeagueName,
        'externalInfo.externalTeamName': base.externalInfo?.externalTeamName,
      },
      {
        league: base.league,
        team: base.team,
      }
    )

    await UserStatsModel.populate(base, [
      { path: 'team', populate: { path: 'organization' } },
      { path: 'league' },
    ])

    response.userStats = base
    return response
  }
}
