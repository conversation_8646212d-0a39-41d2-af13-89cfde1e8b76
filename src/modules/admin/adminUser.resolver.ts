import config from 'config'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import jwt from 'jsonwebtoken'
import { PipelineStage, Types } from 'mongoose'
import pino from 'pino'
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType, ContextUser } from '../..'
import { generateExpirationDate, generateVerificationCode } from '../../helpers/code-generation'
import { EmailService, EmailTemplate } from '../../services/email'
import GoogleService from '../../services/google'
import { GeneralError, Pagination, SortOrder, SystemRole, UserRole } from '../../types/general'
import { PaginationRepository } from '../user/repositories/pagination.repository'
import { InvalidVerificationCodeError, UserNotFoundError } from '../user/types/user'

import { AdminUser, AdminUserModel } from './models/adminUser'
import {
  AdminSocialSignInInput,
  AdminSocialSignInResponse,
  AdminUsersResponse,
  CreateAdminInput,
  CreateAdminResponse,
  LoginAdminResponse,
  VerifyAdminByEmailResponse,
} from './types/adminUser'

@Resolver((_of) => AdminUser)
@Service()
export default class AdminUserResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private emailService: EmailService

  @Inject()
  private googleService: GoogleService

  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  private async generateCodeAndSendEmail(
    email: string
  ): Promise<{ code: string; expirationDate: Date }> {
    const code = generateVerificationCode()

    await this.emailService.sendEmail(email, EmailTemplate.VERIFY_EMAIL, { code, verifyUrl: '' })

    return {
      code,
      expirationDate: generateExpirationDate(),
    }
  }

  @Mutation(() => LoginAdminResponse)
  async loginAdmin(@Arg('email') email: string): Promise<LoginAdminResponse> {
    try {
      const userExists = await AdminUserModel.findOne({ email: email })

      if (userExists) {
        await AdminUserModel.updateOne(
          { _id: userExists.id },
          {
            verificationCode: await this.generateCodeAndSendEmail(email),
          }
        )

        return {
          errors: [],
        }
      }

      return {
        errors: [new UserNotFoundError(`User with email address ${email} not found`, 'email')],
      }
    } catch (err) {
      this.logger.error('Failed to login admin user:', err)

      throw new GraphQLError('Failed to login user by email. Please try again later.')
    }
  }

  @Mutation(() => VerifyAdminByEmailResponse)
  async verifyAdminByEmail(
    @Arg('email') email: string,
    @Arg('code') code: string,
    @Info() info: GraphQLResolveInfo
  ): Promise<VerifyAdminByEmailResponse> {
    const user = await AdminUserModel.findOne({ email })

    if (!user) {
      return {
        errors: [
          new UserNotFoundError(
            `User with email address ${email} not found`,
            info.path.key.toString()
          ),
        ],
      }
    }

    if (user.verificationCode?.code !== code) {
      return {
        errors: [
          new InvalidVerificationCodeError('Invalid verification code', info.path.key.toString()),
        ],
      }
    }

    if (user.verificationCode.expirationDate < new Date()) {
      const newCode = await this.generateCodeAndSendEmail(email)
      await AdminUserModel.updateOne({ email: email }, { verificationCode: newCode })
      return {
        errors: [
          new InvalidVerificationCodeError(
            'Verification code expired, new code sent',
            info.path.key.toString()
          ),
        ],
      }
    }

    return {
      tokens: {
        accessToken: this.getUserToken(user),
        refreshToken: this.getUserToken(user),
      },
      user,
      errors: [],
    }
  }

  @Mutation(() => AdminSocialSignInResponse)
  async googleSignInAdmin(
    @Arg('input') input: AdminSocialSignInInput
  ): Promise<AdminSocialSignInResponse> {
    try {
      const adminInfo = await this.googleService.getUserInfo(input.idToken)

      const adminUser = await AdminUserModel.findOne({ email: adminInfo.email })
      if (!adminUser) {
        return {
          errors: [
            new GeneralError(`User with email address ${adminInfo.email} not found`, 'email'),
          ],
        }
      }

      return {
        tokens: {
          accessToken: this.getUserToken(adminUser),
          refreshToken: this.getUserToken(adminUser),
        },
        user: adminUser,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to sign in with Google:', err)

      throw new GraphQLError('Failed to sign in with Google. Please try again later.', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  private getUserToken(user: AdminUser) {
    const payload: ContextUser = {
      id: user._id.toString(),
      email: user.email,
      admin: true,
      role: UserRole.ADMIN,
    }

    return jwt.sign(payload, config.jwt.secret, { expiresIn: config.jwt.expiresIn })
  }

  @Mutation(() => CreateAdminResponse)
  @Authorized(SystemRole.ADMIN)
  async createAdmin(@Arg('input') input: CreateAdminInput): Promise<CreateAdminResponse> {
    if (await AdminUserModel.findOne({ email: input.email })) {
      throw new GraphQLError('Admin user with this email already exists')
    }

    try {
      const user = await AdminUserModel.create(input)

      return {
        user,
      }
    } catch (err) {
      this.logger.error('Failed to create admin user:', err)
      throw new GraphQLError('Failed to create admin user. Please try again later.')
    }
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  async deleteAdmin(@Arg('id') id: Types.ObjectId): Promise<boolean> {
    const admin = await AdminUserModel.findById(id)
    if (!admin) {
      throw new GraphQLError('Admin user not found')
    }

    try {
      await AdminUserModel.deleteOne({ _id: id })
      return true
    } catch (err) {
      this.logger.error('Failed to delete admin user:', err)
      throw new GraphQLError('Failed to delete admin user. Please try again later.')
    }
  }

  @Query(() => AdminUser)
  @Authorized()
  async currentAdminUser(@Ctx() ctx: ContextType): Promise<AdminUser> {
    const user = await AdminUserModel.findById(ctx.user!.id)

    if (!user) {
      throw new GraphQLError('User does not exist.', { extensions: { code: 'UNAUTHENTICATED' } })
    }

    return user
  }

  @Query(() => AdminUsersResponse)
  @Authorized(SystemRole.ADMIN)
  async adminUsers(
    @Arg('pagination', { nullable: true }) pagination: Pagination
  ): Promise<AdminUsersResponse> {
    const response = new AdminUsersResponse()
    response.edges = []

    const pipeline: PipelineStage[] = []

    const adminUsers = await this.paginationRepository.aggregate({
      templateSortObject: { _id: new Types.ObjectId() },
      pipeline: pipeline,
      model: AdminUserModel,
      sorting: { fields: [{ field: '_id', order: SortOrder.DESC }] },
      pagination: this.paginationRepository.mapPagination(pagination),
    })

    for (const user of adminUsers.nodes) {
      response.edges.push({ node: user })
    }

    response.pageInfo = adminUsers.pageInfo

    return response
  }
}
