import { Types } from 'mongoose'
import { createUnionType, Field, ObjectType } from 'type-graphql'

import { AlternativeName } from '../../stats/models/alternativeName'
import { League } from '../../stats/models/league'
import { Organization } from '../../stats/models/organization'
import { TeamNotFoundError } from '../../stats/types/team'

// TODO maybe try to extend team model and add score field only?
@ObjectType()
class SuggestedTeam {
  @Field({ name: 'id' })
  public _id: Types.ObjectId

  @Field()
  public name: string

  @Field({ nullable: true })
  public country?: string

  @Field(() => [AlternativeName], { nullable: true })
  public alternativeNames?: AlternativeName[]

  @Field(() => [League], { nullable: true })
  public leagues?: League[]

  @Field(() => Organization, { nullable: true })
  public organization?: Organization

  @Field()
  public score: number

  @Field({ nullable: true })
  public abbreviation?: string
}

@ObjectType()
export class SuggestedTeamResponse {
  @Field(() => SuggestedTeam, { nullable: true })
  public bestMatch: SuggestedTeam

  @Field(() => [SuggestedTeam])
  public suggestions: SuggestedTeam[]
}

@ObjectType()
export class RemoveTeamLogoResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => [RemoveTeamLogoError], { nullable: true })
  public errors?: Array<typeof RemoveTeamLogoError>
}

const RemoveTeamLogoError = createUnionType({
  name: 'RemoveTeamLogoError',
  types: () => [TeamNotFoundError] as const,
})
