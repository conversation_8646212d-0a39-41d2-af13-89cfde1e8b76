import { Types } from 'mongoose'
import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { FailedToCreatePresignedUrl, IUserError, PageInfo } from '../../../types/general'
import { AlternativeName } from '../../stats/models/alternativeName'
import {
  League,
  LeagueGenderCategory,
  LeagueLevel,
  LeagueType,
  StatusType,
} from '../../stats/models/league'

@ObjectType()
class SuggestedLeague {
  @Field({ name: 'id' })
  public _id: Types.ObjectId

  @Field()
  public name: string

  @Field(() => [String])
  public countries: string[]

  @Field(() => LeagueLevel)
  public level: LeagueLevel

  @Field(() => [AlternativeName], { nullable: true })
  public alternativeNames: AlternativeName[]

  @Field()
  public score: number
}

@ObjectType()
export class SuggestedLeagueResponse {
  @Field(() => SuggestedLeague, { nullable: true })
  public bestMatch: SuggestedLeague

  @Field(() => [SuggestedLeague])
  public suggestions: SuggestedLeague[]
}

// logo upload

@InputType()
export class UploadLeagueLogoInput {
  @Field({ nullable: false })
  public uploadSuccessful: boolean
}

@ObjectType()
export class UploadLeagueLogoResponse {
  @Field(() => String, { nullable: true })
  public preSignedUrl: string | null

  @Field(() => League, { nullable: true })
  public league?: League | null

  @Field((_type) => [UploadLeagueLogoError], { nullable: true })
  public errors: Array<typeof UploadLeagueLogoError>
}

const UploadLeagueLogoError = createUnionType({
  name: 'UploadLeagueLogoError',
  types: () => [FailedToCreatePresignedUrl] as const,
})

// leagues search

@InputType()
export class AdminLeaguesFilter {
  @Field({ nullable: true })
  public searchQuery?: string

  @Field({ nullable: true })
  public country?: string

  @Field((_) => LeagueGenderCategory, { nullable: true })
  public genderCategory?: LeagueGenderCategory

  @Field((_) => LeagueLevel, { nullable: true })
  public level?: LeagueLevel

  @Field((_) => LeagueType, { nullable: true })
  public type?: LeagueType

  @Field((_) => StatusType, { nullable: true })
  public scraped?: StatusType

  @Field((_) => StatusType, { nullable: true })
  public checked?: StatusType
}

@ObjectType()
export class AdminLeagueEdge {
  @Field((_type) => League, { nullable: false })
  public node: League

  @Field()
  public used: boolean
}

@ObjectType()
export class AdminLeaguesResponse {
  @Field((_type) => [AdminLeagueEdge], { nullable: false })
  public edges: Array<AdminLeagueEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

@ObjectType()
export class RemoveLeagueLogoResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => [RemoveLeagueLogoError], { nullable: true })
  public errors?: Array<typeof RemoveLeagueLogoError>
}

const RemoveLeagueLogoError = createUnionType({
  name: 'RemoveLeagueLogoError',
  types: () => [LeagueNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class LeagueNotFoundError extends IUserError implements IUserError {}
