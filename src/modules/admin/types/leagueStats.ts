import { Types } from 'mongoose'
import { Field, InputType, ObjectType } from 'type-graphql'

import { LeagueStats } from '../../stats/models/leagueStats/leagueStats'

@InputType()
export class AdminLeagueStatsFilter {
  @Field({ nullable: false })
  public leagueId: Types.ObjectId
}

@ObjectType()
export class AdminLeagueStatsResponse {
  @Field(() => [AdminLeagueStatsEdge])
  public edges: Array<AdminLeagueStatsEdge>
}

@ObjectType()
export class AdminLeagueStatsEdge {
  @Field(() => LeagueStats)
  public node: LeagueStats
}
