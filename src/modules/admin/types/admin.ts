import { Field, Float, Int, ObjectType } from 'type-graphql'

@ObjectType()
export class UserCount {
  @Field(() => Int)
  playersCount: number

  @Field(() => Int)
  sportsProfsCount: number

  @Field(() => Int)
  parentsCount: number

  @Field(() => Int)
  sportsFansCount: number

  @Field(() => Int)
  shadowPlayersCount: number
}

@ObjectType()
export class UserStatisticsByYearAndCountry {
  @Field(() => Int)
  totalUsers: number

  @Field(() => [CountryYearStats])
  usersByCountryAndYear: CountryYearStats[]
}

@ObjectType()
export class CountryYearStats {
  @Field(() => String)
  country: string

  @Field(() => String)
  year: string

  @Field(() => Int)
  count: number

  @Field(() => Int)
  target: number

  @Field(() => Float)
  percentageOfTarget: number
}
