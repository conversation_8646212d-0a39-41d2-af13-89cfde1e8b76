import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'
import { WallVideo, WallVideoTags } from '../../user/models/wallVideo'

@ObjectType()
export class UpdateWallVideoResponse {
  @Field(() => WallVideo, { nullable: true })
  public wallVideo?: WallVideo | null

  @Field((_type) => [UpdateWallVideoError])
  public errors: Array<typeof UpdateWallVideoError>
}

const UpdateWallVideoError = createUnionType({
  name: 'UpdateWallVideoError',
  types: () => [WallVideoNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class WallVideoNotFoundError extends IUserError implements IUserError {}

@InputType()
export class UpdateWallVideoInput {
  @Field(() => [WallVideoTags], { nullable: true })
  tags?: WallVideoTags[]
}
