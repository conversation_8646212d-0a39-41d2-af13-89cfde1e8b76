import { createUnionType, Field, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'

@ObjectType()
export class RemoveOrganizationLogoResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => [RemoveOrganizationLogo], { nullable: true })
  public errors?: Array<typeof RemoveOrganizationLogo>
}

const RemoveOrganizationLogo = createUnionType({
  name: 'RemoveOrganizationLogoError',
  types: () => [OrganizationNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class OrganizationNotFoundError extends IUserError implements IUserError {}
