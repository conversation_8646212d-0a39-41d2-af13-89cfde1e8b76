import { createUnionType, Field, InputType, ObjectType, registerEnumType } from 'type-graphql'

import { PushNotificationType } from '../../../services/pushNotification'
import { IUserError, PageInfo } from '../../../types/general'
import { Role } from '../../user/models/user'
import { BatchNotification } from '../models/batchNotification'

export enum BatchNotificationState {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  SENT = 'sent',
  CANCELED = 'canceled',
  FAILED = 'failed',
}

registerEnumType(BatchNotificationState, {
  name: 'BatchNotificationState',
  description: 'Enum for batch notification states',
})

@InputType()
export class BatchNotificationInput {
  @Field(() => Role)
  public role: Role

  @Field()
  public title: string

  @Field()
  public message: string

  @Field(() => PushNotificationType)
  public type: PushNotificationType

  @Field({ nullable: true })
  public forcePush: boolean

  @Field({ nullable: true })
  public destinationPath: string

  @Field(() => [String], { nullable: true })
  public nationalities?: string[]

  @Field(() => [Number], { nullable: true })
  public numberOfVideos?: number[]

  @Field(() => [String], { nullable: true })
  public firstNameInitials?: string[]

  @Field({ nullable: true })
  public scheduledAt?: Date

  @Field(() => [Number], { nullable: true })
  public yearOfBirth?: number[]
}

@ObjectType()
export class BatchNotificationResponse {
  @Field(() => BatchNotification, { nullable: true })
  notification: BatchNotification

  @Field((_type) => [BatchNotificationResponseError], { nullable: true })
  public errors?: Array<typeof BatchNotificationResponseError>
}

@ObjectType()
export class BatchNotificationEdge {
  @Field(() => BatchNotification, { nullable: false })
  public node: BatchNotification
}

@ObjectType()
export class BatchNotificationListResponse {
  @Field(() => [BatchNotificationEdge], { nullable: false })
  public edges: Array<BatchNotificationEdge>

  @Field(() => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

const BatchNotificationResponseError = createUnionType({
  name: 'BatchNotificationResponseError',
  types: () => [BatchNotificationError, BatchNotificationNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class BatchNotificationError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class BatchNotificationNotFoundError extends IUserError implements IUserError {}
