import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { GeneralError, PageInfo } from '../../../types/general'
import { CrawlerDomainConfig, CrawlerEntity, FetchType } from '../models/crawlerDomainConfig'

// CrawlerConfigListResponse

@ObjectType()
export class CrawlerDomainConfigEdge {
  @Field((_type) => CrawlerDomainConfig, { nullable: false })
  public node: CrawlerDomainConfig
}

@ObjectType()
export class CrawlerDomainConfigListResponse {
  @Field(() => [CrawlerDomainConfigEdge], { nullable: false })
  public edges: Array<CrawlerDomainConfigEdge>

  @Field(() => PageInfo)
  public pageInfo: PageInfo
}

// CrawlerDomainConfigCreateResponse

@ObjectType()
export class CrawlerDomainConfigCreateResponse {
  @Field(() => CrawlerDomainConfig)
  public config: CrawlerDomainConfig

  // errors
  @Field((_type) => [CrawlerDomainConfigCreateError], { nullable: true })
  public errors?: Array<typeof CrawlerDomainConfigCreateError>
}

const CrawlerDomainConfigCreateError = createUnionType({
  name: 'CrawlerDomainConfigCreateError',
  types: () => [GeneralError] as const,
})

// CrawlerDomainConfigUpdateResponse

@ObjectType()
export class CrawlerDomainConfigUpdateResponse {
  @Field(() => CrawlerDomainConfig)
  public config: CrawlerDomainConfig

  // errors
  @Field((_type) => [CrawlerDomainConfigUpdateError], { nullable: true })
  public errors?: Array<typeof CrawlerDomainConfigUpdateError>
}

const CrawlerDomainConfigUpdateError = createUnionType({
  name: 'CrawlerDomainConfigUpdateError',
  types: () => [GeneralError] as const,
})

// CrawlerDomainConfigDeleteResponse

@ObjectType()
export class CrawlerDomainConfigDeleteResponse {
  @Field(() => Boolean)
  public success: boolean

  // errors
  @Field((_type) => [CrawlerDomainConfigDeleteError], { nullable: true })
  public errors?: Array<typeof CrawlerDomainConfigDeleteError>
}

const CrawlerDomainConfigDeleteError = createUnionType({
  name: 'CrawlerDomainConfigDeleteError',
  types: () => [GeneralError] as const,
})

// CrawlerDomainConfigCreateInput

@InputType()
export class CrawlerUrlConfigInput {
  @Field()
  public urlPatternRegExp: string

  @Field(() => CrawlerEntity)
  public entity: CrawlerEntity

  @Field(() => [String])
  public initialUrls: string[]

  @Field()
  public externalIdPatternRegExp: string

  @Field()
  public extractionScript: string

  @Field(() => [String], { nullable: true })
  public allowedQueryParams: string[]

  @Field(() => [ParamExtractorInput], { nullable: true })
  public paramExtractors?: ParamExtractorInput[]

  @Field(() => FetchType, { nullable: true })
  public fetchType?: FetchType

  @Field({ nullable: true, defaultValue: false })
  public addToQueue?: boolean
}

@InputType()
export class CrawlerDomainConfigCreateInput {
  @Field()
  public domain: string

  @Field(() => [CrawlerUrlConfigInput])
  public urlConfigs: CrawlerUrlConfigInput[]

  @Field(() => [String], { nullable: true })
  public relevantUrlPatterns?: string[]

  @Field(() => FetchType, { nullable: true })
  public fetchType?: FetchType

  @Field({ nullable: true, defaultValue: true })
  public enabled?: boolean

  @Field({ nullable: true, defaultValue: 5 })
  public parallelism?: number
}

// CrawlerDomainConfigUpdateInput

@InputType()
export class CrawlerDomainConfigUpdateInput {
  @Field({ nullable: true })
  public domain: string

  @Field(() => [CrawlerUrlConfigInput], { nullable: true })
  public urlConfigs: CrawlerUrlConfigInput[]

  @Field(() => [String], { nullable: true })
  public relevantUrlPatterns?: string[]

  @Field(() => FetchType, { nullable: true })
  public fetchType?: FetchType

  @Field({ nullable: true })
  public enabled?: boolean

  @Field({ nullable: true })
  public parallelism?: number
}

@InputType()
export class ParamExtractorInput {
  @Field(() => String)
  selector?: string

  @Field(() => String)
  queryParam?: string
}
