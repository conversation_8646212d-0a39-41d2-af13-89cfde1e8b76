import { Types } from 'mongoose'
import { Field, InputType, ObjectType } from 'type-graphql'

import { PageInfo } from '../../../types/general'
import { CrawlerLogs } from '../models/crawlerLogs'

@InputType()
export class CrawlerLogsFilter {
  @Field({ nullable: true })
  public collectionId?: Types.ObjectId
}

@ObjectType()
export class CrawlerLogsEdge {
  @Field(() => CrawlerLogs, { nullable: false })
  public node: CrawlerLogs
}

@ObjectType()
export class CrawlerLogsResponse {
  @Field(() => [CrawlerLogsEdge], { nullable: false })
  public edges: Array<CrawlerLogsEdge>

  @Field(() => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}
