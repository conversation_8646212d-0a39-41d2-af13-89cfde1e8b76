import { IsISO31661<PERSON>lpha2, ValidateIf } from 'class-validator'
import { Types } from 'mongoose'
import { createUnionType, Field, InputType, Int, ObjectType, registerEnumType } from 'type-graphql'

import { EntityNotFoundError, IUserError } from '../../../types/general'
import { StatsSource, UserStats } from '../../stats/models/userStats'
import { Role } from '../../user/models/user'
import { UserNotFoundError } from '../../user/types/user'

@ObjectType()
export class ScrapeURLResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field((_type) => [ScrapeURLError], { nullable: true })
  public errors?: Array<typeof ScrapeURLError>
}

const ScrapeURLError = createUnionType({
  name: 'ScrapeURLError',
  types: () => [UserNotFoundError] as const,
})

// no stats found error
@ObjectType({ implements: IUserError })
export class NoStatsFoundError extends IUserError implements IUserError {}

// DeleteUserStatsResponse

@ObjectType()
export class DeleteUserStatsResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field((_type) => [DeleteUserStatsError], { nullable: true })
  public errors?: Array<typeof DeleteUserStatsError>
}

const DeleteUserStatsError = createUnionType({
  name: 'DeleteUserStatsError',
  types: () => [UserNotFoundError] as const,
})

export enum ModelType {
  GPT4_O = 'gpt-4o',
  GPT4_O_MINI = 'gpt-4o-mini',
  TEXT_EMBEDDING_3_SMALL = 'text-embedding-3-small',
}

registerEnumType(ModelType, {
  name: 'ModelType',
  description: 'The type of model to use for scraping',
})

// AdminUserStatsFilter

@InputType()
export class AdminUserStatsFilter {
  @Field({ nullable: true })
  public teamMissing: boolean

  @Field({ nullable: true })
  public leagueMissing: boolean

  @Field({ nullable: true })
  public team: Types.ObjectId

  @Field({ nullable: true })
  public league: Types.ObjectId

  @Field({ nullable: true })
  public user: Types.ObjectId

  @Field(() => Role, { nullable: true })
  public role: Role

  @Field({ nullable: true })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country?: string

  @Field(() => StatsSource, { nullable: true })
  public source?: StatsSource
}

// migrate user stats

@ObjectType()
export class MigrateUserStatsResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => Int, { nullable: true })
  public modifiedCount?: number

  @Field((_type) => [MigrateUserStatsError], { nullable: true })
  public errors?: Array<typeof MigrateUserStatsError>
}

const MigrateUserStatsError = createUnionType({
  name: 'MigrateUserStatsError',
  types: () => [FailedToUpdateStats] as const,
})

@ObjectType()
export class FailedToUpdateStats extends IUserError implements IUserError {}

// UserStats sync response

@ObjectType()
export class UserStatsSyncResponse {
  @Field(() => UserStats, { nullable: true })
  public userStats?: UserStats

  @Field((_type) => [UserStatsSyncError], { nullable: true })
  public errors?: Array<typeof UserStatsSyncError>
}

@ObjectType({ implements: IUserError })
export class UserStatsNotFoundError extends EntityNotFoundError implements IUserError {}

const UserStatsSyncError = createUnionType({
  name: 'UserStatsSyncError',
  types: () => [UserStatsNotFoundError] as const,
})

// migrate user stats input

export enum MigrateUserStatsType {
  TEAM = 'team',
  LEAGUE = 'league',
}

registerEnumType(MigrateUserStatsType, {
  name: 'MigrateUserStatsType',
  description: 'The type of migration to perform',
})

@InputType()
export class MigrateUserStatsInput {
  @Field(() => MigrateUserStatsType)
  public type: MigrateUserStatsType

  @Field()
  public from: Types.ObjectId

  @Field()
  public to: Types.ObjectId

  @Field({ nullable: true })
  public league?: Types.ObjectId
}
