import { createUnionType, Field, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'
import { Agency } from '../../user/models/agency'
import { User } from '../../user/models/user'
import { UserNotFoundError } from '../../user/types/user'

@ObjectType()
export class SyncAgencyClientsResponse {
  @Field(() => Agency, { nullable: true })
  public agency?: Agency

  @Field((_type) => [SyncAgencyClientsError], { nullable: true })
  public errors?: Array<typeof SyncAgencyClientsError>
}

const SyncAgencyClientsError = createUnionType({
  name: 'SyncAgencyClientsError',
  types: () =>
    [
      EPAgencyNotFoundError,
      AgencyNotFoundError,
      InvalidEPPlayerDataError,
      EPUserNotFoundError,
    ] as const,
})

@ObjectType({ implements: IUserError })
export class EPAgencyNotFoundError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class AgencyNotFoundError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class InvalidEPPlayerDataError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class EPUserNotFoundError extends IUserError implements IUserError {}

@ObjectType()
export class MarkUserAsDeletedResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => [MarkUserAsDeletedError], { nullable: true })
  public errors?: Array<typeof MarkUserAsDeletedError>

  @Field(() => User, { nullable: true })
  public user?: User
}

const MarkUserAsDeletedError = createUnionType({
  name: 'MarkUserAsDeletedError',
  types: () => [UserAlreadyMarkedForDeletionError, UserNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class UserAlreadyMarkedForDeletionError extends IUserError implements IUserError {}

@ObjectType()
export class AddExternalConnectionResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => [AddExternalConnectionError], { nullable: true })
  public errors?: Array<typeof AddExternalConnectionError>
}

const AddExternalConnectionError = createUnionType({
  name: 'AddExternalConnectionError',
  types: () => [UserNotFoundError, ExternalConnectionError, UserInfoNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class ExternalConnectionError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class UserInfoNotFoundError extends IUserError implements IUserError {}

@ObjectType()
export class RemoveExternalConnectionResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => [RemoveExternalConnectionError], { nullable: true })
  public errors?: Array<typeof RemoveExternalConnectionError>
}

const RemoveExternalConnectionError = createUnionType({
  name: 'RemoveExternalConnectionError',
  types: () => [UserNotFoundError, ExternalConnectionError, UserInfoNotFoundError] as const,
})
