import { Types } from 'mongoose'
import { Field, InputType, ObjectType } from 'type-graphql'

import { PageInfo } from '../../../types/general'
import { CrawlerRunStats } from '../models/crawlerRunStats'

@InputType()
export class CrawlerRunStatsFilter {
  @Field({ nullable: true })
  public crawlerDomainConfig?: Types.ObjectId
}

@ObjectType()
export class CrawlerRunStatsEdge {
  @Field(() => CrawlerRunStats, { nullable: false })
  public node: CrawlerRunStats
}

@ObjectType()
export class CrawlerRunStatsResponse {
  @Field(() => [CrawlerRunStatsEdge], { nullable: false })
  public edges: Array<CrawlerRunStatsEdge>

  @Field(() => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}
