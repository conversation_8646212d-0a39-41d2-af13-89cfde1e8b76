import { createUnionType, Field, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'
import { Agency } from '../../user/models/agency'

@ObjectType()
export class AddAgencyUserResponse {
  @Field(() => Agency, { nullable: true })
  public agency: Agency | null

  @Field((_type) => [AddAgencyError], { nullable: true })
  public errors?: Array<typeof AddAgencyError>
}

const AddAgencyError = createUnionType({
  name: 'AddAgencyError',
  types: () => [AddAgencyUserNotFoundError, AddAgencyUserAlreadyBelongsToAgencyError] as const,
})

@ObjectType({ implements: IUserError })
export class AddAgencyUserNotFoundError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class AddAgencyUserAlreadyBelongsToAgencyError extends IUserError implements IUserError {}

@ObjectType()
export class RemoveAgencyUserResponse {
  @Field(() => Agency, { nullable: true })
  public agency: Agency | null

  @Field((_type) => [RemoveAgencyError], { nullable: true })
  public errors?: Array<typeof RemoveAgencyError>
}

const RemoveAgencyError = createUnionType({
  name: 'RemoveAgencyError',
  types: () => [RemoveAgencyUserNotFoundError, RemoveAgencyUserDoesNotBelongToAgencyError] as const,
})

@ObjectType({ implements: IUserError })
export class RemoveAgencyUserNotFoundError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class RemoveAgencyUserDoesNotBelongToAgencyError extends IUserError implements IUserError {}
