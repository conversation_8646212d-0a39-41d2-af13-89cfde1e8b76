import { Types } from 'mongoose'
import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'
import { BoostState } from '../../user/models/boost'

@InputType()
export class AdminBoostFilter {
  @Field({ nullable: true })
  public currency: string

  @Field({ nullable: true })
  public state: BoostState

  @Field({ nullable: true })
  public user: Types.ObjectId
}

// SyncStripeAccountResponse

@ObjectType()
export class SyncStripeAccountResponse {
  @Field()
  public success: boolean

  @Field((_type) => [SyncStripeAccountResponseFailed], { nullable: true })
  public errors?: Array<typeof SyncStripeAccountResponseFailed>
}

const SyncStripeAccountResponseFailed = createUnionType({
  name: 'SyncStripeAccountResponseFailed',
  types: () => [SyncStripeAccountFailed] as const,
})

@ObjectType({ implements: IUserError })
export class SyncStripeAccountFailed extends IUserError implements IUserError {}
