import { Types } from 'mongoose'
import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { PushNotificationType } from '../../../services/pushNotification'
import { IUserError, PageInfo } from '../../../types/general'
import { Notification } from '../../user/models/notification'
import { UserNotFoundError } from '../../user/types/user'

@ObjectType()
export class AdminNotificationEdges {
  @Field((_type) => Notification, { nullable: false })
  public node: Notification
}

@ObjectType()
export class AdminNotificationResponse {
  @Field((_type) => [AdminNotificationEdges], { nullable: false })
  public edges: Array<AdminNotificationEdges>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

@InputType()
export class AdminNotificationFilter {
  @Field({ nullable: true })
  public user: Types.ObjectId

  @Field({ nullable: true })
  public type: PushNotificationType
}

@ObjectType()
export class SendPushNotificationAdminResponse {
  @Field(() => Boolean)
  public success: boolean

  @Field(() => [SendPushNotificationAdminError], { nullable: true })
  public errors?: Array<typeof SendPushNotificationAdminError>
}

const SendPushNotificationAdminError = createUnionType({
  name: 'sendPushNotificationAdminError',
  types: () => [UserDoesNotHaveRegisteredDeviceError, UserNotFoundError] as const,
})

@ObjectType()
export class UserDoesNotHaveRegisteredDeviceError extends IUserError implements IUserError {}
