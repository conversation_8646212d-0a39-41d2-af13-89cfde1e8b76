import { Types } from 'mongoose'
import { Field, InputType, ObjectType } from 'type-graphql'

import { PageInfo } from '../../../types/general'
import { CrawlerDomainConfigHistory } from '../models/crawlerDomainConfigHistory'

@InputType()
export class CrawlerDomainConfigHistoryFilter {
  @Field({ nullable: true })
  public collectionId?: Types.ObjectId
}

@ObjectType()
export class CrawlerDomainConfigHistoryEdge {
  @Field(() => CrawlerDomainConfigHistory, { nullable: false })
  public node: CrawlerDomainConfigHistory
}

@ObjectType()
export class CrawlerDomainConfigHistoryResponse {
  @Field(() => [CrawlerDomainConfigHistoryEdge], { nullable: false })
  public edges: Array<CrawlerDomainConfigHistoryEdge>

  @Field(() => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}
