import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { GeneralError, PageInfo } from '../../../types/general'
import { InvalidVerificationCodeError, JWTTokens, UserNotFoundError } from '../../user/types/user'
import { AdminUser } from '../models/adminUser'

@ObjectType()
export class LoginAdminResponse {
  @Field((_type) => [LoginAdminError])
  public errors: Array<typeof LoginAdminError>
}

const LoginAdminError = createUnionType({
  name: 'LoginAdminError',
  types: () => [UserNotFoundError] as const,
})

@ObjectType()
export class VerifyAdminByEmailResponse {
  @Field({ nullable: true })
  public tokens?: JWTTokens

  @Field({ nullable: true })
  public user?: AdminUser

  @Field((_type) => [VerifyAdminByEmailError])
  public errors: Array<typeof VerifyAdminByEmailError>
}

@ObjectType()
export class AdminSocialSignInResponse {
  @Field({ nullable: true })
  public tokens?: JWTTokens

  @Field({ nullable: true })
  public user?: AdminUser

  @Field((_type) => [AdminSocialSignInError])
  public errors: Array<typeof AdminSocialSignInError>
}

const AdminSocialSignInError = createUnionType({
  name: 'AdminSocialSignInError',
  types: () => [GeneralError] as const,
})

@InputType()
export class AdminSocialSignInInput {
  @Field()
  public idToken: string
}

@InputType()
export class CreateAdminInput {
  @Field()
  public firstname: string

  @Field()
  public lastname: string

  @Field()
  public email: string
}

@ObjectType()
export class CreateAdminResponse {
  @Field({ nullable: true })
  public user: AdminUser
}

@ObjectType()
export class AminUserEdge {
  @Field((_type) => AdminUser, { nullable: false })
  public node: AdminUser
}

@ObjectType()
export class AdminUsersResponse {
  @Field((_type) => [AminUserEdge], { nullable: false })
  public edges: Array<AminUserEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

const VerifyAdminByEmailError = createUnionType({
  name: 'VerifyAdminByEmailError', // Name of the GraphQL union
  types: () => [InvalidVerificationCodeError, UserNotFoundError] as const,
})
