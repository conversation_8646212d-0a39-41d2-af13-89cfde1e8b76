import { Types } from 'mongoose'
import { Ctx, FieldResolver, Info, Int, Resolver } from 'type-graphql'
import { Service } from 'typedi'

import { ContextType } from '../../..'
import { getTopViewedAthletesFilters } from '../helpers/topViewed'
import { UserModel } from '../models'
import { UserViewAggregatedModel } from '../models/userViewsAggregated'
import { Period, TopViewedUsersFilter, UserViewsResponse } from '../types/user'

import { GraphQLResolveInfoWithCacheControl } from '@apollo/cache-control-types'

@Resolver(() => UserViewsResponse)
@Service()
export class UserViewResponseResolver {
  @FieldResolver(() => Int)
  async myPosition(
    @Ctx() ctx: ContextType,
    @Info()
    info: GraphQLResolveInfoWithCacheControl
  ): Promise<number> {
    const userId = ctx.user?.id
    if (!userId) {
      info.cacheControl.setCacheHint({ maxAge: 300, scope: 'PUBLIC' })

      return 0
    }

    info.cacheControl.setCacheHint({ maxAge: 0, scope: 'PRIVATE' })

    const period = info.variableValues['period'] as Period
    const filters = getTopViewedAthletesFilters(
      period,
      info.variableValues['filter'] as TopViewedUsersFilter
    )
    if (period === Period.ALL_TIME) {
      const userId = ctx.user?.id
      if (userId) {
        const user = await UserModel.findOne({
          _id: userId,
          ...filters,
        })

        if (user) {
          const myPosition = await UserModel.countDocuments({
            ...filters,
            'userStats.views': { $gt: user?.userStats?.views ?? 0 },
          })

          return myPosition + 1
        } else {
          return 0
        }
      }
    } else if (period === Period.MONTHLY) {
      if (userId) {
        const user = await UserViewAggregatedModel.findOne({
          user: new Types.ObjectId(userId),
          ...filters,
        })

        const myPosition = await UserViewAggregatedModel.countDocuments({
          ...filters,
          totalViews: { $gt: user?.totalViews ?? 0 },
        })

        return myPosition + 1
      }
    }

    return 0
  }
}
