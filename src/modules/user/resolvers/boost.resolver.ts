import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import pino from 'pino'
import { Arg, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { S3Service } from '../../../services/s3'
import { StripeService } from '../../../services/stripe'
import { Pagination, SimpleSort, SortOrder } from '../../../types/general'
import { UserModel } from '../models'
import { BoostModel, BoostState } from '../models/boost'
import { PaginationRepository } from '../repositories/pagination.repository'
import {
  BoostMessageAlreadySetError,
  BoostMessageInput,
  BoostMessageResponse,
  BoostNotFoundError,
  BoostsFilter,
  BoostsResponse,
  CreateBoostInput,
  CreateBoostResponse,
  StripeNotConnectedError,
} from '../types/boost'

@Resolver()
@Service()
export default class BoostResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private s3: S3Service

  @Inject()
  private stripe: StripeService

  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  @Query(() => BoostsResponse)
  async boosts(
    @Ctx() ctx: ContextType,
    @Arg('filter', { nullable: true }) filter: BoostsFilter,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('sort', { nullable: true }) sort?: SimpleSort
  ) {
    try {
      const response = new BoostsResponse()
      response.edges = []

      let userId = filter?.slug ?? ctx.user?.id
      if (!userId) {
        throw new GraphQLError('User not logged in or missing userId in filter.')
      }

      if (filter?.slug) {
        const user = await UserModel.findOne({ slug: filter.slug })

        if (!user) {
          throw new GraphQLError('User not found.')
        }

        userId = user.id
      }
      const boosts = await this.paginationRepository.find(
        { amount: 0, createdAt: new Date() },
        { user: userId, state: BoostState.CONFIRMED },
        BoostModel,
        {
          fields: [
            {
              field: sort?.field ?? 'amount',
              order: sort?.order ?? SortOrder.DESC,
            },
          ],
        },
        this.paginationRepository.mapPagination(pagination),
        []
      )

      for (const boost of boosts.nodes) {
        response.edges.push({
          node: boost,
        })
      }

      response.pageInfo = boosts.pageInfo

      return response
    } catch (err) {
      this.logger.error('Failed to fetch boosts.', err)
      throw new GraphQLError('Failed to fetch boosts. Please try again later.')
    }
  }

  @Mutation(() => BoostMessageResponse)
  async createBoostMessage(
    @Arg('input', { nullable: true }) input: BoostMessageInput,
    // @Ctx() ctx: ContextType, // not used, but we might in the future save info if user is logged in
    @Info() info: GraphQLResolveInfo
  ): Promise<BoostMessageResponse> {
    let boost = await BoostModel.findOne({ paymentIntentId: input.paymentIntentId })

    if (!boost) {
      this.logger.error('Boost not found: %o', { paymentIntentId: input.paymentIntentId })
      return {
        boost: null,
        errors: [new BoostNotFoundError('Boost not found', info.path.key.toString())],
      }
    }

    if (boost.message) {
      return {
        boost,
        errors: [
          new BoostMessageAlreadySetError('Boost message already set', info.path.key.toString()),
        ],
      }
    }

    try {
      boost = await BoostModel.findByIdAndUpdate(
        boost.id,
        {
          message: input.message,
          name: input.name,
          ...(boost.state == BoostState.CREATED
            ? { state: BoostState.WAITING_FOR_CONFIRMATION }
            : {}),
        },
        { new: true }
      )

      return {
        boost,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to update boost.', err)
      throw new GraphQLError('Failed to update boost. Please try again later.')
    }
  }

  @Mutation(() => CreateBoostResponse)
  public async createBoost(@Arg('input') input: CreateBoostInput): Promise<CreateBoostResponse> {
    const user = await UserModel.findById(input.userId)

    if (!user) {
      throw new GraphQLError('User not found.')
    }

    if (!user.stripeAccountId) {
      return {
        paymentSheet: null,
        boost: null,
        errors: [new StripeNotConnectedError('Stripe is not connected', 'stripeAccountId')],
      }
    }

    try {
      const paymentSheet = await this.stripe.createPaymentSheet(
        input.amount,
        input.currency,
        user.stripeAccountId
      )

      // create boost
      const boost = await BoostModel.create({
        amount: input.amount,
        currency: input.currency,
        user: user.id,
        paymentIntentId: paymentSheet.paymentIntentId,
        state: BoostState.CREATED,
      })

      return {
        paymentSheet: paymentSheet,
        boost,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to create stripe payment sheet. input: %o', err, input)

      throw new GraphQLError('Failed to create payment sheet. Please try again later.')
    }
  }
}
