import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { createQueryConditions, findShadowPlayers } from '../../stats/helpers/shadowPlayers'
import { TeamModel } from '../../stats/models/team'
import GameRepository from '../../stats/repositories/game.repository'
import { SuggestInvitesResponse } from '../types/suggestInvites'

@Resolver()
@Service()
export default class SuggestInvitesResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private gameRepository: GameRepository

  @Query(() => SuggestInvitesResponse)
  async suggestInvites(@Arg('teamId') teamId: Types.ObjectId): Promise<SuggestInvitesResponse> {
    const response = new SuggestInvitesResponse()
    response.edges = []

    try {
      const team = await TeamModel.findById(teamId)
      if (!team) {
        return response
      }

      const organizationId = team.organization

      const teamExternalUserIdsMap =
        await this.gameRepository.collectExternalUserIdsFromTeamGames(teamId)

      const teamOrConditions = createQueryConditions(teamExternalUserIdsMap)
      const teamShadowPlayers = await findShadowPlayers(teamOrConditions)

      for (const shadowPlayer of teamShadowPlayers) {
        response.edges.push({
          node: shadowPlayer,
        })
      }

      // If we have an organization ID, also collect players from other teams in the organization
      if (organizationId) {
        const teamsInOrg = await TeamModel.find({
          organization: organizationId,
          _id: { $ne: teamId }, // Exclude the current team
        })

        if (teamsInOrg.length > 0) {
          const orgTeamIds = teamsInOrg.map((t) => t._id)
          const orgExternalUserIdsMap =
            await this.gameRepository.collectExternalUserIdsFromMultipleTeams(orgTeamIds)

          const orgOrConditions = createQueryConditions(orgExternalUserIdsMap)
          const existingIds = teamShadowPlayers.map((p) => p._id)
          const orgShadowPlayers = await findShadowPlayers(orgOrConditions, existingIds)

          // Add organization shadow players to response after team shadow players
          for (const shadowPlayer of orgShadowPlayers) {
            response.edges.push({
              node: shadowPlayer,
            })
          }
        }
      }

      return response
    } catch (err) {
      this.logger.error('Failed to suggest invites:', err)
      return response
    }
  }
}
