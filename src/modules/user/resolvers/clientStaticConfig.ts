import config from 'config'
import { FieldResolver, Resolver } from 'type-graphql'
import { Service } from 'typedi'

import { User } from '../models/user'
import { ClientStaticConfig } from '../models/user/clientStaticConfig'

@Service()
@Resolver(() => User)
export class ClientStaticConfigResolver {
  @FieldResolver(() => ClientStaticConfig)
  async clientStaticConfig(): Promise<ClientStaticConfig> {
    return {
      inviteContest: {
        enabled: config.clientStaticConfig.inviteContest.enabled,
      },
    }
  }
}
