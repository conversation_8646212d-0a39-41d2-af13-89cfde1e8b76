import { <PERSON>tx, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Root } from 'type-graphql'
import { Service } from 'typedi'

import { ContextType } from '../../..'
import { getEntity } from '../../../helpers/mongoose'
import { Agency } from '../models/agency'
import { UserBio } from '../models/user'

@Service()
@Resolver(() => UserBio)
export class UserBioResolver {
  @FieldResolver(() => Agency, { nullable: true })
  async agency(@Root() root: UserBio, @Ctx() ctx: ContextType): Promise<Agency | null> {
    const bio = getEntity(root)

    if (!bio.agency) {
      return null
    }

    return ctx.loaders.agency.load(bio.agency.toString())
  }

  @FieldResolver(() => Agency, { nullable: true })
  async agencyUnverified(@Root() root: UserBio, @Ctx() ctx: ContextType): Promise<Agency | null> {
    const bio = getEntity(root)

    if (!bio.agencyUnverified) {
      return null
    }

    return ctx.loaders.agency.load(bio.agencyUnverified.toString())
  }
}
