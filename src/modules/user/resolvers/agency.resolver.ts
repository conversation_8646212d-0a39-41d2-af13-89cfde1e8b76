import config from 'config'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { QuerySelector, Types } from 'mongoose'
import pino from 'pino'
import { Arg, Authorized, Ctx, Info, Int, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { resolveAggregation } from '../../../helpers/aggregate'
import { slugifyString } from '../../../helpers/string'
import { generateYearFilter } from '../../../helpers/user'
import { CacheControl } from '../../../middlewares/cacheControl'
import Analytics from '../../../services/analytics'
import { EmailService, EmailTemplate } from '../../../services/email'
import { MediaConverter } from '../../../services/mediaConverter'
import { S3Service } from '../../../services/s3'
import {
  FailedToCreatePresignedUrl,
  Pagination,
  SimpleSort,
  SortInput,
  SortOrder,
  SystemRole,
} from '../../../types/general'
import { updateAgencyCounts } from '../../admin/agency.resolver'
import { UserModel } from '../models'
import { Agency, AgencyModel, SportCategory } from '../models/agency'
import { AgencyFollowModel } from '../models/agencyFollow'
import { AgencyViewModel } from '../models/agencyView'
import { AgentsInvitationModel, InvitationStatus } from '../models/agentsInvitation'
import { BioPosition, Role, SubRole, User, userTemplateSortObject } from '../models/user'
import AgencyRepository from '../repositories/agency.repository'
import { PaginationRepository } from '../repositories/pagination.repository'
import {
  AcceptAgencyInvitationAlreadyAcceptedError,
  AcceptAgencyInvitationExpiredError,
  AcceptAgencyInvitationInvalidTokenError,
  AcceptAgencyInvitationResponse,
  AgenciesFilter,
  AgenciesFollowingResponse,
  AgenciesResponse,
  AgencyUsersFilter,
  AgencyUsersRole,
  CreateAgencyAlreadyExistsError,
  CreateAgencyInput,
  CreateAgencyResponse,
  FollowAgencyNotFoundError,
  FollowAgencyResponse,
  InviteAgentsAlreadyExistsError,
  InviteAgentsInput,
  InviteAgentsResponse,
  UnfollowAgencyNotFoundError,
  UnfollowAgencyResponse,
  UpdateAgencyInput,
  UpdateAgencyNotFoundError,
  UpdateAgencyResponse,
  UploadAgencyLogoInput,
  UploadAgencyLogoResponse,
  VisitAgencyNotFoundError,
  VisitAgencyResponse,
} from '../types/agency'
import { UsersResponse } from '../types/user'

@Resolver((_of) => Agency)
@Service()
export default class AgencyResolver {
  @Inject('app.logger')
  private readonly logger: pino.Logger

  @Inject()
  private readonly s3: S3Service

  @Inject()
  private readonly agencyRepository: AgencyRepository

  @Inject('analytics.service')
  private readonly analytics: Analytics

  @Inject()
  private readonly emailService: EmailService

  @Inject()
  private readonly mediaConverter: MediaConverter

  private readonly pagination: PaginationRepository = new PaginationRepository()

  @Mutation(() => CreateAgencyResponse)
  @Authorized(SystemRole.ADMIN)
  public async createAgency(
    @Arg('input') input: CreateAgencyInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<CreateAgencyResponse> {
    const response = new CreateAgencyResponse()

    try {
      response.agency = await (
        await AgencyModel.create({
          ...input,
          slug: slugifyString(input.name),
        })
      ).populate([{ path: 'affiliateAgencies' }])
    } catch (err) {
      this.logger.error('Failed to create a agency.', err)
      response.errors = [
        new CreateAgencyAlreadyExistsError('Agency already exists', info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => UpdateAgencyResponse)
  @Authorized(SystemRole.ADMIN)
  public async updateAgency(
    @Arg('id') id: Types.ObjectId,
    @Arg('input') input: UpdateAgencyInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<UpdateAgencyResponse> {
    const response = new UpdateAgencyResponse()

    try {
      response.agency = await AgencyModel.findByIdAndUpdate(
        id,
        {
          ...input,
          ...(input.name ? { slug: slugifyString(input.name) } : {}),
        },
        { new: true }
      ).populate([{ path: 'affiliateAgencies' }])
    } catch (err) {
      this.logger.error('Failed to update a agency.', err)
      response.errors = [
        new UpdateAgencyNotFoundError('Agency not found', info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => Boolean)
  @Authorized(SystemRole.ADMIN)
  public async deleteAgency(@Arg('id') id: Types.ObjectId): Promise<boolean> {
    try {
      await AgencyModel.findByIdAndDelete(id)
      return true
    } catch (err) {
      this.logger.error('Failed to delete agency.', err)
      return false
    }
  }

  @Query(() => Agency, { nullable: true })
  public async agency(
    @Arg('id', { nullable: true }) id: Types.ObjectId,
    @Arg('slug', { nullable: true }) slug: string
  ): Promise<Agency | null> {
    try {
      if (slug) {
        return await AgencyModel.findOne({ slug }).populate([{ path: 'affiliateAgencies' }])
      }
      return await AgencyModel.findById(id).populate([{ path: 'affiliateAgencies' }])
    } catch (err) {
      this.logger.error('Failed to find agency.', err)
      return null
    }
  }

  @Query(() => UsersResponse)
  async agencyUsers(
    @Arg('agencyId') agencyId: Types.ObjectId,
    @Arg('filter', { nullable: true }) filter: AgencyUsersFilter,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string
  ): Promise<UsersResponse> {
    const response = new UsersResponse()
    response.edges = []

    const queryFilter: {
      'bio.agency': Types.ObjectId
      role?: Role | { $in: Array<Role | null> } | undefined
      subrole?: SubRole | undefined
      markedForDeletion?: { $exists: boolean }
      'bio.position'?: { $in: BioPosition[] }
      dateOfBirth?: QuerySelector<User>
      agentV2?: Types.ObjectId
    } = {
      'bio.agency': agencyId,
    }

    // optionally add `$or` with dateBirth conditions, and still have the possibility to not include in every aggregation
    const positionsFilter: { 'bio.position'?: { $in: BioPosition[] } } = {}
    let yearsFilter = {}

    const sorts: SortInput = {
      fields: [],
    }

    if (filter?.role && filter.role === AgencyUsersRole.AGENT) {
      queryFilter['subrole'] = SubRole.AGENT

      sorts.fields.push({
        field: 'createdAt',
        order: SortOrder.ASC,
      })
    } else {
      queryFilter['role'] = { $in: [Role.PLAYER, Role.SHADOW_PLAYER, null] }

      sorts.fields.push(
        ...[
          {
            field: 'role',
            order: SortOrder.ASC,
          },
          {
            field: 'epViews',
            order: SortOrder.DESC,
          },
          {
            field: 'userStats.views',
            order: SortOrder.DESC,
          },
        ]
      )
    }

    if (filter?.agentV2) {
      queryFilter.agentV2 = filter.agentV2
    }

    if (filter?.positions) {
      positionsFilter['bio.position'] = { $in: filter.positions }
      if (filter.positions.includes(BioPosition.DEFENDER)) {
        positionsFilter['bio.position'] = {
          $in: [
            ...new Set([
              ...filter.positions,
              BioPosition.LEFT_DEFENSIVE,
              BioPosition.RIGHT_DEFENSIVE,
            ]),
          ],
        }
      }
    }

    if (filter?.years) {
      yearsFilter = generateYearFilter(filter.years)
    }

    const users = await this.pagination.find(
      userTemplateSortObject,
      { ...queryFilter, ...yearsFilter, ...positionsFilter },
      UserModel,
      sorts,
      this.pagination.mapPagination(pagination ?? { first, after, before }),
      [{ path: 'currentTeam', populate: { path: 'organization' } }]
    )

    for (const user of users.nodes) {
      response.edges.push({
        node: user,
      })
    }

    response.pageInfo = users.pageInfo

    // TODO remove from inut and detect fropm qql request, e.g.:
    // fields.fieldsByTypeName.UpdateUserResponse?.user.fieldsByTypeName.User.bio.fieldsByTypeName.UserBio.agencyUnverified
    // then divide queries on mobile/web for agency users (clients vr agents)

    // TODO add generic approach to create selective filters for aggregations
    if (filter?.includeAggregation) {
      const aggregateYearsMatch = {
        ...queryFilter,
        ...positionsFilter,
      }

      const agencyUsersAggregatedYear = await UserModel.aggregate([
        {
          $match: aggregateYearsMatch,
        },
        {
          $group: {
            _id: {
              years: {
                $cond: {
                  if: { $lte: [{ $year: '$dateOfBirth' }, 2006] },
                  then: '2006+',
                  else: { $year: '$dateOfBirth' },
                },
              },
            },
            count: { $sum: 1 },
          },
        },
      ])

      const aggregatePositionsMatch = {
        ...queryFilter,
        ...yearsFilter,
      }
      const agencyUsersAggregatedPosition = await UserModel.aggregate([
        {
          $match: aggregatePositionsMatch,
        },
        {
          $group: {
            _id: {
              positions: '$bio.position',
            },
            count: { $sum: 1 },
          },
        },
      ])

      response.aggregatedInfo = resolveAggregation([
        ...agencyUsersAggregatedPosition,
        ...agencyUsersAggregatedYear,
      ])
    }

    return response
  }

  @Query(() => AgenciesResponse)
  @CacheControl({ maxAge: 300 })
  async agencies(
    @Ctx() ctx: ContextType,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string,

    @Arg('filter', { nullable: true }) filter: AgenciesFilter,
    @Arg('sort', { nullable: true }) sort?: SimpleSort
  ): Promise<AgenciesResponse> {
    const response = new AgenciesResponse()
    response.edges = []

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline = {} as unknown as any

    if (filter?.checked) {
      pipeline.checked = true
    }

    if (filter?.integrated || !ctx.user?.admin) {
      pipeline.integrated = true
    }

    if (!filter?.category) {
      pipeline.categories = SportCategory.HOCKEY
    } else {
      pipeline.categories = filter.category
    }

    const agencies = await this.agencyRepository.search(
      filter?.searchQuery ?? null,
      pagination ?? { first, after, before },
      pipeline,
      sort
    )

    for (const agency of agencies.nodes) {
      response.edges.push({
        node: agency,
      })
    }

    response.pageInfo = agencies.pageInfo

    return response
  }

  @Mutation(() => UploadAgencyLogoResponse)
  @Authorized(SystemRole.ADMIN)
  async uploadAgencyLogo(
    @Arg('input', { nullable: true }) input: UploadAgencyLogoInput,
    @Arg('id', (_type) => Types.ObjectId) id: Types.ObjectId,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadAgencyLogoResponse> {
    const response = new UploadAgencyLogoResponse()

    if (input) {
      if (input.uploadSuccessful) {
        response.agency = await AgencyModel.findById(id)

        if (response.agency?.logoUnconfirmed) {
          if (response.agency.logo) {
            // we don't need to await this
            this.s3
              .remove(response.agency.logo)
              .catch((err) => this.logger.error('Failed to remove old agency logo', err))
          }

          const ogImageKey = await this.mediaConverter.createAndUploadOGImage(
            response.agency.logoUnconfirmed
          )

          response.agency = await AgencyModel.findOneAndUpdate(
            { _id: id },
            { logo: response.agency.logoUnconfirmed, logoUnconfirmed: null, ogLogo: ogImageKey }
          )
        }

        return response
      } else {
        response.agency = await AgencyModel.findOneAndUpdate({ _id: id }, { logoUnconfirmed: null })

        return response
      }
    }

    try {
      const logoKey = `images/agencies/${id}/logo/${new Date().getTime()}.png`
      response.preSignedUrl = await this.s3.createPresignedUrl(logoKey, 'image/png')

      response.agency = await AgencyModel.findOneAndUpdate(
        { _id: id },
        { logoUnconfirmed: logoKey }
      )
    } catch (err) {
      this.logger.error('Failed to upload agency logo.', err)
      response.errors = [
        new FailedToCreatePresignedUrl(`Failed to create preSignedUrl`, info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => VisitAgencyResponse)
  async visitAgency(
    @Arg('id') id: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<VisitAgencyResponse> {
    try {
      const agency = await AgencyModel.findById(id)

      if (!agency) {
        return {
          agency: null,
          errors: [new VisitAgencyNotFoundError('Agency does not exist.', 'visitAgency')],
        }
      }

      const viewExists = await AgencyViewModel.findOne({
        agency: id,
        fingerprint: ctx.fingerprint,
        createdAt: { $gt: new Date(Date.now() - 1000 * 60 * 60 * 4) },
      })

      if (viewExists) {
        return {
          agency,
          errors: [],
        }
      }

      let agencyUpdated
      if (!agency.stats) {
        agency.stats = {
          followers: 0,
          views: 1,
        }

        agencyUpdated = await AgencyModel.findOneAndUpdate(
          { _id: id },
          { stats: agency.stats },
          { new: true }
        )
      } else {
        agencyUpdated = await AgencyModel.findOneAndUpdate(
          { _id: id },
          { $inc: { 'stats.views': 1 } },
          { new: true }
        )
      }

      await AgencyViewModel.create({
        agency: id,
        ipAddress: ctx.ipAddress,
        fingerprint: ctx.fingerprint,
      })

      this.analytics.trackEvent('newAgencyView', {}, id.toString())

      return {
        agency: agencyUpdated,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to visit agency:', err)

      throw new GraphQLError('Failed to visit agency', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => FollowAgencyResponse)
  @Authorized()
  async followAgency(
    @Arg('id') id: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<FollowAgencyResponse> {
    try {
      let agency = await AgencyModel.findById(id)

      if (!agency) {
        return {
          agency,
          errors: [new FollowAgencyNotFoundError('Agency does not exist.', 'followAgency')],
        }
      }

      await AgencyFollowModel.create({
        follower: ctx.user?.id,
        agency: agency._id,
      })

      if (!agency.stats) {
        agency.stats = {
          followers: 1,
          views: 0,
        }

        agency = await AgencyModel.findOneAndUpdate(
          { _id: id },
          { stats: agency.stats },
          { new: true }
        )
      } else {
        agency = await AgencyModel.findOneAndUpdate(
          { _id: id },
          { $inc: { 'stats.followers': 1 } },
          { new: true }
        )
      }

      this.analytics.trackEvent('newAgencyFollow', {}, id.toString())

      return {
        agency,
        errors: [],
      }
    } catch (err) {
      if (err.name === 'MongoServerError' && err.code === 11000) {
        // duplicate key error, we don't mind this
      } else {
        this.logger.error('DB error:', err)
      }

      throw new GraphQLError('Failed to follow agency', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => UnfollowAgencyResponse)
  @Authorized()
  async unfollowAgency(
    @Arg('id') id: Types.ObjectId,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<UnfollowAgencyResponse> {
    try {
      const agencyToUnfollow = await AgencyModel.findById(id)
      if (!agencyToUnfollow) {
        return {
          agency: null,
          errors: [
            new UnfollowAgencyNotFoundError(`Agency does not exist`, info.path.key.toString()),
          ],
        }
      }

      const follow = await AgencyFollowModel.findOne({
        agency: id,
        follower: ctx.user!.id,
      })

      if (follow) {
        await follow.deleteOne()

        agencyToUnfollow.stats!.followers -= 1
        await agencyToUnfollow.save()

        return {
          agency: agencyToUnfollow,
          errors: [],
        }
      }

      return {
        agency: agencyToUnfollow,
        errors: [
          new UnfollowAgencyNotFoundError(`Agency follow not found`, info.path.key.toString()),
        ],
      }
    } catch (err) {
      this.logger.error('Failed to unfollow agency:', err)

      throw new GraphQLError('Failed to unfollow agency', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  private async sendInvitationEmail(email: string, token: string, agency: Agency) {
    const inviteLink = `${config.webUrl}/invitation?agencyId=${agency._id}&token=${token}`

    await this.emailService.sendEmail(email, EmailTemplate.AGENT_INVITATION, {
      verifyUrl: inviteLink,
      agency: agency.name,
    })
  }

  @Mutation(() => InviteAgentsResponse)
  @Authorized(SystemRole.USER)
  public async inviteAgents(
    @Ctx() ctx: ContextType,
    @Arg('input') input: InviteAgentsInput
  ): Promise<InviteAgentsResponse> {
    const { emails, agencyId } = input
    const user = await UserModel.findById(ctx.user!.id)
    const agency = await AgencyModel.findById(agencyId)

    if (!ctx.user?.admin && user?.bio?.agency?._id.toString() !== input.agencyId?.toString()) {
      throw new GraphQLError('You are not allowed to invite agents to this agency', {
        extensions: { code: 'FORBIDDEN' },
      })
    }

    if (!agency) {
      throw new GraphQLError('Agency not found', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }

    const response = new InviteAgentsResponse()
    response.success = true
    response.errors = []

    try {
      for (const email of emails) {
        const existingInvitation = await AgentsInvitationModel.findOne({
          email,
          agencyId,
        })

        if (existingInvitation) {
          if (
            existingInvitation.status === InvitationStatus.PENDING &&
            existingInvitation.expiresAt > new Date()
          ) {
            response.errors.push(
              new InviteAgentsAlreadyExistsError(
                `An active invitation for ${email} already exists, email was not sent.`,
                'inviteAgents'
              )
            )
            response.success = false
            continue
          } else {
            await AgentsInvitationModel.deleteOne({ _id: existingInvitation._id })
          }
        }

        const token = Math.random().toString(36).substring(2, 15)

        await AgentsInvitationModel.create({
          email,
          agencyId,
          token,
          status: InvitationStatus.PENDING,
          expiresAt: new Date(Date.now() + 1000 * 60 * 15),
        })

        await this.sendInvitationEmail(email, token, agency)
      }

      return response
    } catch (err) {
      this.logger.error('Failed to invite agents:', err)
      throw new GraphQLError('Failed to invite agents', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => AcceptAgencyInvitationResponse)
  @Authorized(SystemRole.USER)
  public async acceptAgencyInvitation(
    @Arg('token') token: string,
    @Arg('agencyId') agencyId: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<AcceptAgencyInvitationResponse> {
    const response = new AcceptAgencyInvitationResponse()
    response.success = false

    try {
      const invitation = await AgentsInvitationModel.findOne({ token, agencyId })

      if (!invitation) {
        response.errors = [
          new AcceptAgencyInvitationInvalidTokenError(
            'Invitation token is invalid or does not match the agency',
            'acceptAgencyInvitation'
          ),
        ]
        return response
      }

      if (invitation.status === InvitationStatus.ACCEPTED) {
        response.errors = [
          new AcceptAgencyInvitationAlreadyAcceptedError(
            'This invitation has already been accepted',
            'acceptAgencyInvitation'
          ),
        ]
        return response
      }

      if (invitation.expiresAt < new Date()) {
        response.errors = [
          new AcceptAgencyInvitationExpiredError(
            'This invitation has expired',
            'acceptAgencyInvitation'
          ),
        ]
        return response
      }

      const user = await UserModel.findById(ctx.user!.id)

      if (!user) {
        throw new GraphQLError('User not found', {
          extensions: { code: 'INTERNAL_SERVER_ERROR', path: 'acceptAgencyInvitation' },
        })
      }

      const oldAgency = await AgencyModel.findById(user?.bio?.agency)

      invitation.status = InvitationStatus.ACCEPTED
      await invitation.save()

      await UserModel.findOneAndUpdate(
        { _id: ctx.user?.id },
        {
          $set: {
            subrole: SubRole.AGENT,
            'bio.agency': agencyId,
          },
        }
      )

      // update agency counts
      const agency = await AgencyModel.findById(agencyId)

      if (!agency) {
        throw new GraphQLError('Agency not found', {
          extensions: { code: 'INTERNAL_SERVER_ERROR', path: 'acceptAgencyInvitation' },
        })
      }

      await updateAgencyCounts(user, agency, oldAgency)

      response.success = true

      return response
    } catch (err) {
      this.logger.error('Failed to accept invitation:', err)
      throw new GraphQLError('Failed to accept invitation due to internal error', {
        extensions: { code: 'INTERNAL_SERVER_ERROR', path: 'acceptAgencyInvitation' },
      })
    }
  }

  @Query(() => AgenciesFollowingResponse)
  @Authorized()
  async myFollowingAgencies(
    @Ctx() ctx: ContextType,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string
  ): Promise<AgenciesFollowingResponse> {
    const response = new AgenciesFollowingResponse()
    response.edges = []

    const mergedPagination = {
      first: first ?? pagination?.first,
      after: after ?? pagination?.after,
      before: before ?? pagination?.before,
    }

    const agencies = await this.pagination.find(
      { _id: new Types.ObjectId() },
      { follower: new Types.ObjectId(ctx.user!.id) },
      AgencyFollowModel,
      { fields: [{ field: '_id', order: SortOrder.DESC }] },
      this.pagination.mapPagination(mergedPagination),
      [{ path: 'agency' }]
    )

    for (const agency of agencies.nodes) {
      response.edges.push({
        node: agency.agency as Agency,
      })
    }

    response.pageInfo = agencies.pageInfo

    return response
  }

  @Query(() => Boolean)
  @Authorized()
  async isFollowingAgency(
    @Arg('agencyId') agencyId: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<boolean> {
    const follow = await AgencyFollowModel.findOne({
      agency: agencyId,
      follower: ctx.user!.id,
    })

    return !!follow
  }

  @Query(() => UsersResponse)
  async agentsClients(
    @Arg('slug') slug: string,
    @Arg('first', (_) => Int, { nullable: true }) first?: number,
    @Arg('after', { nullable: true }) after?: string,
    @Arg('before', { nullable: true }) before?: string
  ) {
    const response = new UsersResponse()
    response.edges = []

    const agent = await UserModel.findOne({ slug })

    if (!agent) {
      response.pageInfo = {
        hasNextPage: false,
        hasPreviousPage: false,
        endCursor: undefined,
        startCursor: undefined,
        totalCount: 0,
      }

      return response
    }

    const users = await this.pagination.find(
      { _id: new Types.ObjectId() },
      { agentV2: agent._id, public: { $ne: false } },
      UserModel,
      { fields: [{ field: '_id', order: SortOrder.DESC }] },
      this.pagination.mapPagination({ before, first, after }),
      [{ path: 'currentTeam', populate: { path: 'organization' } }]
    )

    for (const user of users.nodes) {
      response.edges.push({
        node: user as User,
      })
    }

    response.pageInfo = users.pageInfo

    return response
  }
}
