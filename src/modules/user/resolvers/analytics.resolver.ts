import pino from 'pino'
import { Arg, Authorized, Mutation, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import Analytics from '../../../services/analytics'
import { CreatePassiveEventInput } from '../types/analytics'

@Resolver()
@Service()
export default class AnalyticsResolver {
  @Inject('app.logger')
  private readonly logger: pino.Logger

  @Inject('analytics.service')
  private readonly analytics: Analytics

  @Mutation(() => Boolean)
  @Authorized()
  public async createPassiveEvent(@Arg('input') input: CreatePassiveEventInput): Promise<boolean> {
    const data = JSON.parse(input.params)

    this.analytics.trackEvent(input.pathname, data, input.userId).catch((err) => {
      this.logger.error('Failed to track passive event', err)
    })

    return true
  }
}
