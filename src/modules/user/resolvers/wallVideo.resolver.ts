import config from 'config'
import { StreamClient } from 'getstream'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { ResolveTree } from 'graphql-parse-resolve-info'
import { FilterQuery, Types } from 'mongoose'
import pino from 'pino'
import { Arg, Authorized, Ctx, Info, Int, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { Fields } from '../../../helpers/decorators/fields'
import { capitalizeWords } from '../../../helpers/format'
import { CacheControl } from '../../../middlewares/cacheControl'
import Analytics from '../../../services/analytics'
import { addNotificationActivity, FeedActivityVerb } from '../../../services/helpers/streamFeed'
import { MediaConverter } from '../../../services/mediaConverter'
import { PushNotificationService, PushNotificationType } from '../../../services/pushNotification'
import { S3Service } from '../../../services/s3'
import { SlackService } from '../../../services/slack'
import { SQSService } from '../../../services/sqs'
import {
  FailedToCreatePresignedUrl,
  FailedToDelete,
  FailedToEdit,
  Pagination,
  SortOrder,
  WallVideoNotFound,
} from '../../../types/general'
import { UserModel, WallVideoModel } from '../models'
import { BioPosition, Role, SubRole, User } from '../models/user'
import { PaginationRepository } from '../repositories/pagination.repository'
import { FEED_NAMES } from '../types/streamFeed'
import {
  DeleteWallVideoResponse,
  EditHighlightsInput,
  EditHighlightsResponse,
  EditWallVideoCategoryResponse,
  EditWallVideoInput,
  LatestClipsFilter,
  UploadWallVideoInput,
  UploadWallVideoResponse,
  UploadWallVideoSegmentationInput,
  UploadWallVideoSegmentationResponse,
  VideosByIdsInput,
  VideosByIdsResponse,
  VideoSegmentationMessageBody,
  WallVideosFilter,
  WallVideosResponse,
} from '../types/video'

import { videoTemplateSortObject, WallVideo, WallVideoType } from './../models/wallVideo'

@Resolver()
@Service()
export default class WallVideoResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private s3: S3Service

  @Inject()
  private mediaConverter: MediaConverter

  @Inject()
  private slackService: SlackService

  @Inject('analytics.service')
  private analytics: Analytics

  @Inject()
  private sqs: SQSService

  @Inject('streamFeed.client')
  private streamFeed: StreamClient

  @Inject('pushNotification.service')
  private pushNotificationService: PushNotificationService

  private readonly pagination: PaginationRepository = new PaginationRepository()

  @Query(() => WallVideosResponse)
  @CacheControl({ maxAge: 60, scope: 'PUBLIC' })
  async latestClips(
    @Arg('first', () => Int, { nullable: true }) first?: number,
    @Arg('last', () => Int, { nullable: true }) last?: number,
    @Arg('after', { nullable: true }) after?: string,
    @Arg('before', { nullable: true }) before?: string,
    @Arg('filter', { nullable: true }) filter?: LatestClipsFilter
  ): Promise<WallVideosResponse> {
    const response = new WallVideosResponse()
    response.edges = []

    const sorting = { fields: [{ field: 'createdAt', order: SortOrder.DESC }] }
    const latestYear = 2005

    // Query filter - AND
    const userQuery: FilterQuery<typeof UserModel> = { $and: [] }

    if (filter?.teamId) {
      userQuery.$and?.push({ currentTeam: filter.teamId })
    }
    if (filter?.agencyId) {
      userQuery.$and?.push({ 'bio.agency': filter.agencyId })
    }
    if (filter?.countries?.length) {
      userQuery.$and?.push({ country: { $in: filter.countries } })
    }

    if (filter?.positions?.length) {
      userQuery.$and?.push({
        $or: [
          { 'bio.position': { $in: filter.positions } },
          ...(filter.positions.includes(BioPosition.DEFENDER)
            ? [
                { 'bio.position': BioPosition.LEFT_DEFENSIVE },
                { 'bio.position': BioPosition.RIGHT_DEFENSIVE },
              ]
            : []),
        ],
      })
    }

    if (filter?.yearOfBirth?.length) {
      const yearCondition = filter.yearOfBirth.includes(latestYear)
        ? { $lte: latestYear }
        : { $in: filter.yearOfBirth }

      userQuery.$and?.push({
        $expr: {
          [yearCondition.$in ? '$in' : '$lte']: [
            { $year: { date: '$dateOfBirth', timezone: 'UTC' } },
            yearCondition.$in || latestYear,
          ],
        },
      })
    }

    // TODO: in case of pagination we should be able to omit fetching of users
    let userIds: { _id: string }[] = []
    // If there are no filters, we should match all users
    if (userQuery.$and?.length === 0) {
      delete userQuery.$and
    } else {
      // Find matching user IDs
      userIds = await UserModel.find(userQuery, { _id: 1 }, { limit: 200 })
    }

    const userObjectIds = userIds.map((u) => new Types.ObjectId(u._id))

    // If no users match the filter, return an empty response
    if (userObjectIds.length === 0 && userQuery.$and) {
      response.pageInfo = {
        hasNextPage: false,
        hasPreviousPage: false,
      }
      return response
    }

    let paginationLast = 0
    if (before || last || (!before && !after)) {
      paginationLast = last ?? (!before && !after ? (first ?? 10) : 10)
    }

    const wallVideos = await this.pagination.find(
      videoTemplateSortObject,
      {
        ...(!userQuery.$and ? {} : { user: { $in: userObjectIds } }),
        uploadFinished: true,
        hidden: { $ne: true },
        type: { $nin: [WallVideoType.OTHER, WallVideoType.TRAINING] },
      },
      WallVideoModel,
      sorting,
      {
        first: (after || first) && !before ? (first ?? 10) : 0,
        last: paginationLast,
        cursor: after ?? before,
      },
      null
    )

    await WallVideoModel.populate(wallVideos.nodes, {
      path: 'user',
      populate: { path: 'currentTeam' },
    })

    for (const wallVideo of wallVideos.nodes) {
      response.edges.push({ node: wallVideo })
    }

    response.pageInfo = wallVideos.pageInfo
    return response
  }

  @Query(() => VideosByIdsResponse)
  async videosByIds(@Arg('input') input: VideosByIdsInput): Promise<VideosByIdsResponse> {
    const response = new VideosByIdsResponse()

    const wallVideos = await WallVideoModel.find({
      // TODO: remove temporary fix for older version of app which send gameId as videoId
      _id: { $in: input.videoIds.filter((id) => !id.includes('gameId')) },
    })

    await WallVideoModel.populate(wallVideos, {
      path: 'user',
      populate: { path: 'currentTeam' },
    })

    response.videos = wallVideos

    return response
  }

  @Query(() => WallVideosResponse)
  async wallVideos(
    @Ctx() ctx: ContextType,
    @Fields() fields: ResolveTree,
    @Arg('pagination', {
      nullable: true,
      deprecationReason: 'Use first, last, after and before directly.',
    })
    pagination: Pagination,
    @Arg('first', () => Int, { nullable: true }) first: number,
    @Arg('last', () => Int, { nullable: true }) last: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string,
    @Arg('filter', { nullable: true }) filter: WallVideosFilter,
    @Arg('includeCounts', { nullable: true }) includeCounts: boolean
  ): Promise<WallVideosResponse> {
    const response = new WallVideosResponse()
    response.edges = []

    let userId = filter?.userId ?? ctx.user?.id

    let videoFromFilterCursor: string | null = null

    if (filter?.slug) {
      const user = await UserModel.findOne({ slug: filter.slug })
      userId = user?._id
    }

    const sorting = {
      fields: [
        {
          field: 'createdAt',
          order: SortOrder.DESC,
        },
      ],
    }

    let video

    if (filter?.videoId && !after && !pagination?.after && !before && !pagination?.before) {
      video = await WallVideoModel.findOne({ _id: filter.videoId })
      userId = video?.user?._id
      videoFromFilterCursor = this.pagination.buildCursorFromNodeWithSort(
        videoTemplateSortObject,
        video,
        sorting
      )

      if (!video) {
        throw new GraphQLError('Video not found', {
          extensions: { code: 'NOT_FOUND', noLog: true },
        })
      }
    }

    if (!userId) {
      throw new GraphQLError('User not logged in or missing userId or slug in filter.')
    }

    if (filter?.slug) {
      const user = await UserModel.findOne({ slug: filter.slug })
      userId = user?._id
    }

    let paginationLast = 0
    if (before || last || (!before && !after)) {
      paginationLast = last ?? (!before && !after ? (first ?? 10) : 10)
    }

    const resolvedPagination = {
      // TODO: refactor to something meaningful
      first:
        (after || first || pagination?.first) && !before ? (first ?? pagination?.first ?? 10) : 0,
      last: paginationLast,
      cursor: videoFromFilterCursor ?? after ?? before,
    }

    const wallVideos = await this.pagination.find(
      videoTemplateSortObject,
      {
        user: userId,
        uploadFinished: true,
        hidden: { $ne: true },
        ...(filter?.type ? { type: filter.type } : {}),
        ...(filter?.onlyLandscape ? { $expr: { $gt: ['$width', '$height'] } } : {}),
      },
      WallVideoModel,
      sorting,
      resolvedPagination,
      null,
      video
    )

    if (
      fields.fieldsByTypeName.WallVideosResponse.edges.fieldsByTypeName.WallVideoEdge.node
        .fieldsByTypeName.WallVideo.user
    ) {
      await WallVideoModel.populate(wallVideos.nodes, {
        path: 'user',
        populate: { path: 'currentTeam' },
      })
    }

    for (const wallVideo of wallVideos.nodes) {
      response.edges.push({
        node: wallVideo,
      })
    }

    if (includeCounts) {
      const categoryCounts = await WallVideoModel.aggregate([
        { $match: { user: new Types.ObjectId(userId) } },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 },
          },
        },
      ])

      response.categoryCounts = categoryCounts.map((categoryCount) => {
        return {
          category: categoryCount._id,
          count: categoryCount.count,
        }
      })
    }

    response.pageInfo = wallVideos.pageInfo

    return response
  }

  @Mutation(() => UploadWallVideoResponse)
  @Authorized()
  async uploadWallVideo(
    @Arg('input', { nullable: true }) input: UploadWallVideoInput,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadWallVideoResponse> {
    const response = new UploadWallVideoResponse()

    let user = await UserModel.findOne({ _id: ctx.user!.id })

    if (!user) {
      throw new GraphQLError('User not found', { extensions: { code: 'NOT_FOUND' } })
    }

    const wallURL = `${config.webUrl}${user.slug}/wall`.toLowerCase()
    if (input.confirmation) {
      if (input.confirmation && input.confirmation.uploadSuccessful) {
        response.wallVideo = await WallVideoModel.findOneAndUpdate(
          { _id: input.confirmation.id },
          {
            uploadFinished: true,
            hls: true,
          },
          { new: true }
        )

        const mediaResponse = await this.mediaConverter.createHLSVideo(response.wallVideo)

        if (!mediaResponse || mediaResponse.Job?.Status === 'ERROR') {
          this.logger.error(
            `Failed to create media converter job for wall video ${response.wallVideo?._id}`,
            mediaResponse?.Job
          )

          await WallVideoModel.findOneAndUpdate(
            { _id: input.confirmation.id },
            {
              hls: false,
            }
          )
        }

        if (response.wallVideo?.segmentation) {
          await this.sqs.sendMessage(
            config.aws.sqs.segmentationQueueUrl,
            {
              videoId: response.wallVideo._id,
              userId: ctx.user!.id,
            } as VideoSegmentationMessageBody,
            { MessageDeduplicationId: response.wallVideo._id.toString() }
          )
        }

        user = await UserModel.findOneAndUpdate(
          { _id: ctx.user!.id },
          {
            $inc: { numberOfVideos: 1 },
          }
        ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

        // Stream feed activity
        try {
          const userFeed = this.streamFeed.feed(FEED_NAMES.USER, ctx.user!.id)
          const newActivity = await userFeed.addActivity({
            actor: ctx.user!.id,
            verb: FeedActivityVerb.ADD,
            object: 'wallVideo',
            foreign_id: input.confirmation.id.toString(),
          })

          if (newActivity?.id) {
            await WallVideoModel.findOneAndUpdate(
              { _id: input.confirmation.id },
              {
                streamFeedActivityId: newActivity.id,
              }
            )
          } else {
            this.logger.error(`Added streamfeed activity missing ID, for ${input.confirmation.id}:`)
          }
        } catch (error) {
          this.logger.error(
            `Failed to add streamfeed activity for ${input.confirmation.id}:`,
            error
          )
        }

        // analytics
        await Promise.all([
          this.slackService.sendMessage(
            `User ${user!.name} uploaded new wall video (${input.confirmation.id}), ${wallURL}`,
            config.slack.channels.videoUpload
          ),
          this.analytics.trackEvent(
            'clipVideoUploaded',
            {
              category: response.wallVideo?.type ?? '',
              added_date: response.wallVideo?.createdAt.toISOString() ?? '',
            },
            ctx
          ),
          this.analytics.updateCustomAttrs(user!, {
            player_clips_last_added: new Date().toISOString(),
          }),
          this.analytics.addToCustomAttr(user!, 'player_clips_count', 1),
          this.analytics.updateUserAttrs(user),
        ])

        return response
      } else {
        // TODO: maybe also delete the file from S3 if it exists
        await WallVideoModel.deleteOne({ _id: input.confirmation.id })

        return response
      }
    }

    try {
      const user = await UserModel.findOne({ _id: ctx.user!.id })
      // TODO if we generate video id here, we don't need to update video later
      const wallVideo = await WallVideoModel.create({
        title: input.data?.title,
        type: input.data?.type,
        size: input.data?.size,
        width: input.data?.width,
        height: input.data?.height,
        duration: input.data?.duration,
        frameRate: input.data?.frameRate,
        repeat: input.data?.repeat,
        ...(input.data?.segmentation
          ? {
              segmentation: {
                ...input.data.segmentation,
                finished: false,
              },
            }
          : {}),
        user,
      })

      const commonKey = wallVideo.id

      const videoThumbnailKey = `images/${ctx.user!.id}/wall-video-thumbnails/${commonKey}.jpg`
      const videoKey = `videos/${ctx.user!.id}/wall-video/${commonKey}.mp4`
      const hlsKey = `videos/${ctx.user!.id}/wall-video/${commonKey}-hls`

      response.preSignedUrl = await this.s3.createPresignedUrl(videoKey, 'video/mp4')
      response.thumbnailPreSignedUrl = await this.s3.createPresignedUrl(
        videoThumbnailKey,
        'video/jpg'
      )

      // save the key to the user
      response.wallVideo = await WallVideoModel.findOneAndUpdate(
        { _id: wallVideo.id },
        {
          path: videoKey,
          thumbnail: videoThumbnailKey,
          hlsPath: hlsKey,
          uploadFinished: false,
        },
        { new: true }
      )
    } catch (err) {
      this.logger.error("Failed to create preSignedUrl for users' avatar:", err)
      response.errors = [
        new FailedToCreatePresignedUrl(`Failed to create preSignedUrl`, info.path.key.toString()),
      ]
    }

    return response
  }

  @Mutation(() => DeleteWallVideoResponse)
  @Authorized()
  async deleteWallVideo(
    @Arg('id', { nullable: false }) id: string,
    @Info() info: GraphQLResolveInfo,
    @Ctx() ctx: ContextType
  ): Promise<DeleteWallVideoResponse> {
    const response = new DeleteWallVideoResponse()

    const wallVideo = await WallVideoModel.findOne({ _id: id, user: ctx.user!.id })

    if (!wallVideo) {
      response.errors = [new FailedToDelete(`Wall video not found`, info.path.key.toString())]
      this.logger.error(`Delete video: wall video not found %o`, ctx)
      return response
    }

    const user = await UserModel.findOneAndUpdate(
      { _id: ctx.user!.id },
      {
        $inc: { numberOfVideos: -1 },
        $pull: { highlights: { _id: wallVideo._id } },
      }
    ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

    if (!user) {
      throw new GraphQLError('User not found', { extensions: { code: 'NOT_FOUND' } })
    }

    const videoKey = wallVideo.path
    const videoThumbnailKey = wallVideo.thumbnail
    try {
      if (wallVideo.hlsPath) {
        await this.s3.emptyS3Directory(wallVideo.hlsPath)
      }

      // Stream feed activity
      const userFeed = this.streamFeed.feed(FEED_NAMES.USER, ctx.user!.id)

      await Promise.all([
        this.s3.remove(videoKey),
        this.s3.remove(videoThumbnailKey),
        WallVideoModel.deleteOneByUser(ctx.user!.id, { _id: id }),
        this.analytics.trackEvent(
          'clipVideoDeleted',
          {
            category: wallVideo.type,
            deleted_date: new Date().toISOString(),
            spotlight: !!wallVideo.segmentation,
            processed: wallVideo.segmentation?.finished ?? false,
          },
          ctx
        ),
        this.analytics.addToCustomAttr(user, 'player_clips_count', -1),
        this.analytics.updateUserAttrs(user),
        userFeed.removeActivity({
          foreign_id: id,
        }),
      ])

      response.success = true

      this.logger.info(`Deleted wall video ${videoKey} and thumbnail ${videoThumbnailKey} from S3`)
    } catch (err) {
      this.logger.error(
        `Failed to delete wall video ${videoKey} and thumbnail ${videoThumbnailKey} from S3`,
        err
      )

      response.errors = [
        new FailedToDelete(`Failed to delete wall video`, info.path.key.toString()),
      ]
      response.success = false
    }

    return response
  }

  @Mutation(() => EditWallVideoCategoryResponse)
  @Authorized()
  async editWallVideoCategory(
    @Arg('input', { nullable: false }) input: EditWallVideoInput,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<EditWallVideoCategoryResponse> {
    const response = new EditWallVideoCategoryResponse()

    try {
      const wallVideo = await WallVideoModel.findOneAndUpdate(
        { _id: input.id, user: ctx.user!.id },
        {
          type: input.type,
        },
        { new: true }
      )

      if (!wallVideo) {
        response.errors = [new WallVideoNotFound(`Wall video not found`, info.path.key.toString())]
        this.logger.error(`Edit category: wall video not found`)
        return response
      }

      response.wallVideo = wallVideo
    } catch {
      response.errors = [
        new FailedToEdit(`Failed to edit wall video category`, info.path.key.toString()),
      ]
      this.logger.error(`Failed to edit wall video category`)
    }

    return response
  }

  // TODO change this to react to messages from Amazon SQS
  @Mutation(() => Boolean)
  @Authorized()
  async likeWallVideo(
    @Arg('id', { nullable: false }) id: string,
    @Arg('isHighlight', { nullable: true, defaultValue: false }) isHighlight: boolean,
    @Ctx() ctx: ContextType
  ): Promise<boolean> {
    let video: WallVideo | null = null

    if (!ctx.user?.id) {
      return false
    }

    try {
      video = await WallVideoModel.findById(id).populate([{ path: 'user' }])

      if (!video) {
        return false
      }

      // Stream Feed add activity to notification feed
      const user = await UserModel.findById(ctx.user?.id)

      if (user) {
        const activity = await addNotificationActivity({
          userId: video.user._id.toString(),
          actor: user,
          verb: isHighlight ? FeedActivityVerb.LIKE_HIGHLIGHT : FeedActivityVerb.LIKE_VIDEO,
          object: id,
          foreignId: id,
        })

        const clipOrHighlight = isHighlight ? 'Highlight' : 'Clip'

        if (user.role === Role.SPORTS_PROFESSIONAL && user.subrole) {
          await this.pushNotificationService.sendPushNotification(video.user as User, {
            title: `New like from ${user.subrole.toLowerCase()}! 👍🕵️`,
            message: `${capitalizeWords(user.subrole)} ${user.name} just liked your ${clipOrHighlight}.`,
            type: isHighlight
              ? PushNotificationType.VIDEO_HIGHLIGHT_LIKE
              : PushNotificationType.VIDEO_CLIP_LIKE,
            key: `video${clipOrHighlight}Like-${video.user.id}-${user.id}-${new Date().getDate()}`,
            params: {
              destinationPath:
                user.subrole === SubRole.AGENT ? `/agent/${user.slug}` : `/${user.slug}`,
            },
            forcePush: true,
          })
        } else {
          await this.pushNotificationService.sendPushNotification(video.user as User, {
            title: 'New like! 👍',
            message: `${user.name ?? 'Someone'} just liked your ${clipOrHighlight}.`,
            type: isHighlight
              ? PushNotificationType.VIDEO_HIGHLIGHT_LIKE
              : PushNotificationType.VIDEO_CLIP_LIKE,
            key: `video${clipOrHighlight}Like-${video.user.id}-${user.id}-${new Date().getDate()}`,
            params: {
              destinationPath: `/${user.slug}`,
            },
            forcePush: true,
          })
        }

        return !!activity
      } else {
        return false
      }
    } catch (error) {
      this.logger.error(`Failed to like wall video: ${video?._id.toString()}`, error)

      return false
    }
  }

  @Mutation(() => EditHighlightsResponse)
  @Authorized()
  async editHighlights(
    @Arg('input', { nullable: false }) input: EditHighlightsInput,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<EditHighlightsResponse> {
    const response = new EditHighlightsResponse()

    try {
      const highlightVideos = await WallVideoModel.find({
        _id: { $in: input.highlights },
        user: ctx.user!.id,
      })

      // order the videos by the order in the input
      const orderedVideos = []
      for (const videoId of input.highlights) {
        orderedVideos.push(highlightVideos.find((v) => v._id.toString() === videoId.toString()))
      }

      const user = await UserModel.findOneAndUpdate(
        { _id: ctx.user!.id },
        {
          highlights: orderedVideos,
        },
        { new: true }
      )

      if (!user) {
        throw new GraphQLError('User not found', { extensions: { code: 'NOT_FOUND' } })
      }

      response.user = user
    } catch (err) {
      this.logger.error(`Failed to edit highlights`, err)

      response.errors = [new FailedToEdit(`Failed to edit highlights`, info.path.key.toString())]
    }

    return response
  }

  @Mutation(() => UploadWallVideoSegmentationResponse)
  @Authorized()
  async uploadWallVideoSegmentation(
    @Arg('input', { nullable: false }) input: UploadWallVideoSegmentationInput,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadWallVideoSegmentationResponse> {
    const response = new UploadWallVideoSegmentationResponse()

    try {
      if (input.confirmation) {
        if (input.confirmation.segmentationSuccessful) {
          const wallVideo = await WallVideoModel.findOneAndUpdate(
            { _id: input.id, user: ctx.user!.id },
            {
              $set: {
                'segmentation.finished': true,
              },
            }
          )

          if (!wallVideo) {
            response.errors = [
              new WallVideoNotFound(`Wall video not found`, info.path.key.toString()),
            ]
            this.logger.error(`Upload segmentation confirmation: wall video not found`)
            return response
          }

          response.wallVideo = wallVideo
          return response
        } else {
          this.logger.error(`Upload segmentation confirmation: segmentation failed`)

          return response
        }
      }

      const segmentationPath = `videos/${ctx.user!.id}/wall-video/${input.id}-segmentation.json`
      const wallVideo = await WallVideoModel.findOneAndUpdate(
        { _id: input.id, user: ctx.user!.id },
        {
          segmentation: {
            path: segmentationPath,
            finished: false,
          },
        }
      )

      if (!wallVideo) {
        response.errors = [new WallVideoNotFound(`Wall video not found`, info.path.key.toString())]
        this.logger.error(`Upload segmentation: wall video not found`)
        return response
      }

      response.wallVideo = wallVideo
      response.preSignedUrl = await this.s3.createPresignedUrl(segmentationPath, 'application/json')
    } catch {
      response.errors = [
        new FailedToEdit(`Failed to edit wall video segmentation`, info.path.key.toString()),
      ]
      this.logger.error(`Failed to edit wall video segmentation`)
    }

    return response
  }

  @Query(() => [WallVideo])
  @Authorized()
  async exampleWallVideos(): Promise<WallVideo[]> {
    const examples = WallVideoModel.find({
      'segmentation.finished': true,
      hidden: { $ne: true },
    })
      .sort({
        createdAt: -1,
      })
      .limit(9)
      .populate([{ path: 'user', populate: { path: 'currentTeam' } }])

    return examples
  }
}
