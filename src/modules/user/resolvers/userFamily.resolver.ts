import { GraphQLError } from 'graphql'
import { GraphQLResolveInfo } from 'graphql'
import pino from 'pino'
import { Arg, Ctx, Info, Mutation, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import Analytics from '../../../services/analytics'
import { UserModel } from '../models'
import { User } from '../models/user'
import { UserNotFoundError } from '../types/user'
import {
  FamilyMemberAlreadyExistsError,
  FamilyMemberUpdateResponse,
  SelfFamilyMemberError,
  UserNotInFamilyError,
  UserNotInPendingError,
} from '../types/userFamily'

@Resolver((_of) => User)
@Service()
export default class UserResolverFamily {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject('analytics.service')
  private analytics: Analytics

  @Mutation(() => FamilyMemberUpdateResponse)
  async addFamilyMember(
    @Arg('userId') userId: string,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<FamilyMemberUpdateResponse> {
    const response = new FamilyMemberUpdateResponse()

    if (userId === ctx.user?.id) {
      response.errors = [
        new SelfFamilyMemberError(
          'Cannot add yourself as a family member',
          info.path.key.toString()
        ),
      ]
      return response
    }

    try {
      // Check if user exists
      const userToAdd = await UserModel.findById(userId).lean()
      if (!userToAdd) {
        response.errors = [new UserNotFoundError('User not found', info.path.key.toString())]
        return response
      }

      // Check if family member is already added or pending
      const userHasFamilyMember = await UserModel.findOne({
        $or: [
          {
            _id: userId,
            familyMembers: ctx.user?.id,
          },
          {
            _id: userId,
            pendingFamilyMembers: ctx.user?.id,
          },
        ],
      }).lean()

      if (userHasFamilyMember) {
        this.logger.error('User already has this family member')
        response.errors = [
          new FamilyMemberAlreadyExistsError(
            'User already has this family member',
            info.path.key.toString()
          ),
        ]
        return response
      }

      // TODO if needed, add requestedFamilyMembers to track current users pending requests. e.g.:
      // await UserModel.findOneAndUpdate(
      //   { _id: ctx.user?.id },
      //   { $addToSet: { requestedFamilyMembers: userId } },
      // ).lean()

      // Adding current user to pendingFamily members of the other user (userId)
      const updatedUser = await UserModel.findOneAndUpdate(
        { _id: userId },
        { $addToSet: { pendingFamilyMembers: ctx.user?.id } },
        { new: true }
      ).lean()

      response.user = updatedUser
      return response
    } catch (err) {
      this.logger.error('Failed to add family member', err)
      throw new GraphQLError('Failed to add family member', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => FamilyMemberUpdateResponse)
  async confirmFamilyMember(
    @Arg('userId') userId: string,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<FamilyMemberUpdateResponse> {
    const response = new FamilyMemberUpdateResponse()

    if (userId === ctx.user?.id) {
      response.errors = [
        new SelfFamilyMemberError(
          'Cannot add yourself as a family member',
          info.path.key.toString()
        ),
      ]
      return response
    }

    try {
      // Check if user exists
      const userToConfirm = await UserModel.findById(userId).lean()
      if (!userToConfirm) {
        response.errors = [new UserNotFoundError('User not found', info.path.key.toString())]
        return response
      }

      // Check if family member is already added or pending
      const userHasFamilyMember = await UserModel.findOne({
        _id: ctx.user?.id,
        familyMembers: userId,
      }).lean()

      if (userHasFamilyMember) {
        this.logger.error('User already has this family member')
        response.errors = [
          new FamilyMemberAlreadyExistsError(
            'User already has this family member',
            info.path.key.toString()
          ),
        ]

        return response
      }

      const userHasPendingFamilyMember = await UserModel.findOne({
        _id: ctx.user?.id,
        pendingFamilyMembers: userId,
      }).lean()

      if (!userHasPendingFamilyMember) {
        this.logger.error('User not in pending family members')
        response.errors = [
          new UserNotInPendingError('User not in pending family members', info.path.key.toString()),
        ]
        return response
      }

      const updatedUser = await UserModel.findOneAndUpdate(
        { _id: ctx.user?.id },
        { $pull: { pendingFamilyMembers: userId }, $addToSet: { familyMembers: userId } },
        { new: true }
      ).lean()

      // Adding current user to family members of the other user (userId)
      const updatedFamilyMember = await UserModel.findOneAndUpdate(
        { _id: userId },
        {
          $pull: { pendingFamilyMembers: ctx.user?.id },
          $addToSet: { familyMembers: ctx.user?.id },
        }
      ).lean()

      // update analytics
      await Promise.all([
        this.analytics.updateUserAttrs(updatedUser),
        this.analytics.updateUserAttrs(updatedFamilyMember),
      ])

      response.user = updatedUser
      return response
    } catch (err) {
      this.logger.error('Failed to confirm family member', err)
      throw new GraphQLError('Failed to confirm family member', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => FamilyMemberUpdateResponse)
  async removePendingFamilyMember(
    @Arg('userId') userId: string,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<FamilyMemberUpdateResponse> {
    const response = new FamilyMemberUpdateResponse()

    try {
      // Check if user exists
      const userToRemove = await UserModel.findById(userId).lean()
      if (!userToRemove) {
        response.errors = [new UserNotFoundError('User not found', info.path.key.toString())]
        return response
      }

      const userHasPendingFamilyMember = await UserModel.findOne({
        _id: ctx.user?.id,
        pendingFamilyMembers: userId,
      }).lean()

      if (!userHasPendingFamilyMember) {
        this.logger.error('User not in pending family members')
        response.errors = [
          new UserNotInPendingError('User not in pending family members', info.path.key.toString()),
        ]
        return response
      }

      const updatedUser = await UserModel.findOneAndUpdate(
        { _id: ctx.user?.id },
        { $pull: { pendingFamilyMembers: userId } },
        { new: true }
      ).lean()

      // Removing current user frompending family members of the other user (userId)
      await UserModel.findOneAndUpdate(
        { _id: userId },
        { $pull: { pendingFamilyMembers: ctx.user?.id } }
      ).lean()

      response.user = updatedUser
      return response
    } catch (err) {
      this.logger.error('Failed to remove pending family member', err)
      throw new GraphQLError('Failed to remove pending family member', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => FamilyMemberUpdateResponse)
  async removeFamilyMember(
    @Arg('userId') userId: string,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<FamilyMemberUpdateResponse> {
    const response = new FamilyMemberUpdateResponse()

    try {
      // Check if user exists
      const userToRemove = await UserModel.findById(userId).lean()
      if (!userToRemove) {
        response.errors = [new UserNotFoundError('User not found', info.path.key.toString())]
        return response
      }

      const userHasFamilyMember = await UserModel.findOne({
        _id: ctx.user?.id,
        familyMembers: userId,
      }).lean()

      if (!userHasFamilyMember) {
        this.logger.error('User not in family members')
        response.errors = [
          new UserNotInFamilyError('User not in family members', info.path.key.toString()),
        ]
        return response
      }

      const updatedUser = await UserModel.findOneAndUpdate(
        { _id: ctx.user?.id },
        { $pull: { familyMembers: userId } },
        { new: true }
      ).lean()

      // Removing current user frompending family members of the other user (userId)
      const updatedFamilyMember = await UserModel.findOneAndUpdate(
        { _id: userId },
        { $pull: { familyMembers: ctx.user?.id } }
      ).lean()

      // update analytics
      await Promise.all([
        this.analytics.updateUserAttrs(updatedUser),
        this.analytics.updateUserAttrs(updatedFamilyMember),
      ])

      response.user = updatedUser
      return response
    } catch (err) {
      this.logger.error('Failed to remove family member', err)
      throw new GraphQLError('Failed to remove family member', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }
}
