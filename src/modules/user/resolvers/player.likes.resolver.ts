import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import { Types } from 'mongoose'
import pino from 'pino'
import { Arg, Authorized, Ctx, Info, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { capitalizeWords } from '../../../helpers/format'
import Analytics from '../../../services/analytics'
import {
  addNotificationActivity,
  buildGameActivityPayload,
  FeedActivityVerb,
} from '../../../services/helpers/streamFeed'
import { PushNotificationService, PushNotificationType } from '../../../services/pushNotification'
import { GameModel } from '../../stats/models/game/game'
import { Team, TeamModel } from '../../stats/models/team'
import { UserModel } from '../models'
import { PlayerPerformanceLikeModel } from '../models/playerPerformanceLike'
import { SubRole, User } from '../models/user'
import {
  LikePlayerPerformanceResponse,
  PlayersByGame,
  UnlikePlayerPerformanceResponse,
  UserLikesPlayersGamesPerformanceInput,
  UserLikesPlayersGamesPerformanceResponse,
  UserLikesPlayersPerformanceInput,
  UserLikesPlayersPerformanceResponse,
  UserNotFoundError,
} from '../types/user'

import { EntityNotFoundError } from './../../../types/general'

import { DocumentType } from '@typegoose/typegoose'

@Resolver((_of) => User)
@Service()
export default class PlayerPerformanceLikeResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject('pushNotification.service')
  private pushNotificationService: PushNotificationService

  @Inject('analytics.service')
  private analytics: Analytics

  @Mutation(() => LikePlayerPerformanceResponse)
  @Authorized()
  async likePlayerPerformance(
    @Arg('gameId') gameId: Types.ObjectId,
    @Arg('playerId') playerId: Types.ObjectId,
    @Ctx() ctx: ContextType
  ): Promise<LikePlayerPerformanceResponse> {
    try {
      const player = await UserModel.findById(playerId).lean()

      if (!player) {
        return {
          player,
          errors: [new UserNotFoundError('Player does not exist.', 'likePlayerPerformance')],
        }
      }

      const game = await GameModel.findById(gameId).lean()

      if (!game) {
        return {
          player,
          errors: [new EntityNotFoundError('Game does not exist.', 'likePlayerPerformance')],
        }
      }

      // add a like to the player's game
      try {
        await PlayerPerformanceLikeModel.create({
          game: game._id,
          player: player._id,
          userLiked: ctx.user!.id,
        })

        // send push notification
        const userWhoLiked = await UserModel.findById(ctx.user!.id)

        const title = 'Your game is fire! 💥'

        const capitalizedSubRole = userWhoLiked?.subrole
          ? `${capitalizeWords(userWhoLiked?.subrole)}`
          : ''

        const message = `${capitalizedSubRole ? `${capitalizedSubRole} ` : ''}${userWhoLiked?.name} just dropped 🔥.`

        const hometeam = await TeamModel.findById(game.homeTeam)
        const awayteam = await TeamModel.findById(game.awayTeam)

        const { home, away } = await buildGameActivityPayload(
          game,
          hometeam as DocumentType<Team>,
          awayteam as DocumentType<Team>
        )

        await addNotificationActivity({
          home,
          away,
          userId: player._id.toString(),
          actor: userWhoLiked,
          verb: FeedActivityVerb[`Fire${capitalizedSubRole}` as keyof typeof FeedActivityVerb],
          foreignId: userWhoLiked?.id || ctx.user!.id,
        })

        await this.pushNotificationService.sendPushNotification(player, {
          title,
          message,
          type: PushNotificationType[
            `Fire${capitalizedSubRole}` as keyof typeof PushNotificationType
          ],
          params: {
            destinationPath:
              userWhoLiked?.subrole === SubRole.AGENT ? `/agent/${userWhoLiked.slug}` : `/`,
          },
          key: `newFire-${ctx.user!.id}-${game._id.toString()}-${player._id.toString()}`,
        })

        this.analytics.trackEvent('newFire', { gameId: game._id.toString() }, playerId.toString())
      } catch (err) {
        if (err.name === 'MongoServerError' && err.code === 11000) {
          // duplicate key error, we don't mind this
        } else {
          this.logger.error('DB error:', err)
        }
      }

      return {
        player,
        errors: [],
      }
    } catch (err) {
      this.logger.error("Failed to add like to a player's game", err)
      if (err instanceof GraphQLError) {
        throw err
      }

      throw new GraphQLError("Failed to add like to a player's game", {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => UnlikePlayerPerformanceResponse)
  @Authorized()
  async unlikePlayerPerformance(
    @Arg('gameId') gameId: Types.ObjectId,
    @Arg('playerId') playerId: Types.ObjectId,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<UnlikePlayerPerformanceResponse> {
    try {
      const player = await UserModel.findById(playerId).lean()

      if (!player) {
        return {
          player,
          errors: [new UserNotFoundError('Player does not exist.', 'unlikePlayerPerformance')],
        }
      }

      const game = await GameModel.findById(gameId).lean()

      if (!game) {
        return {
          player,
          errors: [new EntityNotFoundError('Game does not exist.', 'unlikePlayerPerformance')],
        }
      }

      // Find the like from the player's game performance likes
      const playerPerformanceLike = await PlayerPerformanceLikeModel.findOne({
        game: game._id,
        player: player._id,
        userLiked: ctx.user!.id,
      })

      // Remove the like from the player's game performance likes
      if (playerPerformanceLike) {
        await playerPerformanceLike.deleteOne()

        return {
          player,
          errors: [],
        }
      }

      return {
        player,
        errors: [new EntityNotFoundError(`PlayerGameLike not found`, info.path.key.toString())],
      }
    } catch (err) {
      this.logger.error("Failed to remove like from a player's game", err)
      if (err instanceof GraphQLError) {
        throw err
      }

      throw new GraphQLError("Failed to remove like from a player's game", {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Query(() => UserLikesPlayersPerformanceResponse)
  @Authorized()
  async userLikesPlayersPerformance(
    @Arg('input') input: UserLikesPlayersPerformanceInput,
    @Ctx() ctx: ContextType
  ): Promise<UserLikesPlayersPerformanceResponse> {
    const { gameId, playerIds } = input

    try {
      const game = await GameModel.findById(gameId).lean()

      if (!game) {
        return {
          playersByGame: null,
          errors: [new EntityNotFoundError('Game does not exist.', 'userLikesPlayersPerformance')],
        }
      }

      // TODO check existing players?

      // Find the user's likes of the players' game performance
      const playerPerformanceLikes = await PlayerPerformanceLikeModel.find({
        game: game._id,
        ...(playerIds && playerIds.length > 0 ? { player: { $in: playerIds } } : {}),
        userLiked: ctx.user!.id,
      })

      return {
        playersByGame: {
          _id: game._id,
          players: playerPerformanceLikes.map((playerPerformanceLike) => {
            return playerPerformanceLike.player
          }),
        },
        errors: [],
      }
    } catch (err) {
      this.logger.error("Failed to find user's likes of the player's game", err)
      if (err instanceof GraphQLError) {
        throw err
      }

      throw new GraphQLError("Failed to find user's likes of the player's game", {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Query(() => UserLikesPlayersGamesPerformanceResponse)
  @Authorized()
  async userLikesPlayersGamesPerformance(
    @Arg('input') input: UserLikesPlayersGamesPerformanceInput,
    @Ctx() ctx: ContextType
  ): Promise<UserLikesPlayersGamesPerformanceResponse> {
    const { gameIds, playerIds } = input

    try {
      const games = await GameModel.find({ _id: { $in: gameIds } }).lean()

      if (!games || games.length !== gameIds.length) {
        return {
          playersByGame: null,
          errors: [
            new EntityNotFoundError(
              'Game(s) do(es) not exist.',
              'userLikesPlayersGamesPerformance'
            ),
          ],
        }
      }

      const playersByGame: PlayersByGame[] = await PlayerPerformanceLikeModel.aggregate([
        {
          $match: {
            game: { $in: gameIds },
            ...(playerIds && playerIds.length > 0 ? { player: { $in: playerIds } } : {}),
            userLiked: new Types.ObjectId(ctx.user!.id),
          },
        },
        {
          $group: {
            _id: '$game',
            players: {
              $push: '$player',
            },
          },
        },
      ])

      return {
        playersByGame,
        errors: [],
      }
    } catch (err) {
      this.logger.error("Failed to find user's likes of the players in games", err)
      if (err instanceof GraphQLError) {
        throw err
      }

      throw new GraphQLError("Failed to find user's likes of the players in games", {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }
}
