import config from 'config'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import pino from 'pino'
import { Arg, Ctx, Info, Mutation, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { EmailService, EmailTemplate } from '../../../services/email'
import { SlackService } from '../../../services/slack'
import { UserModel } from '../models'
import { AgencyModel } from '../models/agency'
import { Role } from '../models/user'
import { ContactMessageInput, ContactMessageResponse, ContactNotFoundError } from '../types/contact'

@Resolver()
@Service()
export default class ContactResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private emailService: EmailService

  @Inject()
  private slackService: SlackService

  @Mutation(() => ContactMessageResponse)
  async sendContactMessage(
    @Arg('input', { nullable: true }) input: ContactMessageInput,
    @Info() info: GraphQLResolveInfo,
    @Ctx() ctx: ContextType
  ): Promise<ContactMessageResponse> {
    try {
      const emailData: {
        emailTo: string
        profileLink: string
        profileName: string
      } = {
        emailTo: '',
        profileLink: '',
        profileName: '',
      }

      let senderIsPlayer = false
      if (ctx.user?.id) {
        const fromUser = await UserModel.findById(ctx.user.id)
        if (fromUser?.role === Role.PLAYER || fromUser?.role === null) {
          senderIsPlayer = true
        }
      }

      if (input.userId) {
        const user = await UserModel.findById(input.userId)
        if (!user) {
          this.logger.error('User not found')
          return {
            success: false,
            errors: [new ContactNotFoundError('Contact not found', info.path.key.toString())],
          }
        }

        emailData.emailTo = user.contact?.email && !senderIsPlayer ? user.contact.email : user.email
        emailData.profileLink = `${config.webUrl}${user.slug}`
        emailData.profileName = user.name
      } else if (input.agencyId) {
        const agency = await AgencyModel.findById(input.agencyId)
        if (!agency || !agency.email) {
          this.logger.error('Agency not found')
          return {
            success: false,
            errors: [new ContactNotFoundError('Contact not found', info.path.key.toString())],
          }
        }

        emailData.emailTo = agency.email
        emailData.profileLink = `${config.webUrl}/agency/${agency.slug}`
        emailData.profileName = agency.name
      } else {
        this.logger.error('No user or agency id provided')
        return {
          success: false,
          errors: [new ContactNotFoundError('Contact not found', info.path.key.toString())],
        }
      }

      const sender = {
        name: input.name,
        email: input.email,
      }

      await this.emailService.sendEmail(
        emailData.emailTo,
        EmailTemplate.CONTACT_MESSAGE,
        {
          message: input.message,
          profileLink: emailData.profileLink,
          profileName: emailData.profileName,
          fromFullName: sender.name,
          replyToEmail: sender.email,
        },
        input.email
      )

      await this.slackService.sendMessage(
        `Message to ${emailData.profileName} from ${sender.name} (${sender.email}):
        "${input.message}"
        `,
        config.slack.channels.messages
      )

      return {
        success: true,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to send contact message.', err)

      throw new GraphQLError('Failed to send contact message. Please try again later.')
    }
  }
}
