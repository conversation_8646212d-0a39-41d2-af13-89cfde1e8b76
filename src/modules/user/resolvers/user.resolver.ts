import config from 'config'
import { StreamClient } from 'getstream'
import { GraphQLError, GraphQLResolveInfo } from 'graphql'
import jwt from 'jsonwebtoken'
import latinize from 'latinize'
import moment from 'moment'
import { $unset, flatten } from 'mongo-dot-notation'
import { Types } from 'mongoose'
import pino from 'pino'
import { StreamChat } from 'stream-chat'
import {
  Arg,
  Authorized,
  Ctx,
  FieldResolver,
  Info,
  Int,
  Mutation,
  Query,
  Resolver,
  Root,
} from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType, ContextUser } from '../../..'
import { recalculateAgentsCount, recalculateClientsCount } from '../../../helpers/agency'
import { generateExpirationDate, generateVerificationCode } from '../../../helpers/code-generation'
import { capitalizeWords } from '../../../helpers/format'
import { CURRENT_SEASON } from '../../../helpers/league'
import { getEntity } from '../../../helpers/mongoose'
import { slugifyString } from '../../../helpers/string'
import { findUserSlug } from '../../../helpers/user'
import { userCleanup } from '../../../helpers/userCleanup'
import { CacheControl } from '../../../middlewares/cacheControl'
import EliteProspectsService from '../../../modules/stats/services/eliteprospects/ep'
import { parseImperialHeight } from '../../../modules/stats/services/eliteprospects/helpers'
import Analytics from '../../../services/analytics'
import AppleService from '../../../services/apple'
import { EmailService, EmailTemplate } from '../../../services/email'
import GoogleService from '../../../services/google'
import { addNotificationActivity, FeedActivityVerb } from '../../../services/helpers/streamFeed'
import { MediaConverter } from '../../../services/mediaConverter'
import { PushNotificationService, PushNotificationType } from '../../../services/pushNotification'
import { S3Service } from '../../../services/s3'
import { SlackService } from '../../../services/slack'
import { SNSService } from '../../../services/sns'
import { StripeService } from '../../../services/stripe'
import {
  FailedToCreatePresignedUrl,
  GraetPlusType,
  Pagination,
  SortOrder,
  SystemRole,
  UserRole,
} from '../../../types/general'
import { addExternalConnections } from '../../admin/helpers/shadowUser'
import { GameModel } from '../../stats/models/game/game'
import { Organization } from '../../stats/models/organization'
import { Team, TeamModel } from '../../stats/models/team'
import { UserStatsModel } from '../../stats/models/userStats'
import { EPPlayer } from '../../stats/services/eliteprospects/graphql'
import UserStatsSyncerService from '../../stats/services/userStatsSyncer.service'
import { recalculateTeamPlayersCount } from '../helpers/currentTeam'
import { findShadowUser } from '../helpers/shadowUser'
import { UserModel, WallVideoModel } from '../models'
import { Agency, AgencyModel } from '../models/agency'
import { BioHandedness, ExternalLinkType, Role, SubRole, User } from '../models/user'
import { SubscribeProtocol, UserSubscriptionModel } from '../models/userSubscription'
import { UserViewModel } from '../models/userViews'
import { UserViewAggregatedModel, ViewAggregationType } from '../models/userViewsAggregated'
import { PaginationRepository } from '../repositories/pagination.repository'
import UserRepository from '../repositories/user.repository'
import { UploadAvatarInput, UploadAvatarResponse } from '../types/avatar'
import { UpsertUsersChatUser } from '../types/streamChat'
import { FEED_NAMES } from '../types/streamFeed'
import {
  AdminUsersFilter,
  Country,
  CreateChatUsersInput,
  CurrencyUpdateError,
  DeviceAlreadyRegisteredError,
  EntityCountsResponse,
  FetchUserDataEPResponse,
  InvalidVerificationCodeError,
  MyFollowersFilter,
  MyFollowersResponse,
  NewUsersCountResponse,
  Period,
  PushNotificationsInput,
  PushNotificationsResponse,
  RandomUsersResponse,
  RegisterDeviceInput,
  RegisterDeviceResponse,
  RegisterOrLoginByEmailInput,
  RegisteryOrLoginByEmailResponse,
  SocialSignInInput,
  SocialSignInResponse,
  TopViewedUsersFilter,
  UnfollowUserResponse,
  UnregisterDeviceInput,
  UpdateDefaultCurrencyResponse,
  UpdateUserAdminResponse,
  UpdateUserInput,
  UpdateUserInputAdmin,
  UpdateUserResponse,
  UserNotFoundError,
  UsersFilter,
  UsersResponse,
  UserViewsResponse,
  UserViewStatsResponse,
  VerifyByEmailResponse,
  YearArguments,
} from '../types/user'

import { DeviceNotRegisteredError, UnregisterDeviceResponse } from './../types/user'

import { DocumentType } from '@typegoose/typegoose'

@Resolver((_of) => User)
@Service()
export default class UserResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject('s3.service')
  private s3: S3Service

  @Inject()
  private sns: SNSService

  @Inject('pushNotification.service')
  private pushNotificationService: PushNotificationService

  @Inject()
  private emailService: EmailService

  @Inject()
  private slackService: SlackService

  @Inject()
  private userRepository: UserRepository

  @Inject()
  private googleService: GoogleService

  @Inject()
  private appleService: AppleService

  @Inject()
  private epService: EliteProspectsService

  @Inject()
  private userStatsSyncerService: UserStatsSyncerService

  @Inject()
  private mediaConverter: MediaConverter

  @Inject('analytics.service')
  private analytics: Analytics

  @Inject()
  private stripe: StripeService

  @Inject('streamChat.client')
  private streamChat: StreamChat

  @Inject('streamFeed.client')
  private streamFeed: StreamClient

  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  private async generateCodeAndSendEmail(
    email: string,
    newVersionFlag?: boolean
  ): Promise<{ code: string; expirationDate: Date }> {
    const code = generateVerificationCode()
    const verifyUrl = newVersionFlag
      ? `${config.webUrl}verify-email?email=${email.replace(/\+/g, '%2B')}&code=${code}`
      : `${config.webUrl}verify-email/${email}?code=${code}`
    await this.emailService.sendEmail(email, EmailTemplate.VERIFY_EMAIL, { code, verifyUrl })

    return {
      code,
      expirationDate: generateExpirationDate(),
    }
  }

  @Mutation(() => RegisteryOrLoginByEmailResponse)
  async registerOrLoginByEmail(
    @Arg('input') input: RegisterOrLoginByEmailInput,
    @Arg('newUrl', { nullable: true, defaultValue: false }) newUrl?: boolean
  ): Promise<RegisteryOrLoginByEmailResponse> {
    try {
      const userExists = await UserModel.findOne({ email: input.email })

      if (userExists) {
        await UserModel.updateOne(
          { _id: userExists.id },
          {
            email: input.email,
            verificationCode: await this.generateCodeAndSendEmail(input.email, newUrl),
          }
        )

        return {
          user: null, // do not expose user data
          errors: [],
        }
      }

      await UserModel.create({
        email: input.email,
        verificationCode: await this.generateCodeAndSendEmail(input.email, newUrl),
        role: input.role,
        subrole: input.subrole,
        paymentsEnabled: false,
      })

      return {
        user: null,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to register user by email:', err)

      throw new GraphQLError('Failed to register user by email. Please try again later.')
    }
  }

  @Mutation(() => VerifyByEmailResponse)
  async verifyByEmail(
    @Arg('email') email: string,
    @Arg('code') code: string,
    @Info() info: GraphQLResolveInfo
  ): Promise<VerifyByEmailResponse> {
    const user = await UserModel.findOne({ email: email }).populate([
      { path: 'currentTeam', populate: { path: 'organization' } },
    ])

    if (!user) {
      return {
        errors: [
          new UserNotFoundError(
            `User with email address ${email} not found`,
            info.path.key.toString()
          ),
        ],
      }
    }

    // exception for e2e tests (on mobile)
    // TODO: this is not great to have exception for tests, but
    // it is simpler then handling emails and getting proper code
    if (process.env.NODE_ENV === 'staging') {
      if (email.includes('graet.com') && code === '123456') {
        return {
          tokens: {
            accessToken: this.getUserToken(user, UserRole.USER),
            refreshToken: this.getUserToken(user, UserRole.USER),
          },
          user,
          errors: [],
        }
      }
    }

    if (user.verificationCode?.code !== code) {
      return {
        errors: [
          new InvalidVerificationCodeError('Invalid verification code', info.path.key.toString()),
        ],
      }
    }

    if (user.verificationCode.expirationDate < new Date()) {
      const newCode = await this.generateCodeAndSendEmail(email, true)
      await UserModel.updateOne({ email: email }, { verificationCode: newCode })
      return {
        errors: [
          new InvalidVerificationCodeError(
            'Verification code expired, new code sent',
            info.path.key.toString()
          ),
        ],
      }
    }

    const userRole = user.email === email ? UserRole.USER : UserRole.CONTACT

    this.analytics.updateUserAttrs(user)

    return {
      tokens: {
        accessToken: this.getUserToken(user, userRole),
        refreshToken: this.getUserToken(user, userRole),
      },
      user,
      errors: [],
    }
  }

  @Mutation(() => SocialSignInResponse)
  async googleSignIn(@Arg('input') input: SocialSignInInput): Promise<SocialSignInResponse> {
    try {
      const userInfo = await this.googleService.getUserInfo(input.idToken)

      let onboardingCompleted = true
      let user = await UserModel.findOne({ email: userInfo.email }).populate([
        { path: 'currentTeam', populate: { path: 'organization' } },
      ])

      if (!user) {
        user = await (
          await UserModel.create({
            email: userInfo.email,
            role: input.role,
            subrole: input.subrole,
            paymentsEnabled: false,
          })
        ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

        onboardingCompleted = false
      }

      const userRole = user.email === userInfo.email ? UserRole.USER : UserRole.CONTACT

      this.analytics.updateUserAttrs(user)

      return {
        tokens: {
          accessToken: this.getUserToken(user, userRole),
          refreshToken: this.getUserToken(user, userRole),
        },
        user,
        onboardingCompleted,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to sign in with Google:', err)

      throw new GraphQLError('Failed to sign in with Google. Please try again later.', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => SocialSignInResponse)
  async appleSignIn(@Arg('input') input: SocialSignInInput): Promise<SocialSignInResponse> {
    try {
      const appleResult = await this.appleService.getUserInfo(input.idToken)
      if (!appleResult.email && !appleResult.appleUserId) {
        throw new GraphQLError('Failed to verify token.', {
          extensions: { code: 'TOKEN_VERIFICATION_FAILED' },
        })
      }

      let onboardingCompleted = true
      let user = await UserModel.findOne({
        $or: [{ email: appleResult.email }, { appleUserId: appleResult.appleUserId }],
      }).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

      // handle already registered accounts, without appleUserId saved
      if (!user && !appleResult.email && appleResult.appleUserId) {
        // in this case we don't have email so we cannot save new user without it
        throw new GraphQLError('Apple user id was not saved with this account.', {
          extensions: { code: 'APPLE_SIGN_IN_FAILED' },
        })
      }

      if (!user) {
        user = await (
          await UserModel.create({
            email: appleResult.email,
            appleUserId: appleResult.appleUserId,
            role: input.role,
            subrole: input.subrole,
            paymentsEnabled: false,
          })
        ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

        onboardingCompleted = false
      }

      this.analytics.updateUserAttrs(user)

      const userRole = UserRole.USER
      return {
        tokens: {
          accessToken: this.getUserToken(user, userRole),
          refreshToken: this.getUserToken(user, userRole),
        },
        user,
        onboardingCompleted,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to sign in with Apple:', err)

      throw new GraphQLError('Failed to sign in with Apple. Please try again later.', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => UpdateDefaultCurrencyResponse)
  @Authorized(SystemRole.ADMIN)
  async updateUserCurrency(
    @Arg('id') id: Types.ObjectId,
    @Arg('currency') currency: string
  ): Promise<UpdateDefaultCurrencyResponse> {
    try {
      const user = await UserModel.findOne({
        _id: id,
      })

      if (!user || !user.stripeAccountId) {
        return {
          currency: '',
          errors: [new UserNotFoundError('User not found', 'id')],
        }
      }

      const response = await this.stripe.updateConnectAccount(
        user.stripeAccountId,
        {
          currency: currency,
        },
        user
      )

      const updatedCurrency = response.currency
      return {
        currency: updatedCurrency,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to update user currency:', err)
      return {
        currency: '',
        errors: [new CurrencyUpdateError(`Failed to update user currency`, err)],
      }
    }
  }

  @Mutation(() => UpdateUserAdminResponse)
  @Authorized(SystemRole.ADMIN)
  async updateUserAdmin(
    @Arg('id') id: Types.ObjectId,
    @Arg('input') input: UpdateUserInputAdmin,
    @Info() info: GraphQLResolveInfo
  ): Promise<UpdateUserAdminResponse> {
    const response = new UpdateUserAdminResponse()

    try {
      const user = await UserModel.findById(id)

      if (!user) {
        return {
          user: null,
          errors: [new UserNotFoundError('User not found', info.path.key.toString())],
        }
      }

      let agent = user.agentV2?.id
      if (input.agent) {
        agent = input.agent
      }

      if (input.agent === null) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        input.agent = $unset() as any
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const data = flatten(input) as any // FIXME: put back as UpdateUserInput

      if (input.premium && !user.premium) {
        data.$set.premium = true
        data.$set.premiumSince = new Date()

        this.analytics.updateCustomAttrs(user!, {
          graet_plus: GraetPlusType.Active,
          graet_plus_activation_date: Math.floor(new Date().getTime() / 1000),
        })
      } else if (input.premium === false && user.premium) {
        data.$set.premium = false

        this.analytics.updateCustomAttrs(user!, {
          graet_plus: GraetPlusType.Expired,
        })
      }

      if (input.agent) {
        data.$set.agentV2 = agent
      }

      const updatedUser = await UserModel.findOneAndUpdate({ _id: user.id }, data, {
        new: true,
      }).populate([
        { path: 'currentTeam', populate: { path: 'organization' } },
        { path: 'agentV2' },
      ])

      if (input.bio?.agency) {
        await recalculateClientsCount(input.bio.agency)

        if (user.bio?.agency && user.bio.agency.toString() !== input.bio.agency.toString()) {
          await recalculateClientsCount(user.bio.agency as Types.ObjectId)
        }
      }

      // update wallVideos
      // TODO: we need to delete/add getstream feed activity for each video
      if (input.public === false && user.public !== false) {
        await WallVideoModel.updateMany({ user: user.id }, { hidden: true })
      } else if (input.public === true && user.public !== true) {
        await WallVideoModel.updateMany({ user: user.id }, { hidden: false })
      }

      if (input.currentTeam || input.public) {
        await recalculateTeamPlayersCount(user, input.currentTeam)
      }

      if (input.subrole && user.bio?.agency) {
        if (user.bio?.agency) {
          await recalculateAgentsCount(user.bio.agency as Types.ObjectId)
        }
      }

      if (!updatedUser) {
        return {
          user: null,
          errors: [new UserNotFoundError('Updated user not found', info.path.key.toString())],
        }
      }

      // update analytics
      await this.analytics.updateUserAttrs(updatedUser)

      return {
        user: updatedUser,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to update user.', err)
      response.errors = [new UserNotFoundError('User not found', info.path.key.toString())]
    }
    return response
  }

  @Mutation(() => UpdateUserResponse)
  @Authorized()
  async updateUser(
    @Ctx() context: ContextType,
    @Arg('input') input: UpdateUserInput,
    @Info() info: GraphQLResolveInfo
  ): Promise<UpdateUserResponse> {
    const user = await UserModel.findById(context.user!.id)

    if (!user) {
      return {
        user: null,
        errors: [
          new UserNotFoundError(
            `User with id ${context.user!.id} not found`,
            info.path.key.toString()
          ),
        ],
      }
    }

    // to ensure that the user can change certain fields only once
    if (user.role) {
      delete input.role
    }

    if (user.subrole) {
      delete input.subrole
    }

    if (user.dateOfBirth) {
      delete input.dateOfBirth
    }

    if (user.country) {
      delete input.country
    }

    if (user.firstname) {
      delete input.firstname
    }

    if (user.lastname) {
      delete input.lastname
    }

    // don't allow to set or update the agency (only delete is supported)
    if (input.bio?.agency) {
      delete input.bio.agency
      if (Object.keys(input.bio).length === 0) {
        delete input.bio
      }
    }

    // remove agencyUnverified if it was set before to the same value
    if (
      input.bio?.agencyUnverified &&
      input.bio?.agencyUnverified.toString() === user.bio?.agencyUnverified?._id.toString()
    ) {
      delete input.bio.agencyUnverified
      if (Object.keys(input.bio).length === 0) {
        delete input.bio
      }
    }

    // remove agencyUnverified if it same as agency
    if (
      input.bio?.agencyUnverified &&
      input.bio?.agencyUnverified?.toString() === user.bio?.agency?._id.toString()
    ) {
      delete input.bio.agencyUnverified
      if (Object.keys(input.bio).length === 0) {
        delete input.bio
      }
    }

    if (input.firstname) {
      input.firstname = capitalizeWords(latinize(input.firstname))
    }

    if (input.lastname) {
      input.lastname = capitalizeWords(latinize(input.lastname))
    }

    // generate slug only first time we set the name
    if (input.firstname && input.lastname && !user.slug) {
      await findUserSlug(input)

      if (user.role === Role.PLAYER || user.role === null) {
        await this.slackService.sendMessage(
          `New athlete registered: ${input.firstname} ${input.lastname}, <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>  `,
          config.slack.channels.newAccount
        )
      } else if (user.role === Role.SPORTS_PROFESSIONAL) {
        await this.slackService.sendMessage(
          `New ${user.subrole} registered: ${input.firstname} ${input.lastname}, ${user.email},  <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>  `,
          config.slack.channels.dbProfessionals
        )
      } else {
        await this.slackService.sendMessage(
          `New ${user.role} (${user.email}) registered <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>  `,
          config.slack.channels.newAccount
        )
      }
    }

    if (input.bio?.agencyUnverified) {
      await this.slackService.sendMessage(
        `User ${user.name} set new agency which needs verification: ${input.bio?.agencyUnverified}, <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>  `,
        config.slack.channels.dbUserAgency
      )
    }

    if (
      user.role === Role.SPORTS_PROFESSIONAL &&
      input.bio?.intro &&
      user.bio?.intro !== input.bio?.intro
    ) {
      await this.slackService.sendMessage(
        `User (${user.name} <https://www.graet.com/${user.slug}|${user.name} at Graet>) added intro: ${input.bio?.intro}, <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>  `,
        config.slack.channels.dbUserAgency
      )
    }

    // external links needs to be updated in a special way, without flattening
    const customLinks = user.externalLinks?.filter((link) => link.type === ExternalLinkType.CUSTOM)
    if (input.externalLinks) {
      // always include custom links (as clients can't update them)
      input.externalLinks = [...input.externalLinks, ...(customLinks ?? [])]
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const data = flatten(input) as any // FIXME: put back as UpdateUserInput

    data['$set'] = data['$set'] || {}

    data['$unset'] = {}
    for (const key in data['$set']) {
      if (data['$set'][key] === null) {
        delete data['$set'][key]
        data['$unset'][key] = ''
      }
    }

    if (!user.experimentGroup) {
      const experimentGroup = Math.floor(Math.random() * 10) + 1 // NOSONAR
      data.$set.experimentGroup = experimentGroup
    }

    let updatedUser = await UserModel.findOneAndUpdate({ _id: context.user!.id }, data, {
      new: true,
    }).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

    // recalculate team players count
    if (input.currentTeam) {
      await recalculateTeamPlayersCount(user, data.currentTeam)

      // send push notification to all players from organization
      const team = updatedUser!.currentTeam
      const organization = (team as Team)?.organization as Organization
      const teams = await TeamModel.find({ organization })

      // TODO: this should be done in a better way, we should create topic in SNS and send notification to all players
      const playersInOrganization = await UserModel.find({
        currentTeam: { $in: teams },
        _id: { $ne: updatedUser?.id },
      })

      for (const player of playersInOrganization) {
        // Stream Feed add activity to notification feed
        addNotificationActivity({
          userId: player.id,
          actor: user as User,
          teamName: organization?.name,
          verb: FeedActivityVerb.NEW_TEAM_MEMBER,
          foreignId: user.id,
        }).catch((err) => {
          this.logger.error('updateUser: Failed to add streamfeed activity notification:', err)
        })

        // TODO: create batch notification instead and don't wait here for sending
        await this.pushNotificationService.sendPushNotification(player, {
          title: 'New teammate on GRAET! 👋',
          message: `Go say hi to ${user.name}, who has just joined ${organization?.name}.`,
          type: PushNotificationType.NEW_TEAM_MEMBER,
          // this is to ensure that even if user tries to update team multiple times
          // to get more views, we will send this kind of notification only once a month
          key: `newTeammate-${player.id}-${user.id}-${new Date().getMonth()}`,
          params: {
            destinationPath: `/${user.slug}`,
          },
          forcePush: true,
        })
      }
    }

    const epLink = input.externalLinks?.find(
      (link: { type: ExternalLinkType }) => link.type === ExternalLinkType.ELITE_PROSPECTS
    )
    const currentEpLink = user.externalLinks?.find(
      (link) => link.type === ExternalLinkType.ELITE_PROSPECTS
    )
    if (epLink && !currentEpLink) {
      await this.userStatsSyncerService.syncUserStats(updatedUser?.id)

      // to get proper current team in response
      updatedUser = await UserModel.findOne({ _id: context.user!.id }).populate([
        { path: 'currentTeam', populate: { path: 'organization' } },
      ])

      await this.slackService.sendMessage(
        `User ${updatedUser?.name} has added Elite Prospects link., ${epLink.url}, view in <http://admin.${config.domain}/dashboard/users/form/?id=${updatedUser?.id}|admin panel>`,
        config.slack.channels.externalLinks
      )
    } else if (epLink && currentEpLink && epLink.url !== currentEpLink.url) {
      await this.userStatsSyncerService.syncUserStats(updatedUser?.id)

      // to get proper current team in response
      updatedUser = await UserModel.findOne({ _id: context.user!.id }).populate([
        { path: 'currentTeam', populate: { path: 'organization' } },
      ])

      await this.slackService.sendMessage(
        `User ${updatedUser?.name} has updated Elite Prospects link., ${epLink.url}, view in <http://admin.${config.domain}/dashboard/users/form/?id=${updatedUser?.id}|admin panel>`,
        config.slack.channels.externalLinks
      )
    }

    // update analytics
    this.analytics.updateUserAttrs(updatedUser)

    return {
      user: updatedUser,
      errors: [],
    }
  }

  @Mutation(() => User)
  async visitUser(@Arg('id') id: Types.ObjectId, @Ctx() ctx: ContextType): Promise<User> {
    try {
      const user = await UserModel.findById(id)

      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      const viewExists = await UserViewModel.findOne({
        user: id,
        fingerprint: ctx.fingerprint,
        createdAt: { $gt: new Date(Date.now() - 1000 * 60 * 60 * 4) },
      })

      if (viewExists) {
        return user
      }

      let userUpdated
      if (user.userStats?.views === 0) {
        user.userStats = {
          followers: 0,
          boosts: 0,
          views: 1,
          followings: 0,
        }

        userUpdated = await UserModel.findOneAndUpdate(
          { _id: id },
          { userStats: user.userStats },
          { new: true }
        )
      } else {
        userUpdated = await UserModel.findOneAndUpdate(
          { _id: id },
          { $inc: { 'userStats.views': 1 } },
          { new: true }
        )
      }

      await UserViewModel.create({
        user: id,
        country: '',
        ipAddress: ctx.ipAddress,
        fingerprint: ctx.fingerprint,
        userYearOfBirth: user.dateOfBirth?.getFullYear(),
        userCountry: user.country,
      })

      const aggregationBase = {
        user: id,
        userCountry: user.country,
        userYearOfBirth: user.dateOfBirth?.getFullYear(),
        $inc: { totalViews: 1 },
      }

      const startOfWeek = moment().utc().startOf('isoWeek').toDate()
      await UserViewAggregatedModel.findOneAndUpdate(
        { user: id, date: startOfWeek, type: ViewAggregationType.WEEKLY },
        {
          ...aggregationBase,
          type: ViewAggregationType.WEEKLY,
          date: startOfWeek,
        },
        { upsert: true }
      )

      const startOfMonth = moment().utc().startOf('month').toDate()
      await UserViewAggregatedModel.findOneAndUpdate(
        { user: id, date: startOfMonth, type: ViewAggregationType.MONTHLY },
        {
          ...aggregationBase,
          type: ViewAggregationType.MONTHLY,
          date: startOfMonth,
        },
        { upsert: true }
      )

      if (userUpdated?.userStats?.views === 50) {
        // Stream Feed add activity to notification feed
        await addNotificationActivity({
          userId: id.toString(),
          actor: null,
          verb: FeedActivityVerb.VIEW_INSIGHTS,
          foreignId: id.toString(),
        })

        await this.pushNotificationService.sendPushNotification(userUpdated, {
          title: 'View insights unlocked! 🎉',
          message: 'Congrats on reaching 50 views. Check out your progress.',
          type: PushNotificationType.VIEW_INSIGHTS,
          params: {
            destinationPath: '/activity',
          },
          forcePush: true,
        })
      }

      if (ctx.user) {
        const user = await UserModel.findById(ctx.user.id).populate('bio.agency')

        if (
          user?.role === Role.SPORTS_PROFESSIONAL &&
          user?.subrole === SubRole.AGENT &&
          user.bio?.agency
        ) {
          const agency = user.bio.agency as Agency

          // Stream Feed add activity to notification feed
          await addNotificationActivity({
            userId: id.toString(),
            actor: user,
            verb: FeedActivityVerb.VIEW_AGENT,
            foreignId: user.id,
          })

          await this.pushNotificationService.sendPushNotification(userUpdated, {
            title: 'New view from agent! 🕵️',
            message: `Agent ${user.name} from ${agency.name} just viewed your profile.`,
            type: PushNotificationType.VIEW_AGENT,
            params: {
              destinationPath: `/agent/${user.slug}`,
            },
            forcePush: true,
          })
        }

        if (user?.role === Role.SPORTS_PROFESSIONAL && user?.subrole === SubRole.SCOUT) {
          // Stream Feed add activity to notification feed
          await addNotificationActivity({
            userId: id.toString(),
            actor: user,
            verb: FeedActivityVerb.VIEW_SCOUT,
            foreignId: user.id,
          })

          await this.pushNotificationService.sendPushNotification(userUpdated, {
            title: 'New view from scout! 🕵️',
            message: `Scout ${user.name} just viewed your profile.`,
            type: PushNotificationType.VIEW_SCOUT,
            params: {
              destinationPath: `/`,
            },
            forcePush: true,
          })
        }

        if (user?.role === Role.SPORTS_PROFESSIONAL && user?.subrole === SubRole.COACH) {
          // Stream Feed add activity to notification feed
          await addNotificationActivity({
            userId: id.toString(),
            actor: user,
            verb: FeedActivityVerb.VIEW_COACH,
            foreignId: user.id,
          })

          await this.pushNotificationService.sendPushNotification(userUpdated, {
            title: 'New view from coach! 🕵️',
            message: `Coach ${user.name} just viewed your profile.`,
            type: PushNotificationType.VIEW_COACH,
            params: {
              destinationPath: `/`,
            },
            forcePush: true,
          })
        }
      }

      // don't track shadow player views as they are not real users (not created in amplitude)
      if (user.role !== Role.SHADOW_PLAYER) {
        this.analytics.trackEvent('newView', {}, id.toString())
      }

      if (!userUpdated) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      return userUpdated
    } catch (err) {
      this.logger.error('Failed to visit user:', err)

      throw new GraphQLError('Failed to visit user', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => User)
  @Authorized()
  async followUser(@Arg('id') id: string, @Ctx() ctx: ContextType): Promise<User> {
    try {
      let user = await UserModel.findById(id)

      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      // create a subscription
      try {
        // FIXME should be in transaction
        await UserSubscriptionModel.create({
          protocol: SubscribeProtocol.EMAIL,
          follower: ctx.user!.id,
          user: user._id,
        })

        user = await UserModel.findOneAndUpdate(
          { _id: id },
          { $inc: { 'userStats.followers': 1 } },
          { new: true }
        )

        await UserModel.findOneAndUpdate(
          { _id: ctx.user!.id },
          { $inc: { 'userStats.followings': 1 } }
        )

        // send push notification
        const follower = await UserModel.findById(ctx.user!.id)

        // Stream Feed follow
        const myTimeLinefeed = this.streamFeed.feed(FEED_NAMES.TIMELINE, ctx.user!.id)
        await myTimeLinefeed.follow(FEED_NAMES.USER, id)

        if (follower?.role === Role.SPORTS_PROFESSIONAL && follower?.subrole) {
          await addNotificationActivity({
            userId: id,
            actor: follower,
            verb: FeedActivityVerb[`FOLLOW_${follower.subrole}`],
            foreignId: follower?.id || ctx.user!.id,
          })

          await this.pushNotificationService.sendPushNotification(user, {
            title: `New follow from ${follower?.subrole?.toLowerCase()}! 🤩🕵️`,
            message: `${capitalizeWords(follower?.subrole)} ${follower?.name} is now following you.`,
            type: PushNotificationType[`FOLLOW_${follower.subrole}`],
            params: {
              destinationPath:
                follower?.subrole === SubRole.AGENT ? `/agent/${follower.slug}` : `/`,
            },
            key: `newFollower-${ctx.user!.id}-${id}`,
          })
        } else {
          // Stream Feed add activity to notification feed
          await addNotificationActivity({
            userId: id,
            actor: follower,
            verb: follower?.name ? FeedActivityVerb.FOLLOW : FeedActivityVerb.FOLLOW_OTHER,
            foreignId: follower?.id || ctx.user!.id,
          })

          let message = ''

          if (follower?.name) {
            message = `${follower?.name} is now following you. Check out their profile!`
          } else {
            message = 'Someone just started following you.'
          }

          await this.pushNotificationService.sendPushNotification(user, {
            title: 'New follower! 🤩',
            message,
            type: follower?.name ? PushNotificationType.FOLLOW : PushNotificationType.FOLLOW_OTHER,
            params: {
              destinationPath: `/${follower?.slug}`,
            },
            key: `newFollower-${ctx.user!.id}-${id}`,
          })
        }

        this.analytics.trackEvent('newFollower', {}, id)
      } catch (err) {
        if (err.name === 'MongoServerError' && err.code === 11000) {
          // duplicate key error, we don't mind this
        } else {
          this.logger.error('DB error:', err)
        }
      }

      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      return user
    } catch (err) {
      if (err instanceof GraphQLError) {
        throw err
      }

      throw new GraphQLError('Failed to follow user', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => UnfollowUserResponse)
  @Authorized()
  async unfollowUser(
    @Arg('id') id: string,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<UnfollowUserResponse> {
    try {
      // Find the user to unfollow
      const userToUnfollow = await UserModel.findById(id).populate([
        { path: 'currentTeam', populate: { path: 'organization' } },
      ])
      if (!userToUnfollow) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      // Remove the user from the follower list
      const follow = await UserSubscriptionModel.findOne({
        user: id,
        follower: ctx.user!.id,
      })

      // Stream Feed unfollow
      const myTimeLinefeed = this.streamFeed.feed(FEED_NAMES.TIMELINE, ctx.user!.id)
      await myTimeLinefeed.unfollow(FEED_NAMES.USER, id)

      if (follow) {
        // TODO: should be in transaction
        await follow.deleteOne()

        // userStats will not be null as it has to have follow already
        userToUnfollow.userStats!.followers -= 1
        userToUnfollow.save()

        await UserModel.findOneAndUpdate(
          { _id: ctx.user!.id },
          { $inc: { 'userStats.followings': -1 } }
        )

        return {
          user: userToUnfollow,
          errors: [],
        }
      }

      return {
        user: userToUnfollow,
        errors: [new UserNotFoundError(`Follow not found`, info.path.key.toString())],
      }
    } catch (err) {
      this.logger.error('Failed to unfollow user:', err)
      if (err instanceof GraphQLError) {
        throw err
      }

      throw new GraphQLError('Failed to unfollow user', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Query(() => Boolean)
  @Authorized()
  async isFollowing(@Arg('userId') userId: string, @Ctx() ctx: ContextType): Promise<boolean> {
    const follow = await UserSubscriptionModel.findOne({
      user: userId,
      follower: ctx.user!.id,
    })

    return !!follow
  }

  @Query(() => UsersResponse)
  @Authorized()
  async myFollowing(
    @Ctx() ctx: ContextType,
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string
  ): Promise<UsersResponse> {
    const response = new UsersResponse()
    response.edges = []

    const mergedPagination: Pagination = {
      first: first ?? pagination?.first ?? 10,
      after: after ?? pagination?.after,
      before: before ?? pagination?.before,
    }

    const users = await this.paginationRepository.find(
      { _id: new Types.ObjectId() },
      { follower: ctx.user!.id },
      UserSubscriptionModel,
      { fields: [{ field: '_id', order: SortOrder.DESC }] },
      this.paginationRepository.mapPagination(mergedPagination)
    )
    await UserSubscriptionModel.populate(users.nodes, {
      path: 'user',
      populate: { path: 'currentTeam', populate: { path: 'organization' } },
    })

    for (const user of users.nodes) {
      response.edges.push({
        node: user.user as User,
      })
    }

    response.pageInfo = users.pageInfo

    return response
  }

  @Query(() => MyFollowersResponse)
  @Authorized()
  async myFollowers(
    @Ctx() ctx: ContextType,
    @Arg('filter') filter: MyFollowersFilter,
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string
  ): Promise<MyFollowersResponse> {
    const response = new MyFollowersResponse()
    response.edges = []

    const pipeline = [
      { $match: { user: new Types.ObjectId(ctx.user!.id), follower: { $exists: true } } },
      { $lookup: { from: 'users', localField: 'follower', foreignField: '_id', as: 'user' } },
      { $unwind: '$user' },
      { $match: { 'user.role': filter.role, 'user.markedForDeletion': { $exists: false } } },
    ]

    const userSubscriptions = await this.paginationRepository.aggregate({
      templateSortObject: { _id: new Types.ObjectId() },
      pipeline,
      model: UserSubscriptionModel,
      sorting: { fields: [{ field: '_id', order: SortOrder.DESC }] },
      pagination: this.paginationRepository.mapPagination({ first, after, before }),
    })

    const users = await UserModel.find({
      _id: { $in: userSubscriptions.nodes.map((u) => u.follower.toString()) },
    }).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

    const usersMap = new Map<string, User>()
    for (const user of users) {
      usersMap.set(user._id.toString(), user)
    }

    // do I follow all these users?
    const following = await UserSubscriptionModel.find({
      follower: ctx.user!.id,
      user: { $in: users.map((u) => u._id) },
    })

    for (const userSubscription of userSubscriptions.nodes) {
      const user = usersMap.get(userSubscription.follower._id.toString())
      if (user) {
        response.edges.push({
          node: user,
          iFollow: following.some((f) => f.user.toString() === user._id.toString()),
        })
      }
    }

    response.pageInfo = userSubscriptions.pageInfo

    return response
  }

  private getUserToken(user: User, role = UserRole.USER) {
    const payload: ContextUser = {
      id: user._id.toString(),
      email: user.email,
      role,
    }

    return jwt.sign(payload, config.jwt.secret, { expiresIn: '9000d' })
  }

  @Query(() => User)
  @Authorized()
  async currentUser(@Ctx() ctx: ContextType): Promise<User> {
    const user = await UserModel.findOne({ _id: ctx.user!.id })

    if (!user) {
      throw new GraphQLError('User does not exist.', { extensions: { code: 'UNAUTHENTICATED' } })
    }

    // Update lastActiveAt field if it's not set or if it's older than 10 minutes
    const now = new Date()
    const UPDATE_INTERVAL = 10 * 60 * 1000 // 10 minutes in milliseconds

    if (!user.lastActiveAt || now.getTime() - user.lastActiveAt.getTime() > UPDATE_INTERVAL) {
      // Update the lastActiveAt field without waiting for the result
      // We don't want to block the request for this update
      UserModel.updateOne({ _id: user._id }, { $set: { lastActiveAt: now } }).catch((err) => {
        this.logger.error('Failed to update lastActiveAt:', err)
      })
    }

    return user
  }

  @Mutation(() => UploadAvatarResponse)
  @Authorized()
  async uploadAvatar(
    @Arg('input', { nullable: true }) input: UploadAvatarInput,
    @Ctx() ctx: ContextType,
    @Info() info: GraphQLResolveInfo
  ): Promise<UploadAvatarResponse> {
    const response = new UploadAvatarResponse()

    if (input) {
      if (input.uploadSuccessful) {
        response.user = await UserModel.findById(ctx.user!.id)

        if (response.user?.avatarUnconfirmed) {
          if (response.user.avatar) {
            // we don't need to await this
            Promise.all([
              this.s3.remove(response.user.avatar),
              this.s3.remove(response.user.avatars.tiny),
              this.s3.remove(response.user.avatars.small),
              this.s3.remove(response.user.avatars.medium),
              this.s3.remove(response.user.avatars.large),
              this.s3.remove(response.user.avatars.og),
            ]).catch((err) => {
              this.logger.error('Failed to remove old avatars: ', err)
            })
          }

          const avatars = await this.mediaConverter.createImageThumbnails(
            response.user.avatarUnconfirmed
          )

          response.user = await UserModel.findOneAndUpdate(
            { _id: ctx.user!.id },
            {
              avatar: response.user.avatarUnconfirmed,
              avatarUnconfirmed: null,
              avatars,
            },
            { new: true }
          ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])
        }
      } else {
        // TODO: maybe also delete the file from S3 if it exists
        response.user = await UserModel.findOneAndUpdate(
          { _id: ctx.user!.id },
          { avatarUnconfirmed: null }
        ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

        return response
      }
    }

    try {
      const avatarKey = `images/avatars/${ctx.user!.id}-${new Date().getTime()}.jpg`
      response.preSignedUrl = await this.s3.createPresignedUrl(avatarKey, 'image/jpeg')

      // save the key to the user
      response.user = await UserModel.findOneAndUpdate(
        { _id: ctx.user!.id },
        { avatarUnconfirmed: avatarKey }
      ).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])
    } catch (err) {
      this.logger.error("Failed to create preSignedUrl for users' avatar:", err)
      response.errors = [
        new FailedToCreatePresignedUrl(`Failed to create preSignedUrl`, info.path.key.toString()),
      ]
    }

    return response
  }

  @Query(() => UsersResponse)
  async searchUsers(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter') filter: UsersFilter
  ): Promise<UsersResponse> {
    const response = new UsersResponse()
    response.edges = []

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline: any = {
      searchEnabled: { $ne: false },
      public: { $ne: false },
      role: filter.role || { $in: filter.roles || [Role.PLAYER, Role.SHADOW_PLAYER, null] },
      ...(filter.country ? { country: filter.country } : {}),
    }
    const users = await this.userRepository.search(filter.searchQuery ?? null, pagination, pipeline)

    for (const user of users.nodes) {
      response.edges.push({
        node: user,
      })
    }

    response.pageInfo = users.pageInfo

    return response
  }

  @Query(() => UsersResponse)
  @Authorized(SystemRole.ADMIN)
  async users(
    @Arg('pagination', { nullable: true }) pagination: Pagination,
    @Arg('filter') filter: AdminUsersFilter
  ): Promise<UsersResponse> {
    const response = new UsersResponse()
    response.edges = []

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline = {} as unknown as any
    if (filter.recommended) {
      pipeline.recommended = true
    }

    if (filter.premium) {
      pipeline.premium = true
    }

    if (filter.inspired) {
      pipeline.inspired = true
    }

    if (filter.moreSelected) {
      pipeline.moreSelected = true
    }

    if (filter.withoutTeam) {
      pipeline.currentTeam = null
    }

    if (filter.withoutPicture) {
      pipeline.avatar = null
    }

    if (filter.yearOfBirth) {
      pipeline.dateOfBirth = {
        $gte: new Date(filter.yearOfBirth, 1, 1),
        $lte: new Date(filter.yearOfBirth, 12, 31),
      }
    }

    if (filter.country) {
      pipeline.country = filter.country
    }

    if (filter.role) {
      pipeline.role = filter.role
    }

    if (filter.subrole) {
      pipeline.subrole = filter.subrole
    }

    if (filter.agency) {
      pipeline['bio.agency'] = filter.agency
    }

    if (filter.possibleYearsOfBirth) {
      pipeline.possibleYearsOfBirth = {
        $in: [filter.possibleYearsOfBirth],
      }
    }

    if (filter.searchQuery?.includes('@')) {
      pipeline.email = filter.searchQuery
    }

    const users = await this.userRepository.search(filter.searchQuery ?? null, pagination, pipeline)

    for (const user of users.nodes) {
      response.edges.push({
        node: user,
      })
    }

    response.pageInfo = users.pageInfo

    return response
  }

  @Query(() => [User])
  async recommendedUsers(
    @Ctx() context: ContextType,
    @Arg('activeUserId', { nullable: true }) activeUserId: string,
    @Arg('yearOfBirth', { nullable: true }) yearOfBirth?: YearArguments,
    @Arg('country', { nullable: true }) country?: Country,
    @Arg('inspired', { nullable: true }) inspired?: boolean,
    @Arg('limit', (_) => Int, { nullable: true }) limit?: number
  ): Promise<User[]> {
    const NUMBER_OF_RECOMMENDED_USERS = limit || 3
    const yearFrom = yearOfBirth ? yearOfBirth.year - 1 : undefined
    const yearTo = yearOfBirth ? yearOfBirth.year + 1 : undefined

    let result: User[] = []
    let excludedIds: Types.ObjectId[] = []

    const loggedUserObjectId = context?.user?.id ? new Types.ObjectId(context.user.id) : undefined
    const activeUserObjectId = activeUserId ? new Types.ObjectId(activeUserId) : undefined

    const users = await this.userRepository.getRecommendedUsers(
      loggedUserObjectId,
      activeUserObjectId,
      country?.country,
      yearFrom,
      yearTo,
      undefined,
      undefined,
      undefined,
      undefined,
      inspired
    )

    result = result.concat(users)
    excludedIds = excludedIds.concat(result.map((user) => user._id))

    if (result.length < NUMBER_OF_RECOMMENDED_USERS) {
      // load more users without country and yearOfBirth
      const moreUsers = await this.userRepository.getRecommendedUsers(
        loggedUserObjectId,
        activeUserObjectId,
        undefined,
        undefined,
        undefined,
        true,
        true,
        NUMBER_OF_RECOMMENDED_USERS - result.length,
        excludedIds,
        inspired
      )
      result = result.concat(moreUsers)
      excludedIds = excludedIds.concat(moreUsers.map((user) => user._id))
    }

    if (result.length < NUMBER_OF_RECOMMENDED_USERS) {
      // load more users without country and yearOfBirth and Sport Details
      const moreUsers = await this.userRepository.getRecommendedUsers(
        loggedUserObjectId,
        activeUserObjectId,
        undefined,
        undefined,
        undefined,
        true,
        false,
        NUMBER_OF_RECOMMENDED_USERS - result.length,
        excludedIds,
        inspired
      )
      result = result.concat(moreUsers)
      excludedIds = excludedIds.concat(moreUsers.map((user) => user._id))
    }

    if (result.length < NUMBER_OF_RECOMMENDED_USERS) {
      // load more users without country and yearOfBirth and Sport Details and Eliteprospects link
      const moreUsers = await this.userRepository.getRecommendedUsers(
        loggedUserObjectId,
        activeUserObjectId,
        undefined,
        undefined,
        undefined,
        false,
        false,
        NUMBER_OF_RECOMMENDED_USERS - result.length,
        excludedIds,
        inspired
      )
      result = result.concat(moreUsers)
      excludedIds = excludedIds.concat(moreUsers.map((user) => user._id))
    }

    return result
  }

  @Query(() => RandomUsersResponse)
  async randomUsers(): Promise<RandomUsersResponse> {
    const queryFilter = {
      role: { $in: [Role.PLAYER, null] },
      public: { $ne: false },
      slug: { $ne: null },
    }

    const [totalCount, users] = await Promise.all([
      UserModel.countDocuments(queryFilter),
      UserModel.aggregate([
        { $match: { ...queryFilter, avatar: { $exists: true } } },
        { $sample: { size: 60 } },
        {
          $lookup: {
            from: 'teams',
            localField: 'currentTeam',
            foreignField: '_id',
            as: 'currentTeam',
          },
        },
        { $unwind: { path: '$currentTeam', preserveNullAndEmptyArrays: true } },
      ]),
    ])

    const edges = users.map((user) => ({ node: user }))

    return {
      edges,
      totalCount,
    }
  }

  @Query(() => [User])
  async moreSelectedUsers(): Promise<User[]> {
    return UserModel.find({ moreSelected: true, public: { $ne: false } }).limit(30)
  }

  @Query(() => [User])
  @CacheControl({ maxAge: 300, scope: 'PUBLIC' })
  async inspiredUsers(
    @Arg('first', (_) => Int, { nullable: true }) first: number
  ): Promise<User[]> {
    const users = await UserModel.aggregate([
      { $match: { inspired: true, public: { $ne: false } } },
      { $sample: { size: first ?? 3 } },
    ])

    return users
  }

  @Query(() => UserViewsResponse)
  @CacheControl({ maxAge: 300, scope: 'PUBLIC' })
  async topViewedUsers(
    @Arg('period', (_) => Period) period: Period,
    @Arg('first', (_) => Int, { nullable: true }) first: number,
    @Arg('after', { nullable: true }) after: string,
    @Arg('before', { nullable: true }) before: string,
    @Arg('pagination', { nullable: true }) paginationArgs: Pagination,
    @Arg('filter', () => TopViewedUsersFilter, { nullable: true }) filter?: TopViewedUsersFilter
  ): Promise<UserViewsResponse> {
    const pagination = paginationArgs ?? { first: first, after: after, before: before }
    return this.userRepository.getTopViewedAthletes(period, pagination, filter)
  }

  @Query(() => UserViewStatsResponse)
  async userViewsStats(@Ctx() ctx: ContextType): Promise<UserViewStatsResponse> {
    try {
      return await this.userRepository.getUserViewsStats(ctx)
    } catch (err) {
      this.logger.error('Failed to get user views stats:', err)

      throw new GraphQLError('Failed to get user views stats')
    }
  }

  @Query(() => EntityCountsResponse)
  @CacheControl({ maxAge: 300, scope: 'PUBLIC' })
  async entityCounts(): Promise<EntityCountsResponse> {
    const [players, professionals, agencies] = await Promise.all([
      UserModel.countDocuments({ role: { $in: [Role.PLAYER, null] } }),
      UserModel.countDocuments({
        role: Role.SPORTS_PROFESSIONAL,
      }),
      AgencyModel.countDocuments({
        integrated: true,
      }),
    ])

    return {
      players,
      professionals,
      agencies,
    }
  }

  @Query(() => NewUsersCountResponse)
  @CacheControl({ maxAge: 300, scope: 'PUBLIC' })
  async newUsersCount(
    @Arg('country', { nullable: false }) country: string
  ): Promise<NewUsersCountResponse> {
    // hardcoded date, since we count new users
    // probably we will move this to admin one day
    const sinceDate = new Date(2025, 4, 1)

    const count = await UserModel.countDocuments({
      country,
      role: [Role.PLAYER],
      createdAt: { $gte: sinceDate },
    })

    return {
      count,
    }
  }

  @Mutation(() => Boolean)
  @Authorized()
  async deleteAccount(@Ctx() ctx: ContextType): Promise<boolean> {
    try {
      const user = await UserModel.findById(ctx.user!.id)

      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      await userCleanup(user, this.sns)

      this.analytics.updateCustomAttrs(user, {
        deleted: true,
        deleted_date: new Date().toISOString(),
      })

      await this.slackService.sendMessage(
        `User ${user.name} has deleted their account.`,
        config.slack.channels.deleteAccount
      )

      return true
    } catch (err) {
      this.logger.error('Failed to delete user:', err)
      throw new GraphQLError('Failed to delete user', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Query(() => User, { nullable: true })
  async user(@Arg('slug') slug: string, @Ctx() ctx: ContextType): Promise<User | null> {
    if (slug.includes('.')) {
      this.logger.warn(`Slug contains invalid characters: ${slug}`)
      return null
    }

    let filter: object = {
      public: { $ne: false },
    }

    if (ctx.user?.admin) {
      filter = {}
    }

    const user = await UserModel.findOne({
      slug,
      ...filter,
    })

    if (!user) {
      // just warn to see in the logs if something is wrong
      // but error would trigger alert for no reason
      this.logger.warn(`User with slug ${slug} not found`)

      return null
    }

    return user
  }

  @Query(() => User, { nullable: true })
  async userById(@Arg('id') id: Types.ObjectId): Promise<User | null> {
    const user = await UserModel.findById(id)

    if (!user) {
      // just warn to see in the logs if something is wrong
      // but error would trigger alert for no reason
      this.logger.warn(`User with id ${id} not found`)

      return null
    }

    return user
  }

  @Query(() => [User], { nullable: true })
  @Authorized()
  async usersByIds(
    @Arg('userIds', (_) => [Types.ObjectId]) userIds: Types.ObjectId[]
  ): Promise<User[] | null> {
    const users = await UserModel.find({ _id: { $in: userIds } })

    if (!users.length) {
      return null
    }

    return users
  }

  @Mutation(() => RegisterDeviceResponse)
  @Authorized()
  async registerDevice(
    @Arg('input') input: RegisterDeviceInput,
    @Ctx() ctx: ContextType
  ): Promise<RegisterDeviceResponse> {
    const user = await UserModel.findById(ctx.user!.id).lean()

    try {
      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      if (!user.registeredDevices) {
        user.registeredDevices = []
      }

      let updatedUser
      // check if device is already registered
      const device = user.registeredDevices.find((d) => d.token === input.token)
      if (!device) {
        let snsResponse
        try {
          snsResponse = await this.sns.registerDeviceToken(input.token, input.platform)
        } catch (err) {
          this.logger.error('Register device failed with: ', err)

          return {
            user,
            errors: [
              new DeviceAlreadyRegisteredError(
                `This device token is already registered.`,
                'registerDevice'
              ),
            ],
          }
        }

        // use addToSet to avoid duplicates
        updatedUser = await UserModel.findOneAndUpdate(
          { _id: user._id },
          {
            $addToSet: {
              registeredDevices: {
                token: input.token,
                platform: input.platform,
                arn: snsResponse.platformArn,
              },
              ...(snsResponse.platformSandboxArn && {
                registeredDevices: {
                  token: input.token,
                  platform: input.platform,
                  arn: snsResponse.platformSandboxArn,
                },
              }),
            },
          },
          { new: true }
        )
      } else {
        return {
          user,
          errors: [
            new DeviceAlreadyRegisteredError(
              `This device token is already registered.`,
              'registerDevice'
            ),
          ],
        }
      }

      return {
        user: updatedUser || user,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to register device: ', err)
      throw new GraphQLError('Failed to register device. Please try again later.', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => UnregisterDeviceResponse)
  @Authorized()
  async unregisterDevice(
    @Arg('input') input: UnregisterDeviceInput,
    @Ctx() ctx: ContextType
  ): Promise<UnregisterDeviceResponse> {
    const user = await UserModel.findById(ctx.user!.id).lean()

    try {
      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      if (!user.registeredDevices) {
        throw new GraphQLError('User has no registerd devices.', {
          extensions: { code: 'NOT_FOUND' },
        })
      }

      // check if device is already registered
      const device = user.registeredDevices.find((d) => d.token === input.token)
      if (!device) {
        return {
          user,
          errors: [new DeviceNotRegisteredError(`This device was not found.`, 'registerDevice')],
        }
      }

      // use pull to remove from array
      const updatedUser = await UserModel.findOneAndUpdate(
        { _id: user._id },
        {
          $pull: {
            registeredDevices: {
              token: input.token,
              platform: input.platform,
            },
          },
        },
        { new: true }
      )

      return {
        user: updatedUser || user,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to unregister device: ', err)
      throw new GraphQLError('Failed to unregister device. Please try again later.', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => PushNotificationsResponse)
  @Authorized()
  async pushNotifications(
    @Ctx() ctx: ContextType,
    @Arg('input') input: PushNotificationsInput
  ): Promise<PushNotificationsResponse> {
    try {
      const user = await UserModel.findById(ctx.user!.id)

      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      user.pushEnabled = input.enabled
      await user.save()

      await this.analytics.updateUserAttrs(user)

      return {
        user,
        errors: [],
      }
    } catch (err) {
      this.logger.error('Failed to update push notifications:', err)

      throw new GraphQLError('Failed to update push notifications. Please try again later.', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  @Mutation(() => FetchUserDataEPResponse)
  @Authorized()
  async fetchUserDataEP(@Ctx() ctx: ContextType): Promise<FetchUserDataEPResponse> {
    try {
      const user = await UserModel.findById(ctx.user!.id)

      if (!user) {
        throw new GraphQLError('User does not exist.', { extensions: { code: 'NOT_FOUND' } })
      }

      if (user.statsSyncedAt || user.statsSyncFailedAt) {
        throw new GraphQLError('User stats have been already synced and messages have been sent.', {
          extensions: { code: 'NOT_FOUND', noLog: true },
        })
      }

      if (user.role !== Role.PLAYER && user.role !== Role.SHADOW_PLAYER && user.role !== null) {
        return {
          user,
          errors: [],
        }
      }

      const shadowUser = await findShadowUser(user)
      if (shadowUser) {
        // claim account
        user.userStats = shadowUser.userStats
        user.avatar = shadowUser.avatar
        user.avatars = shadowUser.avatars
        user.bio = shadowUser.bio
        user.externalLinks = shadowUser.externalLinks

        // if we slug without unique suffix - John Doe == john-doe
        // we need to copy over slug from shadow user
        if (shadowUser.slug === slugifyString(shadowUser.name)) {
          user.slug = shadowUser.slug
        }

        // TODO change this to external connection
        // change stats and views
        await UserStatsModel.updateMany(
          {
            user: shadowUser.id,
          },
          {
            user: user.id,
          }
        )

        await UserViewModel.updateMany(
          {
            user: shadowUser.id,
          },
          {
            user: user.id,
          }
        )

        await UserSubscriptionModel.updateMany(
          {
            user: shadowUser.id,
          },
          {
            user: user.id,
          }
        )

        // cleanup shadow user account
        await UserModel.updateOne(
          { _id: shadowUser._id },
          {
            markedForDeletion: true,
            email: shadowUser.id,
            slug: null,
            shadowSlug: null,
          }
        )

        await this.slackService.sendMessage(
          `Auto sync (shadow account claimed): User ${user.name} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>`,
          config.slack.channels.shadowAccountClaim
        )

        await addExternalConnections(user)

        user.statsSyncedAt = new Date()
        await user.save()

        if (user.bio?.agency) {
          await recalculateClientsCount(user.bio.agency as Types.ObjectId)
        }

        return {
          user: user,
          errors: [],
        }
      } else if (user.country === 'CZ') {
        // if user is from Czechia and we could not find shadow profile we need to know about it
        // so sending slack message, this is just temporary solution, can be removed in the future
        await this.slackService.sendMessage(
          'Shadow profile not found for user ' +
            user.name +
            ' - Admin: <http://admin.' +
            config.domain +
            '/dashboard/users/form/?id=' +
            user.id +
            '|admin panel>',
          config.slack.channels.externalLinks
        )
      }

      const player = await this.epService.getUserData({
        firstname: user.firstname!,
        lastname: user.lastname!,
        possibleYearsOfBirth: user.dateOfBirth
          ? [user.dateOfBirth!.getFullYear()]
          : (user.possibleYearsOfBirth ?? []),
        country: user.country!,
      })

      await addExternalConnections(user)

      if (player) {
        await this.syncUserFromEPData(player, user)
      } else {
        await this.slackService.sendMessage(
          `Auto sync (Elite Prospects) failed: User ${user.name} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>`,
          config.slack.channels.externalLinks
        )
        user.statsSyncFailedAt = new Date()
        await user.save()
      }

      return {
        user,
        errors: [],
      }
    } catch (err) {
      if (err instanceof GraphQLError) {
        throw err
      }

      this.logger.error('Failed to sync player', err)

      throw new GraphQLError('Failed to fetch data. Please try again later.', {
        extensions: { code: 'INTERNAL_SERVER_ERROR' },
      })
    }
  }

  private async syncUserFromEPData(player: EPPlayer, user: DocumentType<User>): Promise<User> {
    const height = parseImperialHeight(player.height?.imperial)
    user.bio = {
      ...(height
        ? {
            height: {
              inches: height.inches ?? 0,
              feet: height.feet ?? 0,
              centimeters: player.height?.metrics ?? 0,
            },
          }
        : {}),
      ...(player.weight
        ? {
            weight: {
              pounds: player.weight.imperial ?? 0,
              kilograms: player.weight.metrics ?? 0,
            },
          }
        : {}),
      handedness: player.shoots === 'L' ? BioHandedness.LEFT : BioHandedness.RIGHT,
    }

    if (!user.externalLinks) {
      user.externalLinks = []
    }

    user.externalLinks.push({
      name: 'Elite prospects',
      url: player.links.eliteprospectsUrl,
      type: ExternalLinkType.ELITE_PROSPECTS,
    })

    await user.save()
    await this.userStatsSyncerService.syncUserStats(user.id.toString())

    await this.slackService.sendMessage(
      `Auto sync: User ${user.name} - Profile: ${player.links.eliteprospectsUrl} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${user.id}|admin panel>`,
      config.slack.channels.externalLinks
    )

    return user
  }

  @Query(() => [User])
  @Authorized()
  async exampleUsers(): Promise<User[]> {
    const users = await UserModel.aggregate([
      {
        $match: {
          'highlights.0': { $exists: true },
          public: { $ne: false },
        },
      },
      { $sample: { size: 9 } },
    ])

    return users
  }

  @Mutation(() => Boolean)
  @Authorized()
  async createChatUsers(@Arg('input') input: CreateChatUsersInput): Promise<boolean> {
    const { userIds } = input

    const users = await UserModel.find({ _id: { $in: userIds } })
      .populate([{ path: 'bio.agency' }])
      .lean()

    const chatUsers: UpsertUsersChatUser[] = users.map(
      ({ _id, name, firstname, lastname, slug, email, avatar: image, role, subrole, bio }) => ({
        id: _id.toString(),
        name: name || email,
        firstname,
        lastname,
        slug,
        image: !image
          ? null
          : !image?.startsWith(config.assetsUrl)
            ? `${config.assetsUrl}${image}`
            : image,
        graet_role: role,
        graet_sub_role: subrole,
        ...(bio?.agency ? { graet_agent_agency_name: (bio.agency as Agency)?.name } : {}),
      })
    )

    await this.streamChat.upsertUsers(chatUsers)

    return true
  }

  @Mutation(() => String)
  @Authorized()
  async createChatUserToken(@Ctx() context: ContextType): Promise<string> {
    const currentUserId = new Types.ObjectId(context?.user?.id)

    return this.streamChat.createToken(currentUserId.toString())
  }

  @Mutation(() => String)
  @Authorized()
  async createStreamFeedUserToken(@Ctx() context: ContextType): Promise<string> {
    const currentUserId = new Types.ObjectId(context?.user?.id)

    return this.streamFeed.createUserToken(currentUserId.toString())
  }

  @FieldResolver(() => Boolean)
  async following(@Root() root: User, @Ctx() ctx: ContextType): Promise<boolean> {
    if (!ctx.user) {
      return false
    }

    const user = root.getUser()
    if (ctx.user.id === user._id?.toString()) {
      return true
    }

    const follow = await ctx.loaders.userSubscription?.load(user._id?.toString())

    return !!follow
  }

  // TODO: every field resolver should use loaders, because this can be called in list
  @FieldResolver(() => Boolean, { nullable: true })
  async hasGames(@Root() root: User): Promise<boolean | null> {
    const user = root.getUser()

    const externalIds = user.externalConnections?.map((conn) => conn.externalId) || []

    if (externalIds.length === 0) {
      return false
    }

    const hasGames = await GameModel.exists({
      season: CURRENT_SEASON,
      $or: [
        { 'homeTeamRoster.externalInfo.playerExternalId': { $in: externalIds } },
        { 'awayTeamRoster.externalInfo.playerExternalId': { $in: externalIds } },
      ],
    })

    return !!hasGames
  }

  @FieldResolver(() => [User], { nullable: true })
  async familyMembers(
    @Root() root: User,
    @Ctx() ctx: ContextType
  ): Promise<(User | null)[] | null> {
    const user = getEntity(root)

    if (!user.familyMembers) {
      return []
    }

    const members = await Promise.all(
      user.familyMembers.map(({ _id }) => ctx.loaders.user.load(_id.toString()))
    )

    return members.filter((member): member is User => member !== null)
  }

  @FieldResolver(() => [User], { nullable: true })
  async pendingFamilyMembers(
    @Root() root: User,
    @Ctx() ctx: ContextType
  ): Promise<(User | null)[] | null> {
    const user = getEntity(root)

    if (!user.pendingFamilyMembers) {
      return []
    }

    const members = await Promise.all(
      user.pendingFamilyMembers.map(({ _id }) => ctx.loaders.user.load(_id.toString()))
    )

    return members.filter((member): member is User => member !== null)
  }

  @FieldResolver(() => [User], { nullable: true })
  async agentV2(@Root() root: User, @Ctx() ctx: ContextType): Promise<User | null> {
    const user = getEntity(root)

    if (!user.agentV2) {
      return null
    }

    // TODO: move this to loader - pass populated object and cache it
    if (!(user.agentV2 instanceof Types.ObjectId) && user.agentV2._id) {
      return user.agentV2
    }

    return await ctx.loaders.user.load(user.agentV2.toString())
  }

  @FieldResolver(() => Team, { nullable: true })
  async currentTeam(@Root() root: User, @Ctx() ctx: ContextType): Promise<Team | null> {
    const user = getEntity(root)

    if (!user.currentTeam) {
      return null
    }

    // TODO: move this to loader - pass populated object and cache it
    if (!(user.currentTeam instanceof Types.ObjectId) && user.currentTeam._id) {
      return user.currentTeam
    }

    return await ctx.loaders.team.load(user.currentTeam.toString())
  }
}
