import { <PERSON>tx, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'type-graphql'
import { Service } from 'typedi'

import { ContextType } from '../../..'
import { getEntity } from '../../../helpers/mongoose'
import { Team } from '../../stats/models/team'
import { SubRoleSpecification } from '../models/user'

@Service()
@Resolver(() => SubRoleSpecification)
export class UserSubRoleSpecificationResolver {
  @FieldResolver(() => Team, { nullable: true })
  async team(@Root() root: SubRoleSpecification, @Ctx() ctx: ContextType): Promise<Team | null> {
    const subRoleSpecification = getEntity(root)

    if (!subRoleSpecification.team) {
      return null
    }

    return await ctx.loaders.team.load(subRoleSpecification.team.toString())
  }
}
