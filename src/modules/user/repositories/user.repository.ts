import { StreamClient } from 'getstream'
import moment from 'moment'
import mongoose, { FilterQuery, Types } from 'mongoose'
import pino from 'pino'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { S3Service } from '../../../services/s3'
import { Pagination, SortOrder } from '../../../types/general'
import { TeamModel } from '../../stats/models/team'
import { UserStatsModel } from '../../stats/models/userStats'
import { getTopViewedAthletesFilters } from '../helpers/topViewed'
import { UserModel, WallVideoModel } from '../models'
import { BoostModel } from '../models/boost'
import { NotificationModel } from '../models/notification'
import { ExternalLinkType, Role, User, userTemplateSortObject } from '../models/user'
import { UserSubscription, UserSubscriptionModel } from '../models/userSubscription'
import { UserViewModel } from '../models/userViews'
import { UserViewAggregated, UserViewAggregatedModel } from '../models/userViewsAggregated'
import { FEED_NAMES } from '../types/streamFeed'
import {
  Period,
  TopViewedUsersFilter,
  UserViewsResponse,
  UserViewStatsResponse,
} from '../types/user'

import { PaginationRepository } from './pagination.repository'

@Service()
export default class UserRepository {
  @Inject()
  private readonly s3Service: S3Service

  private readonly pagination = new PaginationRepository()

  @Inject('app.logger')
  private readonly logger: pino.Logger

  @Inject('streamFeed.client')
  private streamFeed: StreamClient

  async search(query: string | null, pagination: Pagination, matchPipeline: object = {}) {
    const pipelines = []

    if (query && query.length > 0 && !query.includes('@')) {
      pipelines.push({
        $search: {
          index: 'search',
          autocomplete: {
            query,
            path: 'name',
            tokenOrder: 'sequential',
          },
        },
      })

      pipelines.push({ $addFields: { score: { $meta: 'searchScore' } } })
    }

    pipelines.push({ $match: { ...matchPipeline } })

    const templateSortObject = {
      createdAt: new Date(),
      score: 0,
      _id: new Types.ObjectId(),
    }
    const sorting = {
      fields: [
        { field: query ? 'score' : 'createdAt', order: SortOrder.DESC },
        { field: '_id', order: SortOrder.DESC },
      ],
    }
    const users = await this.pagination.aggregate({
      templateSortObject,
      pipeline: pipelines,
      model: UserModel,
      sorting,
      pagination: this.pagination.mapPagination(pagination),
    })

    // fetch all teams by user.currentTeam
    const teamIds = users.nodes.map((user) => user.currentTeam).filter(Boolean)
    const teams = await TeamModel.find({ _id: { $in: teamIds } })

    users.nodes.forEach((user) => {
      user.currentTeam = teams.find((team) =>
        team._id.equals(user.currentTeam as unknown as string)
      )
    })

    return users
  }

  async getTopViewedAthletes(
    period: Period,
    pagination: Pagination,
    filter?: TopViewedUsersFilter
  ): Promise<UserViewsResponse> {
    const response = new UserViewsResponse()
    const filters = getTopViewedAthletesFilters(period, filter)
    switch (period) {
      case Period.ALL_TIME:
        const dbResponse = await this.pagination.find(
          userTemplateSortObject,
          filters,
          UserModel,
          {
            fields: [
              {
                field: 'userStats.views',
                order: SortOrder.DESC,
              },
            ],
          },
          this.pagination.mapPagination(pagination),
          [{ path: 'currentTeam', populate: { path: 'organization' } }]
        )

        response.edges = dbResponse.nodes.map((user) => {
          const u = user as User
          return { node: u, views: u.userStats?.views ?? 0 }
        })

        response.pageInfo = dbResponse.pageInfo

        break
      case Period.MONTHLY:
        const result = await this.pagination.find(
          { totalViews: 0 },
          filters,
          UserViewAggregatedModel,
          {
            fields: [{ field: 'totalViews', order: SortOrder.DESC }],
          },
          this.pagination.mapPagination(pagination)
        )

        const userIdsAndViews = result.nodes.map((view) => {
          const v = view as UserViewAggregated
          return { userId: v.user as Types.ObjectId, views: v.totalViews }
        })

        const users = await UserModel.find({
          // TODO: we can also remove this part and use only findPaged
          // markedForDeletion: { $exists: false },
          public: { $ne: false },
          role: { $in: [Role.PLAYER, null] },
          _id: { $in: userIdsAndViews.map((item) => item.userId) },
        }).populate([{ path: 'currentTeam', populate: { path: 'organization' } }])

        response.edges = userIdsAndViews
          .filter((item) => !!users.find((user) => user._id.equals(item.userId)))
          .map((item) => {
            const user = users.find((user) => user._id.equals(item.userId))

            return { node: user!, views: item.views }
          })

        response.pageInfo = result.pageInfo

        break
      default:
        throw new Error('Invalid period')
    }

    return response
  }

  async getUserViewsStats(context: ContextType): Promise<UserViewStatsResponse> {
    const currentUserId = new Types.ObjectId(context?.user?.id)

    const currentWeek = moment().utcOffset('+0000').endOf('isoWeek')
    const threeMonthsAgo = currentWeek.clone().subtract(13, 'weeks').startOf('isoWeek')

    const pipelines = [
      {
        $match: {
          user: currentUserId,
          createdAt: { $gte: threeMonthsAgo.toDate(), $lte: currentWeek.toDate() },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            week: { $isoWeek: '$createdAt' },
          },
          weeklyViews: { $sum: 1 },
        },
      },
      {
        $group: {
          _id: null,
          weeklyStats: {
            $push: {
              year: '$_id.year',
              week: '$_id.week',
              views: '$weeklyViews',
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          weeklyStats: 1,
        },
      },
    ]

    const result = await UserViewModel.aggregate(pipelines)

    // go week by week and add 0 values for missing weeks
    const firstWeek = parseInt(threeMonthsAgo.format('W'))
    const lastWeek = parseInt(currentWeek.format('W'))
    const firstYear = parseInt(threeMonthsAgo.format('YYYY'))
    const lastYear = parseInt(currentWeek.format('YYYY'))

    let weeks = []
    const spanAcrossYears = firstYear !== lastYear

    if (!spanAcrossYears) {
      // Weeks are within the same year
      weeks = Array.from({ length: lastWeek - firstWeek + 1 }, (_, i) => i + firstWeek)
    } else {
      // Weeks span across two years
      const totalWeeks = currentWeek.diff(threeMonthsAgo, 'weeks') + 1

      weeks = Array.from({ length: totalWeeks }, (_, i) =>
        threeMonthsAgo.clone().add(i, 'weeks').isoWeek()
      )
    }

    const weeklyStats = weeks.map((week, index) => {
      const currentWeek = moment(threeMonthsAgo).add(index, 'week')
      const endOfWeek = moment(currentWeek).endOf('isoWeek')

      const views =
        result[0]?.weeklyStats.find(
          (stat: { year: number; week: number }) =>
            (stat.year === currentWeek.toDate().getFullYear() && stat.week === week) ||
            // if weeks span across two years, we need to check if the week is the same as the week in the last year
            (spanAcrossYears &&
              stat.year === endOfWeek.toDate().getFullYear() &&
              stat.week === week)
        )?.views ?? 0

      return { from: currentWeek.toDate(), to: endOfWeek.toDate(), views }
    })

    return {
      weeklyStats,
    }
  }

  async getRecommendedUsers(
    loggedUserId?: Types.ObjectId,
    activeUserId?: Types.ObjectId,
    country?: string,
    yearFrom?: number,
    yearTo?: number,
    hasSportDetails: boolean = true,
    hasEliteprospects: boolean = true,
    sampleSize: number = 3,
    excludedIds: Types.ObjectId[] = [],
    inspired?: boolean
  ): Promise<User[]> {
    const pipeline: FilterQuery<User> = {
      searchEnabled: { $ne: false },
      public: { $ne: false },
      avatar: { $ne: null },
      numberOfVideos: { $gt: 1 },

      role: { $in: [Role.PLAYER, null] },
      _id: { $nin: [...excludedIds, loggedUserId, activeUserId].filter(Boolean) },
    }

    if (country) {
      pipeline.country = country
    }

    if (yearFrom && yearTo) {
      pipeline.dateOfBirth = {
        $gte: new Date(`${yearFrom}-01-01T00:00:00.000Z`),
        $lte: new Date(`${yearTo}-12-31T23:59:59.999Z`),
      }
    }

    if (hasSportDetails) {
      pipeline['bio.position'] = { $ne: null }
      pipeline['bio.handedness'] = { $ne: null }
      pipeline.currentTeam = { $ne: null }
    }

    if (hasEliteprospects) {
      pipeline['externalLinks.type'] = ExternalLinkType.ELITE_PROSPECTS
    }

    if (inspired) {
      pipeline.inspired = true
    }

    const aggregationPipeline = []

    // TODO: removing as it is very slow and slowing down a lot database
    // we will rewrite this method once we have Neo4j database, for now
    // it should be fine to include even people I follow

    // if (loggedUserId) {
    //   aggregationPipeline.push(
    //     {
    //       $lookup: {
    //         from: 'usersubscriptions',
    //         localField: '_id',
    //         foreignField: 'user',
    //         as: 'subscriptions',
    //       },
    //     },
    //     {
    //       $match: {
    //         ...pipeline,
    //         'subscriptions.follower': { $ne: loggedUserId },
    //       },
    //     }
    //   )
    // } else {
    aggregationPipeline.push({
      $match: pipeline,
    })

    aggregationPipeline.push({
      $sample: { size: sampleSize },
    })

    const result = await UserModel.aggregate(aggregationPipeline)

    return result
  }

  async deleteUserData(user: User) {
    const session = await mongoose.startSession()

    let followers: UserSubscription[] = []
    let followings: UserSubscription[] = []

    await session.withTransaction(
      async () => {
        // delete user
        await UserModel.deleteOne({ _id: user._id }, { session })

        // delete user stats
        await UserViewAggregatedModel.deleteMany({ user: user._id }, { session })
        await UserViewModel.deleteMany({ user: user._id }, { session })

        const wallVideos = await WallVideoModel.find({ user: user._id }, { _id: 1 }, { session })

        // delete user videos
        await WallVideoModel.deleteMany({ user: user._id }, { session })

        // delete user from getstream
        for (const video of wallVideos) {
          await this.streamFeed.feed(FEED_NAMES.USER, user._id.toString()).removeActivity({
            foreign_id: video.id.toString(),
          })
        }

        // delete user stats
        await UserStatsModel.deleteMany({ user: user._id }, { session })

        // delete getstream follows
        followers = await UserSubscriptionModel.find(
          { user: user._id },
          { follower: 1 },
          { session }
        )

        followings = await UserSubscriptionModel.find(
          { follower: user._id },
          { user: 1 },
          { session }
        )

        // follows
        await UserSubscriptionModel.deleteMany(
          { $or: [{ user: user._id }, { follower: user._id }] },
          { session }
        )

        // notifications
        await NotificationModel.deleteMany({ user: user._id }, { session })

        // boosts
        await BoostModel.deleteMany({ user: user._id }, { session })

        // cleanup s3 files
        const deleteFilesPromises = []

        // avatars
        deleteFilesPromises.push(this.s3Service.remove(user.avatar))
        deleteFilesPromises.push(this.s3Service.remove(user.avatars?.tiny))
        deleteFilesPromises.push(this.s3Service.remove(user.avatars?.small))
        deleteFilesPromises.push(this.s3Service.remove(user.avatars?.medium))
        deleteFilesPromises.push(this.s3Service.remove(user.avatars?.large))
        deleteFilesPromises.push(this.s3Service.remove(user.avatars?.og))

        // clips
        deleteFilesPromises.push(this.s3Service.emptyS3Directory(`images/${user._id.toString()}`))
        deleteFilesPromises.push(this.s3Service.emptyS3Directory(`videos/${user._id.toString()}`))

        await Promise.all(deleteFilesPromises)
      },
      {
        readPreference: 'primary',
      }
    )

    // We need to unfollow users outside of transaction as there is a 60 seconds limit
    for (const follower of followers) {
      const followerId = (follower.follower as Types.ObjectId)?.toString()

      if (followerId) {
        const myTimeLinefeed = this.streamFeed.feed(FEED_NAMES.TIMELINE, followerId)
        await myTimeLinefeed.unfollow(FEED_NAMES.USER, user._id.toString())

        // we need to wait between unfollows, getstream has limit of 250 unfollows per minute
        // adding one second delay between unfollows as this limit is global
        // so we need to have some buffer for mutations
        await new Promise((resolve) => setTimeout(resolve, 1000))
      }
    }

    for (const following of followings) {
      const followingId = (following.user as Types.ObjectId)?.toString()

      if (followingId) {
        const myTimeLinefeed = this.streamFeed.feed(FEED_NAMES.TIMELINE, user._id.toString())
        myTimeLinefeed.unfollow(FEED_NAMES.USER, followingId).catch((err) => {
          this.logger.error(`Failed to unfollow ${followingId} from ${user._id}`, err)
        })

        // we need to wait between unfollows, getstream has limit of 250 unfollows per minute
        // adding one second delay between unfollows as this limit is global
        // so we need to have some buffer for mutations
        await new Promise((resolve) => setTimeout(resolve, 500))
      }
    }
  }
}
