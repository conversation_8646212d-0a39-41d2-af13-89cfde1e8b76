import mongoose, { Model } from 'mongoose'

import { PageInfo, Pagination, SortInput, SortOrder } from '../../../types/general'

type TSimpleSortableValue = string | number | boolean | null
type TSortableValue = Date | mongoose.Types.ObjectId | TSimpleSortableValue

type TSimpleCursorItem = [string, boolean, TSimpleSortableValue]

class CursorItem {
  fieldName: string
  value: TSortableValue
  order: SortOrder
}

class Cursor {
  items: CursorItem[]
}

class ResultConnection<T> {
  pageInfo: PageInfo
  nodesPrev: T[]
  nodesNext: T[]
}

class BaseConnection<T> {
  pageInfo: PageInfo
  nodes: T[]
}

class InputFindInfo {
  baseCursor: Cursor | null
  nextCnt: number
  prevCnt: number
  firstFind: boolean
}

interface AggregateInput<T> {
  pipeline: mongoose.PipelineStage[]
  model: mongoose.Model<T>

  templateSortObject?: Partial<T>
  sorting?: SortInput

  pagination: { first: number; last: number; cursor?: string }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getDeepValue(obj: Record<string, any>, path: string): TSortableValue | undefined {
  if (!obj || !path) {
    return undefined
  }

  const keys = path.split(/\.(.*)/, 2)
  if (!keys?.length || !keys[0]) {
    return undefined
  }

  if (keys.length > 1) {
    return obj[keys[0]] === undefined ? undefined : getDeepValue(obj[keys[0]], keys[1])
  } else {
    return obj[path]
  }
}

function hasDeepKey(obj: object, path: string): boolean {
  return getDeepValue(obj, path) !== undefined
}

class CursorString {
  cursorToString(cursor: Cursor): string | undefined {
    if (!cursor?.items?.length) {
      return undefined
    }

    //create simplified cursor with no complex object items and transform CursorItem int into array [string,bool,any] to decrease size of output cursor string
    const cursorSimple = new Array(cursor.items.length)
    for (let i = 0; i < cursor.items.length; ++i) {
      const itm = cursor.items[i]
      const curSimItem: TSimpleCursorItem = [itm.fieldName, itm.order === SortOrder.ASC, null]

      //transform known object to string
      const val = itm.value
      if (val instanceof mongoose.Types.ObjectId) {
        curSimItem[2] = mongoose.Types.ObjectId.isValid(val) ? val.toString() : null
      } else if (val instanceof Date) {
        curSimItem[2] = val.getTime()
      } else if (typeof val !== 'object' || val === null) {
        curSimItem[2] = val
      }
      cursorSimple[i] = curSimItem
    }

    return Buffer.from(JSON.stringify(cursorSimple), 'utf8').toString('base64')
  }

  stringToCursor<T extends object>(templateSortObject: Partial<T>, input?: string): Cursor | null {
    if (!input) {
      return null
    }
    const cursorSimple: TSimpleCursorItem[] = JSON.parse(
      Buffer.from(input, 'base64').toString('utf8')
    )
    if (!cursorSimple?.length) {
      return null
    }

    //build cursor from simplified cursor and parse values from string
    const cursor = new Cursor()
    cursor.items = new Array(cursorSimple.length)

    for (let i = 0; i < cursorSimple.length; ++i) {
      const itmSim = cursorSimple[i]
      const curItem: CursorItem = {
        fieldName: itmSim[0],
        order: itmSim[1] ? SortOrder.ASC : SortOrder.DESC,
        value: itmSim[2],
      }

      if (hasDeepKey(templateSortObject, curItem.fieldName)) {
        const valTemplate = getDeepValue(templateSortObject, curItem.fieldName)
        if (valTemplate instanceof mongoose.Types.ObjectId) {
          curItem.value = curItem.value ? new mongoose.Types.ObjectId(<string>curItem.value) : null
        } else if (valTemplate instanceof Date) {
          curItem.value = curItem.value ? new Date(<string>curItem.value) : null
        }
      } else if (curItem.fieldName !== '_id') {
        //it seem that cursor has field which is not defined in object, this should not be seen
        //because search required fields are compared against template object at start searching
        throw new Error(
          `invalid cursor, templateSortObject does not contain required cursor field:${curItem.fieldName}, this should not happen`
        )
      }
      cursor.items[i] = curItem
    }

    return cursor
  }
}

export class PaginationRepository {
  private cursorString: CursorString = new CursorString()
  private readonly defaultGetNodesCnt = 10
  private readonly sortFieldLimit = 5 //32 max for mongo

  private buildCursorFromSorting<T extends object>(
    templateSortObject: Partial<T>,
    sorting: SortInput
  ): Cursor {
    const cursor = new Cursor()
    cursor.items = new Array(sorting?.fields?.length ?? 0)

    let cursorHasId = false
    //if exist sorting, build cursor by sorting
    if (sorting?.fields?.length) {
      if (sorting.fields.length > this.sortFieldLimit) {
        throw new Error(`invalid sorting, more than ${this.sortFieldLimit} items`)
      }

      //build cursor items by sorting
      for (let i = 0; i < sorting.fields.length; ++i) {
        const field = sorting.fields[i]
        if (!field.field && (cursorHasId || i + 1 !== sorting.fields.length)) {
          //only last item may not have sortBy value (it will be set as _id) but only if id was not defined
          throw new Error(`invalid sorting, not defined sortBy for item at position: ${i}`)
        }
        const itm = new CursorItem()
        itm.fieldName = field.field || '_id'
        itm.order = field.order
        itm.value = null
        cursor.items[i] = itm
        cursorHasId = cursorHasId || itm.fieldName === '_id'
        if (!hasDeepKey(templateSortObject, itm.fieldName)) {
          throw new Error(
            `invalid sorting, template sort object not contain required sortBy field: ${itm.fieldName}, sorting by this field is not allowed`
          )
        }
      }
    }

    if (!cursorHasId) {
      //no sorting by id, add cursor item sort by id as last value by default sorting asc
      const itm = new CursorItem()
      itm.fieldName = '_id'
      itm.order = SortOrder.ASC
      itm.value = null
      cursor.items.push(itm)
    }

    return cursor
  }

  private validateCursorItems<T extends object>(
    templateSortObject: Partial<T>,
    cursor: Cursor,
    firstFind: boolean
  ): void {
    let cursorHasId = false

    if (!cursor?.items?.length) {
      throw new Error('invalid cursor, no items, this should not be seen')
    }

    if (cursor.items.length > this.sortFieldLimit + 1) {
      //+1 for space for automatic add _id into sort arguments
      throw new Error(`invalid cursor, more than  ${this.sortFieldLimit} items`)
    }

    for (const item of cursor.items) {
      if (!item.fieldName) {
        throw new Error(
          'invalid cursor, all defined fields must have name, this should not be seen'
        )
      }
      if (!item.order) {
        throw new Error(
          'invalid cursor, all defined fields must have sort order, this should not be seen'
        )
      }
      if (item.value === undefined) {
        throw new Error(
          'invalid cursor, value must be set or null, not undefined, this should not be seen'
        )
      }
      if (!firstFind && item.fieldName === '_id' && !item.value) {
        throw new Error('invalid cursor, field id must have value')
      }
      if (item.fieldName === '_id') {
        cursorHasId = true
      }
      if (!hasDeepKey(templateSortObject, item.fieldName) && item.fieldName !== '_id') {
        throw new Error(
          `invalid cursor, templateSortObject does not contain required cursor field:${item.fieldName}, this should not happen`
        )
      }
    }

    if (!cursorHasId) {
      throw new Error('invalid cursor, cursor has no _id, this should not be seen')
    }
  }

  private buildInputFindInfo<T extends object>(
    templateSortObject: Partial<T>,
    sorting: SortInput,
    pagination: { first: number; last?: number; cursor?: string }
  ): InputFindInfo {
    const findInfo = new InputFindInfo()
    findInfo.baseCursor = this.cursorString.stringToCursor<T>(
      templateSortObject,
      pagination?.cursor
    )
    findInfo.nextCnt =
      pagination?.first > 0
        ? pagination.first
        : (pagination?.last ?? 0) > 0
          ? 0
          : this.defaultGetNodesCnt
    findInfo.prevCnt =
      (pagination?.last ?? 0) > 0 && (!findInfo.nextCnt || findInfo.baseCursor?.items?.length)
        ? (pagination.last ?? 0)
        : 0 //cannot make both prev and next search if cursor was not as input parameter

    if (!findInfo.baseCursor?.items?.length) {
      //no cursor so it must be first find, build findInfo cursor
      findInfo.firstFind = true
      findInfo.baseCursor = this.buildCursorFromSorting<T>(templateSortObject, sorting)
    } else {
      findInfo.firstFind = false
    }

    this.validateCursorItems<T>(templateSortObject, findInfo.baseCursor, findInfo.firstFind)

    return findInfo
  }

  private buildSortConditionArray(forward: boolean, cursor: Cursor): [string, 1 | -1][] | null {
    if (!cursor?.items?.length) {
      return null
    }

    const sort: [string, 1 | -1][] = new Array(cursor.items.length) //[string, SortDirection][] is one of sort types in sort.ts node-mongodb-native driver
    for (let i = 0; i < cursor.items.length; ++i) {
      const itm = cursor.items[i]
      const sortDir = (itm.order === SortOrder.ASC) === forward ? 1 : -1
      sort[i] = [itm.fieldName, sortDir]
    }

    return sort
  }

  private buildSortConditionMap(forward: boolean, cursor: Cursor): Map<string, 1 | -1> | null {
    if (!cursor?.items?.length) {
      return null
    }

    const sort = new Map<string, 1 | -1>() //Map<string, 1 | -1> is one of sort types in sort.ts node-mongodb-native driver
    for (const itm of cursor.items) {
      const sortDir = (itm.order === SortOrder.ASC) === forward ? 1 : -1
      sort.set(itm.fieldName, sortDir)
    }

    return sort
  }

  private buildSearchCursorCondition(forward: boolean, cursor: Cursor): object | null {
    if (!cursor?.items?.length) {
      return null
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const cursorConditions: Record<string, any>[] = new Array(cursor.items.length)
    for (let i = 0; i < cursor.items.length; ++i) {
      cursorConditions[i] = {}
    }

    for (let i = 0; i < cursor.items.length; ++i) {
      const itm = cursor.items[i]
      const comparisonOperator = (itm.order === SortOrder.ASC) === forward ? '$gt' : '$lt'

      cursorConditions[i][itm.fieldName] = { [comparisonOperator]: itm.value }

      for (let j = i + 1; j < cursor.items.length; ++j) {
        cursorConditions[j][itm.fieldName] = itm.value
      }
    }

    return cursorConditions.length === 1 ? cursorConditions[0] : { $or: cursorConditions }
  }

  private buildFullSearchConditions(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    cursorConditions: Record<string, any>,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    queryConditions: Record<string, any>
  ): object {
    if (!cursorConditions || !Object.keys(cursorConditions).length) {
      return queryConditions || null
    }

    if (!queryConditions || !Object.keys(queryConditions).length) {
      //only cursorConditions exist
      return cursorConditions
    } else {
      //queryConditions exist, cursorConditions exist
      const returnQuery = { ...queryConditions } //this is shallow copy, we must have copy for next prev conditions, we will not modify any deep values

      if (Array.isArray(queryConditions['$or'])) {
        if (Array.isArray(queryConditions['$and'])) {
          returnQuery['$and'] = [
            ...returnQuery['$and'],
            { $or: returnQuery['$or'] },
            cursorConditions,
          ]
        } else {
          returnQuery['$and'] = [{ $or: returnQuery['$or'] }, cursorConditions]
        }
        delete returnQuery['$or']
      } else if (Array.isArray(queryConditions['$and'])) {
        returnQuery['$and'] = [...returnQuery['$and'], cursorConditions]
      } else {
        //queryConditions not contain keys and/or
        return { $and: [{ ...queryConditions }, cursorConditions] }
      }

      return returnQuery
    }
  }

  private buildCursorFromNode<T extends object>(
    node: T | null,
    baseCursor: Cursor | null
  ): Cursor | null {
    if (!node || !baseCursor?.items?.length) {
      return null
    }

    const cursor = new Cursor()
    cursor.items = new Array(baseCursor.items.length)

    for (let i = 0; i < baseCursor.items.length; ++i) {
      const itm = baseCursor.items[i]
      cursor.items[i] = {
        fieldName: itm.fieldName,
        order: itm.order,
        value: getDeepValue(node, itm.fieldName) ?? null,
      }
      /*
        if node has no item, it means that object in database has undefined required search field,
        it is not critical error, next objects can be found on null value because are ordered by this field,
        so then all next objects (or previous depends on search direction asc/desc) have undefined this field
        but in cursor item value must be defined, so set it to null
      */
    }

    return cursor
  }

  public buildCursorFromNodeWithSort<T extends object>(
    templateObject: Partial<T>,
    node: T | null,
    sorting: SortInput
  ): string | null {
    const baseCursor = this.buildCursorFromSorting(templateObject, sorting)
    const cursor = this.buildCursorFromNode(node, baseCursor)

    if (!cursor) {
      return null
    }

    return this.cursorString.cursorToString(cursor) ?? null
  }

  async find<T extends object>(
    templateSortObject: Partial<T>,
    matchConditions: object,
    mongoModel: Model<T>,
    sorting: SortInput,
    pagination: { first: number; last?: number; cursor?: string },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    populate?: any,
    middleNode?: T | null
  ): Promise<BaseConnection<T>> {
    const findInfo = this.buildInputFindInfo(templateSortObject, sorting, pagination)

    const result = new ResultConnection<T>()
    result.pageInfo = new PageInfo()

    if (findInfo.nextCnt) {
      const completeMatchCondNext = findInfo.firstFind
        ? matchConditions
        : this.buildFullSearchConditions(
            this.buildSearchCursorCondition(true, findInfo.baseCursor!)!,
            matchConditions
          )
      result.nodesNext = await mongoModel
        .find(completeMatchCondNext)
        .sort(this.buildSortConditionArray(true, findInfo.baseCursor!)!)
        .limit(findInfo.nextCnt + 1)
        .populate(populate ?? [])
    } else {
      result.nodesNext = []
    }

    if (findInfo.prevCnt) {
      const completeMatchCondPrev = findInfo.firstFind
        ? matchConditions
        : this.buildFullSearchConditions(
            this.buildSearchCursorCondition(false, findInfo.baseCursor!)!,
            matchConditions
          )

      result.nodesPrev = (await mongoModel
        .find(completeMatchCondPrev)
        .sort(this.buildSortConditionArray(false, findInfo.baseCursor!))
        .limit(findInfo.prevCnt + 1)
        .lean()
        .populate(populate ?? [])) as T[]
    } else {
      result.nodesPrev = []
    }

    result.pageInfo.totalCount = await mongoModel.countDocuments(matchConditions)

    return this.buildPageInfoAndBaseConnection(result, findInfo, middleNode)
  }

  async aggregate<T extends object>(input: AggregateInput<T>): Promise<BaseConnection<T>> {
    const findInfo = this.buildInputFindInfo(
      input.templateSortObject ?? {},
      input.sorting ?? { fields: [] },
      input.pagination
    )

    type TResultFacet = { metadata: { total: number }[]; data: T[] }
    const result = new ResultConnection<T>()
    result.pageInfo = new PageInfo()

    if (findInfo.nextCnt) {
      const facetNext: mongoose.PipelineStage = {
        $facet: { metadata: [{ $count: 'total' }], data: [] },
      }
      if (!findInfo.firstFind) {
        const matchCursorCondNext = findInfo.baseCursor
          ? this.buildSearchCursorCondition(true, findInfo.baseCursor)
          : null
        if (matchCursorCondNext) {
          facetNext.$facet.data.push({ $match: matchCursorCondNext })
        }
      }
      facetNext.$facet.data.push({
        $sort: <Record<string, 1 | -1>>(
          (<unknown>this.buildSortConditionMap(true, findInfo.baseCursor!))
        ),
      }) //$sort in pipeline has defined only interface Record<string, 1 | -1>, so Map must be casted to pass throught (node-mongodb-native driver suport map, see sort.ts), record does not have guaranteed fields order but map does
      facetNext.$facet.data.push({ $limit: findInfo.nextCnt + 1 })
      const resFacetNext = await input.model.aggregate<TResultFacet>([...input.pipeline, facetNext])
      result.nodesNext = resFacetNext?.[0]?.data?.length ? resFacetNext[0].data : []
      result.pageInfo.totalCount = resFacetNext?.[0]?.metadata?.[0]?.total ?? undefined
    } else {
      result.nodesNext = []
    }

    if (findInfo.prevCnt) {
      const facetPrev: mongoose.PipelineStage = {
        $facet: { metadata: [{ $count: 'total' }], data: [] },
      }
      if (!findInfo.firstFind) {
        const matchCursorCondPrev = findInfo.baseCursor
          ? this.buildSearchCursorCondition(false, findInfo.baseCursor)
          : null
        if (matchCursorCondPrev) {
          facetPrev.$facet.data.push({ $match: matchCursorCondPrev })
        }
      }
      facetPrev.$facet.data.push({
        $sort: <Record<string, 1 | -1>>(
          (<unknown>this.buildSortConditionMap(false, findInfo.baseCursor!))
        ),
      })
      facetPrev.$facet.data.push({ $limit: findInfo.prevCnt + 1 })
      const resFacetPrev = await input.model.aggregate<TResultFacet>([...input.pipeline, facetPrev])
      result.nodesPrev = resFacetPrev?.[0]?.data?.length ? resFacetPrev[0].data : []
      result.pageInfo.totalCount =
        result.pageInfo.totalCount ?? resFacetPrev?.[0]?.metadata?.[0].total ?? 0
    } else {
      result.nodesPrev = []
    }

    result.pageInfo.totalCount = result.pageInfo.totalCount ?? 0

    return this.buildPageInfoAndBaseConnection(result, findInfo)
  }

  private buildPageInfoAndBaseConnection<T extends object>(
    result: ResultConnection<T>,
    findInfo: InputFindInfo,
    middleNode?: T | null
  ): BaseConnection<T> {
    result.pageInfo.hasPreviousPage = findInfo.prevCnt
      ? result.nodesPrev.length === findInfo.prevCnt + 1 || //get cnt + 1 mean that there are available another data for next find request
        (result.nodesPrev.length ? false : !findInfo.firstFind && !!result.nodesNext.length) //if there are no data but has cursor, returned startCursor will be one node after current cursor and has previous data
      : !findInfo.firstFind //not firstFind means that find input has valid cursor as parameter, so exist at leats one at previous page
    result.pageInfo.hasNextPage = findInfo.nextCnt
      ? result.nodesNext.length === findInfo.nextCnt + 1 ||
        (result.nodesNext.length ? false : !findInfo.firstFind && !!result.nodesPrev.length)
      : !findInfo.firstFind

    if (result.nodesPrev.length === findInfo.prevCnt + 1) {
      result.nodesPrev.pop()
    }
    result.nodesPrev.reverse()
    if (result.nodesNext.length === findInfo.nextCnt + 1) {
      result.nodesNext.pop()
    }

    let startNode: T | null = null
    let endNode: T | null = null

    if (result.nodesPrev.length) {
      startNode = result.nodesPrev[0]
      if (!result.nodesNext.length) {
        endNode = result.nodesPrev[result.nodesPrev.length - 1]
      }
    }
    if (result.nodesNext.length) {
      endNode = result.nodesNext[result.nodesNext.length - 1]
      if (!result.nodesPrev.length) {
        startNode = result.nodesNext[0]
      }
    }

    const startCursor = this.buildCursorFromNode<T>(startNode, findInfo.baseCursor)
    const endCursor = this.buildCursorFromNode<T>(endNode, findInfo.baseCursor)
    result.pageInfo.startCursor = startCursor
      ? this.cursorString.cursorToString(startCursor)
      : undefined
    result.pageInfo.endCursor = endCursor ? this.cursorString.cursorToString(endCursor) : undefined

    //create baseConnection and join arrays
    const baseCont = new BaseConnection<T>()
    baseCont.pageInfo = result.pageInfo
    baseCont.pageInfo.previousNodesCount = result.nodesPrev.length
    if (middleNode) {
      result.nodesPrev.push(middleNode)
    }
    baseCont.nodes = result.nodesPrev.concat(result.nodesNext)

    if (middleNode && baseCont.nodes[0] === middleNode) {
      baseCont.pageInfo.hasPreviousPage = false
    }

    if (middleNode && baseCont.nodes[baseCont.nodes.length - 1] === middleNode) {
      baseCont.pageInfo.hasNextPage = false
    }

    return baseCont
  }

  public mapPagination(pagination?: Pagination) {
    return {
      cursor: pagination?.after ?? pagination?.before,
      first: pagination?.after || !pagination?.before ? (pagination?.first ?? 10) : 0,
      last: pagination?.before ? (pagination?.first ?? 10) : 0,
    }
  }
}
