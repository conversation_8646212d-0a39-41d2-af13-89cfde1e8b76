import { PipelineStage, Types } from 'mongoose'
import { Service } from 'typedi'

import { Pagination, SimpleSort, SortOrder } from '../../../types/general'
import { AgencyModel } from '../models/agency'

import { PaginationRepository } from './pagination.repository'

@Service()
export default class AgencyRepository {
  private readonly paginationRepository: PaginationRepository = new PaginationRepository()

  async search(
    query: string | null,
    pagination: Pagination,
    matchPipeline: object = {},
    sort?: SimpleSort
  ) {
    const pipeline: PipelineStage[] = []

    if (query && query.length > 0) {
      pipeline.push({
        $search: {
          index: 'agencies_search',
          compound: {
            should: [
              {
                autocomplete: {
                  query: query,
                  path: 'name',
                },
              },
            ],
            minimumShouldMatch: 1,
          },
        },
      })

      pipeline.push({ $addFields: { score: { $meta: 'searchScore' } } })
    }

    if (Object.keys(matchPipeline).length > 0) {
      pipeline.push({ $match: { ...matchPipeline } })
    }

    const sorting = sort
      ? { fields: [sort] }
      : { fields: [{ field: query ? 'score' : '_id', order: SortOrder.DESC }] }

    const templateSortObject = {
      _id: new Types.ObjectId(),
      score: 0,
      stats: {
        views: 0,
        followers: 0,
      },
    }

    return await this.paginationRepository.aggregate({
      templateSortObject,
      pipeline,
      model: AgencyModel,
      sorting,
      pagination: this.paginationRepository.mapPagination(pagination),
    })
  }
}
