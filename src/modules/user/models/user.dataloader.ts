import DataLoader from 'dataloader'

import { User } from './user'
import { UserModel } from './index'

/**
 * Creates a DataLoader for User entities
 * This batches multiple user lookups into a single database query
 */
export const createUserLoader = () => {
  return new DataLoader<string, User | null>((keys) => generateUserLoader(keys))
}

/**
 * Batch loader function for users
 * @param keys Array of user IDs to load
 * @returns Array of users in the same order as the keys
 */
const generateUserLoader = async (keys: readonly string[]) => {
  const users = await UserModel.find({
    _id: { $in: keys },
  })

  // TODO make generic, reusable, model<{_id: string}> to map, map to model by key
  const usersMap = new Map<string, User>()
  users.forEach((user) => {
    const key = user._id.toString()

    usersMap.set(key, user)
  })

  return keys.map((key) => usersMap.get(key) || null)
}
