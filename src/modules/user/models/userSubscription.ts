import DataLoader from 'dataloader'
import mongoose, { Types } from 'mongoose'
import { ObjectType } from 'type-graphql'

import { User } from './user'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

export enum SubscribeProtocol {
  EMAIL = 'EMAIL',
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@Index({ user: 1, endpoint: 1, follower: 1 }, { unique: true })
@Index({ follower: 1 })
@Index({ user: 1 })
@Index({ user: 1, follower: 1 })
export class UserSubscription {
  readonly _id: Types.ObjectId

  @prop()
  public protocol: SubscribeProtocol

  // endpoint is either email or device token
  @prop()
  public endpoint: string

  @prop()
  public endpointArn: string

  @prop({ type: Types.ObjectId, ref: User })
  public user: Ref<User>

  @prop({ type: Types.ObjectId, ref: User })
  public follower: Ref<User>
}

export const UserSubscriptionModel = getModelForClass(UserSubscription, {
  existingMongoose: mongoose,
})
export const createUserSubscriptionLoader = (contextUserId: string) => {
  return new DataLoader<string, UserSubscription | null>((keys) =>
    generateLoader(keys, contextUserId)
  )
}

const generateLoader = async (keys: readonly string[], contextUserId: string) => {
  const subscriptions = await UserSubscriptionModel.find({
    user: { $in: keys },
    follower: contextUserId,
  }).hint({ user: 1, follower: 1 })

  const subscriptionsMap = new Map<string, UserSubscription>()
  subscriptions.forEach((subscription) => {
    const key = subscription.user._id.toString()

    subscriptionsMap.set(key, subscription)
  })

  return keys.map((key) => subscriptionsMap.get(key) || null)
}
