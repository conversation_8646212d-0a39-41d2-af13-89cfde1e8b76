import mongoose, { Types } from 'mongoose'

import { User } from './user'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@Index({ createdAt: 1 })
@Index({ user: 1, fingerprint: 1, createdAt: 1 })
@Index({ userCountry: 1, userYearOfBirth: 1 })
@Index({ userCountry: 1 })
@Index({ userYearOfBirth: 1 })
export class UserView {
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: User })
  public user: Ref<User>

  @prop({ required: false })
  public country?: string

  @prop({ required: false })
  public ipAddress?: string

  @prop({ required: false })
  public fingerprint: string

  @prop({ required: false })
  public userYearOfBirth?: number

  @prop({ required: false })
  public userCountry?: string

  public totalViews: number

  public createdAt: Date
}

export const UserViewModel = getModelForClass(UserView, {
  existingMongoose: mongoose,
})
