import DataLoader from 'dataloader'

import { Agency, AgencyModel } from './agency'

/**
 * Creates a DataLoader for Agency entities
 * This batches multiple agency lookups into a single database query
 */
export const createAgencyLoader = () => {
  return new DataLoader<string, Agency | null>((keys) => generateAgencyLoader(keys))
}

/**
 * Batch loader function for agencies
 * @param keys Array of agency IDs to load
 * @returns Array of agencies in the same order as the keys
 */
const generateAgencyLoader = async (keys: readonly string[]) => {
  const agencies = await AgencyModel.find({ _id: { $in: keys } })

  const agencyMap = new Map<string, Agency>()
  agencies.forEach((agency) => {
    agencyMap.set(agency._id.toString(), agency)
  })

  // Return agencies in the same order as the keys
  return keys.map((key) => {
    return agencyMap.get(key) || null
  })
}
