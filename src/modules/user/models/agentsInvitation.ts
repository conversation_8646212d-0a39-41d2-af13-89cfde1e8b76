import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql'

import { getModelForClass, index, modelOptions, prop } from '@typegoose/typegoose'

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  EXPIRED = 'expired',
}

registerEnumType(InvitationStatus, {
  name: 'InvitationStatus',
  description: 'The status of an invitation',
})

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ agencyId: 1 })
@index({ token: 1 })
export class AgentsInvitation {
  @prop()
  @Field(() => ID)
  id: Types.ObjectId

  @prop()
  @Field()
  email: string

  @prop()
  @Field(() => ID)
  agencyId: Types.ObjectId

  @prop()
  @Field()
  token: string

  @prop()
  @Field((_type) => InvitationStatus)
  status: InvitationStatus

  @prop()
  @Field()
  expiresAt: Date

  @prop()
  public createdAt: Date
}

export const AgentsInvitationModel = getModelForClass(AgentsInvitation, {
  existingMongoose: mongoose,
})
