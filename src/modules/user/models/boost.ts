import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql'

import { User } from './user'

import { getModelForClass, index, modelOptions, prop, Ref } from '@typegoose/typegoose'

export enum BoostState {
  CREATED = 'CREATED',
  WAITING_FOR_CONFIRMATION = 'WAITING_FOR_CONFIRMATION',
  CONFIRMED = 'CONFIRMED',
  FAILED = 'FAILED',
}

registerEnumType(BoostState, {
  name: 'BoostState',
  description: 'The boost state type',
})

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ paymentIntentId: 1 }, { unique: true })
@index({ user: 1 })
export class Boost {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: User })
  @Field(() => User, { nullable: true })
  public user: Ref<User>

  @prop()
  public paymentIntentId: string

  @prop()
  @Field({ nullable: false })
  public amount: number

  @prop()
  @Field({ nullable: false })
  public currency: string

  @prop()
  @Field({ nullable: true })
  public name?: string

  @prop()
  @Field({ nullable: true })
  public message?: string

  @prop()
  public email: string

  @prop({ nullable: false })
  @Field({ nullable: false })
  public state: BoostState

  @Field({ nullable: false })
  public createdAt: Date
}

export const BoostModel = getModelForClass(Boost, {
  existingMongoose: mongoose,
})
