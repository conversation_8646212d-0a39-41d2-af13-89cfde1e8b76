import mongoose, { Types } from 'mongoose'
import { Field, ID, ObjectType } from 'type-graphql'

import { PushNotificationType } from '../../../services/pushNotification'

import { User } from './user'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@Index({ user: 1 })
@Index({ key: 1 }, { unique: true })
export class Notification {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop()
  @Field({ nullable: false })
  public title: string

  @prop()
  @Field({ nullable: false })
  public body: string

  @prop()
  @Field({ nullable: true })
  public destinationPath: string

  @prop({ type: Types.ObjectId, ref: User })
  @Field((_type) => User, { nullable: true })
  public user: Ref<User>

  @prop({ default: false })
  @Field({ nullable: false })
  public sent: boolean

  @prop({ enum: PushNotificationType })
  @Field({ nullable: false })
  public type: PushNotificationType

  // unique key for the notification
  @prop()
  public key: string
}

export const NotificationModel = getModelForClass(Notification, {
  existingMongoose: mongoose,
})
