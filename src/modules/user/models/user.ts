import DataLoader from 'dataloader'
import latinize from 'latinize'
import { Types } from 'mongoose'
import { Authorized, Field, ID, Int, ObjectType, registerEnumType } from 'type-graphql'

import { CacheControl } from '../../../middlewares/cacheControl'
import { SystemRole } from '../../../types/general'
import { Team } from '../../stats/models/team'
import { ExternalConnection } from '../../stats/types/shared'

import { AggregatedStats } from './user/aggregatedStats'
import { Agency } from './agency'
import { WallVideo } from './wallVideo'
import { UserModel } from '.'

import { index, modelOptions, pre, prop, Ref, searchIndex } from '@typegoose/typegoose'

export enum CreationSource {
  AccountDeletion = 'AccountDeletion',
  Scraper = 'Scraper',
  AdminSyncAgency = 'AdminSyncAgency',
}

@ObjectType()
class PhoneNumber {
  @prop()
  @Field({ nullable: false })
  public countryPrefix: string

  @prop()
  @Field({ nullable: false })
  public number: string
}

export enum ContactType {
  PLAYER = 'PLAYER',
  FATHER = 'FATHER',
  MOTHER = 'MOTHER',
  AGENT = 'AGENT',
  OTHER = 'OTHER',
}

registerEnumType(ContactType, {
  name: 'ContactType',
})

@ObjectType()
export class Contact {
  @prop()
  @Field({ nullable: true })
  public name: string

  @prop()
  @Field({ nullable: true })
  public firstname: string

  @prop()
  @Field({ nullable: true })
  public lastname: string

  @prop()
  @Field({ nullable: false })
  @Authorized(SystemRole.PERSONAL)
  public email: string

  @prop()
  @Field((_type) => PhoneNumber, { nullable: true })
  @Authorized(SystemRole.PERSONAL)
  public phoneNumber: PhoneNumber

  @prop()
  @Field((_type) => ContactType, { nullable: true })
  public type: ContactType
}

export enum ExternalLinkType {
  INSTAGRAM = 'INSTAGRAM',
  ELITE_PROSPECTS = 'ELITE_PROSPECTS',
  X = 'X',
  TIKTOK = 'TIKTOK',
  LINKEDIN = 'LINKEDIN',
  CUSTOM = 'CUSTOM',
}

registerEnumType(ExternalLinkType, {
  name: 'ExternalLinkType',
})

@ObjectType()
class ExternalLink {
  @Field((_type) => ID, { name: 'id' })
  readonly _id?: Types.ObjectId

  @prop()
  @Field({ nullable: false })
  public name: string

  @prop()
  @Field({ nullable: false })
  public url: string

  @prop()
  @Field((_type) => ExternalLinkType, { nullable: true })
  public type: ExternalLinkType

  @prop()
  @Field({ nullable: true })
  public scrapable?: boolean
}

export class VerificationCode {
  @prop()
  public code: string

  @prop()
  public expirationDate: Date
}

@ObjectType()
class BoostGoal {
  @prop()
  @Field({ nullable: false })
  public currency: string

  @prop()
  @Field({ nullable: false })
  public goalAmount: number

  @prop()
  @Field({ nullable: true })
  public currentAmount?: number
}

export enum BioPosition {
  CENTER = 'CENTER',
  LEFT_WING = 'LEFT_WING',
  RIGHT_WING = 'RIGHT_WING',
  LEFT_DEFENSIVE = 'LEFT_DEFENSEMAN',
  RIGHT_DEFENSIVE = 'RIGHT_DEFENSEMAN',
  DEFENDER = 'DEFENDER',
  GOALTENDER = 'GOALTENDER',
}

registerEnumType(BioPosition, {
  name: 'BioPosition',
})

export enum BioHandedness {
  LEFT = 'LEFT',
  RIGHT = 'RIGHT',
}

registerEnumType(BioHandedness, {
  name: 'BioHandedness',
})

export enum BioGender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  PREFER_NOT_TO_SAY = 'PREFER_NOT_TO_SAY',
}

registerEnumType(BioGender, {
  name: 'BioGender',
})

export enum BioPlayerType {
  ENFORCER = 'ENFORCER',
  GRINDER = 'GRINDER',
  PLAYMAKER = 'PLAYMAKER',
  POWER_FORWARD = 'POWER_FORWARD',
  SNIPER = 'SNIPER',
  TWO_WAY = 'TWO_WAY',
  DEFENSIVE = 'DEFENSIVE',
  OFFENSIVE = 'OFFENSIVE',

  // goalies
  BUTTERFLY = 'BUTTERFLY',
  HYBRID = 'HYBRID',
  STANDUP = 'STANDUP',
}

registerEnumType(BioPlayerType, {
  name: 'BioPlayerType',
})

export enum BioSchoolType {
  ELEMENTARY_SCHOOL = 'ELEMENTARY_SCHOOL',
  MIDDLE_SCHOOL = 'MIDDLE_SCHOOL',
  HIGH_SCHOOL = 'HIGH_SCHOOL',
  HOMESCHOOLING = 'HOMESCHOOLING',
  MILITARY_ACADEMY = 'MILITARY_ACADEMY',
  COLLEGE = 'COLLEGE',
  UNIVERSITY = 'UNIVERSITY',
  GRADUATE_SCHOOL = 'GRADUATE_SCHOOL',
  PROFESSIONAL_SCHOOL = 'PROFESSIONAL_SCHOOL',
  OTHER = 'OTHER',
}

registerEnumType(BioSchoolType, {
  name: 'BioSchoolType',
})

export enum Role {
  PLAYER = 'PLAYER',
  SHADOW_PLAYER = 'SHADOW_PLAYER',
  PARENT = 'PARENT',
  SPORTS_PROFESSIONAL = 'SPORTS_PROFESSIONAL',
  SPORTS_FAN = 'SPORTS_FAN',
}

registerEnumType(Role, {
  name: 'Role',
})

export enum SubRole {
  AGENT = 'AGENT',
  COACH = 'COACH',
  SCOUT = 'SCOUT',
}

registerEnumType(SubRole, {
  name: 'SubRole',
})

export enum SubRoleSpecificationType {
  // coaching
  HeadCoach = 'HeadCoach',
  AssistantCoach = 'AssistantCoach',
  GoalieCoach = 'GoalieCoach',
  ConditioningCoach = 'ConditioningCoach',
  SkillsCoach = 'SkillsCoach',
  VideoCoach = 'VideoCoach',

  // management
  Scout = 'Scout',
  HeadScout = 'HeadScout',
  HockeyOperations = 'HockeyOperations',
  TeamManager = 'TeamManager',
  PlayerDevelopmentManager = 'PlayerDevelopmentManager',
  GeneralManager = 'GeneralManager',
  AssistantGm = 'AssistantGm',

  // other
  AthleticTrainer = 'AthleticTrainer',
  EquipmentManager = 'EquipmentManager',
  MedicalStaff = 'MedicalStaff',
  Other = 'Other',
}

registerEnumType(SubRoleSpecificationType, { name: 'SubRoleSpecificationType' })

export enum Platform {
  IOS = 'IOS',
  ANDROID = 'ANDROID',
}

registerEnumType(Platform, {
  name: 'Platform',
})

@ObjectType()
export class Device {
  @prop()
  @Field({ nullable: false })
  public token: string

  @prop()
  @Field((_type) => Platform, { nullable: false })
  public platform: Platform

  @prop()
  @Field({ nullable: true })
  public arn?: string
}

@ObjectType()
export class Height {
  @prop()
  @Field((_type) => Int, { nullable: true })
  public feet: number

  @prop()
  @Field((_type) => Int, { nullable: true })
  public inches: number

  @prop()
  @Field((_type) => Int, { nullable: true })
  public centimeters: number
}

@ObjectType()
export class Weight {
  @prop()
  @Field((_type) => Int, { nullable: true })
  public pounds: number

  @prop()
  @Field((_type) => Int, { nullable: true })
  public kilograms: number
}

@ObjectType()
export class Agent {
  @prop()
  @Field({ nullable: true })
  public name: string

  @prop()
  @Field({ nullable: true })
  public firstname: string

  @prop()
  @Field({ nullable: true })
  public lastname: string

  @prop({ type: Types.ObjectId, ref: Agency })
  @Field((_type) => Agency, { nullable: true })
  public agency?: Ref<Agency>

  @prop()
  @Field({ nullable: false })
  @Authorized(SystemRole.PERSONAL)
  public email: string

  @prop()
  @Field((_type) => PhoneNumber, { nullable: true })
  @Authorized(SystemRole.PERSONAL)
  public phoneNumber: PhoneNumber
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class UserBio {
  @prop()
  @Field((_type) => BioPosition, { nullable: true })
  public position?: BioPosition

  @prop()
  @Field((_type) => BioHandedness, { nullable: true })
  public handedness?: BioHandedness

  @prop()
  @Field((_type) => BioPlayerType, { nullable: true })
  public playerType?: BioPlayerType

  @prop()
  @Field((_type) => BioSchoolType, { nullable: true })
  public schoolType?: BioSchoolType

  @prop({ type: Types.ObjectId, ref: Agency })
  @Field((_type) => Agency, { nullable: true })
  public agency?: Ref<Agency>

  @prop({ type: Types.ObjectId, ref: Agency })
  @Field((_type) => Agency, { nullable: true })
  public agencyUnverified?: Ref<Agency>

  @prop()
  @Field((_type) => BioGender, { nullable: true })
  public gender?: BioGender

  @prop()
  @Field({ nullable: true })
  public height?: Height

  @prop()
  @Field({ nullable: true })
  public weight?: Weight

  @prop()
  @Field({ nullable: true })
  public birthplace?: string

  @prop()
  @Field({ nullable: true })
  public nameOfSchool?: string

  @prop()
  @Field({ nullable: true })
  public intro?: string

  @prop()
  @Field((_type) => Int, { nullable: true })
  public graduationYear?: number

  @prop()
  @Field({ nullable: true })
  public cumulativeGPA?: number

  @prop()
  @Field({ nullable: true })
  public coreGPA?: number

  @prop()
  @Field({ nullable: true })
  public unweightedGPA?: number

  @prop()
  @Field({ nullable: true })
  public weightedGPA?: number

  @prop()
  @Field((_type) => Int, { nullable: true })
  public satScore?: number

  @prop()
  @Field((_type) => Int, { nullable: true })
  public actScore?: number

  @prop()
  @Field((_type) => Int, { nullable: true })
  public toeflScore?: number

  @prop()
  @Field({ nullable: true })
  public ncaaEligibility?: boolean

  @prop()
  @Field({ nullable: true })
  public naiaEligibility?: boolean
}

@ObjectType()
export class UserBasicStats {
  @prop()
  @Field((_type) => Int, { nullable: false })
  public followers: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public boosts: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public views: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public followings: number
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class Avatars {
  @prop()
  @Field()
  public tiny: string

  @prop()
  @Field()
  public small: string

  @prop()
  @Field()
  public medium: string

  @prop()
  @Field()
  public large: string

  @prop()
  @Field({ nullable: true })
  public og?: string
}

@ObjectType()
class StripeAccountRequirementError {
  @prop()
  @Field({ nullable: false })
  public requirement: string

  @prop()
  @Field({ nullable: false })
  public code: string

  @prop()
  @Field({ nullable: false })
  public reason: string
}

@ObjectType()
export class SubRoleSpecification {
  @prop({ type: Types.ObjectId, ref: Team })
  @Field((_type) => Team, { nullable: false })
  public team: Ref<Team>

  @prop()
  @Field({ nullable: false })
  public type: SubRoleSpecificationType

  @prop()
  @Field({ nullable: true })
  public current?: boolean

  @prop()
  @Field({ nullable: true })
  public season?: string
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ agentV2: 1, public: 1 })
@index({ email: 1 }, { unique: true })
@index({ email: 1, appleUserId: 1 })
@index({ agent: 1 })
@index({ 'contact.email': 1 })
@index({ 'bio.agency': 1 })
@index({ 'userStats.views': 1 })
@index({ slug: 1 }) // { unique: true } TODO: probably need to recreate db again, or fill in slugs for all users
@index({ shadowSlug: 1 })
@index({
  statsSyncedAt: 1,
  'externalLinks.type': 1,
  'externalLinks.scrapable': 1,
})
@index({ country: 1 })
@index({ dateOfBirth: 1 })
@index({ country: 1, dateOfBirth: 1 })
@index({ role: 1 })
@index({ subrole: 1 })
@index({ currentTeam: 1, _id: -1, public: 1 })
@index({
  'externalLinks.type': 1,
  role: 1,
  _id: 1,
  numberOfVideos: 1,
  public: 1,
  searchEnabled: 1,
  avatar: 1,
  'bio.handedness': 1,
  'bio.position': 1,
  currentTeam: 1,
})
@index({
  'userStats.views': -1,
  _id: -1,
})
@index({
  'externalLinks.type': 1,
  statsSyncedAt: 1,
})
@index({
  'externalLinks.scrapable': 1,
  statsSyncedAt: 1,
})
@index({
  role: 1,
  'userStats.views': -1,
  _id: 1,
  public: 1,
})
@index({ 'externalConnections.crawlerDomainConfig': 1, 'externalConnections.externalId': 1 })
@index({
  country: 1,
  'externalLinks.type': 1,
  role: 1,
  _id: 1,
  avatar: 1,
  'bio.handedness': 1,
  'bio.position': 1,
  currentTeam: 1,
  dateOfBirth: 1,
  numberOfVideos: 1,
  public: 1,
  searchEnabled: 1,
})
@index({ inspired: 1, public: 1 })
@index({ nonUniqueSlug: 1, role: 1 })
@index({ numberOfVideos: 1 })
@index({ 'highlights.0': 1, public: 1 })
@index({ markedForDeletion: 1 })
@searchIndex({
  name: 'search',
  definition: {
    mappings: {
      fields: {
        name: {
          tokenization: 'edgeGram',
          minGrams: 2,
          maxGrams: 15,
          type: 'autocomplete',
        },
      },
    },
  },
})
@pre<User>('save', function () {
  if (this.firstname || this.lastname) {
    this.name = `${this.firstname} ${this.lastname}`
  }
})
@pre<User>(['updateOne', 'findOneAndUpdate'], function () {
  // FIXME: seems like wrong type from typegoose, any better way?
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const document = this as unknown as { getUpdate: () => any }
  if (document.getUpdate()['$set'].firstname || document.getUpdate()['$set'].lastname) {
    document.getUpdate()['$set'].name =
      `${document.getUpdate()['$set'].firstname} ${document.getUpdate()['$set'].lastname}`
  }
})
@CacheControl({ inheritMaxAge: true })
export class User {
  @Field((_type) => ID, { name: 'id' })
  public _id: Types.ObjectId

  @prop()
  @Field({ nullable: true })
  public lastActiveAt?: Date

  @prop()
  @Field({ nullable: true })
  public name: string

  @prop()
  @Field({ nullable: true, name: 'originalFirstname' })
  public firstname?: string

  @Field({ nullable: true, name: 'firstname' })
  get _firstname(): string {
    const user = this.getUser()

    return latinize(user?.firstname ?? '')
  }

  set _firstname(value: string) {}

  @prop()
  @Field({ nullable: true, name: 'originalLastname' })
  public lastname?: string

  @Field({ nullable: true, name: 'lastname' })
  get _lastname(): string {
    const user = this.getUser()

    return latinize(user?.lastname ?? '')
  }

  @prop()
  @Field({ nullable: false })
  @Authorized(SystemRole.PERSONAL)
  public email: string

  @prop()
  @Field({ nullable: true })
  public slug?: string

  @prop()
  public nonUniqueSlug?: string

  @prop()
  @Field({ nullable: true })
  public shadowSlug?: string

  @prop()
  @Field({ nullable: true })
  public dateOfBirth?: Date

  @prop({ type: [Number] })
  @Field(() => [Int], { nullable: true })
  public possibleYearsOfBirth?: number[]

  @prop()
  @Field({ nullable: true })
  public country?: string

  @prop()
  @Field((_type) => Contact, { nullable: true })
  public contact?: Contact

  @prop()
  @Field((_type) => Agent, { nullable: true })
  public agent?: Agent

  @prop({ type: Types.ObjectId, ref: User })
  @Field((_type) => User, { nullable: true })
  public agentV2?: Ref<User>

  @prop()
  @Field({ nullable: true })
  @Authorized(SystemRole.PERSONAL)
  public phoneNumber?: PhoneNumber

  @prop()
  @Field((_type) => UserBio, { nullable: true })
  public bio?: UserBio

  @prop()
  @Field((_) => String, { nullable: true })
  public avatar?: string

  @prop()
  @Field((_) => Avatars, { nullable: true })
  public avatars: Avatars

  @prop()
  public avatarUnconfirmed?: string

  @prop({ type: [ExternalLink] })
  @Field((_type) => [ExternalLink], { nullable: true })
  public externalLinks?: ExternalLink[]

  @prop()
  @Field({ nullable: true })
  public boostMessage?: string

  @prop({ type: Types.ObjectId, ref: Team })
  @Field((_type) => Team, { nullable: true })
  public currentTeam?: Ref<Team>

  @prop({ type: Types.ObjectId, ref: Team })
  @Field({ nullable: true })
  public epCurrentTeam?: Types.ObjectId

  @prop()
  public verificationCode?: VerificationCode

  @prop()
  @Field({ nullable: true })
  public stripeAccountId?: string

  @prop()
  @Field({ nullable: true })
  @Authorized(SystemRole.PERSONAL)
  public stripeExternalAccountId?: string

  @prop()
  @Field(() => BoostGoal, { nullable: true })
  public boost?: BoostGoal

  @prop()
  @Field({ nullable: true })
  @Authorized(SystemRole.ADMIN)
  public recommended: boolean

  @prop()
  @Field({ nullable: true })
  public inspired: boolean

  @prop()
  @Field({ nullable: true })
  @Authorized(SystemRole.ADMIN)
  public moreSelected: boolean

  @prop()
  @Field((_type) => Role, { nullable: true })
  public role?: Role

  @prop()
  @Field((_type) => SubRole, { nullable: true })
  public subrole?: SubRole

  @prop({ type: () => SubRoleSpecification })
  @Field((_type) => [SubRoleSpecification], { nullable: true })
  public subroleSpecifications?: SubRoleSpecification[]

  @prop({ default: { views: 0, followers: 0, boosts: 0, followings: 0 } })
  @Field((_type) => UserBasicStats, { nullable: true })
  public userStats?: UserBasicStats

  @prop()
  @Field({ nullable: true })
  @Authorized(SystemRole.ADMIN)
  public public: boolean

  @prop()
  @Field({ nullable: true })
  @Authorized(SystemRole.ADMIN)
  public searchEnabled: boolean

  @prop({ default: false })
  @Field({ nullable: false, defaultValue: false })
  public paymentsEnabled: boolean

  @Field({ nullable: false })
  get isAdult(): boolean {
    const user = this.getUser()

    const birthDate: Date = new Date(user.dateOfBirth ?? new Date())
    const today: Date = new Date()
    const age: number = today.getFullYear() - birthDate.getFullYear()
    const monthDiff: number = today.getMonth() - birthDate.getMonth()

    // Check if the birth month is later in the year or if it's the birth month and the day is later
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1 >= 18 // Subtract 1 from age if the birthday hasn't occurred yet this year
    } else {
      return age >= 18
    }
  }

  @prop({ type: [Device] })
  @Field(() => [Device], { nullable: true })
  public registeredDevices?: Device[]

  @prop()
  @Field({ nullable: true })
  public pushEnabled?: boolean

  @prop({ type: WallVideo })
  @Field((_) => [WallVideo], { nullable: true })
  public highlights: [WallVideo]

  @prop()
  @Field({ nullable: true })
  public numberOfVideos?: number

  @prop()
  public statsAdded?: boolean

  @prop()
  public subscribeTopicArn?: string

  @prop()
  public markedForDeletion?: boolean

  @prop()
  public markedForDeletionEmail?: string

  @prop()
  public statsSyncedAt?: Date

  @prop()
  public appleUserId?: string

  @prop()
  @Field()
  public createdAt: Date

  @prop()
  public lastNotificationSentAt?: Date

  @prop()
  public statsSyncFailedAt?: Date

  @prop()
  @Field({ nullable: true })
  public premium: boolean

  @prop()
  @Field({ nullable: true })
  public premiumSince?: Date

  // number of views from EliteProspects (mainly for shadow users)
  @prop()
  @Field((_type) => Int, { nullable: true })
  public epViews?: number

  // Stripe connected account verification related fields
  @prop()
  @Field({ nullable: true, defaultValue: false })
  public verificationNeeded?: boolean

  @prop()
  @Field({ nullable: true, defaultValue: false })
  public verificationPending?: boolean

  @prop({ type: [StripeAccountRequirementError] })
  @Field(() => [StripeAccountRequirementError], { nullable: true })
  public accountUpdateErrors?: StripeAccountRequirementError[]

  @prop()
  @Field({ nullable: true })
  public stats?: AggregatedStats

  @prop({ type: [ExternalConnection] })
  @Field(() => [ExternalConnection], { nullable: true })
  public externalConnections: ExternalConnection[]

  @prop({ type: String, enum: CreationSource })
  public creationSource?: CreationSource

  @prop()
  @Field({ nullable: true })
  public experimentGroup?: number

  @prop({ type: Types.ObjectId, ref: User })
  @Field((_type) => [User], { nullable: true })
  public familyMembers: Ref<User>[]

  @prop({ type: Types.ObjectId, ref: User })
  @Field((_type) => [User], { nullable: true })
  public pendingFamilyMembers: Ref<User>[]

  public getUser(): User {
    if ((this as User)._id !== undefined) {
      return this as User
    } else {
      return (this as unknown as { _doc: User })._doc
    }
  }
}

export const userTemplateSortObject: Partial<User> = {
  _id: new Types.ObjectId(),
  role: Role.PLAYER,
  epViews: 0,
  createdAt: new Date(),
  userStats: {
    views: 0,
    followers: 0,
    boosts: 0,
    followings: 0,
  },
}

/**
 * Creates a DataLoader for User entities
 * This batches multiple user lookups into a single database query
 */
export const createUserLoader = () => {
  return new DataLoader<string, User | null>((keys) => generateLoader(keys))
}

/**
 * Batch loader function for users
 * @param keys Array of user IDs to load
 * @returns Array of users in the same order as the keys
 */
const generateLoader = async (keys: readonly string[]) => {
  const users = await UserModel.find({
    _id: { $in: keys },
  })

  // TODO make generic, reusable, model<{_id: string}> to map, map to model by key
  const usersMap = new Map<string, User>()
  users.forEach((user) => {
    const key = user._id.toString()

    usersMap.set(key, user)
  })

  return keys.map((key) => usersMap.get(key) || null)
}
