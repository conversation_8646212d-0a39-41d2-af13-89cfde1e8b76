import config from 'config'
import mongoose, { Types } from 'mongoose'
import { Field, ID, Int, ObjectType, registerEnumType } from 'type-graphql'

import { CacheControl } from '../../../middlewares/cacheControl'

import { getModelForClass, index, modelOptions, prop, Ref, searchIndex } from '@typegoose/typegoose'

export enum SportCategory {
  HOCKEY = 'HOCKEY',
  VOLLEYBALL = 'VOLLEYBALL',
  BASKETBALL = 'BASKETBALL',
  FOOTBALL = 'FOOTBALL',
  BASEBALL = 'BASEBALL',
  SOCCER = 'SOCCER',
}

registerEnumType(SportCategory, {
  name: 'SportCategory',
})

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class AgencyStats {
  @prop()
  @Field((_type) => Int, { nullable: false })
  public followers: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public views: number
}

@ObjectType()
export class AgencyDrafts {
  @prop()
  @Field((_type) => Int, { nullable: false })
  public NHL: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public NCAA: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public CHL: number

  @prop()
  @Field((_type) => Int, { nullable: false })
  public USHL: number
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@index({ checked: 1 })
@index({ name: 1 }, { unique: true })
@index({ slug: 1 })
@index({ integrated: 1 })
@index({ externalNumberOfClients: 1 })
@index({ instagramFollowers: 1 })
@index({ categories: 1 })
@searchIndex({
  name: 'agencies_search',
  definition: {
    mappings: {
      dynamic: false,
      fields: {
        name: {
          type: 'autocomplete',
        },
      },
    },
  },
})
@CacheControl({ inheritMaxAge: true })
export class Agency {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop()
  @Field({ nullable: false })
  public name: string

  @prop()
  @Field({ nullable: true })
  public slug?: string

  @prop()
  @Field({ nullable: true })
  public country?: string

  @prop({ default: 0 })
  @Field({ nullable: true })
  public clientsCount?: number

  @prop({ default: 0 })
  @Field({ nullable: true })
  public shadowClientsCount?: number

  @prop({ default: 0 })
  @Field({ nullable: true })
  public agentsCount?: number

  @prop()
  @Field((_) => String, { nullable: true })
  public logo?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public ogLogo?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public website?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public epUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public email?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public phone?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public address?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public instagramUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public facebookUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public twitterUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public linkedinUrl?: string

  @prop()
  @Field((_) => String, { nullable: true })
  public tiktokUrl?: string

  @prop()
  @Field((_type) => Int, { nullable: true })
  public externalNumberOfClients: number

  @prop()
  @Field((_type) => Int, { nullable: true })
  public instagramFollowers: number

  @prop()
  @Field((_type) => AgencyStats, { nullable: true })
  public stats?: AgencyStats

  @prop()
  public logoUnconfirmed?: string

  @prop()
  @Field({ nullable: true })
  public checked?: boolean

  @prop()
  @Field({ nullable: true })
  public integrated?: boolean

  @prop({ type: [String], enum: SportCategory })
  @Field((_type) => [SportCategory], { nullable: true })
  public categories?: SportCategory[]

  @prop()
  @Field((_type) => Int, { nullable: true })
  public founded?: number

  @prop({ type: AgencyDrafts })
  @Field((_type) => AgencyDrafts, { nullable: true })
  public drafts?: AgencyDrafts

  @prop({ type: () => [String], default: [] })
  @Field((_type) => [String], { nullable: true })
  public licences: string[]

  @prop({ type: [Types.ObjectId], ref: Agency })
  @Field((_type) => [Agency], { nullable: true })
  public affiliateAgencies?: Ref<Agency>[]
}

export const AgencyModel = getModelForClass(Agency, {
  existingMongoose: mongoose,
  schemaOptions: {
    autoSearchIndex: config.mongo.autoSearchIndex,
  },
})
