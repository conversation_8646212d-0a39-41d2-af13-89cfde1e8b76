import mongoose, { Types } from 'mongoose'
import { ObjectType } from 'type-graphql'

import { Game } from '../../stats/models/game/game'

import { User } from './user'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@Index({ player: 1, game: 1, userLiked: 1 }, { unique: true })
@Index({ userLiked: 1 })
@Index({ player: 1, game: 1 })
export class PlayerPerformanceLike {
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: User })
  public player: Ref<User>

  @prop({ type: Types.ObjectId, ref: Game })
  public game: Ref<Game>

  @prop({ type: Types.ObjectId, ref: User })
  public userLiked: Ref<User>
}

export const PlayerPerformanceLikeModel = getModelForClass(PlayerPerformanceLike, {
  existingMongoose: mongoose,
})
