import mongoose, { Types } from 'mongoose'

import { Agency } from './agency'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@Index({ createdAt: 1 })
@Index({ agency: 1, fingerprint: 1, createdAt: 1 })
export class AgencyView {
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: Agency })
  public agency: Ref<Agency>

  @prop({ required: false })
  public country?: string

  @prop({ required: false })
  public ipAddress?: string

  @prop({ required: false })
  public fingerprint: string

  public totalViews: number
}

export const AgencyViewModel = getModelForClass(AgencyView, {
  existingMongoose: mongoose,
})
