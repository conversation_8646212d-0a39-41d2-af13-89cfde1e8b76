import { Types } from 'mongoose'
import mongooseDelete from 'mongoose-delete-ts'
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql'

import { CacheControl } from '../../../middlewares/cacheControl'

import { User } from './user'

import { index, modelOptions, plugin, prop, Ref } from '@typegoose/typegoose'

export enum WallVideoType {
  HIGHLIGHT = 'HIGHLIGHT',
  INTERVIEW = 'INTERVIEW',
  TRAINING = 'TRAINING',
  DEFENSIVE = 'DEFENSIVE',
  OFFENSIVE = 'OFFENSIVE',
  PUCK_HANDLING = 'PUCK_HANDLING',
  GAME_SAVE = 'GAME_SAVE',
  OTHER = 'OTHER',
}

registerEnumType(WallVideoType, {
  name: 'WallVideoType',
  description: 'The wall video type',
})

export enum WallVideoTags {
  CORRECT = 'CORRECT',
  MISSING_IN_BEGINNING = 'MISSING_IN_BEGINNING',
  MISSING_AT_END = 'MISSING_AT_END',
  MISSING_IN_REPLAY = 'MISSING_IN_REPLAY',
  NOT_ACCURATE_POSITION = 'NOT_ACCURATE_POSITION',
  INCORRECT_OBJECT = 'INCORRECT_OBJECT',
  DIDNT_SEGMENT = 'DIDNT_SEGMENT',
  SEGMENTED_MULTIPLE_PLAYERS = 'SEGMENTED_MULTIPLE_PLAYERS',
}

registerEnumType(WallVideoTags, {
  name: 'WallVideoTags',
  description: 'The wall video tags',
})

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class WallVideoSegmentation {
  @prop()
  @Field({ nullable: true })
  public path: string

  @prop()
  @Field({ nullable: true })
  public videoPath?: string

  @prop()
  @Field({ nullable: true })
  public videoHlsPath?: string

  @prop()
  @Field({ nullable: true })
  public finished: boolean

  @prop()
  public selectionX: number

  @prop()
  public selectionY: number

  @prop()
  public frame: number

  @Field({ nullable: true })
  public createdAt: Date
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class WallVideoRepeat {
  @prop()
  @Field({ nullable: false })
  public start: number

  @prop()
  @Field({ nullable: false })
  public end: number
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@plugin(mongooseDelete, {
  deletedAt: true,
  deletedBy: true,
  indexFields: ['deleted'],
})
@ObjectType()
@index({ type: 1 })
@index({ user: 1 })
@index({ 'segmentation.finished': 1 })
@index({ tags: 1 })
@index({ hidden: 1 })
@index({
  deleted: 1,
  uploadFinished: 1,
  createdAt: -1,
  _id: 1,
  hidden: 1,
  type: 1,
})
@CacheControl({ inheritMaxAge: true })
export class WallVideo {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @prop()
  @Field((_) => WallVideoType, { nullable: false })
  public type: WallVideoType

  @prop()
  @Field({ nullable: false })
  public title: string

  @prop()
  @Field({ nullable: false })
  public path: string

  @prop()
  @Field({ nullable: false })
  public thumbnail: string

  @prop()
  @Field({ nullable: false })
  public size: number

  @prop()
  @Field({ nullable: true })
  public frameRate: number

  @prop()
  @Field({ nullable: true })
  public width: number

  @prop()
  @Field({ nullable: true })
  public height: number

  @prop()
  @Field({ nullable: false })
  public duration: number

  @prop()
  @Field({ nullable: true })
  public hls: boolean // To be able to migrate old videos without HLS

  @prop()
  @Field({ nullable: true })
  public hlsPath: string

  @prop()
  @Field((_) => WallVideoSegmentation, { nullable: true })
  public segmentation: WallVideoSegmentation

  @prop()
  @Field((_) => WallVideoRepeat, { nullable: true })
  public repeat: WallVideoRepeat

  @prop({ ref: () => User })
  @Field(() => User, { nullable: true })
  public user: Ref<User>

  @prop()
  public uploadFinished: boolean

  @Field({ nullable: false })
  public createdAt: Date

  @prop({ type: () => [String], enum: WallVideoTags })
  @Field((_) => [WallVideoTags], { nullable: true })
  public tags: WallVideoTags[]

  @Field({ nullable: true })
  public deleted: boolean

  @Field({ nullable: true })
  public deletedBy: string

  @prop()
  @Field({ nullable: true })
  public hidden?: boolean

  @prop()
  @Field({ nullable: true })
  public streamFeedActivityId?: string
}

export const videoTemplateSortObject: Partial<WallVideo> = {
  _id: new Types.ObjectId(),
  createdAt: new Date(),
}
