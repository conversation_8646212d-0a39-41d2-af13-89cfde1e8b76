import mongoose, { Types } from 'mongoose'
import { ObjectType } from 'type-graphql'

import { Agency } from './agency'
import { User } from './user'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@ObjectType()
@Index({ agency: 1, follower: 1 }, { unique: true })
export class AgencyFollow {
  readonly _id: Types.ObjectId

  @prop({ type: Types.ObjectId, ref: Agency })
  public agency: Ref<Agency>

  @prop({ type: Types.ObjectId, ref: User })
  public follower: Ref<User>
}

export const AgencyFollowModel = getModelForClass(AgencyFollow, {
  existingMongoose: mongoose,
})
