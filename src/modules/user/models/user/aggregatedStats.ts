import { Types } from 'mongoose'
import { Field, Float, ID, Int, ObjectType } from 'type-graphql'

import { prop } from '@typegoose/typegoose'

@ObjectType()
export class AggregatedStatsItem {
  @Field((_type) => ID, { name: 'id' })
  readonly _id?: Types.ObjectId

  // common
  @prop()
  @Field(() => Int, { nullable: false })
  public gamesPlayed?: number

  // players
  @prop()
  @Field(() => Int, { nullable: true })
  public goals?: number

  @prop()
  @Field(() => Int, { nullable: true })
  public assists?: number

  @prop()
  @Field(() => Int, { nullable: true })
  public points?: number

  @prop()
  @Field(() => Float, { nullable: true })
  public pointsPerGame?: number

  // goalies
  @prop()
  @Field({ nullable: true })
  public wins?: number

  @prop()
  @Field({ nullable: true })
  public losses?: number

  @prop()
  @Field({ nullable: true })
  public ties?: number

  @prop()
  @Field(() => Int, { nullable: true })
  public shutouts?: number

  @prop()
  @Field(() => Float, { nullable: true })
  public gaa?: number

  @prop()
  @Field(() => Float, { nullable: true })
  public svp?: number
}

@ObjectType()
export class AggregatedStats {
  @prop()
  @Field()
  public season: AggregatedStatsItem

  @prop()
  @Field()
  public career: AggregatedStatsItem
}
