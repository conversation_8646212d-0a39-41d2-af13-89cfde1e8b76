import config from 'config'

import { User } from './user'
import { WallVideo } from './wallVideo'

import { getModelForClass, mongoose, ReturnModelType } from '@typegoose/typegoose'

export const UserModel = getModelForClass(User, {
  existingMongoose: mongoose,
  schemaOptions: {
    autoSearchIndex: config.mongo.autoSearchIndex,
  },
})

type DeleteMethods = {
  deleteOneByUser(
    user: string,
    filter?: { _id: string } // FilterQuery<WallVideo>
    // options?: DeleteOptions<WallVideo> | null
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ): any
}

type WallVideoModelType = ReturnModelType<typeof WallVideo> & DeleteMethods
export const WallVideoModel = getModelForClass(WallVideo, {
  existingMongoose: mongoose,
}) as WallVideoModelType
