import mongoose, { Types } from 'mongoose'

import { User } from './user'

import { getModelForClass, Index, modelOptions, prop, Ref } from '@typegoose/typegoose'

export enum ViewAggregationType {
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
}

@modelOptions({
  schemaOptions: {
    timestamps: true,
  },
})
@Index({ user: 1, date: 1, type: 1 })
@Index({ userCountry: 1, userYearOfBirth: 1, date: 1, type: 1 })
@Index({ userCountry: 1, date: 1, type: 1 })
@Index({ userYearOfBirth: 1, date: 1, type: 1 })
@Index({ totalViews: 1 })
@Index({ type: 1, date: 1, totalViews: -1, _id: -1 })
export class UserViewAggregated {
  readonly _id: Types.ObjectId

  @prop({ type: String, enum: ViewAggregationType, required: true })
  public type: ViewAggregationType

  @prop({ required: true })
  public date: Date

  @prop({ type: Types.ObjectId, ref: User })
  public user: Ref<User>

  @prop({ required: false })
  public userYearOfBirth: number

  @prop({ required: false })
  public userCountry: string

  @prop({ required: true })
  public totalViews: number
}

export const UserViewAggregatedModel = getModelForClass(UserViewAggregated, {
  existingMongoose: mongoose,
})
