import { Types } from 'mongoose'
import { createUnionType, Field, Float, InputType, Int, ObjectType } from 'type-graphql'

import { CacheControl } from '../../../middlewares/cacheControl'
import {
  FailedToCreatePresignedUrl,
  FailedToDelete,
  FailedToEdit,
  PageInfo,
  WallVideoNotFound,
} from '../../../types/general'
import { BioPosition, User } from '../models/user'
import { WallVideo, WallVideoTags, WallVideoType } from '../models/wallVideo'

@InputType()
export class UploadWallVideoConfirmationInput {
  @Field({ nullable: false })
  public id: Types.ObjectId

  @Field({ nullable: false })
  public uploadSuccessful: boolean
}

@InputType()
export class UploadWallVideoDataSegmentationInput {
  @Field({ nullable: false })
  public selectionX: number

  @Field({ nullable: false })
  public selectionY: number

  @Field({ nullable: false })
  public frame: number
}

@InputType()
export class UploadWallVideoRepeatInput {
  @Field({ nullable: false })
  public start: number

  @Field({ nullable: false })
  public end: number
}

@InputType()
export class UploadWallVideoDataInput {
  @Field((_) => WallVideoType, { nullable: false })
  public type: WallVideoType

  @Field({ nullable: true })
  public title?: string

  @Field({ nullable: true })
  public size?: number

  @Field({ nullable: true })
  public frameRate?: number

  @Field((_) => UploadWallVideoDataSegmentationInput, { nullable: true })
  public segmentation?: UploadWallVideoDataSegmentationInput

  @Field((_) => UploadWallVideoRepeatInput, { nullable: true })
  public repeat?: UploadWallVideoRepeatInput

  @Field({ nullable: true })
  public duration?: number

  @Field({ nullable: true })
  public width?: number

  @Field({ nullable: true })
  public height?: number
}

@InputType()
export class EditWallVideoInput {
  @Field((_) => WallVideoType, { nullable: false })
  public type: WallVideoType

  @Field({ nullable: false })
  public id: Types.ObjectId
}

@InputType()
export class UploadWallVideoInput {
  @Field((_) => UploadWallVideoConfirmationInput, { nullable: true })
  public confirmation?: UploadWallVideoConfirmationInput

  @Field((_) => UploadWallVideoDataInput, { nullable: true })
  public data?: UploadWallVideoDataInput
}

@ObjectType()
export class UploadWallVideoResponse {
  @Field({ nullable: true })
  public preSignedUrl?: string

  @Field({ nullable: true })
  public thumbnailPreSignedUrl?: string

  @Field(() => WallVideo, { nullable: true })
  public wallVideo?: WallVideo | null

  @Field((_type) => [UploadWallVideoError], { nullable: true })
  public errors?: Array<typeof UploadWallVideoError>
}

@ObjectType()
export class DeleteWallVideoResponse {
  @Field({ nullable: true })
  public success?: boolean

  @Field((_type) => [DeleteWallVideoError], { nullable: true })
  public errors?: Array<typeof DeleteWallVideoError>
}

@ObjectType()
export class EditWallVideoCategoryResponse {
  @Field((_type) => [EditWallVideoCategoryError], { nullable: true })
  public errors?: Array<typeof EditWallVideoCategoryError>

  @Field({ nullable: true })
  public wallVideo?: WallVideo
}

const UploadWallVideoError = createUnionType({
  name: 'UploadWallVideoError',
  types: () => [FailedToCreatePresignedUrl] as const,
})

const DeleteWallVideoError = createUnionType({
  name: 'DeleteWallVideoError',
  types: () => [FailedToDelete] as const,
})

const EditWallVideoCategoryError = createUnionType({
  name: 'EditWallVideoCategoryError',
  types: () => [WallVideoNotFound, FailedToEdit] as const,
})

@InputType()
export class LatestClipsFilter {
  @Field({ nullable: true })
  public teamId?: Types.ObjectId

  @Field({ nullable: true })
  public agencyId?: Types.ObjectId

  @Field((_) => [String], { nullable: true })
  public countries: string[]

  @Field(() => [BioPosition], { nullable: true })
  public positions?: BioPosition[]

  @Field(() => [Int], { nullable: true })
  public yearOfBirth?: number[]
}

// wallVideos connection

@InputType()
export class WallVideosFilter {
  @Field((_) => WallVideoType, { nullable: true })
  public type?: WallVideoType

  @Field({ nullable: true })
  public onlyLandscape?: boolean

  @Field({ nullable: true })
  public userId?: Types.ObjectId

  @Field({ nullable: true })
  public slug?: string

  @Field({ nullable: true })
  public videoId?: string
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class WallVideoEdge {
  @Field((_type) => WallVideo, { nullable: false })
  public node: WallVideo
}

@ObjectType()
export class CategoryCount {
  @Field({ nullable: false })
  public category: WallVideoType

  @Field({ nullable: false })
  public count: number
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class WallVideosResponse {
  @Field((_type) => [WallVideoEdge], { nullable: false })
  public edges: Array<WallVideoEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo

  @Field((_type) => [CategoryCount], { nullable: true })
  public categoryCounts?: CategoryCount[]
}

// VideosByIdsInput
@InputType()
export class VideosByIdsInput {
  @Field((_) => [String])
  public videoIds: string[]
}

// VideosByIdsResponse
@ObjectType()
export class VideosByIdsResponse {
  @Field((_type) => [WallVideo], { nullable: false })
  public videos: WallVideo[]
}

// EditHighlightsResponse

@ObjectType()
export class EditHighlightsResponse {
  @Field({ nullable: true })
  public user?: User

  @Field((_type) => [EditHighlightsError], { nullable: true })
  public errors?: Array<typeof EditHighlightsError>
}

const EditHighlightsError = createUnionType({
  name: 'EditHighlightsError',
  types: () => [FailedToEdit] as const,
})

// EditHighlightsInput

@InputType()
export class EditHighlightsInput {
  @Field((_type) => [Types.ObjectId], { nullable: false })
  public highlights: Types.ObjectId[]
}

// upload wallVideo segmentation

@InputType()
export class UploadWallVideoSegmentationConfirmationInput {
  @Field({ nullable: false })
  public segmentationSuccessful: boolean
}

@InputType()
export class UploadWallVideoSegmentationInput {
  @Field((_) => UploadWallVideoSegmentationConfirmationInput, { nullable: true })
  public confirmation?: UploadWallVideoSegmentationConfirmationInput

  @Field({ nullable: false })
  public id: Types.ObjectId
}

@ObjectType()
export class UploadWallVideoSegmentationResponse {
  @Field((_) => WallVideo, { nullable: true })
  public wallVideo?: WallVideo

  @Field({ nullable: true })
  public preSignedUrl?: string

  @Field((_type) => [UploadWallVideoSegmentationError], { nullable: true })
  public errors?: Array<typeof UploadWallVideoSegmentationError>
}

const UploadWallVideoSegmentationError = createUnionType({
  name: 'UploadWallVideoSegmentationError',
  types: () => [WallVideoNotFound, FailedToEdit] as const,
})

export type VideoSegmentationMessageBody = {
  videoId: Types.ObjectId
  userId: string
}

@ObjectType()
export class VideosCountByType {
  @Field(() => Int)
  defensiveVideosCount: number

  @Field(() => Int)
  gameSaveVideosCount: number

  @Field(() => Int)
  highlightVideosCount: number

  @Field(() => Int)
  interviewVideosCount: number

  @Field(() => Int)
  offensiveVideosCount: number

  @Field(() => Int)
  otherVideosCount: number

  @Field(() => Int)
  puckHandlingVideosCount: number

  @Field(() => Int)
  trainingVideosCount: number
}

@InputType()
export class VideoFilter {
  @Field({ nullable: true })
  public segmentation?: boolean

  @Field(() => [WallVideoTags], { nullable: true })
  tags?: WallVideoTags[]

  @Field({ nullable: true })
  userId?: string

  @Field(() => [WallVideoType], { nullable: true })
  type?: WallVideoType[]
}

@ObjectType()
class TagCountWithPercentage {
  @Field(() => Int)
  count: number

  @Field(() => Float)
  percentage: number

  @Field(() => String)
  tag: string
}

@ObjectType()
export class VideosCountByTags {
  @Field(() => [TagCountWithPercentage])
  videoTagsCount: TagCountWithPercentage[]

  @Field(() => Int)
  totalTaggedVideosCount: number
}
