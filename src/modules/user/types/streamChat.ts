import { Field, ObjectType } from 'type-graphql'

@ObjectType()
export class GraetChatUser {
  @Field(() => String, { nullable: false })
  public id: string

  @Field(() => String, { nullable: false })
  public name: string

  @Field(() => String, { nullable: true })
  public firstname?: string

  @Field(() => String, { nullable: true })
  public lastname?: string

  @Field(() => String, { nullable: true })
  public image?: string | null

  @Field(() => String, { nullable: true })
  public slug?: string

  @Field(() => String, { nullable: true })
  public graet_role?: string

  @Field(() => String, { nullable: true })
  public graet_sub_role?: string

  @Field(() => String, { nullable: true })
  public graet_agent_agency_name?: string
}

export type UpsertUsersChatUser = GraetChatUser & { [key: string]: undefined | null | string }
