import { Types } from 'mongoose'
import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'

@InputType()
export class ContactMessageInput {
  @Field({ nullable: true })
  public userId: Types.ObjectId

  @Field({ nullable: true })
  public agencyId: Types.ObjectId

  @Field()
  public message: string

  @Field()
  public name: string

  @Field()
  public email: string
}

@ObjectType()
export class ContactMessageResponse {
  @Field(() => Boolean, { nullable: true })
  public success?: boolean

  @Field((_type) => [ContactMessagerError], { nullable: true })
  public errors?: Array<typeof ContactMessagerError>
}

const ContactMessagerError = createUnionType({
  name: 'ContactMessagerError',
  types: () => [ContactNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class ContactNotFoundError extends IUserError implements IUserError {}
