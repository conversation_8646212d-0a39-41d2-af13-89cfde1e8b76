import { IsEmail, IsISO31661<PERSON><PERSON><PERSON>2, ValidateIf } from 'class-validator'
import { Types } from 'mongoose'
import {
  createUnionType,
  Field,
  ID,
  InputType,
  Int,
  ObjectType,
  registerEnumType,
} from 'type-graphql'

import { CacheControl } from '../../../middlewares/cacheControl'
import { FailedToCreatePresignedUrl, IUserError, PageInfo } from '../../../types/general'
import { Agency, SportCategory } from '../models/agency'
import { BioPosition } from '../models/user'

// create agency

@ObjectType()
export class CreateAgencyResponse {
  @Field(() => Agency, { nullable: true })
  public agency?: Agency | null

  @Field((_type) => [CreateAgencyError], { nullable: true })
  public errors?: Array<typeof CreateAgencyError>
}

const CreateAgencyError = createUnionType({
  name: 'CreateAgencyError',
  types: () => [CreateAgencyAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class CreateAgencyAlreadyExistsError extends IUserError implements IUserError {}

@InputType()
export class AgencyDraftsInput {
  @Field((_type) => Int, { nullable: false })
  public NHL: number

  @Field((_type) => Int, { nullable: false })
  public NCAA: number

  @Field((_type) => Int, { nullable: false })
  public CHL: number

  @Field((_type) => Int, { nullable: false })
  public USHL: number
}

@InputType()
export class CreateAgencyInput {
  @Field()
  public name: string

  @Field({ nullable: true })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country?: string

  @Field({ nullable: true })
  public website?: string

  @Field({ nullable: true })
  public address?: string

  @Field({ nullable: true })
  public phone?: string

  @Field({ nullable: true })
  public email?: string

  @Field({ nullable: true })
  public epUrl?: string

  @Field({ nullable: true })
  public instagramUrl?: string

  @Field({ nullable: true })
  public facebookUrl?: string

  @Field({ nullable: true })
  public twitterUrl?: string

  @Field({ nullable: true })
  public linkedinUrl?: string

  @Field({ nullable: true })
  public tiktokUrl?: string

  @Field((_type) => Int, { nullable: true })
  public externalNumberOfClients: number

  @Field((_type) => Int, { nullable: true })
  public instagramFollowers: number

  @Field((_type) => [SportCategory], { nullable: true })
  public categories?: SportCategory[]

  @Field((_type) => Int, { nullable: true })
  public founded?: number

  @Field((_type) => AgencyDraftsInput, { nullable: true })
  public drafts?: AgencyDraftsInput

  @Field((_type) => [String], { nullable: true })
  public licences?: string[]

  @Field((_type) => [Types.ObjectId], { nullable: true })
  public affiliateAgencies?: Types.ObjectId[]
}

// update agency

@InputType()
export class UploadAgencyLogoInput {
  @Field({ nullable: false })
  public uploadSuccessful: boolean
}

@ObjectType()
export class UploadAgencyLogoResponse {
  @Field({ nullable: true })
  public preSignedUrl?: string

  @Field(() => Agency, { nullable: true })
  public agency?: Agency | null = undefined

  @Field((_type) => [UploadAgencyLogoError], { nullable: true })
  public errors?: Array<typeof UploadAgencyLogoError>
}

const UploadAgencyLogoError = createUnionType({
  name: 'UploadAgencyLogoError',
  types: () => [FailedToCreatePresignedUrl] as const,
})

@ObjectType()
export class UpdateAgencyResponse {
  @Field(() => Agency, { nullable: true })
  public agency?: Agency | null

  @Field((_type) => [UpdateAgencyError], { nullable: true })
  public errors?: Array<typeof UpdateAgencyError>
}

const UpdateAgencyError = createUnionType({
  name: 'UpdateAgencyError',
  types: () => [UpdateAgencyNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class UpdateAgencyNotFoundError extends IUserError implements IUserError {}

@InputType()
export class UpdateAgencyInput {
  @Field({ nullable: true })
  public name?: string

  @Field({ nullable: true })
  public country?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field({ nullable: true })
  public integrated?: boolean

  @Field({ nullable: true })
  public website?: string

  @Field({ nullable: true })
  public address?: string

  @Field({ nullable: true })
  public phone?: string

  @Field({ nullable: true })
  public email?: string

  @Field({ nullable: true })
  public epUrl?: string

  @Field({ nullable: true })
  public instagramUrl?: string

  @Field({ nullable: true })
  public facebookUrl?: string

  @Field({ nullable: true })
  public twitterUrl?: string

  @Field({ nullable: true })
  public linkedinUrl?: string

  @Field({ nullable: true })
  public tiktokUrl?: string

  @Field((_type) => Int, { nullable: true })
  public externalNumberOfClients: number

  @Field((_type) => Int, { nullable: true })
  public instagramFollowers: number

  @Field((_type) => [SportCategory], { nullable: true })
  public categories?: SportCategory[]

  @Field((_type) => Int, { nullable: true })
  public founded?: number

  @Field((_type) => AgencyDraftsInput, { nullable: true })
  public drafts?: AgencyDraftsInput

  @Field((_type) => [String], { nullable: true })
  public licences?: string[]

  @Field((_type) => [Types.ObjectId], { nullable: true })
  public affiliateAgencies?: Types.ObjectId[]
}

// delete agency

@ObjectType()
export class DeleteAgencyResponse {
  @Field(() => Agency, { nullable: true })
  public agency?: Agency

  @Field((_type) => [DeleteAgencyError], { nullable: true })
  public errors?: Array<typeof DeleteAgencyError>
}

const DeleteAgencyError = createUnionType({
  name: 'DeleteAgencyError',
  types: () => [DeleteAgencyNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class DeleteAgencyNotFoundError extends IUserError implements IUserError {}

// agencies search

@InputType()
export class AgenciesFilter {
  @Field({ nullable: true })
  public searchQuery?: string

  @Field({ nullable: true })
  public checked?: boolean

  @Field({ nullable: true })
  public integrated?: boolean

  @Field((_type) => SportCategory, { nullable: true })
  public category?: SportCategory
}

@ObjectType()
export class AgencyFollowingEdge {
  @Field((_type) => Agency, { nullable: false })
  public node: Agency
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class AgencyEdge {
  @Field((_type) => Agency, { nullable: false })
  public node: Agency
}

@ObjectType()
export class AgenciesFollowingResponse {
  @Field((_type) => [AgencyFollowingEdge], { nullable: false })
  public edges: Array<AgencyFollowingEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class AgenciesResponse {
  @Field((_type) => [AgencyEdge], { nullable: false })
  public edges: Array<AgencyEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

export enum AgencyUsersRole {
  PLAYER = 'PLAYER',
  AGENT = 'AGENT',
}

registerEnumType(AgencyUsersRole, {
  name: 'AgencyUsersRole',
})

@InputType()
export class AgencyUsersFilter {
  @Field(() => AgencyUsersRole, { nullable: true })
  public role?: AgencyUsersRole

  @Field(() => Types.ObjectId, { nullable: true })
  public agentV2?: Types.ObjectId

  @Field(() => [BioPosition], { nullable: true })
  public positions?: BioPosition[]

  @Field(() => [String], { nullable: true })
  public years?: string[]

  @Field(() => Boolean, { nullable: false })
  public includeAggregation: boolean = false
}

// follow agency response

@ObjectType()
export class FollowAgencyResponse {
  @Field(() => Agency, { nullable: true })
  public agency: Agency | null

  @Field((_type) => [FollowAgencyError], { nullable: true })
  public errors?: Array<typeof FollowAgencyError>
}

const FollowAgencyError = createUnionType({
  name: 'FollowAgencyError',
  types: () => [FollowAgencyNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class FollowAgencyNotFoundError extends IUserError implements IUserError {}

// unfollow agency response

@ObjectType()
export class UnfollowAgencyResponse {
  @Field(() => Agency, { nullable: true })
  public agency: Agency | null

  @Field((_type) => [UnfollowAgencyError], { nullable: true })
  public errors?: Array<typeof UnfollowAgencyError>
}

const UnfollowAgencyError = createUnionType({
  name: 'UnfollowAgencyError',
  types: () => [UnfollowAgencyNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class UnfollowAgencyNotFoundError extends IUserError implements IUserError {}

// visit agency response

@ObjectType()
export class VisitAgencyResponse {
  @Field(() => Agency, { nullable: true })
  public agency: Agency | null

  @Field((_type) => [VisitAgencyError], { nullable: true })
  public errors?: Array<typeof VisitAgencyError>
}

const VisitAgencyError = createUnionType({
  name: 'VisitAgencyError',
  types: () => [VisitAgencyNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class VisitAgencyNotFoundError extends IUserError implements IUserError {}

@ObjectType()
export class InviteAgentsResponse {
  @Field()
  public success: boolean

  @Field((_type) => [InviteAgentsError], { nullable: true })
  public errors?: Array<typeof InviteAgentsError>
}

const InviteAgentsError = createUnionType({
  name: 'InviteAgentsError',
  types: () => [InviteAgentsAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class InviteAgentsAlreadyExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@InputType()
export class InviteAgentsInput {
  @Field(() => [String])
  @IsEmail({}, { each: true })
  public emails: string[]

  @Field(() => ID, { nullable: true })
  public agencyId?: Types.ObjectId
}

@ObjectType()
export class AcceptAgencyInvitationResponse {
  @Field()
  public success: boolean

  @Field(() => [AcceptAgencyInvitationError], { nullable: true })
  public errors?: Array<typeof AcceptAgencyInvitationError>
}

const AcceptAgencyInvitationError = createUnionType({
  name: 'AcceptAgencyInvitationError',
  types: () =>
    [
      AcceptAgencyInvitationInvalidTokenError,
      AcceptAgencyInvitationExpiredError,
      AcceptAgencyInvitationAlreadyAcceptedError,
    ] as const,
})

@ObjectType({ implements: IUserError })
export class AcceptAgencyInvitationInvalidTokenError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class AcceptAgencyInvitationExpiredError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class AcceptAgencyInvitationAlreadyAcceptedError extends IUserError implements IUserError {}
