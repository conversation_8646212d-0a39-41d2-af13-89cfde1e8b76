import {
  IsDate,
  IsEmail,
  IsISO31661<PERSON><PERSON>pha2,
  IsUrl,
  Length,
  <PERSON>,
  Min,
  Validate,
  ValidateIf,
  ValidateNested,
} from 'class-validator'
import { Types } from 'mongoose'
import {
  createUnionType,
  Field,
  ID,
  InputType,
  Int,
  ObjectType,
  registerEnumType,
} from 'type-graphql'

import { CacheControl } from '../../../middlewares/cacheControl'
import {
  AggregatedInfoGroup,
  EntityNotFoundError,
  GeneralError,
  IUserError,
  PageInfo,
} from '../../../types/general'
import {
  AgeValidator,
  IsCurrencyCode,
  isMobilePhoneNumber,
} from '../../../validators/custom-validators'
import {
  BioGender,
  BioHandedness,
  BioPlayerType,
  BioPosition,
  BioSchoolType,
  ContactType,
  ExternalLinkType,
  Platform,
  Role,
  SubRole,
  SubRoleSpecificationType,
  User,
} from '../models/user'

import { Ref } from '@typegoose/typegoose'

@InputType()
export class RegisterOrLoginByEmailInput {
  @Field()
  @IsEmail()
  public email: string

  @Field({ nullable: true })
  public role?: Role

  @Field({ nullable: true })
  public subrole?: SubRole
}

@ObjectType()
export class RegisteryOrLoginByEmailResponse {
  @Field(() => User, { nullable: true })
  public user?: User | null

  @Field((_type) => [RegisterOrLoginByEmailError])
  public errors?: Array<typeof RegisterOrLoginByEmailError>
}

const RegisterOrLoginByEmailError = createUnionType({
  name: 'RegisterOrLoginByEmailError',
  types: () => [GeneralError] as const,
})

@ObjectType()
export class JWTTokens {
  @Field()
  public accessToken: string

  @Field()
  public refreshToken: string
}

@ObjectType()
export class VerifyByEmailResponse {
  @Field({ nullable: true })
  public tokens?: JWTTokens

  @Field({ nullable: true })
  public user?: User

  @Field((_type) => [VerifyByEmailError])
  public errors: Array<typeof VerifyByEmailError>
}

@ObjectType({ implements: IUserError })
export class InvalidVerificationCodeError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class UserNotFoundError extends IUserError implements IUserError {}

const VerifyByEmailError = createUnionType({
  name: 'VerifyByEmailError', // Name of the GraphQL union
  types: () => [InvalidVerificationCodeError, UserNotFoundError] as const,
})

@InputType()
export class CreateUserInput {
  @Field()
  public email: string

  @Field()
  public firstname: string

  @Field()
  public lastname: string
}
@ObjectType()
export class CreateUserResponse {
  @Field(() => User, { nullable: true })
  public user?: User

  @Field((_type) => [CreateUserError], { nullable: true })
  public errors?: Array<typeof CreateUserError>
}

const CreateUserError = createUnionType({
  name: 'CreateUserError',
  types: () => [CreateUserAlreadyExistsError] as const,
})

@ObjectType({ implements: IUserError })
export class CreateUserAlreadyExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@ObjectType()
export class UpdateUserResponse {
  @Field(() => User, { nullable: true })
  public user?: User | null

  @Field((_type) => [UpdateUserError])
  public errors: Array<typeof UpdateUserError>
}

const UpdateUserError = createUnionType({
  name: 'UpdateUserError',
  types: () => [UserNotFoundError] as const,
})

@ObjectType()
export class UpdateUserAdminResponse {
  @Field(() => User, { nullable: true })
  public user?: User | null

  @Field((_type) => [UpdateUserAdminError])
  public errors: Array<typeof UpdateUserAdminError>
}

const UpdateUserAdminError = createUnionType({
  name: 'UpdateUserAdminError',
  types: () => [UserNotFoundError] as const,
})
@InputType()
class PhoneNumberInput {
  @Field()
  public countryPrefix: string

  @Field()
  @Validate(isMobilePhoneNumber)
  public number: string
}

@InputType()
class ContactInput {
  @Field()
  @Length(0, 50)
  public firstname: string

  @Field()
  @Length(0, 50)
  public lastname: string

  @Field()
  @IsEmail()
  public email: string

  @Field({ nullable: true })
  @ValidateNested()
  public phoneNumber?: PhoneNumberInput

  @Field((_type) => ContactType)
  public type: ContactType
}

@InputType()
class ExternalLinkInput {
  @Field()
  @Length(0, 50)
  public name: string

  @Field()
  @IsUrl()
  public url: string

  @Field((_type) => ExternalLinkType)
  public type: ExternalLinkType

  @Field({ nullable: true })
  public scrapable?: boolean
}

@InputType()
class BoostInput {
  @Field()
  @Validate(IsCurrencyCode)
  public currency: string

  @Field()
  @Min(0)
  public goalAmount: number
}

@InputType()
export class HeightInput {
  @Field((_type) => Int, { nullable: false })
  @Min(0)
  public feet: number

  @Field((_type) => Int, { nullable: false })
  @Min(0)
  public inches: number

  @Field((_type) => Int, { nullable: false })
  @Min(0)
  public centimeters: number
}

@InputType()
export class WeightInput {
  @Field((_type) => Int, { nullable: false })
  @Min(0)
  public pounds: number

  @Field((_type) => Int, { nullable: false })
  @Min(0)
  public kilograms: number
}

@InputType()
export class UserBioInput {
  @Field((_type) => BioPosition, { nullable: true })
  public position?: BioPosition

  @Field((_type) => BioHandedness, { nullable: true })
  public handedness?: BioHandedness

  @Field((_type) => BioPlayerType, { nullable: true })
  public playerType?: BioPlayerType

  @Field((_type) => BioSchoolType, { nullable: true })
  public schoolType?: BioSchoolType

  @Field((_type) => ID, { nullable: true })
  public agency?: Types.ObjectId

  @Field((_type) => ID, { nullable: true })
  public agencyUnverified?: Types.ObjectId

  @Field((_type) => BioGender, { nullable: true })
  public gender?: BioGender

  @Field((_type) => HeightInput, { nullable: true })
  @ValidateNested()
  public height?: HeightInput

  @Field((_type) => WeightInput, { nullable: true })
  @ValidateNested()
  public weight?: WeightInput

  @Field({ nullable: true })
  @Length(0, 50)
  public birthplace?: string

  @Field({ nullable: true })
  @Length(0, 50)
  public nameOfSchool?: string

  @Field({ nullable: true })
  @Length(0, 400)
  public intro?: string

  @Field((_type) => Int, { nullable: true })
  @Min(1900)
  @Max(2150)
  public graduationYear?: number

  @Field({ nullable: true })
  @Min(0)
  @Max(5)
  public cumulativeGPA?: number

  @Field({ nullable: true })
  @Min(0)
  @Max(5)
  public coreGPA?: number

  @Field({ nullable: true })
  @Min(0)
  @Max(4)
  public unweightedGPA?: number

  @Field({ nullable: true })
  @Min(0)
  @Max(5)
  public weightedGPA?: number

  @Field((_type) => Int, { nullable: true })
  @Min(0)
  @Max(1600)
  public satScore?: number

  @Field((_type) => Int, { nullable: true })
  @Min(0)
  @Max(36)
  public actScore?: number

  @Field((_type) => Int, { nullable: true })
  @Min(0)
  @Max(120)
  public toeflScore?: number

  @Field({ nullable: true })
  public ncaaEligibility?: boolean

  @Field({ nullable: true })
  public naiaEligibility?: boolean
}

@InputType()
export class SubRoleSpecificationInput {
  @Field((_type) => ID)
  public team: Types.ObjectId

  @Field((_type) => SubRoleSpecificationType)
  public type: SubRoleSpecificationType

  @Field({ nullable: true })
  public current?: boolean
}

@InputType()
export class UpdateUserInput {
  public name?: string

  @Field({ nullable: true })
  @Length(0, 50)
  public firstname?: string

  @Field({ nullable: true })
  @Length(0, 50)
  public lastname?: string

  @Field({ nullable: true })
  @IsDate()
  @Validate(AgeValidator)
  public dateOfBirth?: Date

  @Field({ nullable: true })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country?: string

  @Field({ nullable: true })
  @ValidateNested()
  public contact?: ContactInput

  @Field({ nullable: true })
  @ValidateNested()
  public bio?: UserBioInput

  @Field({ nullable: true })
  @ValidateNested()
  public phoneNumber?: PhoneNumberInput

  @Field((_type) => [ExternalLinkInput], { nullable: true })
  @ValidateNested()
  public externalLinks?: Array<ExternalLinkInput>

  @Field((_type) => [SubRoleSpecificationInput], { nullable: true })
  @ValidateNested()
  public subroleSpecifications?: Array<SubRoleSpecificationInput>

  @Field({ nullable: true })
  @Length(0, 500)
  public boostMessage?: string

  @Field(() => BoostInput, { nullable: true })
  @ValidateNested()
  public boost?: BoostInput

  @Field((_type) => Role, { nullable: true })
  public role?: Role

  @Field((_type) => SubRole, { nullable: true })
  public subrole?: SubRole

  @Field((_type) => ID, { nullable: true })
  public currentTeam?: Types.ObjectId

  public slug?: string
}

@InputType()
export class CreateUserAdminInput {
  @Field()
  public email: string

  @Field()
  public firstname: string

  @Field()
  public lastname: string

  @Field()
  public public: boolean

  @Field()
  public searchEnabled: boolean
}

@InputType()
export class UpdateUserInputAdmin {
  @Field({ nullable: true })
  public public?: boolean

  @Field({ nullable: true })
  public premium?: boolean

  @Field({ nullable: true })
  public searchEnabled?: boolean

  @Field({ nullable: true })
  public recommended?: boolean

  @Field({ nullable: true })
  public inspired?: boolean

  @Field({ nullable: true })
  public moreSelected?: boolean

  @Field({ nullable: true })
  @Length(0, 50)
  public name?: string

  @Field(() => String, { nullable: true })
  @Length(0, 50)
  public firstname?: string | null

  @Field(() => String, { nullable: true })
  @Length(0, 50)
  public lastname?: string | null

  @Field({ nullable: true })
  public email?: string

  @Field({ nullable: true })
  @IsDate()
  public dateOfBirth?: Date

  @Field({ nullable: true })
  @Length(0, 50)
  public country?: string

  @Field({ nullable: true })
  @ValidateNested()
  public contact?: ContactInput

  @Field({ nullable: true })
  @ValidateNested()
  public bio?: UserBioInput

  @Field({ nullable: true })
  @ValidateNested()
  public phoneNumber?: PhoneNumberInput

  @Field((_type) => [ExternalLinkInput], { nullable: true })
  @ValidateNested()
  public externalLinks?: Array<ExternalLinkInput>

  @Field((_type) => [SubRoleSpecificationInput], { nullable: true })
  @ValidateNested()
  public subroleSpecifications?: Array<SubRoleSpecificationInput>

  @Field({ nullable: true })
  @Length(0, 500)
  public boostMessage?: string

  @Field(() => BoostInput, { nullable: true })
  @ValidateNested()
  public boost?: BoostInput

  @Field((_type) => Role, { nullable: true })
  public role?: Role

  @Field((_type) => ID, { nullable: true })
  public currentTeam?: Types.ObjectId

  @Field({ nullable: true })
  public slug?: string

  @Field({ nullable: true })
  public agent: Types.ObjectId

  @Field({ nullable: true })
  public subrole: SubRole
}

// search

@InputType()
export class UsersFilter {
  @Field({ nullable: true })
  public searchQuery?: string

  @Field({ nullable: true })
  public country?: string

  @Field({ nullable: true })
  public role?: Role

  @Field((_type) => [Role], { nullable: true })
  public roles?: Role[]
}

@InputType()
export class AdminUsersFilter {
  @Field({ nullable: true })
  public searchQuery?: string

  @Field({ nullable: true })
  public recommended?: boolean

  @Field({ nullable: true })
  public inspired?: boolean

  @Field({ nullable: true })
  public premium?: boolean

  @Field({ nullable: true })
  public moreSelected?: boolean

  @Field({ nullable: true })
  public withoutTeam?: boolean

  @Field({ nullable: true })
  public withoutPicture?: boolean

  @Field({ nullable: true })
  public yearOfBirth?: number

  @Field({ nullable: true })
  public country?: string

  @Field((_type) => Role, { nullable: true })
  public role?: Role

  @Field((_type) => SubRole, { nullable: true })
  public subrole?: SubRole

  @Field({ nullable: true })
  public agency?: Types.ObjectId

  @Field({ nullable: true })
  public possibleYearsOfBirth?: number
}

@ObjectType()
export class UserEdge {
  @Field((_type) => User, { nullable: false })
  public node: User
}

@ObjectType()
export class RandomUsersResponse {
  @Field(() => [UserEdge])
  edges: UserEdge[]

  @Field(() => Int)
  totalCount: number
}

@ObjectType()
export class UsersResponse {
  @Field((_type) => [UserEdge], { nullable: false })
  public edges: Array<UserEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo

  @Field((_type) => [AggregatedInfoGroup], { nullable: true })
  public aggregatedInfo: AggregatedInfoGroup[] | null = null
}

@ObjectType()
export class UpdateDefaultCurrencyResponse {
  @Field({ nullable: true })
  public currency?: string

  @Field((_type) => [UpdateDefaultCurrencyError])
  public errors: Array<typeof UpdateDefaultCurrencyError>
}

const UpdateDefaultCurrencyError = createUnionType({
  name: 'UpdateDefaultCurrencyError',
  types: () => [CurrencyUpdateError, UserNotFoundError] as const,
})

@ObjectType({ implements: IUserError })
export class CurrencyUpdateError extends IUserError implements IUserError {}
// google sign in

@InputType()
export class SocialSignInInput {
  @Field()
  public idToken: string

  @Field({ nullable: true })
  public firstname: string

  @Field({ nullable: true })
  public lastname: string

  @Field({ nullable: true })
  public role?: Role

  @Field({ nullable: true })
  public subrole?: SubRole
}

@ObjectType()
export class SocialSignInResponse {
  @Field({ nullable: true })
  public tokens?: JWTTokens

  @Field({ nullable: true })
  public user?: User

  @Field({ nullable: true })
  public onboardingCompleted?: boolean

  @Field((_type) => [SocialSignInError])
  public errors: Array<typeof SocialSignInError>
}

const SocialSignInError = createUnionType({
  name: 'SocialSignInError',
  types: () => [GeneralError] as const,
})

// register device token
@ObjectType()
export class RegisterDeviceResponse {
  @Field({ nullable: true })
  public user?: User

  @Field((_type) => [RegisterDeviceError])
  public errors: Array<typeof RegisterDeviceError>
}

const RegisterDeviceError = createUnionType({
  name: 'RegisterDeviceError',
  types: () => [DeviceAlreadyRegisteredError] as const,
})

// unregister device token
@ObjectType()
export class UnregisterDeviceResponse {
  @Field({ nullable: true })
  public user?: User

  @Field((_type) => [UnregisterDeviceError])
  public errors: Array<typeof UnregisterDeviceError>
}

const UnregisterDeviceError = createUnionType({
  name: 'UnregisterDeviceError',
  types: () => [DeviceNotRegisteredError] as const,
})

@ObjectType({ implements: IUserError })
export class DeviceAlreadyRegisteredError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class DeviceNotRegisteredError extends IUserError implements IUserError {}

@InputType()
export class RegisterDeviceInput {
  @Field({ nullable: false })
  public token: string

  @Field((_type) => Platform, { nullable: false })
  public platform: Platform
}

@InputType()
export class UnregisterDeviceInput {
  @Field({ nullable: false })
  public token: string

  @Field((_type) => Platform, { nullable: false })
  public platform: Platform
}

// enable/disable push notifications

@ObjectType()
export class PushNotificationsResponse {
  @Field({ nullable: true })
  public user?: User

  @Field((_type) => [PushNotificationsError])
  public errors: Array<typeof PushNotificationsError>
}

const PushNotificationsError = createUnionType({
  name: 'PushNotificationsError',
  types: () => [PushNotificationsChangeError] as const,
})

@ObjectType({ implements: IUserError })
export class PushNotificationsChangeError extends IUserError implements IUserError {}

@InputType()
export class PushNotificationsInput {
  @Field({ nullable: false })
  public enabled: boolean
}

// FetchUserDataEP
@ObjectType()
export class FetchUserDataEPResponse {
  @Field({ nullable: true })
  public user?: User

  @Field((_type) => [FetchUserDataEPError])
  public errors: Array<typeof FetchUserDataEPError>
}

@ObjectType({ implements: IUserError })
export class FetchUserDataEPError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

// UnfollowUserResponse
@ObjectType()
export class UnfollowUserResponse {
  @Field({ nullable: true })
  public user?: User

  @Field((_type) => [UnfollowUserError])
  public errors: Array<typeof UnfollowUserError>
}

const UnfollowUserError = createUnionType({
  name: 'UnfollowUserError',
  types: () => [UserNotFoundError] as const,
})

// LikePlayerPerformanceResponse
@ObjectType()
export class LikePlayerPerformanceResponse {
  @Field((_type) => User, { nullable: true })
  public player?: User | null

  @Field((_type) => [LikePlayerPerformanceError])
  public errors: Array<typeof LikePlayerPerformanceError>
}

const LikePlayerPerformanceError = createUnionType({
  name: 'LikePlayerPerformanceError',
  types: () => [UserNotFoundError, EntityNotFoundError] as const,
})

// UnlikePlayerPerformanceResponse
@ObjectType()
export class UnlikePlayerPerformanceResponse {
  @Field((_type) => User, { nullable: true })
  public player?: User | null

  @Field((_type) => [UnlikePlayerPerformanceError])
  public errors: Array<typeof UnlikePlayerPerformanceError>
}

const UnlikePlayerPerformanceError = createUnionType({
  name: 'UnlikePlayerPerformanceError',
  types: () => [UserNotFoundError, EntityNotFoundError] as const,
})

@ObjectType()
export class PlayersByGame {
  @Field((_type) => ID, { name: 'id' })
  readonly _id: Types.ObjectId

  @Field((_type) => [User], { nullable: true })
  public players?: Array<Ref<User>> | null
}

// UserLikesPlayersPerformanceInput
@InputType()
export class UserLikesPlayersPerformanceInput {
  @Field((_) => Types.ObjectId)
  public gameId: Types.ObjectId

  @Field((_) => [Types.ObjectId], { nullable: true })
  public playerIds: Types.ObjectId[] | null
}

// UserLikesPlayersPerformanceResponse
@ObjectType()
export class UserLikesPlayersPerformanceResponse {
  @Field((_type) => PlayersByGame, { nullable: true })
  public playersByGame?: PlayersByGame | null

  @Field((_type) => [UserLikesPlayersPerformanceError], { nullable: true })
  public errors: Array<typeof UserLikesPlayersPerformanceError> | null
}

const UserLikesPlayersPerformanceError = createUnionType({
  name: 'UserLikesPlayersPerformanceError',
  types: () => [UserNotFoundError, EntityNotFoundError] as const,
})

// UserLikesPlayersGamesPerformanceInput
@InputType()
export class UserLikesPlayersGamesPerformanceInput {
  @Field((_) => [Types.ObjectId])
  public gameIds: Types.ObjectId[]

  @Field((_) => [Types.ObjectId], { nullable: true })
  public playerIds: Types.ObjectId[] | null
}

// UserLikesPlayersGamesPerformanceResponse
@ObjectType()
export class UserLikesPlayersGamesPerformanceResponse {
  @Field((_type) => [PlayersByGame], { nullable: true })
  public playersByGame?: PlayersByGame[] | null

  @Field((_type) => [UserLikesPlayersGamesPerformanceError], { nullable: true })
  public errors: Array<typeof UserLikesPlayersGamesPerformanceError> | null
}

const UserLikesPlayersGamesPerformanceError = createUnionType({
  name: 'UserLikesPlayersGamesPerformanceError',
  types: () => [UserNotFoundError, EntityNotFoundError] as const,
})

export enum Period {
  ALL_TIME = 'ALL_TIME',
  MONTHLY = 'MONTHLY',
}

registerEnumType(Period, {
  name: 'Period',
  description: 'Period for which to get the views',
})

@ObjectType()
export class UserWithViews {
  @Field(() => User)
  public user: User

  @Field(() => Int)
  public views: number
}

@ObjectType()
export class UserViewsResponse {
  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo

  @Field(() => [UserViewEdge])
  public edges: UserViewEdge[]
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class UserViewEdge {
  @Field((_type) => User, { nullable: false })
  public node: User

  @Field(() => Int)
  public views: number
}

@ObjectType()
export class WeeklyViewStats {
  @Field()
  public from: Date

  @Field()
  public to: Date

  @Field(() => Int)
  public views: number
}

@ObjectType()
export class UserViewStatsResponse {
  @Field(() => [WeeklyViewStats])
  public weeklyStats: WeeklyViewStats[]
}
// MyFollowingsResponse
@InputType()
export class MyFollowersFilter {
  @Field((_) => Role, { nullable: false })
  public role?: Role
}

@ObjectType()
export class MyFollowerUserEdge {
  @Field((_type) => User, { nullable: false })
  public node: User

  @Field({ nullable: false })
  public iFollow: boolean // apple's trademark probably
}

@ObjectType()
export class MyFollowersResponse {
  @Field((_type) => [MyFollowerUserEdge], { nullable: false })
  public edges: Array<MyFollowerUserEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

export enum TopViewedFilterType {
  ALL = 'ALL',
  NEW = 'NEW',
}

registerEnumType(TopViewedFilterType, {
  name: 'TopViewedFilterType',
  description: 'Filter for top viewed users',
})

@InputType()
export class TopViewedUsersFilter {
  @Field({ nullable: true })
  public yearOfBirth?: number

  @Field({ nullable: true })
  public country?: string

  @Field(() => TopViewedFilterType, { nullable: true })
  public type: TopViewedFilterType
}

@InputType()
export class YearArguments {
  @Field((_type) => Int)
  public year: number
}

@InputType()
export class Country {
  @Field({ nullable: false })
  @ValidateIf((_, value) => value !== 'INT')
  @IsISO31661Alpha2()
  public country: string
}

// EntityCountsResponse
@ObjectType()
export class EntityCountsResponse {
  @Field(() => Int)
  public players: number

  @Field(() => Int)
  public professionals: number

  @Field(() => Int)
  public agencies: number
}

@ObjectType()
@CacheControl({ inheritMaxAge: true })
export class NewUsersCountResponse {
  @Field(() => Int)
  public count: number
}

// Getstream chat user
@InputType()
export class CreateChatUsersInput {
  @Field((_) => [Types.ObjectId])
  public userIds: Types.ObjectId[]
}
