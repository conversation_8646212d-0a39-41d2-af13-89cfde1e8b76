import { Types } from 'mongoose'
import { createUnionType, Field, InputType, Int, ObjectType } from 'type-graphql'

import { IUserError, PageInfo } from '../../../types/general'
import { PaymentSheet } from '../../stripe/models/payment'
import { Boost } from '../models/boost'

@InputType()
export class BoostMessageInput {
  @Field()
  public paymentIntentId: string

  @Field()
  public message: string

  @Field()
  public name: string
}

@ObjectType()
export class BoostMessageResponse {
  @Field(() => Boost, { nullable: true })
  public boost?: Boost | null

  @Field((_type) => [BoostMessagerError], { nullable: true })
  public errors?: Array<typeof BoostMessagerError>
}

const BoostMessagerError = createUnionType({
  name: 'BoostMessagerError',
  types: () => [BoostNotFoundError, BoostMessageAlreadySetError] as const,
})

@ObjectType({ implements: IUserError })
export class BoostNotFoundError extends IUserError implements IUserError {}

@ObjectType({ implements: IUserError })
export class BoostMessageAlreadySetError extends IUserError implements IUserError {}

// boosts connection

@InputType()
export class BoostsFilter {
  @Field({ nullable: true })
  public slug?: string
}

@ObjectType()
export class BoostEdge {
  @Field((_type) => Boost, { nullable: false })
  public node: Boost
}

@ObjectType()
export class BoostsResponse {
  @Field((_type) => [BoostEdge], { nullable: false })
  public edges: Array<BoostEdge>

  @Field((_type) => PageInfo, { nullable: false })
  public pageInfo: PageInfo
}

// boost payment

@ObjectType()
export class CreateBoostResponse {
  @Field(() => PaymentSheet, { nullable: true })
  public paymentSheet?: PaymentSheet | null

  @Field(() => Boost, { nullable: true })
  public boost?: Boost | null

  @Field((_type) => [CreateBoostError], { nullable: true })
  public errors?: Array<typeof CreateBoostError> | null
}

@ObjectType({ implements: IUserError })
export class StripeNotConnectedError extends IUserError implements IUserError {}

const CreateBoostError = createUnionType({
  name: 'CreateBoostError',
  types: () => [StripeNotConnectedError] as const,
})

@InputType()
export class CreateBoostInput {
  @Field(() => Int, { nullable: false })
  public amount: number

  @Field({ nullable: false })
  public currency: string

  @Field({ nullable: false })
  public userId: Types.ObjectId
}
