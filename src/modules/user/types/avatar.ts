import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { FailedToCreatePresignedUrl } from '../../../types/general'
import { User } from '../models/user'

@InputType()
export class UploadAvatarInput {
  @Field({ nullable: false })
  public uploadSuccessful: boolean
}

@ObjectType()
export class UploadAvatarResponse {
  @Field(() => String, { nullable: true })
  public preSignedUrl?: string | null

  @Field(() => User, { nullable: true })
  public user?: User | null

  @Field((_type) => [UploadAvatarError], { nullable: true })
  public errors?: Array<typeof UploadAvatarError> | null
}

const UploadAvatarError = createUnionType({
  name: 'UploadAvatarError',
  types: () => [FailedToCreatePresignedUrl] as const,
})
