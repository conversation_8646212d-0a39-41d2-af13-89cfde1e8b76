import { Field, ObjectType, registerEnumType } from 'type-graphql'

// GetStream, StreamFeed related
export enum FeedActivityVerb {
  ADD = 'add',
  LIKE = 'like',
  LIKE_VIDEO = 'like_video',
  LIKE_HIGHLIGHT = 'like_highlight',
  /**
   * @deprecated Use `FOLLOW` instead.
   */
  VIEW = 'view',
  VIEW_AGENT = 'view_agent',
  VIEW_SCOUT = 'view_scout',
  VIEW_COACH = 'view_coach',
  VIEW_INSIGHTS = 'view_insights',
  NEW_TEAM_MEMBER = 'new_team_member',
  BOOST_RECEIVED = 'boost_received',
  /**
   * @deprecated Use `FOLLOW` instead.
   */
  FOLLOWED = 'followed',
  FOLLOW = 'follow',
  FOLLOW_OTHER = 'follow_other',
  FOLLOW_AGENT = 'follow_agent',
  FOLLOW_SCOUT = 'follow_scout',
  FOLLOW_COACH = 'follow_coach',

  Fire = 'FIRE',
  FireAgent = 'FIRE_AGENT',
  FireScout = 'FIRE_SCOUT',
  FireCoach = 'FIRE_COACH',

  IceHockeyMatch = 'ICE_HOCKEY_MATCH',
  // TODO future sports maybe
  // SoccerMatch = 'SOCCER_MATCH',
  // BaseballMatch = 'BASEBALL_MATCH',

  GameResult = 'GAME_RESULT',
}

// GetStream, StreamFeed related
export enum FEED_NAMES {
  USER = 'user',
  TIMELINE = 'timeline',
  TEAM = 'team',
  GAME = 'game',
  NOTIFICATION = 'notification',
}

// ids of general feeds, controlled by system, e.g. Game feed
export enum SystemFeedIds {
  IceHockeyGame = 'GraetIceHockeyGameFeedId-1',
}

registerEnumType(FeedActivityVerb, {
  name: 'FeedActivityVerb',
  description: 'StreamFeed activity verbs',
})
registerEnumType(FEED_NAMES, { name: 'FEED_NAMES', description: 'StreamFeed group names' })

@ObjectType()
export class StreamFeedEnums {
  @Field(() => FEED_NAMES, { nullable: true })
  public feedName?: FEED_NAMES | null

  @Field(() => FeedActivityVerb, { nullable: true })
  public feedActivityVerb?: FeedActivityVerb
}
