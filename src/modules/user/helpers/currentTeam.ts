import { Types } from 'mongoose'

import { TeamModel } from '../../stats/models/team'
import { UserModel } from '../models'
import { Role, User } from '../models/user'

export const recalculateTeamPlayersCount = async (user: User, newTeamId?: Types.ObjectId) => {
  if (newTeamId) {
    const teamPlayersCount = await UserModel.countDocuments({
      currentTeam: newTeamId,
      role: { $in: [Role.PLAYER, null] },
      public: { $ne: false },
    })

    await TeamModel.updateOne({ _id: newTeamId }, { playersCount: teamPlayersCount })
  }

  // recalculate previous team players count
  if (user.currentTeam) {
    const previousTeamPlayersCount = await UserModel.countDocuments({
      currentTeam: user.currentTeam,
      role: { $in: [Role.PLAYER, null] },
      public: { $ne: false },
    })

    await TeamModel.updateOne(
      { _id: user.currentTeam },
      { $set: { playersCount: previousTeamPlayersCount } }
    )
  }
}
