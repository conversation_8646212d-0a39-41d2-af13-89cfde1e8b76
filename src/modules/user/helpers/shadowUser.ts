import { slugifyString } from '../../../helpers/string'
import { UserModel } from '../models'
import { Role, User } from '../models/user'

import { DocumentType } from '@typegoose/typegoose'

export const findShadowUser = async (user: User): Promise<DocumentType<User> | null> => {
  const year = user.dateOfBirth ? new Date(user.dateOfBirth).getFullYear() : undefined

  const query = {
    role: Role.SHADOW_PLAYER,
    country: user.country,
    nonUniqueSlug: slugifyString(user.name),
    $or: [
      {
        $expr: {
          $eq: [{ $year: '$dateOfBirth' }, year],
        },
      },
      {
        possibleYearsOfBirth: { $in: [year] },
      },
    ],
  }

  let shadowUser = await UserModel.findOne(query)

  // if we cannot find a match, let's try without country
  if (!shadowUser) {
    delete query.country
    shadowUser = await UserModel.findOne(query)
  }

  return shadowUser
}
