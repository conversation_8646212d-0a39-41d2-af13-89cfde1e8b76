import moment from 'moment'

import { Role } from '../models/user'
import { ViewAggregationType } from '../models/userViewsAggregated'
import { Period, TopViewedFilterType, TopViewedUsersFilter } from '../types/user'

import { getBirthYearLimitFor18 } from './birthYearLimit'

export const getTopViewedAthletesFilters = (
  period: Period,
  filter?: TopViewedUsersFilter
): object => {
  const limitYear = getBirthYearLimitFor18()

  if (period === Period.ALL_TIME) {
    const filters = {
      role: { $in: [Role.PLAYER, null] },
      ...(filter?.country ? { country: filter.country } : {}),
      ...(filter?.yearOfBirth
        ? filter.yearOfBirth >= limitYear
          ? {
              dateOfBirth: {
                $gte: new Date(`${filter.yearOfBirth}-01-01`), // Start of the year
                $lte: new Date(`${filter.yearOfBirth}-12-31`), // End of the year
              },
            }
          : {
              dateOfBirth: {
                $gte: new Date(`${limitYear}-01-01`),
              },
            }
        : {}),
      public: { $ne: false },
      ...(filter?.type === TopViewedFilterType.NEW
        ? { createdAt: { $gte: moment().subtract(14, 'days').toDate() } }
        : {}),
    }

    return filters
  } else if (period === Period.MONTHLY) {
    const match: {
      date?: Date
      type?: ViewAggregationType
      userCountry?: string
      userYearOfBirth?: number | { $gte: number } | { $lte: number }
    } = {
      date: moment().utc().startOf('month').toDate(),
      type: ViewAggregationType.MONTHLY,
    }

    if (filter?.country) {
      match.userCountry = filter.country
    }

    if (filter?.yearOfBirth) {
      if (filter.yearOfBirth >= limitYear) {
        match.userYearOfBirth = filter.yearOfBirth
      } else {
        match.userYearOfBirth = { $lte: new Date(`${limitYear}-01-01`).getFullYear() }
      }
    }

    return match
  }

  return {}
}
