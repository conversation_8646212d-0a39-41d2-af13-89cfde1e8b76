import Stripe from 'stripe'
import { Field, ID, ObjectType } from 'type-graphql'

@ObjectType()
export class StripeAddress {
  @Field({ nullable: true })
  public city?: string

  @Field({ nullable: true })
  public line1?: string

  @Field({ nullable: true })
  public line2?: string

  @Field({ nullable: true })
  public postalCode?: string

  @Field({ nullable: true })
  public state?: string
}

@ObjectType()
export class StripeAccount {
  @Field((_type) => ID, { name: 'id' })
  readonly id: string

  @Field({ nullable: true })
  public firstname?: string

  @Field({ nullable: true })
  public lastname?: string

  @Field({ nullable: true })
  public dob?: Date

  @Field(() => StripeAddress, { nullable: true })
  public address?: StripeAddress

  @Field({ nullable: true })
  public currency?: string

  @Field({ nullable: true })
  public phone?: string

  @Field({ nullable: true })
  public email?: string

  @Field({ nullable: true })
  public country?: string

  @Field({ nullable: true })
  public payoutsEnabled?: boolean

  @Field({ nullable: true })
  public paymentsEnabled?: boolean

  @Field({ nullable: true })
  public ssnLast4Provided?: boolean

  @Field({ nullable: true })
  public jobTitle?: string
  // ip: string,
  // userAgent: string

  public requirements?: Stripe.Account['requirements']
}

@ObjectType()
export class StripeBankAccount {
  @Field({ nullable: true })
  public country?: string

  @Field({ nullable: true })
  public currency?: string

  @Field({ nullable: true })
  public firstname?: string

  @Field({ nullable: true })
  public lastname?: string
}

@ObjectType()
export class StripeExternalAccount {
  @Field((_type) => ID, { name: 'id' })
  readonly id: string

  @Field(() => StripeBankAccount, { nullable: true })
  public bankAccount?: StripeBankAccount

  @Field({ nullable: true })
  public routingNumber?: string

  @Field({ nullable: true })
  public accountNumber?: string

  @Field({ nullable: true })
  public status?: string
}
