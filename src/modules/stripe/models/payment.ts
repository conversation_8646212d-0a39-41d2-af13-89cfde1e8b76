import { Field, ObjectType } from 'type-graphql'

@ObjectType()
export class PaymentSheet {
  @Field(() => String, { nullable: true })
  public paymentIntent?: string | null

  @Field(() => String, { nullable: true })
  public ephemeralKey?: string | null

  @Field(() => String, { nullable: true })
  public customerId?: string | null

  @Field(() => String, { nullable: true })
  public publishableKey?: string | null

  @Field(() => String, { nullable: true })
  public accountId?: string | null
}
