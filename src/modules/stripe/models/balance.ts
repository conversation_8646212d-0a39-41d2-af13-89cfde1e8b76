import { Field, ObjectType } from 'type-graphql'

@ObjectType()
export class StripeMoney {
  @Field({ nullable: false })
  public amount: number

  @Field({ nullable: false })
  public currency: string
}

@ObjectType()
export class StripeBalance {
  @Field((_type) => StripeMoney, { nullable: false })
  public available: StripeMoney

  @Field((_type) => StripeMoney, { nullable: false })
  public pending: StripeMoney
}

@ObjectType()
export class StripeBalanceTransaction {
  @Field({ nullable: false })
  public id: string

  @Field((_type) => StripeMoney, { nullable: false })
  public payout: StripeMoney

  @Field({ nullable: false })
  public status: StripeTransactionStatus

  @Field({ nullable: false })
  public createdAt: Date
}

export enum StripeTransactionStatus {
  PENDING = 'pending',
  AVAILABLE = 'available',
}

@ObjectType()
export class StripeAccountBalance {
  @Field((_type) => StripeBalance, { nullable: false })
  public balance: StripeBalance

  @Field((_type) => [StripeBalanceTransaction], { nullable: false })
  public transactions: StripeBalanceTransaction[]
}
