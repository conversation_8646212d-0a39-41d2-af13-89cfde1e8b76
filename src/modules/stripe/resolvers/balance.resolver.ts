import { GraphQLError } from 'graphql'
import pino from 'pino'
import { Ctx, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import { StripeService } from '../../../services/stripe'
import { UserModel } from '../../user/models'
import { StripeAccountBalance, StripeTransactionStatus } from '../models/balance'

@Resolver()
@Service()
export default class StripeBalanceResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private stripe: StripeService

  @Query(() => StripeAccountBalance)
  public async stripeAccountBalance(@Ctx() ctx: ContextType): Promise<StripeAccountBalance> {
    try {
      const user = await UserModel.findById(ctx.user!.id)

      if (!user) {
        throw new GraphQLError('User not found', {
          extensions: {
            code: 'USER_NOT_FOUND',
          },
        })
      }

      if (!user.stripeAccountId) {
        return {
          balance: {
            available: {
              amount: 0,
              currency: user.boost?.currency ?? 'USD',
            },
            pending: {
              amount: 0,
              currency: user.boost?.currency ?? 'USD',
            },
          },
          transactions: [],
        }
      }

      const accountBalance = await this.stripe.getAccountBalance(user.stripeAccountId)
      return {
        balance: {
          available: accountBalance.balance.available,
          pending: accountBalance.balance.pending,
        },
        transactions: accountBalance.transactions.map((transaction) => ({
          id: transaction.id,
          status:
            transaction.status === 'pending'
              ? StripeTransactionStatus.PENDING
              : StripeTransactionStatus.AVAILABLE,
          payout: transaction.payout,
          createdAt: transaction.createdAt,
        })),
      }
    } catch (err) {
      this.logger.error('Failed to get stripe account balance.', err)

      throw new GraphQLError('Failed to get account balance. Please try again later.')
    }
  }
}
