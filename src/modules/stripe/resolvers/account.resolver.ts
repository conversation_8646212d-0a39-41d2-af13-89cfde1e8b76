import config from 'config'
import { GraphQLError } from 'graphql'
import pino from 'pino'
import { Arg, Authorized, Ctx, Mutation, Query, Resolver } from 'type-graphql'
import { Inject, Service } from 'typedi'

import { ContextType } from '../../..'
import Analytics from '../../../services/analytics'
import { SlackService } from '../../../services/slack'
import { StripeService } from '../../../services/stripe'
import { UserModel } from '../../user/models'
import { StripeAccount, StripeExternalAccount } from '../models/account'
import {
  AccountAlreadyExistsError,
  AccountDoesNotExistsError,
  CreateStripeAccountInput,
  CreateStripeAccountResponse,
  StripeAccountUserInputError,
  UpdateStripeAccountResponse,
} from '../types/account'
import {
  CreateExternalAccountInput,
  CreateStripeExternalAccountResponse,
  ExternalAccountUserInputError,
} from '../types/externalAccount'

Resolver()
@Service()
export default class StripeAccountResolver {
  @Inject('app.logger')
  private logger: pino.Logger

  @Inject()
  private stripe: StripeService

  @Inject()
  private slackService: SlackService

  @Inject('analytics.service')
  private analytics: Analytics

  @Mutation(() => CreateStripeAccountResponse)
  @Authorized()
  public async createStripeAccount(
    @Arg('input') input: CreateStripeAccountInput,
    @Ctx() ctx: ContextType
  ): Promise<CreateStripeAccountResponse> {
    const user = await UserModel.findById(ctx.user!.id)

    if (!user) {
      throw new GraphQLError('User not found', {
        extensions: {
          code: 'USER_NOT_FOUND',
        },
      })
    }

    if (user.stripeAccountId) {
      return {
        account: null,
        errors: [new AccountAlreadyExistsError('Account already exists', 'stripeAccountId')],
      }
    }

    try {
      const account = await this.stripe.createConnectAccount(
        input,
        ctx.ipAddress,
        ctx.userAgent,
        user
      )

      await UserModel.findByIdAndUpdate(ctx.user!.id, { stripeAccountId: account.id })

      await this.slackService.sendMessage(
        `New stripe registration with name: ${user.name}, <http://admin.${config.domain}/dashboard/users/form/?slug=${user.slug}|admin panel>`,
        config.slack.channels.newStripeAccount
      )

      return {
        account,
        errors: [],
      }
    } catch (err) {
      if (
        err.statusCode === 400 &&
        ((ctx.mobileClientOS === 'ios' && ctx.mobileClientVersion! > 74) ||
          (ctx.mobileClientOS === 'android' && ctx.mobileClientVersion! > 64) ||
          ctx.mobileClientVersion === 1)
      ) {
        return {
          account: null,
          errors: [new StripeAccountUserInputError(err.message, 'stripeAccountId')],
        }
      }

      throw new GraphQLError('Failed to create account. Please try again later.', {
        extensions: {
          code: 'STRIPE_ERROR',
          message: err.message,
        },
      })
    }
  }

  @Query(() => StripeAccount, { nullable: true })
  @Authorized()
  public async stripeAccount(@Ctx() ctx: ContextType): Promise<StripeAccount | null> {
    const user = await UserModel.findById(ctx.user!.id)

    if (!user) {
      throw new GraphQLError('User not found', {
        extensions: {
          code: 'USER_NOT_FOUND',
        },
      })
    }

    if (!user.stripeAccountId) {
      return null
    }

    return this.stripe.getConnectAccount(user.stripeAccountId)
  }

  // FIX: create updateStripeAccountInput - DO not reuse inputs
  @Mutation(() => UpdateStripeAccountResponse)
  @Authorized()
  public async updateStripeAccount(
    @Ctx() ctx: ContextType,
    @Arg('input') input: CreateStripeAccountInput
  ): Promise<UpdateStripeAccountResponse> {
    const user = await UserModel.findById(ctx.user!.id)

    if (!user) {
      throw new GraphQLError('User not found', {
        extensions: {
          code: 'USER_NOT_FOUND',
        },
      })
    }

    if (!user.stripeAccountId) {
      return {
        account: null,
        errors: [new AccountDoesNotExistsError('Account does not exist', 'stripeAccountId')],
      }
    }

    try {
      const account = await this.stripe.updateConnectAccount(user.stripeAccountId, input, user)

      if (account.paymentsEnabled) {
        await UserModel.updateOne({ _id: ctx.user!.id }, { paymentsEnabled: true })

        await this.analytics.updateCustomAttrs(user, {
          stripe_verified: true,
          stripe_verified_date: new Date().toISOString(),
        })
      } else {
        await UserModel.updateOne({ _id: ctx.user!.id }, { paymentsEnabled: false })

        await this.analytics.updateCustomAttrs(user, {
          stripe_verified: false,
        })
      }

      return {
        account,
        errors: [],
      }
    } catch (err) {
      if (
        err.statusCode === 400 &&
        ((ctx.mobileClientOS === 'ios' && ctx.mobileClientVersion! > 74) ||
          (ctx.mobileClientOS === 'android' && ctx.mobileClientVersion! > 64) ||
          ctx.mobileClientVersion === 1)
      ) {
        return {
          account: null,
          errors: [new StripeAccountUserInputError(err.message, 'stripeAccountId')],
        }
      }

      throw new GraphQLError('Failed to update account. Please try again later.', {
        extensions: {
          code: 'STRIPE_ERROR',
          message: err.message,
          noLog: true,
        },
      })
    }
  }

  @Mutation(() => CreateStripeExternalAccountResponse)
  @Authorized()
  public async createStripeExternalAccount(
    @Ctx() ctx: ContextType,
    @Arg('input') input: CreateExternalAccountInput
  ): Promise<CreateStripeExternalAccountResponse> {
    const user = await UserModel.findById(ctx.user!.id)

    if (!user) {
      throw new GraphQLError('User not found', {
        extensions: {
          code: 'USER_NOT_FOUND',
        },
      })
    }

    if (!user.stripeAccountId) {
      return {
        externalAccount: null,
        errors: [new AccountDoesNotExistsError('Account does not exist', 'stripeAccountId')],
      }
    }

    try {
      const externalAccount = await this.stripe.createExternalAccount(
        user.stripeAccountId,
        input.bankAccount,
        input.routingNumber,
        input.accountNumber
      )

      // if user already has external account, we should delete it first
      if (user.stripeExternalAccountId) {
        await this.stripe.setDefaultExternalAccount(user.stripeAccountId, externalAccount.id)
        await this.stripe.deleteExternalAccount(user.stripeAccountId, user.stripeExternalAccountId)
      }

      await UserModel.findByIdAndUpdate(ctx.user!.id, {
        stripeExternalAccountId: externalAccount.id,
      })

      return {
        externalAccount,
        errors: [],
      }
    } catch (err) {
      // this was added to not break client before appropriate changes on client were done
      // had to fix handling of stripe error on client first
      if (
        err.statusCode === 400 &&
        ((ctx.mobileClientOS === 'ios' && ctx.mobileClientVersion! > 74) ||
          (ctx.mobileClientOS === 'android' && ctx.mobileClientVersion! > 64) ||
          ctx.mobileClientVersion === 1)
      ) {
        return {
          externalAccount: null,
          errors: [new ExternalAccountUserInputError(err.message, 'stripeAccountId')],
        }
      }

      throw new GraphQLError('Failed to create external account. Please try again later.', {
        extensions: {
          code: 'STRIPE_ERROR',
          message: err.message,
        },
      })
    }
  }

  @Query(() => StripeExternalAccount, { nullable: true })
  @Authorized()
  public async stripeExternalAccount(
    @Ctx() ctx: ContextType
  ): Promise<StripeExternalAccount | null> {
    const user = await UserModel.findById(ctx.user!.id)

    if (!user || !user.stripeExternalAccountId || !user.stripeAccountId) {
      return null
    }

    return this.stripe.getExternalAccount(user.stripeAccountId, user.stripeExternalAccountId)
  }

  @Mutation(() => Boolean)
  @Authorized()
  public async deleteStripeExternalAccount(@Ctx() ctx: ContextType): Promise<boolean> {
    const user = await UserModel.findById(ctx.user!.id)

    if (!user) {
      throw new GraphQLError('User not found', {
        extensions: {
          code: 'USER_NOT_FOUND',
        },
      })
    }

    if (!user.stripeExternalAccountId || !user.stripeAccountId) {
      throw new AccountDoesNotExistsError(
        'External account does not exist',
        'stripeExternalAccountId'
      )
    }

    await this.stripe.deleteExternalAccount(user.stripeAccountId, user.stripeExternalAccountId)

    return true
  }
}
