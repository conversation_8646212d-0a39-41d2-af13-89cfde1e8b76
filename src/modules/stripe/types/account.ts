import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'
import { StripeAccount } from '../models/account'

@ObjectType()
export class CreateStripeAccountResponse {
  @Field(() => StripeAccount, { nullable: true })
  public account?: StripeAccount | null

  @Field((_type) => [CreateStripeAccountError])
  public errors: Array<typeof CreateStripeAccountError>
}

@ObjectType()
export class UpdateStripeAccountResponse {
  @Field(() => StripeAccount, { nullable: true })
  public account: StripeAccount | null

  @Field((_type) => [UpdateStripeAccountError])
  public errors: Array<typeof UpdateStripeAccountError>
}

@ObjectType({ implements: IUserError })
export class AccountAlreadyExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

const UpdateStripeAccountError = createUnionType({
  name: 'UpdateStripeAccountError',
  types: () => [AccountDoesNotExistsError, StripeAccountUserInputError] as const,
})

@ObjectType({ implements: IUserError })
export class AccountDoesNotExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@ObjectType({ implements: IUserError })
export class StripeAccountUserInputError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

const CreateStripeAccountError = createUnionType({
  name: 'CreateStripeAccountError',
  types: () => [AccountAlreadyExistsError, StripeAccountUserInputError] as const,
})

@InputType()
export class StripeAddressInput {
  @Field({ nullable: true })
  public city?: string

  @Field({ nullable: true })
  public line1?: string

  @Field({ nullable: true })
  public line2?: string

  @Field({ nullable: true })
  public postalCode?: string

  @Field({ nullable: true })
  public state?: string
}

@InputType()
export class StripeAccountVerificationDocument {
  @Field({ nullable: true })
  public front?: string

  @Field({ nullable: true })
  public back?: string

  @Field({ nullable: true })
  public selfie?: string
}
@InputType()
export class StripeAccountVerification {
  @Field(() => StripeAccountVerificationDocument, { nullable: true })
  public document?: StripeAccountVerificationDocument
}

@InputType()
export class CreateStripeAccountInput {
  @Field({ nullable: true })
  public firstname?: string

  @Field({ nullable: true })
  public lastname?: string

  @Field({ nullable: true })
  public email?: string

  @Field({ nullable: true })
  public dob?: Date

  @Field(() => StripeAddressInput, { nullable: true })
  public address?: StripeAddressInput

  @Field({ nullable: true })
  public currency?: string

  @Field({ nullable: true })
  public phone?: string

  @Field({ nullable: true })
  public country?: string

  @Field({ nullable: true })
  public ssnLast4?: string

  @Field({ nullable: true })
  public jobTitle?: string

  @Field(() => StripeAccountVerification, { nullable: true })
  public verification?: StripeAccountVerification
}
