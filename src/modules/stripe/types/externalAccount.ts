import { createUnionType, Field, InputType, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'
import { StripeExternalAccount } from '../models/account'

import { AccountDoesNotExistsError } from './account'

@ObjectType()
export class CreateStripeExternalAccountResponse {
  @Field(() => StripeExternalAccount, { nullable: true })
  public externalAccount: StripeExternalAccount | null

  @Field((_type) => [CreateStripeExternalAccountError])
  public errors: Array<typeof CreateStripeExternalAccountError>
}

@ObjectType({ implements: IUserError })
export class ExternalAccountAlreadyExistsError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

@ObjectType({ implements: IUserError })
export class ExternalAccountUserInputError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

const CreateStripeExternalAccountError = createUnionType({
  name: 'CreateStripeExternalAccountError',
  types: () =>
    [
      ExternalAccountAlreadyExistsError,
      ExternalAccountUserInputError,
      AccountDoesNotExistsError,
    ] as const,
})

@InputType()
export class StripeBankAccountInput {
  @Field()
  public country: string

  @Field()
  public currency: string

  @Field()
  public firstname: string

  @Field()
  public lastname: string
}

@InputType()
export class CreateExternalAccountInput {
  @Field(() => StripeBankAccountInput, { nullable: false })
  public bankAccount: StripeBankAccountInput

  @Field({ nullable: true })
  public routingNumber: string

  @Field({ nullable: false })
  public accountNumber: string
}
