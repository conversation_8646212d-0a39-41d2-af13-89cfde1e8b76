import { Types } from 'mongoose'
import { createUnionType, Field, InputType, Int, ObjectType } from 'type-graphql'

import { IUserError } from '../../../types/general'
import { PaymentSheet } from '../models/payment'

@ObjectType()
export class CreatePaymentSheetResponse {
  @Field({ nullable: true })
  public paymentSheet: PaymentSheet

  @Field((_type) => [CreatePaymentSheetError])
  public errors: Array<typeof CreatePaymentSheetError>
}

@ObjectType({ implements: IUserError })
export class StripeNotConnectedError extends IUserError implements IUserError {
  @Field()
  public message: string

  @Field()
  public path: string
}

const CreatePaymentSheetError = createUnionType({
  name: 'CreatePaymentSheetError',
  types: () => [StripeNotConnectedError] as const,
})

@InputType()
export class CreatePaymentSheetInput {
  @Field(() => Int, { nullable: false })
  public amount: number

  @Field({ nullable: false })
  public currency: string

  @Field({ nullable: false })
  public userId: Types.ObjectId
}
