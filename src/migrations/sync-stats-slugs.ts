import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { slugifyString } from '../helpers/string'
import { OrganizationModel } from '../modules/stats/models/organization'
import { TeamModel } from '../modules/stats/models/team'

const fix = async () => {
  await connectToMongoDB()

  const teams = await TeamModel.find({ shortNameSlug: { $exists: false } })
  const orgs = await OrganizationModel.find({ slug: { $exists: false } })

  for (const team of teams) {
    if (team.shortName) {
      team.shortNameSlug = slugifyString(team.shortName)
      await team.save()
      console.log(`Updated team ${team.name} with shortNameSlug ${team.shortNameSlug}`)
    } else {
      console.log(`Team ${team.name} has no shortName`)
    }
  }

  for (const org of orgs) {
    org.slug = slugifyString(org.name)
    await org.save()
    console.log(`Updated organization ${org.name} with slug ${org.slug}`)
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
