import moment from 'moment'
import { Types } from 'mongoose'

import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserViewModel } from '../modules/user/models/userViews'
import {
  UserViewAggregated,
  UserViewAggregatedModel,
  ViewAggregationType,
} from '../modules/user/models/userViewsAggregated'

const aggregateViews = async () => {
  await connectToMongoDB()

  const limit = 500
  let offset = 0
  let views = await UserViewModel.find({}, undefined, { limit })

  const aggregatedViews = new Map<string, UserViewAggregated>()
  while (views.length > 0) {
    for (const view of views) {
      const weekStart = moment(view.createdAt).utc().startOf('week').toDate()
      const monthStart = moment(view.createdAt).utc().startOf('month').toDate()

      const weekKey = `${view.user._id.toString()}-${weekStart}`
      const monthKey = `${view.user._id.toString()}-${monthStart}`

      const weeklyAggregation = aggregatedViews.get(weekKey)
      if (weeklyAggregation) {
        weeklyAggregation.totalViews += 1
      } else {
        aggregatedViews.set(weekKey, {
          _id: new Types.ObjectId(),
          user: view.user,
          totalViews: 1,
          date: weekStart,
          userYearOfBirth: view.userYearOfBirth!,
          userCountry: view.userCountry!,
          type: ViewAggregationType.WEEKLY,
        })
      }

      const monthlyAggregation = aggregatedViews.get(monthKey)
      if (monthlyAggregation) {
        monthlyAggregation.totalViews += 1
      } else {
        aggregatedViews.set(monthKey, {
          _id: new Types.ObjectId(),
          user: view.user,
          totalViews: 1,
          date: monthStart,
          userYearOfBirth: view.userYearOfBirth!,
          userCountry: view.userCountry!,
          type: ViewAggregationType.MONTHLY,
        })
      }
    }

    offset += limit
    views = await UserViewModel.find({}, undefined, { skip: offset, limit })
    console.log(offset)
  }

  console.log('Aggregating views...', aggregatedViews.size)

  // clear all aggregated views
  await UserViewAggregatedModel.deleteMany({})

  // insert all rows into db with chunks of 1000 rows
  const chunkSize = 1000
  const aggregatedViewsArray = Array.from(aggregatedViews.values())
  for (let i = 0; i < aggregatedViewsArray.length; i += chunkSize) {
    await UserViewAggregatedModel.insertMany(aggregatedViewsArray.slice(i, i + chunkSize))
    console.log('Inserted', i + chunkSize, ' aggregated views')
  }

  await closeMongoDBConnection()
  process.exit(0)
}

aggregateViews()
