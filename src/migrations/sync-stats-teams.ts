import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserStats, UserStatsModel } from '../modules/stats/models/userStats'

const fix = async () => {
  await connectToMongoDB()

  const statsWithNonExistingTeam = await UserStatsModel.aggregate<UserStats>([
    {
      $lookup: {
        from: 'teams',
        localField: 'team',
        foreignField: '_id',
        as: 'teamInfo',
      },
    },
    {
      $match: {
        team: { $exists: true, $ne: null }, // Ensure `team` field is set in `userStats`
        teamInfo: { $size: 0 }, // Filter where the team does not exist in `teams`
      },
    },
    {
      $project: {
        teamInfo: 0, // Exclude the `teamInfo` field from the final output
      },
    },
  ])

  const statsWithNonExistingLeague = await UserStatsModel.aggregate<UserStats>([
    {
      $lookup: {
        from: 'leagues',
        localField: 'league',
        foreignField: '_id',
        as: 'leagueInfo',
      },
    },
    {
      $match: {
        league: { $exists: true, $ne: null }, // Ensure `league` field is set in `userStats`
        leagueInfo: { $size: 0 }, // Filter where the league does not exist in `leagues`
      },
    },
    {
      $project: {
        leagueInfo: 0, // Exclude the `leagueInfo` field from the final output
      },
    },
  ])

  for (const stat of statsWithNonExistingTeam) {
    console.log(`Removing team from userStats: ${stat._id} for user ${stat.user}`)
    await UserStatsModel.updateOne(
      { _id: stat._id },
      {
        $set: {
          team: null, // Remove the `team` field from the `userStats` document
        },
      }
    )
  }

  for (const stat of statsWithNonExistingLeague) {
    console.log(`Removing league from userStats: ${stat._id} for user ${stat.user}`)
    await UserStatsModel.updateOne(
      { _id: stat._id },
      {
        $set: {
          league: null, // Remove the `league` field from the `userStats` document
        },
      }
    )
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
