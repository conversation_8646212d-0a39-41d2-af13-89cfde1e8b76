// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
// TODO remove after tested on staging

import { client } from '../../tests/utils/graphql'
import { GameStats, GameStatsModel } from '../modules/stats/models/gameStats/gameStats'
import {
  addIceHockeyGameActivity,
  removeIceHockeyGameActivity,
} from '../services/helpers/streamFeed'

const main = async () => {
  const liberecTeamId = '663b8ba8f8b387f1507d7d40'

  const games: GameStats[] = await GameStatsModel.find({
    $or: [{ awayTeam: liberecTeamId }, { homeTeam: liberecTeamId }],
  })

  for (const game of games) {
    await removeIceHockeyGameActivity(game)
    console.log('=====================================GAME========================================')

    console.log('game id:', game?._id.toString())
    console.log('game teams:', game?.awayTeam, game?.homeTeam)

    try {
      const gameDetail = await client().game({
        gameId: game._id,
      })

      console.log('=====================================GAME=DETAIL===============================')

      console.log('gameDetail id', gameDetail.game?.id)
      console.log('gameDetail teams', gameDetail.game?.awayTeam, gameDetail.game?.homeTeam)
    } catch {
      console.log('=====================================GAME=DETAIL=ERROR ========================')
      // console.error('gameDetail error', error)
    }

    console.log('=================================================================================')
    console.log('=================================================================================')

    const newActivity = await addIceHockeyGameActivity(game as GameStats)

    console.log('newActivity', newActivity)

    const updatedGame = await GameStatsModel.updateOne(
      { _id: game._id },
      {
        streamFeedActivityId: newActivity?.id,
      }
    )

    console.log('updatedGame', updatedGame)
  }
}

main()
