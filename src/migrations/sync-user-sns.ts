import 'reflect-metadata'
import config from 'config'
import pino, { Logger } from 'pino'
import Container from 'typedi'

import { wrapLogger } from '../helpers/logger'
import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'
import { SNSService } from '../services/sns'

import * as AWS from '@aws-sdk/client-sns'

const fix = async () => {
  await connectToMongoDB()

  const snsClient = new AWS.SNS({ region: config.aws.region })
  Container.set({ id: 'sns.client', factory: () => snsClient })

  const logger = wrapLogger(pino()) as Logger
  Container.set({ id: 'app.logger', factory: () => logger })

  const snsService = Container.get(SNSService)

  const users = await UserModel.find({
    registeredDevices: { $exists: true, $ne: [] },
    'registeredDevices.arn': { $eq: null },
  })

  for (const u of users) {
    const registeredDevices = [...(u.registeredDevices ?? [])]
    console.log(`User ${u.email} ${u.name}`)
    for (const device of registeredDevices) {
      console.log(`Registered device: ${device.platform}: ${device.arn}`)
      try {
        const response = await snsService.registerDeviceToken(device.token, device.platform)

        if (response.platformArn) {
          device.arn = response.platformArn
        }

        if (response.platformSandboxArn) {
          u.registeredDevices?.push({
            platform: device.platform,
            token: device.token,
            arn: response.platformSandboxArn,
          })
        }
      } catch (err) {
        console.log(err.message)
        if (err.message.includes('already exists with the same Token, but different attributes')) {
          // remove this arn from users registeredDevices
          // u.registeredDevices = u.registeredDevices.filter(d => d.token !== device.token)
        } else {
          console.error(err)
        }
      }
    }

    await u.save()
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
