import 'reflect-metadata'
import { StreamClient } from 'getstream'
import { Types } from 'mongoose'
import Container from 'typedi'

import { connectToMongoDB } from '../helpers/mongoose.connection'
import { modelFind } from '../modules/modules.helper'
import { UserModel, WallVideoModel } from '../modules/user/models'
import { Role } from '../modules/user/models/user'
import { FEED_NAMES } from '../modules/user/types/streamFeed'

import { FeedActivityVerb } from './../services/helpers/streamFeed'

const addActivities = async () => {
  await connectToMongoDB()

  const users = await modelFind(
    UserModel,
    {
      role: { $in: [Role.PLAYER, null] },
      public: { $ne: false },
      slug: { $ne: null },
    },
    { _id: 1 },
    null
  )

  const userIds = (users as { _id: Types.ObjectId }[]).map(({ _id }) => _id.toString())

  const videos = await WallVideoModel.find({
    user: { $in: userIds },
  })

  const streamFeed = Container.get('streamFeed.client') as StreamClient

  const activityResponses = []

  for (const video of videos) {
    const userId = video.user.toString()
    try {
      const userFeed = streamFeed.feed(FEED_NAMES.USER, userId)
      const activityResponse = await userFeed.addActivity({
        actor: userId,
        verb: FeedActivityVerb.ADD,
        object: 'wallVideo',
        foreign_id: video.id,
      })

      if (activityResponse?.id) {
        await WallVideoModel.findOneAndUpdate(
          { _id: video._id },
          {
            streamFeedActivityId: activityResponse.id,
          }
        )
      }

      activityResponses.push(activityResponse)
    } catch (error) {
      console.error(error)
    }
  }

  console.log(
    'num of videos, num of activities put in streamFeed',
    videos.length,
    activityResponses.length
  )

  process.exit(0)
}

addActivities()
