import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'

const sync = async () => {
  await connectToMongoDB()

  const usersWithoutName = await UserModel.find({ name: { $exists: false } })

  for (const user of usersWithoutName) {
    await user.save()
  }

  await closeMongoDBConnection()
  process.exit(0)
}

sync()
