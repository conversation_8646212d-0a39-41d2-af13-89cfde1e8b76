import config from 'config'
import Stripe from 'stripe'

import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'

const fix = async () => {
  await connectToMongoDB()
  const stripe = new Stripe(config.stripe.secretKey)

  const user = await UserModel.findOne({
    slug: 'damien-stodulka',
  })

  if (!user?.stripeAccountId) {
    console.error('User not found')
    process.exit(1)
  }

  await stripe.accounts.update(user.stripeAccountId, {
    default_currency: 'EUR',
  })

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
