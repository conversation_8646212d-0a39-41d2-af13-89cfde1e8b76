import 'reflect-metadata'
import { parse } from 'csv-parse/sync'
import fs from 'fs'

import { connectToMongoDB } from '../helpers/mongoose.connection'
import { AgencyModel } from '../modules/user/models/agency'

const importAgencies = async () => {
  await connectToMongoDB()

  // load csv file from disk
  const csvFile = fs.readFileSync('agencies.csv', 'utf8')
  const agencies = parse(csvFile, {
    columns: true,
    skip_empty_lines: true,
  })

  for (const agency of agencies) {
    const agencyId = agency['GRAET ADMIN AGENCY LINK'].split('/').pop().replace('?id=', '')

    if (agencyId && agencyId !== 'new') {
      const agencyModel = await AgencyModel.findOne({ _id: agencyId })

      if (agencyModel) {
        if (agency['Following on IG']) {
          agencyModel.instagramFollowers = parseInt(agency['Following on IG'])
        } else {
          agencyModel.instagramFollowers = 0
        }

        if (agency['# of Cilents']) {
          agencyModel.externalNumberOfClients = parseInt(agency['# of Cilents'])
        } else {
          agencyModel.externalNumberOfClients = 0
        }

        agencyModel.website = agency['Agency website']
        agencyModel.epUrl = agency['EliteProspects link']
        agencyModel.phone = agency['Agency phone no.']
        agencyModel.email = agency['Agency mail']
        agencyModel.address = agency['Agency adress']
        agencyModel.instagramUrl = agency['Instagram ']
        agencyModel.facebookUrl = agency['Facebook']
        agencyModel.twitterUrl = agency['Twitter']
        agencyModel.linkedinUrl = agency['Linkedin']
        agencyModel.tiktokUrl = agency['TikTok']

        await agencyModel.save()
      }
    }
  }

  process.exit(0)
}

importAgencies()
