import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { AgencyModel, SportCategory } from '../modules/user/models/agency'

const fix = async () => {
  await connectToMongoDB()

  const agencies = await AgencyModel.find()

  for (const agency of agencies) {
    agency.categories = [SportCategory.HOCKEY]

    await agency.save()
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
