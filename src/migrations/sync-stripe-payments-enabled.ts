import config from 'config'
import Stripe from 'stripe'

import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'

const fix = async () => {
  await connectToMongoDB()
  const stripe = new Stripe(config.stripe.secretKey)

  console.log(config.get('mongo.url'))
  const users = await UserModel.find({ paymentsEnabled: { $exists: false } })

  console.log('Total users to update: ', users.length)
  let count = users.length
  for (const user of users) {
    let paymentsEnabled = false
    if (user.stripeAccountId) {
      const account = await stripe.accounts.retrieve(user.stripeAccountId)
      paymentsEnabled = account.capabilities?.card_payments === 'active'
    }

    console.log('User: ', user.slug, ', paymentsEnabled:', paymentsEnabled)

    await UserModel.updateOne({ _id: user.id }, { paymentsEnabled })

    console.log('Users left:', --count)

    await new Promise((resolve) => setTimeout(resolve, 300))
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
