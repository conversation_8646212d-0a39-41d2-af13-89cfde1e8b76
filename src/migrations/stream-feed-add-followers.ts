import 'reflect-metadata'
import { StreamClient } from 'getstream'
import { Types } from 'mongoose'
import Container from 'typedi'

import { connectToMongoDB } from '../helpers/mongoose.connection'
import { modelFind } from '../modules/modules.helper'
import { UserModel } from '../modules/user/models'
import { Role } from '../modules/user/models/user'
import { UserSubscriptionModel } from '../modules/user/models/userSubscription'
import { FEED_NAMES } from '../modules/user/types/streamFeed'

const addFollowers = async () => {
  await connectToMongoDB()

  const users = await modelFind(
    UserModel,
    {
      role: { $in: [Role.PLAYER, null] },
      public: { $ne: false },
      slug: { $ne: null },
    },
    { _id: 1 },
    null
  )

  const userIds = (users as { _id: Types.ObjectId }[]).map(({ _id }) => _id.toString())

  const follows = await UserSubscriptionModel.find({
    user: { $in: userIds },
  })

  const streamFeed = Container.get('streamFeed.client') as StreamClient

  const followResponses = []

  for (const follow of follows) {
    try {
      const myTimeLinefeed = streamFeed.feed(FEED_NAMES.TIMELINE, follow.user.toString())
      const followResponse = await myTimeLinefeed.follow(
        FEED_NAMES.USER,
        follow.follower?.toString()
      )
      followResponses.push(followResponse)
    } catch (error) {
      console.error(error)
    }
  }

  console.log('num of follows, follows in streamFeed', follows.length, followResponses.length)

  process.exit(0)
}

addFollowers()
