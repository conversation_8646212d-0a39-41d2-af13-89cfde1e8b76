import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { LeagueModel } from '../modules/stats/models/league'

const fix = async () => {
  await connectToMongoDB()

  const leagues = await LeagueModel.find()

  for (const league of leagues) {
    league.countries = league.country ? [league.country] : []

    await league.save()
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
