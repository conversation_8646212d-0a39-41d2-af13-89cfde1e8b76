import { Game, GameModel } from '../modules/stats/models/game/game'
import { removeIceHockeyGameActivity } from '../services/helpers/streamFeed'

const main = async () => {
  const games: Game[] = await GameModel.find({
    $and: [{ streamFeedActivityId: { $exists: true } }, { streamFeedActivityId: { $ne: null } }],
  })

  console.log('games.length', games?.length)

  let updateGames = []

  for (const game of games) {
    console.log('game.id', game._id.toString())
    console.log('game.streamFeedActivityId', game.streamFeedActivityId)
    const removedActivityResponse = await removeIceHockeyGameActivity(game)
    console.log('removedActivityResponse', removedActivityResponse)

    updateGames.push({
      updateOne: {
        filter: { _id: game._id },
        update: {
          streamFeedActivityId: null,
        },
        upsert: false,
      },
    })

    if (updateGames?.length > 10) {
      const bulkGamesUpadate = await GameModel.bulkWrite(updateGames)
      console.log('bulkGamesUpadate', bulkGamesUpadate)

      updateGames = []
    }
  }

  if (updateGames?.length > 0) {
    const bulkGamesUpadate = await GameModel.bulkWrite(updateGames)
    console.log('last bulkGamesUpadate', bulkGamesUpadate)
  }
}

main()
