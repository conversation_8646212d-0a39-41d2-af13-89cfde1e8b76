import dayjs from 'dayjs'

import { connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'
import { User } from '../modules/user/models/user'
import { UserView, UserViewModel } from '../modules/user/models/userViews'

const distributeViews = async (user: User) => {
  const totalViews = user.userStats?.views ?? 0
  const createdAt = user.createdAt
  const daysSinceCreation = dayjs().diff(createdAt, 'day')
  const records = []

  if (daysSinceCreation > 0) {
    const viewsPerDay = Math.floor(totalViews / daysSinceCreation)
    const remainderViews = totalViews % daysSinceCreation

    for (let i = 0; i < daysSinceCreation; i++) {
      const date = dayjs(createdAt).add(i, 'day')
      const viewDate = new Date(date.format('YYYY-MM-DD'))
      const views = i === daysSinceCreation - 1 ? viewsPerDay + remainderViews : viewsPerDay
      for (let j = 0; j < views; j++) {
        records.push({
          user: user._id,
          createdAt: viewDate,
        })
        if (records.length >= 100) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          await addRecords(records as any)
          records.length = 0

          await sleep(2000)
        }
      }
    }

    if (records.length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await addRecords(records as any)
      await sleep(2000)
    }
  }
}

const addRecords = async (records: UserView) => {
  try {
    await UserViewModel.insertMany(records)
  } catch (err) {
    console.log(err)
  }
}

const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

const syncViews = async () => {
  await connectToMongoDB()
  const users = await UserModel.find({ 'userStats.views': { $gt: 0 } })

  for (const user of users) {
    await distributeViews(user)
  }

  process.exit(0)
}

syncViews()
