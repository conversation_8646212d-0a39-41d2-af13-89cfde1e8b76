import 'reflect-metadata'
import config from 'config'
import { connect as streamConnect } from 'getstream'
import { Activity, StreamClient } from 'getstream'
import { Types } from 'mongoose'
import Container from 'typedi'

import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { modelFind } from '../modules/modules.helper'
import { UserModel, WallVideoModel } from '../modules/user/models'
import { Role, User } from '../modules/user/models/user'
import { FEED_NAMES } from '../modules/user/types/streamFeed'

const addActivityIds = async () => {
  await connectToMongoDB()

  let streamFeed: StreamClient | null = null
  try {
    streamFeed = Container.get('streamFeed.client') as StreamClient
  } catch {
    streamFeed = streamConnect(
      config.getstream.apiKey,
      config.getstream.apiSecret,
      config.getstream.appId
    )
  }

  const users = await modelFind(
    UserModel,
    {
      role: { $in: [Role.PLAYER, null] },
      public: { $ne: false },
      slug: { $ne: null },
    },
    { _id: 1 },
    null
  )

  const bulkWriteResponses = []

  if (!streamFeed) {
    console.error('Stream feed client not initialized')
    process.exit(0)
  }

  for (const user of users) {
    try {
      const userTimeLineFeed = streamFeed?.feed(FEED_NAMES.USER, (user as User)._id.toString())
      const updateUserVideosPayloads = []

      const userActivitiesResponse = await userTimeLineFeed.get({ limit: 120 })

      if (userActivitiesResponse?.results?.length > 0) {
        for (const activity of userActivitiesResponse.results) {
          updateUserVideosPayloads.push({
            updateOne: {
              filter: { _id: new Types.ObjectId((activity as Activity).foreign_id) },
              update: {
                streamFeedActivityId: activity.id,
              },
              upsert: false,
            },
          })

          const bulkWriteResponse = await WallVideoModel.bulkWrite(updateUserVideosPayloads)

          bulkWriteResponses.push(bulkWriteResponse)
        }
      }
    } catch (error) {
      console.error(error)
    }
  }

  console.log(
    'num of users, num of bulk updates on videos',
    users.length,
    bulkWriteResponses.length
  )

  await closeMongoDBConnection()
  process.exit(0)
}

addActivityIds()
