import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'
import { UserViewModel } from '../modules/user/models/userViews'

const fix = async () => {
  await connectToMongoDB()

  const userViews = await UserViewModel.find({ userCountry: { $exists: false } })

  console.log(`Found ${userViews.length} user views`)
  for (const userView of userViews) {
    const user = await UserModel.findById(userView.user)

    if (!user) {
      console.log(`User ${userView.user} has not been found`)
      continue
    }

    userView.userCountry = user.country
    userView.userYearOfBirth = user.dateOfBirth?.getFullYear()
    await userView.save()
    await new Promise((resolve) => setTimeout(resolve, 100))

    // after 100 updates show console log
    if (userViews.indexOf(userView) % 100 === 0) {
      console.log(`Updated ${userViews.indexOf(userView)} user views`)
    }
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
