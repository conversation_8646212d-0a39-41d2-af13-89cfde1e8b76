import config from 'config'

import { connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'
import Analytics from '../services/analytics'

import { flush, init } from '@amplitude/analytics-node'

const fix = async () => {
  await connectToMongoDB()
  const analytics = new Analytics()

  const user = await UserModel.find()
  init(config.amplitude.apiKey)

  // updateUserAttrs with analytics for each user using Analytics service
  let counter = 0
  for (const u of user) {
    await analytics.updateUserAttrs(u)
    await analytics.trackEvent('User Updated', {}, u.id)
    await new Promise((resolve) => setTimeout(resolve, 100))

    if (counter % 100 === 0) {
      await flush().promise
    }

    console.log(`User ${counter} updated`)
    counter++
  }

  await flush().promise
  await new Promise((resolve) => setTimeout(resolve, 20000))

  process.exit(0)
}

fix()
