import config from 'config'
import Container from 'typedi'

import { connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'
import { MediaConverter } from '../services/mediaConverter'

import * as AWSMediaConverter from '@aws-sdk/client-mediaconvert'
import { S3Client } from '@aws-sdk/client-s3'

const createThumbnails = async () => {
  await connectToMongoDB()

  const s3Client = new S3Client({ region: config.aws.region })
  Container.set({ id: 's3.client', factory: () => s3Client })

  const mediaConverterClient = new AWSMediaConverter.MediaConvertClient({
    region: config.aws.region,
  })
  Container.set({ id: 'mediaConverter.client', factory: () => mediaConverterClient })

  const mediaConverter = Container.get(MediaConverter)

  const users = await UserModel.find(
    { avatar: { $exists: true }, avatars: { $exists: false } },
    {},
    { limit: 100 }
  )

  // updateUserAttrs with analytics for each user using Analytics service
  let counter = 0
  for (const u of users) {
    await new Promise((resolve) => setTimeout(resolve, 100))

    if (!u.avatar) {
      continue
    }

    const avatars = await mediaConverter.createImageThumbnails(u.avatar)
    await UserModel.updateOne(
      { _id: u.id },
      {
        avatars,
      }
    )

    console.log(`${counter}. user ${u.slug} updated`)
    counter++
  }

  process.exit(0)
}

createThumbnails()
