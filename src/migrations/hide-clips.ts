import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel, WallVideoModel } from '../modules/user/models'

const fix = async () => {
  await connectToMongoDB()

  const nonPublicUsers = await UserModel.find({ public: false })

  console.log(`Found ${nonPublicUsers.length} hidden users`)
  for (const user of nonPublicUsers) {
    await WallVideoModel.updateMany({ user: user._id }, { $set: { hidden: false } })
  }

  await closeMongoDBConnection()
  process.exit(0)
}

fix()
