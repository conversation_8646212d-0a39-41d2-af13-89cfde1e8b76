import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'

const fix = async () => {
  try {
    // Connect to MongoDB
    console.log('Connecting to MongoDB...')
    const connection = await connectToMongoDB()
    console.log('Connected to MongoDB')

    // Get direct access to the MongoDB collection to bypass Mongoose schema validation
    const db = connection.db
    const collection = db?.collection('gamestats')

    // Count documents with goalsAgainstPercentage field
    const totalDocuments = await collection?.countDocuments({
      $or: [
        { 'homeTeamRoster.goalsAgainstPercentage': { $exists: true } },
        { 'awayTeamRoster.goalsAgainstPercentage': { $exists: true } },
      ],
    })

    console.log(`Found ${totalDocuments} GameStats documents with goalsAgainstPercentage field`)

    // Use MongoDB's aggregation pipeline to update all documents in a single operation
    const result = await collection?.updateMany(
      {
        $or: [
          { 'homeTeamRoster.goalsAgainstPercentage': { $exists: true } },
          { 'awayTeamRoster.goalsAgainstPercentage': { $exists: true } },
        ],
      },
      [
        // Stage 1: Add svp field with value from goalsAgainstPercentage
        {
          $addFields: {
            homeTeamRoster: {
              $map: {
                input: '$homeTeamRoster',
                as: 'player',
                in: {
                  $mergeObjects: [
                    '$$player',
                    {
                      svp: { $ifNull: ['$$player.goalsAgainstPercentage', '$$player.svp'] },
                    },
                  ],
                },
              },
            },
            awayTeamRoster: {
              $map: {
                input: '$awayTeamRoster',
                as: 'player',
                in: {
                  $mergeObjects: [
                    '$$player',
                    {
                      svp: { $ifNull: ['$$player.goalsAgainstPercentage', '$$player.svp'] },
                    },
                  ],
                },
              },
            },
          },
        },
        // Stage 2: Remove goalsAgainstPercentage field
        {
          $set: {
            homeTeamRoster: {
              $map: {
                input: '$homeTeamRoster',
                as: 'player',
                in: {
                  $arrayToObject: {
                    $filter: {
                      input: { $objectToArray: '$$player' },
                      as: 'field',
                      cond: { $ne: ['$$field.k', 'goalsAgainstPercentage'] },
                    },
                  },
                },
              },
            },
            awayTeamRoster: {
              $map: {
                input: '$awayTeamRoster',
                as: 'player',
                in: {
                  $arrayToObject: {
                    $filter: {
                      input: { $objectToArray: '$$player' },
                      as: 'field',
                      cond: { $ne: ['$$field.k', 'goalsAgainstPercentage'] },
                    },
                  },
                },
              },
            },
          },
        },
      ]
    )

    console.log(`Migration completed. Modified ${result?.modifiedCount} documents.`)
    process.exit(0)
  } catch (error) {
    console.error('Error during migration:', error)
    process.exit(1)
  } finally {
    // Close MongoDB connection
    await closeMongoDBConnection()
    console.log('Disconnected from MongoDB')
  }
}

fix()
