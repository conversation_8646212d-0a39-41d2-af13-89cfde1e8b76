import config from 'config'

import * as Sentry from '@sentry/node'

const environment = process.env.NODE_ENV ?? 'dev'

Sentry.init({
  dsn: config.sentry.dsn,
  environment,
  enabled: ['staging', 'production'].includes(environment),
  integrations: [
    Sentry.graphqlIntegration(),
    Sentry.dataloaderIntegration(),
    Sentry.mongooseIntegration(),
  ],

  tracesSampler: (samplingContext) => {
    if (
      samplingContext.name.includes('health') ||
      samplingContext.normalizedRequest?.headers?.['x-no-trace'] === 'true' ||
      samplingContext.name.includes('graphql') // this is initial request on server
    ) {
      return 0
    }

    return environment === 'production' ? 0.01 : 0.2
  },
})
