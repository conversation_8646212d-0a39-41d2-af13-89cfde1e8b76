import { ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator'
import validator from 'validator'

@ValidatorConstraint({ name: 'AgeValidator', async: false })
export class AgeValidator implements ValidatorConstraintInterface {
  validate(dateOfBirth: Date) {
    const thirteenYearsAgo = new Date()
    thirteenYearsAgo.setFullYear(thirteenYearsAgo.getFullYear() - 13)
    return dateOfBirth <= thirteenYearsAgo
  }

  defaultMessage() {
    return 'You must be at least 13 years old.'
  }
}

@ValidatorConstraint({ name: 'isCurrencyCode', async: false })
export class IsCurrencyCode implements ValidatorConstraintInterface {
  validate(currency: string) {
    const validCurrencies = ['USD', 'EUR', 'CAD']
    return validCurrencies.includes(currency)
  }

  defaultMessage() {
    return 'Invalid currency code.'
  }
}

@ValidatorConstraint({ name: 'isMobilePhoneNumber', async: false })
export class isMobilePhoneNumber implements ValidatorConstraintInterface {
  validate(number: string) {
    return validator.isMobilePhone(number, 'any')
  }

  defaultMessage() {
    return 'Invalid phone number.'
  }
}
