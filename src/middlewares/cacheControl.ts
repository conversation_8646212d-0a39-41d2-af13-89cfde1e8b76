import { Directive } from 'type-graphql'

import { type CacheHint } from '@apollo/cache-control-types'

// https://stackoverflow.com/a/49725198/6676781

export type RequireAtLeastOne<T, <PERSON> extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &
  {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>
  }[Keys]

export function CacheControl({
  maxAge,
  scope,
  inheritMaxAge,
}: RequireAtLeastOne<CacheHint & { inheritMaxAge: boolean }>) {
  if (maxAge === undefined && scope === undefined && inheritMaxAge === undefined) {
    throw new Error('Missing maxAge, scope or inheritMaxAge param for @CacheControl')
  }

  let sdl = '@cacheControl('
  if (maxAge !== undefined) {
    sdl += `maxAge: ${maxAge}`
  }
  if (scope) {
    sdl += ` scope: ${scope}`
  }
  if (inheritMaxAge) {
    sdl += ` inheritMaxAge: ${inheritMaxAge}`
  }
  sdl += ')'

  return Directive(sdl)
}
