import { StreamClient } from 'getstream'
import { Types } from 'mongoose'
import Container from 'typedi'

import { connectToMongoDB } from '../helpers/mongoose.connection'
import { UserModel } from '../modules/user/models'
import { UserSubscriptionModel } from '../modules/user/models/userSubscription'
import { FEED_NAMES } from '../modules/user/types/streamFeed'

async function removeAllFollowingsFromUser(userId: string): Promise<void> {
  try {
    if (!Types.ObjectId.isValid(userId)) {
      throw new Error(`Invalid userId: ${userId}`)
    }

    await connectToMongoDB()

    const user = await UserModel.findById(userId)
    if (!user) {
      throw new Error(`User with ID ${userId} not found`)
    }

    console.log(`Starting to remove all followings for user: ${user.name} (${userId})`)

    const followings = await UserSubscriptionModel.find({ follower: userId }, { user: 1 })

    if (followings.length === 0) {
      console.log(`User ${userId} is not following anyone. Nothing to remove.`)
      return
    }

    console.log(`Found ${followings.length} followings to remove`)

    const streamFeed = Container.get('streamFeed.client') as StreamClient
    const timeLineFeed = streamFeed.feed(FEED_NAMES.TIMELINE, user._id.toString())

    let unfollowCount = 0
    for (const following of followings) {
      const followingId = (following.user as Types.ObjectId).toString()

      try {
        await timeLineFeed.unfollow(FEED_NAMES.USER, followingId)
        unfollowCount++
        if (unfollowCount % 10 === 0) {
          console.log(`Unfollowed ${unfollowCount}/${followings.length} in GetStream`)
        }
        await new Promise((resolve) => setTimeout(resolve, 500))
      } catch (error) {
        console.log(`Failed to unfollow user ${followingId} in GetStream: ${error}`)
      }
    }

    const deleteResult = await UserSubscriptionModel.deleteMany({ follower: userId })

    await UserModel.findOneAndUpdate({ _id: userId }, { $set: { 'userStats.followings': 0 } })

    console.log(`Successfully removed ${deleteResult.deletedCount} followings from the database`)
    console.log(`Successfully unfollowed ${unfollowCount} users in GetStream`)
    console.log('Operation completed successfully')
  } catch (error) {
    console.log(`Failed to remove followings: ${error}`)
    throw error
  }
}

const userIdFromArgs = process.argv[2]

if (!userIdFromArgs) {
  console.error('Please provide a userId as argument: ts-node remove-followings.ts <userId>')
  process.exit(1)
}

removeAllFollowingsFromUser(userIdFromArgs)
  .then(() => {
    console.log('Script completed successfully')
    process.exit(0)
  })
  .catch((error) => {
    console.log(`Script failed: ${error}`)
    process.exit(1)
  })
