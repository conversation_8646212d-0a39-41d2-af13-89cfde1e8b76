import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { slugifyString } from '../helpers/string'
import { addExternalConnections, createShadowUser } from '../modules/admin/helpers/shadowUser'
import { UserInfoModel } from '../modules/stats/models/userInfo'
import { UserModel } from '../modules/user/models'
import { CreationSource, Role } from '../modules/user/models/user'

import '../container'

const run = async () => {
  await connectToMongoDB()

  const userInfos = await UserInfoModel.find({
    synced: { $ne: true },
  })

  console.log('Found user infos to sync:', userInfos.length)

  let counter = 0
  for (const userInfo of userInfos) {
    userInfo.slug = slugifyString(userInfo.name)
    userInfo.country = 'CZ'

    const possibleYearsOfBirth = userInfo.possibleYearsOfBirth.sort()
    const user = await UserModel.findOne({
      $and: [
        { role: { $in: [Role.PLAYER, Role.SHADOW_PLAYER, null] } },
        { nonUniqueSlug: userInfo.slug },
        { country: userInfo.country },
        {
          $or: [
            possibleYearsOfBirth?.length === 2
              ? {
                  dateOfBirth: {
                    $gte: new Date(possibleYearsOfBirth[0], 0, 1).toISOString(),
                    $lt: new Date(possibleYearsOfBirth[1], 11, 31).toISOString(),
                  },
                }
              : {},
            { possibleYearsOfBirth: { $in: userInfo.possibleYearsOfBirth } },
          ],
        },
      ],
    })

    if (user) {
      console.log(`User found adding external connection: ${user.name}`)
      await addExternalConnections(user, [userInfo])
      await user.save()
    } else {
      console.log(`User not found creating shadow profile: ${userInfo.name}`)
      await createShadowUser({
        name: userInfo.name,
        country: userInfo.country,
        yearsOfBirth: userInfo.possibleYearsOfBirth,
        source: CreationSource.Scraper,
      })
    }

    userInfo.synced = true
    await userInfo.save()

    counter++

    if (counter % 100 === 0) {
      console.log(`Processed ${counter} user infos`)
    }
  }

  await closeMongoDBConnection()
  process.exit(0)
}

run()
