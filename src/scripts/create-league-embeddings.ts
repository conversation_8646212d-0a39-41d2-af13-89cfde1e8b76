import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { slugifyString } from '../helpers/string'
import { createLeagueEmbedding } from '../modules/admin/helpers/league'
import { LeagueModel } from '../modules/stats/models/league'

const run = async () => {
  await connectToMongoDB()

  const leagues = await LeagueModel.find({ vectorEmbedding: { $exists: false } })

  for (const league of leagues) {
    console.log(`Creating embedding for league ${league.name}`)
    league.vectorEmbedding = await createLeagueEmbedding(league)

    if (!league.slug) {
      league.slug = slugifyString(league.name)
    }
    await league.save()
  }

  await closeMongoDBConnection()
  process.exit(0)
}

run()
