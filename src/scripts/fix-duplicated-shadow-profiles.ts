import Container from 'typedi'

import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { userCleanup } from '../helpers/userCleanup'
import { addExternalConnections } from '../modules/admin/helpers/shadowUser'
import { findShadowUser } from '../modules/user/helpers/shadowUser'
import { UserModel } from '../modules/user/models'
import { Role } from '../modules/user/models/user'
import { SNSService } from '../services/sns'

import '../container'

const run = async () => {
  await connectToMongoDB()

  const snsService = Container.get(SNSService)

  const users = await UserModel.find({
    role: { $in: [null, Role.PLAYER] },
    'externalConnections.0': { $exists: false },
    country: { $exists: true, $ne: '' },
  })

  console.log(`Found ${users.length} users`)

  for (const user of users) {
    const shadow = await findShadow<PERSON><PERSON>(user)

    if (shadow && shadow.externalConnections.length > 0) {
      console.log(`Found shadow user for ${user.name} (${user.country})`)

      addExternalConnections(user)
      user.externalConnections = shadow.externalConnections
      await user.save()

      await userCleanup(shadow, snsService)
    }
  }

  await closeMongoDBConnection()

  process.exit(0)
}

run()
