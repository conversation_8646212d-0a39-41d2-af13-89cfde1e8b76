import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { createTeamEmbedding } from '../modules/admin/helpers/team'
import { modelFind } from '../modules/modules.helper'
import { TeamModel } from '../modules/stats/models/team'

const fix = async () => {
  await connectToMongoDB()

  console.log('Loading teams...')

  const teams = await modelFind(
    TeamModel,
    {
      vectorEmbedding: { $exists: false },
    },
    {},
    null,
    500
  )

  console.log('Processing ', teams.length, ' teams')

  let counter = 0
  for (const team of teams) {
    const vectorEmbedding = await createTeamEmbedding(team._id)

    await TeamModel.updateOne({ _id: team._id }, { vectorEmbedding })

    process.stdout.clearLine(0)
    process.stdout.cursorTo(0)
    const progress = Math.round((counter / teams.length) * 20)
    const progressBar = `[${'='.repeat(progress)}${' '.repeat(20 - progress)}]`
    process.stdout.write(
      `${progressBar} Processed ${counter} teams, ${teams.length - counter} left`
    )

    counter++
  }

  console.log('Done for ', teams.length, ' teams')
  await closeMongoDBConnection()
  process.exit(0)
}

fix()
