import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { Game, GameModel } from '../modules/stats/models/game/game'
import { Organization, OrganizationModel } from '../modules/stats/models/organization'
import { Team } from '../modules/stats/models/team'

import '../container'

const BATCH_SIZE = 100

const run = async () => {
  console.log('Connecting to database...')
  await connectToMongoDB()

  // Get total count of records
  const totalCount = await GameModel.countDocuments({})
  console.log(`Total Game records in database: ${totalCount}`)

  // Process records in batches
  const teamAbbreviations = new Map<string, { team: Team; game: Game }>()
  let processedCount = 0
  let skip = 0

  while (processedCount < totalCount) {
    // Fetch a batch of records
    console.log(`Fetching records ${skip} to ${skip + BATCH_SIZE}...`)
    const gameStats = await GameModel.find({}, { events: 1, homeTeam: 1, awayTeam: 1 })
      .skip(skip)
      .limit(BATCH_SIZE)
      .populate({
        path: 'homeTeam',
        select: 'name',
        populate: { path: 'organization', select: 'name' },
      })
      .populate({
        path: 'awayTeam',
        select: 'name',
        populate: { path: 'organization', select: 'name' },
      })

    if (gameStats.length === 0) {
      console.log('No more records to process.')
      break
    }

    console.log(`Processing batch of ${gameStats.length} records...`)

    // Process each Game record in this batch
    for (const game of gameStats) {
      const homeTeamId = game.homeTeam?._id?.toString()
      const awayTeamId = game.awayTeam?._id?.toString()

      if (!homeTeamId && !awayTeamId) {
        continue // Skip games without proper team references
      }

      // Variables to track abbreviations found in this game
      let homeTeamAbbreviation: string | null = null
      let awayTeamAbbreviation: string | null = null

      // Extract abbreviations from events
      if (game.events && game.events.length > 0) {
        for (const event of game.events) {
          if (!event.externalInfo?.teamName) continue

          const teamName = event.externalInfo.teamName

          if (!teamName) continue

          // Determine if this is home or away team based on isHomeTeam flag
          if (event.isHomeTeam === true) {
            homeTeamAbbreviation = teamName
          } else if (event.isHomeTeam === false) {
            awayTeamAbbreviation = teamName
          }

          // If we found both abbreviations, we can stop looking
          if (homeTeamAbbreviation && awayTeamAbbreviation) break
        }

        // Process home team abbreviation
        if (homeTeamAbbreviation && game.homeTeam && awayTeamAbbreviation && game.awayTeam) {
          teamAbbreviations.set(homeTeamAbbreviation, { team: game.homeTeam as Team, game })
          teamAbbreviations.set(awayTeamAbbreviation, { team: game.awayTeam as Team, game })
        }
      }
    }

    // Update counters for next batch
    processedCount += gameStats.length
    skip += BATCH_SIZE

    console.log(
      `Processed ${processedCount} of ${totalCount} (found ${teamAbbreviations.size} abbreviations) records (${Math.round((processedCount / totalCount) * 100)}%)`
    )
  }

  console.log(`\nFound ${teamAbbreviations.size} unique team abbreviations`)

  // Display team abbreviations
  console.log('\nTeam Abbreviations:')

  for (const [abbreviation, info] of teamAbbreviations.entries()) {
    const orgId = info.team.organization?._id?.toString()

    if (info.team.organization) {
      await OrganizationModel.updateOne(
        { _id: orgId },
        { abbreviation: abbreviation.toLowerCase() }
      )

      console.log(
        `${abbreviation.toLowerCase()} -> ${info.team.name} ${(info.team.organization as Organization).name} -> ${info.game._id}`
      )
    } else {
      console.log(`Team ${info.team.name} has no organization}`)
    }
  }

  // Disconnect from database
  await closeMongoDBConnection()
  console.log('\nDone!')
  process.exit(0)
}

run().catch((error) => {
  console.error('Error:', error)
  process.exit(1)
})
