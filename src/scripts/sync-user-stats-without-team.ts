import { PipelineStage } from 'mongoose'

import { closeMongoDBConnection, connectToMongoDB } from '../helpers/mongoose.connection'
import { StatsSource, UserStats, UserStatsModel } from '../modules/stats/models/userStats'

import '../container'

const BATCH_SIZE = 100

const run = async () => {
  console.log('Connecting to database...')
  await connectToMongoDB()

  // Set up the aggregation pipeline
  const pipeline: PipelineStage[] = []

  // Match userStats that don't have a team or league assigned
  pipeline.push({
    $match: {
      team: null,
      league: { $ne: null },
      source: StatsSource.SCRAPER,
    },
  })

  // Group by both externalTeamName and externalLeagueName
  pipeline.push({
    $group: {
      _id: {
        externalTeamName: '$externalInfo.externalTeamName',
        externalLeagueName: '$externalInfo.externalLeagueName',
      },
      userStats: {
        $first: '$$ROOT',
      },
    },
  })

  pipeline.push({
    $replaceRoot: {
      newRoot: '$userStats',
    },
  })

  // First, collect all the results in memory
  console.log('Fetching all unique team/league combinations...')
  const allResults = await UserStatsModel.aggregate<UserStats>(pipeline)
  const totalCount = allResults.length

  console.log(`Total unique team/league combinations: ${totalCount}`)

  // Process records in batches
  let processedCount = 0

  // Process in batches from the in-memory array
  while (processedCount < totalCount) {
    const endIndex = Math.min(processedCount + BATCH_SIZE, totalCount)
    const batch = allResults.slice(processedCount, endIndex)

    console.log(`Processing batch of ${batch.length} records...`)

    // Process each userStat from the in-memory batch
    for (const userStat of batch) {
      const teamName = userStat.externalInfo?.externalTeamName
      const leagueName = userStat.externalInfo?.externalLeagueName

      console.log(`Team: ${teamName || 'N/A'}, League: ${leagueName || 'N/A'}`)

      const dbStat = await UserStatsModel.findOne({ _id: userStat._id })
      const changed = (await dbStat?.syncEntities()) ?? false

      if (changed && dbStat) {
        console.log(`Updating ${teamName} -> ${dbStat.team} in ${leagueName}...`)
        await UserStatsModel.updateOne(
          {
            'externalInfo.externalTeamName': teamName,
            'externalInfo.externalLeagueName': leagueName,
          },
          {
            team: dbStat.team,
            league: dbStat.league,
          }
        )
      }
    }

    processedCount += batch.length
    console.log(`Processed ${processedCount}/${totalCount} records`)
  }

  // Disconnect from database
  await closeMongoDBConnection()
  console.log('\nDone!')
  process.exit(0)
}

run().catch((error) => {
  console.error('Error:', error)
  process.exit(1)
})
