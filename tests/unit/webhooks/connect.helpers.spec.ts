import assert from 'assert'

import {
  isStripeAccountVerificationPending,
  stripeAccountNeedsDocumentVerification,
} from '../../../src/webhooks/connect.helpers'

const mockStrapiAccount = {
  id: 'acct_1Q9pO3QYKvcZY99J',
  object: 'account',
  requirements: {
    alternatives: [],
    current_deadline: null,
    currently_due: [
      'business_profile.product_description',
      'business_profile.support_phone',
      'business_profile.url',
      'external_account',
      'tos_acceptance.date',
      'tos_acceptance.ip',
    ],
    disabled_reason: 'requirements.past_due',
    errors: [],
    eventually_due: [
      'business_profile.product_description',
      'business_profile.support_phone',
      'business_profile.url',
      'external_account',
      'tos_acceptance.date',
      'tos_acceptance.ip',
    ],
    past_due: ['external_account', 'tos_acceptance.date', 'tos_acceptance.ip'],
    pending_verification: [],
  },
}

const mockStrapiAccountVerification = {
  id: 'acct_1Q9pO3QYKvcZY99J',
  object: 'account',
  requirements: {
    alternatives: [],
    current_deadline: null,
    currently_due: [
      'individual.verification.document',
      'company.verification.document',
      'individual.verification.additional_document',
      'company.verification.additional_document',
    ],
    disabled_reason: 'requirements.past_due',
    errors: [],
    eventually_due: [
      'individual.verification.document',
      'company.verification.document',
      'individual.verification.additional_document',
      'company.verification.additional_document',
    ],
    past_due: ['external_account', 'tos_acceptance.date', 'tos_acceptance.ip'],
    pending_verification: ['individual.verification.document', 'company.verification.document'],
  },
}

describe('Webhook/Connect helpers', () => {
  it('should find no verification requirements on account', () => {
    const verificatioNeeded = stripeAccountNeedsDocumentVerification(mockStrapiAccount)

    assert.strictEqual(verificatioNeeded, false)
  })

  it('should find verification requirements on account', () => {
    const verificatioNeeded = stripeAccountNeedsDocumentVerification(mockStrapiAccountVerification)

    assert.strictEqual(verificatioNeeded, true)
  })

  it('should not find requirements on account', () => {
    const verificatioNeeded = stripeAccountNeedsDocumentVerification({ id: mockStrapiAccount.id })

    assert.strictEqual(verificatioNeeded, false)
  })

  it('should not have verification pending', () => {
    const verificationPending = isStripeAccountVerificationPending({ id: mockStrapiAccount.id })

    assert.strictEqual(verificationPending, false)
  })

  it('should not have verification pending', () => {
    const verificationPending = isStripeAccountVerificationPending(mockStrapiAccount)

    assert.strictEqual(verificationPending, false)
  })

  it('should have verification pending', () => {
    const verificationPending = isStripeAccountVerificationPending(mockStrapiAccountVerification)

    assert.strictEqual(verificationPending, true)
  })
})
