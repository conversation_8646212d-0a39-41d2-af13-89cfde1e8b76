import assert from 'assert'

import { getCountries } from '../../../../../src/services/webcrawler/fetchers/countries'

describe('Countries fetcher', () => {
  it('should correctly map country names to ISO codes', () => {
    const countries = getCountries()

    // Test a few specific country mappings
    assert.strictEqual(countries.get('United States'), 'US')
    assert.strictEqual(countries.get('USA'), 'US')
    assert.strictEqual(countries.get('Canada'), 'CA')
    assert.strictEqual(countries.get('Czech Republic'), 'CZ')
    assert.strictEqual(countries.get('Germany'), 'DE')
  })
})
