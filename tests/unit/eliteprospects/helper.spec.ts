import assert from 'assert'

import { parseImperialHeight } from '../../../src/modules/stats/services/eliteprospects/helpers'

describe('parse imperial height function', () => {
  it('parses null value', () => {
    const parsed = parseImperialHeight()

    assert.strictEqual(parsed, null)
  })

  it('parses correct height', () => {
    const parsed = parseImperialHeight(`6'2"`)

    assert.strictEqual(parsed?.feet, 6)
    assert.strictEqual(parsed?.inches, 2)
  })

  it('throws an error if format is wrong', () => {
    try {
      parseImperialHeight(`6622`)
    } catch (err) {
      assert.strictEqual(err.message, 'Invalid height format')
    }
  })
})
