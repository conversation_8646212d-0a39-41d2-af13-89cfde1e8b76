import assert from 'assert'
import sinon from 'sinon'

import { LeagueModel } from '../../../src/modules/stats/models/league'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { SubRole } from '../../../src/modules/user/models/user'
import {
  addIceHockeyGameActivity,
  addNotificationActivity,
  FeedActivityVerb,
} from '../../../src/services/helpers/streamFeed'
import { streamFeedAddActivityStub, streamFeedMockActivityId } from '../../e2e'
import { generateRandomGameStats } from '../../utils/stats'
import { generateRandomUser } from '../../utils/users'

describe('services/helpers/StreamFeed', () => {
  it('should call addNotificationActivity', async () => {
    const userId = '123'
    const actor = (await generateRandomUser({ subrole: SubRole.AGENT }))?.user
    const verb = FeedActivityVerb.VIEW_AGENT
    const foreignId = '123'
    const result = await addNotificationActivity({
      userId,
      actor,
      verb,
      foreignId,
    })
    assert.deepStrictEqual(result, { id: streamFeedMockActivityId })

    assert.strictEqual(streamFeedAddActivityStub.calledOnce, true)

    sinon.assert.calledWith(
      streamFeedAddActivityStub,
      sinon.match({
        actor: actor._id.toString(),
        foreign_id: foreignId,
        object: 'user',
        verb,
        actor_name: actor?.name,
        actor_slug: actor?.slug,
        actor_avatar_url: actor?.avatars?.small,
        actor_role: actor?.role,
        actor_sub_role: actor?.subrole,
      })
    )
  })

  it('should call addIceHockeyGameActivity', async () => {
    const game = await generateRandomGameStats({ nameSuffix: 'g1' })

    const hometeam = await TeamModel.findById(game.homeTeam)
    const awayteam = await TeamModel.findById(game.awayTeam)
    const league = await LeagueModel.findById(game.league)

    const toHomePlayers =
      game.homeTeamRoster?.map(({ player }) => `user:${player?._id.toString()}`) || []
    const toAwayPlayers =
      game.awayTeamRoster?.map(({ player }) => `user:${player?._id.toString()}`) || []

    const addedGameActivity = await addIceHockeyGameActivity(game)

    assert.deepStrictEqual(addedGameActivity, { id: streamFeedMockActivityId })

    assert.strictEqual(streamFeedAddActivityStub.calledOnce, true)

    sinon.assert.calledWith(
      streamFeedAddActivityStub,
      sinon.match({
        actor: game._id.toString(),
        verb: FeedActivityVerb.IceHockeyMatch,
        object: `gameId:${game._id.toString()}`,
        foreign_id: `gameId:${game._id.toString()}`,
        home: {
          id: hometeam?._id.toString(),
          name: hometeam?.name,
          logo: hometeam?.logo || '',
          score: game.stats?.homeTeam?.score,
        },
        away: {
          id: awayteam?._id.toString(),
          name: awayteam?.name,
          logo: awayteam?.logo || '',
          score: game.stats?.awayTeam?.score,
        },
        league: {
          id: league?._id.toString(),
          name: league?.name,
          logo: league?.logo,
          country: league?.countries[0],
        },
        to: [
          `team:${hometeam?._id.toString()}`,
          `team:${awayteam?._id.toString()}`,
          ...toHomePlayers,
          ...toAwayPlayers,
        ],
      })
    )
  })
})
