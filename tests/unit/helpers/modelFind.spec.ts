import { strictEqual } from 'assert'

import { modelFind } from '../../../src/modules/modules.helper'
import { UserModel } from '../../../src/modules/user/models'
import { generateRandomUser } from '../../utils/users'

describe('test modelFind helper', () => {
  before(async () => {
    await UserModel.deleteMany({})
  })
  it('should find users', async () => {
    const genUsers = await Promise.all([
      generateRandomUser(),
      generateRandomUser(),
      generateRandomUser(),
      generateRandomUser(),
      generateRandomUser(),
    ])

    const users = await modelFind(UserModel, {}, {}, null)

    strictEqual(users.length, genUsers.length)
  })
})
