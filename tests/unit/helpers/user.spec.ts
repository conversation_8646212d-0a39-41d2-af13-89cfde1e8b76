import assert from 'assert'

import { generateYearFilter } from '../../../src/helpers/user'

describe('helpers/user', () => {
  it('should create mongoos filter from years', () => {
    const years = ['2010', '2011', '2006+']
    const filter = generateYearFilter(years)

    assert.deepStrictEqual(filter, {
      $or: [
        {
          dateOfBirth: {
            $gte: new Date('2010-01-01T00:00:00.000Z'),
            $lt: new Date('2010-12-31T23:59:59.999Z'),
          },
        },
        {
          dateOfBirth: {
            $gte: new Date('2011-01-01T00:00:00.000Z'),
            $lt: new Date('2011-12-31T23:59:59.999Z'),
          },
        },
        { dateOfBirth: { $lte: new Date('2006-12-31T23:59:59.999Z') } },
      ],
    })
  })

  it('should create mongoos filter out invalid years from years', () => {
    const years = ['2007', '2009', '19566', '2002+', '55']
    const filter = generateYearFilter(years)

    assert.deepStrictEqual(filter, {
      $or: [
        {
          dateOfBirth: {
            $gte: new Date('2007-01-01T00:00:00.000Z'),
            $lt: new Date('2007-12-31T23:59:59.999Z'),
          },
        },
        {
          dateOfBirth: {
            $gte: new Date('2009-01-01T00:00:00.000Z'),
            $lt: new Date('2009-12-31T23:59:59.999Z'),
          },
        },
        {
          dateOfBirth: {
            $lte: new Date('2002-12-31T23:59:59.999Z'),
          },
        },
      ],
    })
  })
})
