import { Types } from 'mongoose'
import assert from 'node:assert'

import { CURRENT_SEASON } from '../../../src/helpers/league'
import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrl, DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { populateUsersByExternalIds } from '../../../src/modules/stats/helpers/userStats'
import { StatsSource, UserStats, UserStatsModel } from '../../../src/modules/stats/models/userStats'
import { UserModel } from '../../../src/modules/user/models'
import { User } from '../../../src/modules/user/models/user'
import { generateDiscoverUrl } from '../../utils/stats'
import { generateRandomUser, GenerateRandomUserResponse } from '../../utils/users'

import { DocumentType } from '@typegoose/typegoose'

const generateRandomUsers = async (
  num = 10,
  discoveredUrl?: DocumentType<DiscoveredUrl>,
  userExternalIds?: string[]
): Promise<GenerateRandomUserResponse[]> => {
  return Promise.all(
    Array(num)
      .fill(null)
      .map((_, index) =>
        generateRandomUser({
          ...(discoveredUrl && userExternalIds?.length
            ? {
                externalConnections: [
                  {
                    externalId: userExternalIds?.[index],
                    crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
                  },
                ],
              }
            : {}),
        })
      )
  )
}

describe('should popupate usersStats user from UserModel via userExternalId', () => {
  beforeEach(async () => {
    await UserStatsModel.deleteMany({})
    await UserModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
  })
  it('should populate users', async () => {
    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(
      10,
      discoveredUrl,
      Array(10)
        .fill(null)
        .map((_, index) => `${100000 + index}`)
    )

    let iteration = 0

    for (const randomUserResponse of randomUsers) {
      await UserStatsModel.create({
        userExternalId: randomUserResponse.user?.externalConnections?.[0]?.externalId,
        team: new Types.ObjectId(),
        league: new Types.ObjectId(),
        season: CURRENT_SEASON,
        gamesPlayed: 3 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        source: StatsSource.SCRAPER,
        externalInfo: {
          stage: { value: 'Regular Season', label: 'Regular Season' },
        },
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
      })

      iteration += 1
    }

    const userStats: UserStats[] = await UserStatsModel.find({}).lean()

    assert.strictEqual(userStats.length, randomUsers.length)

    assert.strictEqual(userStats[0].user, undefined)

    await populateUsersByExternalIds(userStats)

    const stats0: UserStats = userStats[0]

    const populatedUser0 = stats0.user as DocumentType<User>

    assert.strictEqual(populatedUser0?._id.toString(), randomUsers[0].user._id.toString())
    assert.strictEqual(populatedUser0?.firstname, randomUsers[0].user.firstname)
    assert.strictEqual(populatedUser0?.lastname, randomUsers[0].user.lastname)
    assert.strictEqual(populatedUser0?.email, randomUsers[0].user.email)
    assert.strictEqual(populatedUser0?.slug, randomUsers[0].user.slug)
    assert.strictEqual(
      stats0.userExternalId,
      randomUsers[0].user.externalConnections?.[0]?.externalId
    )

    const rIndex = Math.floor(Math.random() * userStats.length)
    const statsR: UserStats = userStats[rIndex]

    const populatedUserR = statsR.user as DocumentType<User>

    assert.strictEqual(populatedUserR?._id.toString(), randomUsers[rIndex].user._id.toString())
    assert.strictEqual(populatedUserR?.firstname, randomUsers[rIndex].user.firstname)
    assert.strictEqual(populatedUserR?.lastname, randomUsers[rIndex].user.lastname)
    assert.strictEqual(populatedUserR?.email, randomUsers[rIndex].user.email)
    assert.strictEqual(populatedUserR?.slug, randomUsers[rIndex].user.slug)
    assert.strictEqual(
      statsR.userExternalId,
      randomUsers[rIndex].user.externalConnections?.[0]?.externalId
    )
  })

  it('should not succeed in populating users', async () => {
    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(
      10,
      discoveredUrl,
      Array(10)
        .fill(null)
        .map((_, index) => `${100000 + index}`)
    )

    let iteration = 0

    for (const randomUserResponse of randomUsers) {
      await UserStatsModel.create({
        userExternalId: randomUserResponse.user?.externalConnections?.[0]?.externalId,
        team: new Types.ObjectId(),
        league: new Types.ObjectId(),
        season: CURRENT_SEASON,
        gamesPlayed: 3 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        source: StatsSource.SCRAPER,
        externalInfo: {
          stage: { value: 'Regular Season', label: 'Regular Season' },
        },
      })

      iteration += 1
    }

    const userStats: UserStats[] = await UserStatsModel.find({}).lean()

    const rIndex = Math.floor(Math.random() * userStats.length)

    assert.strictEqual(userStats.length, randomUsers.length)

    assert.strictEqual(userStats[0].user, undefined)
    assert.strictEqual(userStats[1].user, undefined)
    assert.strictEqual(userStats[rIndex].user, undefined)

    await populateUsersByExternalIds(userStats)

    assert.strictEqual(userStats[0].user, undefined)
    assert.strictEqual(userStats[1].user, undefined)
    assert.strictEqual(userStats[rIndex].user, undefined)
  })

  it('should not succeed in populating users', async () => {
    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(10)

    let iteration = 0

    for (const randomUserResponse of randomUsers) {
      await UserStatsModel.create({
        userExternalId: randomUserResponse.user?.externalConnections?.[0]?.externalId,
        team: new Types.ObjectId(),
        league: new Types.ObjectId(),
        season: CURRENT_SEASON,
        gamesPlayed: 3 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        source: StatsSource.SCRAPER,
        externalInfo: {
          stage: { value: 'Regular Season', label: 'Regular Season' },
        },
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
      })

      iteration += 1
    }

    const userStats: UserStats[] = await UserStatsModel.find({}).lean()

    const rIndex = Math.floor(Math.random() * userStats.length)

    assert.strictEqual(userStats.length, randomUsers.length)

    assert.strictEqual(userStats[0].user, undefined)
    assert.strictEqual(userStats[1].user, undefined)
    assert.strictEqual(userStats[rIndex].user, undefined)

    await populateUsersByExternalIds(userStats)

    assert.strictEqual(userStats[0].user, undefined)
    assert.strictEqual(userStats[1].user, undefined)
    assert.strictEqual(userStats[rIndex].user, undefined)
  })

  it('should not succeed in populating users', async () => {
    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(
      1,
      discoveredUrl,
      Array(1)
        .fill(null)
        .map((_, index) => `${100000 + index}`)
    )

    const iteration = 0
    await UserStatsModel.create({
      team: new Types.ObjectId(),
      league: new Types.ObjectId(),
      season: CURRENT_SEASON,
      gamesPlayed: 3 + iteration,
      goals: iteration,
      assists: iteration,
      pim: iteration,
      plusMinus: iteration,
      source: StatsSource.SCRAPER,
      externalInfo: {
        stage: { value: 'Regular Season', label: 'Regular Season' },
      },
      crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
    })

    const userStats: UserStats[] = await UserStatsModel.find({}).lean()

    assert.strictEqual(userStats.length, randomUsers.length)

    assert.strictEqual(userStats[0].user, undefined)

    await populateUsersByExternalIds(userStats)

    assert.strictEqual(userStats[0].user, undefined)
  })
})
