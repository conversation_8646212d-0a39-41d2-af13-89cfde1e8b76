import assert from 'assert'

import {
  AggregatedType,
  resolveAggregation,
  resolveItemNameDefenders,
} from '../../../src/helpers/aggregate'
import { BioPosition } from '../../../src/modules/user/models/user'

describe('aggregate helpers', () => {
  it('should resolve all defender postions properly', () => {
    assert.strictEqual(resolveItemNameDefenders(BioPosition.DEFENDER), BioPosition.DEFENDER)
    assert.strictEqual(resolveItemNameDefenders(BioPosition.LEFT_DEFENSIVE), BioPosition.DEFENDER)
    assert.strictEqual(resolveItemNameDefenders(BioPosition.RIGHT_DEFENSIVE), BioPosition.DEFENDER)
  })
  it('should parse aggregatioan to certain level of readability and usefulness', () => {
    const aggr = [
      {
        _id: {
          positions: 'CENTER',
        },
        count: 2,
      },
      {
        _id: {
          positions: null,
        },
        count: 7,
      },
      {
        _id: {
          positions: 'GOALTENDER',
        },
        count: 2,
      },
      {
        _id: {
          years: 2010,
        },
        count: 3,
      },
      {
        _id: {
          years: '2006+',
        },
        count: 7,
      },
      {
        _id: {
          years: 2011,
        },
        count: 1,
      },
    ] as AggregatedType[]

    const aggrInfo = [
      {
        name: 'positions',
        items: [
          {
            key: 'CENTER',
            value: 2,
          },
          {
            key: null,
            value: 7,
          },
          {
            key: 'GOALTENDER',
            value: 2,
          },
        ],
      },
      {
        name: 'years',
        items: [
          {
            key: 2010,
            value: 3,
          },
          {
            key: 2011,
            value: 1,
          },
          {
            key: '2006+',
            value: 7,
          },
        ],
      },
    ]

    assert.deepStrictEqual(resolveAggregation(aggr), aggrInfo)
  })
})
