import assert from 'assert'
import sinon from 'sinon'

import { isCurrentOrFutureSeason } from '../../../src/modules/stats/helpers/general'

describe('isCurrentOrFutureSeason', () => {
  let clock: sinon.SinonFakeTimers

  afterEach(() => {
    clock?.restore()
  })

  it('should return false for invalid season format', () => {
    const invalidFormats = ['2024', '2024/25', '2024-25', '202A-2025', 'invalid']

    invalidFormats.forEach((format) => {
      assert.strictEqual(isCurrentOrFutureSeason(format), false)
    })
  })

  it('should return false if years are not consecutive', () => {
    assert.strictEqual(isCurrentOrFutureSeason('2024-2026'), false)
  })

  it('should return true for current season before mid-year', () => {
    // Mock date to April 2, 2025
    clock = sinon.useFakeTimers(new Date('2025-04-02').getTime())

    assert.strictEqual(isCurrentOrFutureSeason('2024-2025'), true)
    assert.strictEqual(isCurrentOrFutureSeason('2025-2026'), true)

    clock.restore()
  })

  it('should return true for current season after mid-year', () => {
    // Mock date to September 1, 2025
    clock = sinon.useFakeTimers(new Date('2025-09-01').getTime())

    assert.strictEqual(isCurrentOrFutureSeason('2024-2025'), false)
    assert.strictEqual(isCurrentOrFutureSeason('2025-2026'), true)

    clock.restore()
  })

  it('should return true for future season', () => {
    // Mock date to September 1, 2025
    clock = sinon.useFakeTimers(new Date('2025-09-01').getTime())

    assert.strictEqual(isCurrentOrFutureSeason('2026-2027'), true)

    clock.restore()
  })
})
