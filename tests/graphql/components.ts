import { GraphQLClient, RequestOptions } from 'graphql-request';
import gql from 'graphql-tag';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
type GraphQLClientRequestHeaders = RequestOptions['requestHeaders'];
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTimeISO: { input: any; output: any; }
  JSONObject: { input: any; output: any; }
  ObjectId: { input: any; output: any; }
};

export type AcceptAgencyInvitationAlreadyAcceptedError = IUserError & {
  __typename?: 'AcceptAgencyInvitationAlreadyAcceptedError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AcceptAgencyInvitationError = AcceptAgencyInvitationAlreadyAcceptedError | AcceptAgencyInvitationExpiredError | AcceptAgencyInvitationInvalidTokenError;

export type AcceptAgencyInvitationExpiredError = IUserError & {
  __typename?: 'AcceptAgencyInvitationExpiredError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AcceptAgencyInvitationInvalidTokenError = IUserError & {
  __typename?: 'AcceptAgencyInvitationInvalidTokenError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AcceptAgencyInvitationResponse = {
  __typename?: 'AcceptAgencyInvitationResponse';
  errors?: Maybe<Array<AcceptAgencyInvitationError>>;
  success: Scalars['Boolean']['output'];
};

export type AccountAlreadyExistsError = IUserError & {
  __typename?: 'AccountAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AccountDoesNotExistsError = IUserError & {
  __typename?: 'AccountDoesNotExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AddAgencyError = AddAgencyUserAlreadyBelongsToAgencyError | AddAgencyUserNotFoundError;

export type AddAgencyUserAlreadyBelongsToAgencyError = IUserError & {
  __typename?: 'AddAgencyUserAlreadyBelongsToAgencyError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AddAgencyUserNotFoundError = IUserError & {
  __typename?: 'AddAgencyUserNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AddAgencyUserResponse = {
  __typename?: 'AddAgencyUserResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<AddAgencyError>>;
};

export type AddExternalConnectionError = ExternalConnectionError | UserInfoNotFoundError | UserNotFoundError;

export type AddExternalConnectionResponse = {
  __typename?: 'AddExternalConnectionResponse';
  errors?: Maybe<Array<AddExternalConnectionError>>;
  success: Scalars['Boolean']['output'];
};

export type AdminAgencyMutations = {
  __typename?: 'AdminAgencyMutations';
  addUser: AddAgencyUserResponse;
  removeAgencyUser: RemoveAgencyUserResponse;
};


export type AdminAgencyMutationsAddUserArgs = {
  agencyId: Scalars['ObjectId']['input'];
  userId: Scalars['ObjectId']['input'];
};


export type AdminAgencyMutationsRemoveAgencyUserArgs = {
  agencyId: Scalars['ObjectId']['input'];
  userId: Scalars['ObjectId']['input'];
};

export type AdminBatchNotificationMutations = {
  __typename?: 'AdminBatchNotificationMutations';
  cancel: BatchNotificationResponse;
  create: BatchNotificationResponse;
};


export type AdminBatchNotificationMutationsCancelArgs = {
  id: Scalars['ObjectId']['input'];
};


export type AdminBatchNotificationMutationsCreateArgs = {
  input: BatchNotificationInput;
};

export type AdminBatchNotificationQueries = {
  __typename?: 'AdminBatchNotificationQueries';
  list: BatchNotificationListResponse;
};


export type AdminBatchNotificationQueriesListArgs = {
  pagination?: InputMaybe<Pagination>;
};

export type AdminBoostFilter = {
  currency?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type AdminBoostMutations = {
  __typename?: 'AdminBoostMutations';
  syncAccount: SyncStripeAccountResponse;
  updateBoostMessage: BoostMessageResponse;
};


export type AdminBoostMutationsSyncAccountArgs = {
  accountId: Scalars['String']['input'];
};


export type AdminBoostMutationsUpdateBoostMessageArgs = {
  boostId: Scalars['ObjectId']['input'];
  message: Scalars['String']['input'];
};

export type AdminBoostQueries = {
  __typename?: 'AdminBoostQueries';
  list: BoostsResponse;
};


export type AdminBoostQueriesListArgs = {
  filter?: InputMaybe<AdminBoostFilter>;
  pagination?: InputMaybe<Pagination>;
};

export type AdminCrawlerLogsQueries = {
  __typename?: 'AdminCrawlerLogsQueries';
  get?: Maybe<CrawlerLogs>;
  list: CrawlerLogsResponse;
};


export type AdminCrawlerLogsQueriesGetArgs = {
  id: Scalars['String']['input'];
};


export type AdminCrawlerLogsQueriesListArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<CrawlerLogsFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
};

export type AdminCrawlerRunStatsQueries = {
  __typename?: 'AdminCrawlerRunStatsQueries';
  get?: Maybe<CrawlerRunStats>;
  list: CrawlerRunStatsResponse;
};


export type AdminCrawlerRunStatsQueriesGetArgs = {
  id: Scalars['String']['input'];
};


export type AdminCrawlerRunStatsQueriesListArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<CrawlerRunStatsFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
};

export type AdminDiscoveredUrlMutations = {
  __typename?: 'AdminDiscoveredUrlMutations';
  reScrape: ReScrapeResponse;
};


export type AdminDiscoveredUrlMutationsReScrapeArgs = {
  id: Scalars['ObjectId']['input'];
};

export type AdminDiscoveredUrlQueries = {
  __typename?: 'AdminDiscoveredUrlQueries';
  list: DiscoveredUrlListResponse;
};


export type AdminDiscoveredUrlQueriesListArgs = {
  filter?: InputMaybe<DiscoveredUrlFilter>;
  pagination?: InputMaybe<Pagination>;
};

export type AdminGameFilter = {
  hideIgnored?: InputMaybe<Scalars['Boolean']['input']>;
  league?: InputMaybe<Scalars['String']['input']>;
  leagueMissing?: InputMaybe<Scalars['Boolean']['input']>;
  period?: InputMaybe<AdminGamePeriod>;
  team?: InputMaybe<Scalars['String']['input']>;
  teamMissing?: InputMaybe<Scalars['Boolean']['input']>;
  user?: InputMaybe<Scalars['String']['input']>;
};

export type AdminGameMutations = {
  __typename?: 'AdminGameMutations';
  sync: GameStats;
  syncSingle: SyncSingleGameResponse;
};


export type AdminGameMutationsSyncArgs = {
  id: Scalars['ObjectId']['input'];
};


export type AdminGameMutationsSyncSingleArgs = {
  id: Scalars['ObjectId']['input'];
};

export enum AdminGamePeriod {
  All = 'ALL',
  Future = 'FUTURE',
  History = 'HISTORY'
}

export type AdminGameQueries = {
  __typename?: 'AdminGameQueries';
  get: GameStats;
  list: GameResponse;
};


export type AdminGameQueriesGetArgs = {
  id: Scalars['ObjectId']['input'];
};


export type AdminGameQueriesListArgs = {
  filter?: InputMaybe<AdminGameFilter>;
  pagination?: InputMaybe<Pagination>;
  sort?: InputMaybe<SortInput>;
};

export type AdminLeagueEdge = {
  __typename?: 'AdminLeagueEdge';
  node: League;
  used: Scalars['Boolean']['output'];
};

export type AdminLeagueMutations = {
  __typename?: 'AdminLeagueMutations';
  removeLeagueLogo: RemoveLeagueLogoResponse;
  uploadLeagueLogo: UploadLeagueLogoResponse;
};


export type AdminLeagueMutationsRemoveLeagueLogoArgs = {
  id: Scalars['ObjectId']['input'];
};


export type AdminLeagueMutationsUploadLeagueLogoArgs = {
  id: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadLeagueLogoInput>;
};

export type AdminLeagueQueries = {
  __typename?: 'AdminLeagueQueries';
  get?: Maybe<League>;
  list: AdminLeaguesResponse;
  suggestLeagues: SuggestedLeagueResponse;
};


export type AdminLeagueQueriesGetArgs = {
  id: Scalars['ObjectId']['input'];
};


export type AdminLeagueQueriesListArgs = {
  filter?: InputMaybe<AdminLeaguesFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type AdminLeagueQueriesSuggestLeaguesArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
  exact?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
};

export type AdminLeagueStatsEdge = {
  __typename?: 'AdminLeagueStatsEdge';
  node: LeagueStats;
};

export type AdminLeagueStatsFilter = {
  leagueId: Scalars['ObjectId']['input'];
};

export type AdminLeagueStatsQueries = {
  __typename?: 'AdminLeagueStatsQueries';
  list: AdminLeagueStatsResponse;
};


export type AdminLeagueStatsQueriesListArgs = {
  filter: AdminLeagueStatsFilter;
};

export type AdminLeagueStatsResponse = {
  __typename?: 'AdminLeagueStatsResponse';
  edges: Array<AdminLeagueStatsEdge>;
};

export type AdminLeaguesFilter = {
  checked?: InputMaybe<StatusType>;
  country?: InputMaybe<Scalars['String']['input']>;
  genderCategory?: InputMaybe<LeagueGenderCategory>;
  level?: InputMaybe<LeagueLevel>;
  scraped?: InputMaybe<StatusType>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<LeagueType>;
};

export type AdminLeaguesResponse = {
  __typename?: 'AdminLeaguesResponse';
  edges: Array<AdminLeagueEdge>;
  pageInfo: PageInfo;
};

export type AdminMutation = {
  __typename?: 'AdminMutation';
  agency: AdminAgencyMutations;
  batchNotification: AdminBatchNotificationMutations;
  boost: AdminBoostMutations;
  crawlerDomainConfig: CrawlerDomainConfigMutations;
  discoveredUrl: AdminDiscoveredUrlMutations;
  game: AdminGameMutations;
  league: AdminLeagueMutations;
  notifications: AdminNotificationsMutations;
  organization: AdminOrganizationMutations;
  team: AdminTeamMutations;
  user: AdminUserMutations;
  userStats: AdminUserStatsMutations;
  wallVideo: AdminWallVideosMutations;
};

export type AdminNotificationEdges = {
  __typename?: 'AdminNotificationEdges';
  node: Notification;
};

export type AdminNotificationFilter = {
  type?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type AdminNotificationResponse = {
  __typename?: 'AdminNotificationResponse';
  edges: Array<AdminNotificationEdges>;
  pageInfo: PageInfo;
};

export type AdminNotificationsMutations = {
  __typename?: 'AdminNotificationsMutations';
  sendPushNotification: SendPushNotificationAdminResponse;
};


export type AdminNotificationsMutationsSendPushNotificationArgs = {
  destinationPath?: InputMaybe<Scalars['String']['input']>;
  forcePush?: InputMaybe<Scalars['Boolean']['input']>;
  message: Scalars['String']['input'];
  title: Scalars['String']['input'];
  type: PushNotificationType;
  userId: Scalars['ObjectId']['input'];
};

export type AdminNotificationsQueries = {
  __typename?: 'AdminNotificationsQueries';
  getNotifications: AdminNotificationResponse;
};


export type AdminNotificationsQueriesGetNotificationsArgs = {
  filter?: InputMaybe<AdminNotificationFilter>;
  pagination?: InputMaybe<Pagination>;
};

export type AdminOrganizationMutations = {
  __typename?: 'AdminOrganizationMutations';
  removeOrganizationLogo: RemoveOrganizationLogoResponse;
};


export type AdminOrganizationMutationsRemoveOrganizationLogoArgs = {
  id: Scalars['ObjectId']['input'];
};

export type AdminQuery = {
  __typename?: 'AdminQuery';
  batchNotification: AdminBatchNotificationQueries;
  boost: AdminBoostQueries;
  crawlerDomainConfig: CrawlerDomainConfigQueries;
  crawlerLogs: AdminCrawlerLogsQueries;
  crawlerRunStats: AdminCrawlerRunStatsQueries;
  discoveredUrl: AdminDiscoveredUrlQueries;
  game: AdminGameQueries;
  league: AdminLeagueQueries;
  leagueStats: AdminLeagueStatsQueries;
  notifications: AdminNotificationsQueries;
  team: AdminTeamQueries;
  user?: Maybe<User>;
  userCount?: Maybe<UserCount>;
  userInfo: AdminUserInfoQueries;
  userStatisticsByYearAndCountry: UserStatisticsByYearAndCountry;
  userStats: AdminUserStatsQueries;
  wallVideo: AdminWallVideosQueries;
};


export type AdminQueryUserArgs = {
  id: Scalars['ObjectId']['input'];
};


export type AdminQueryUserCountArgs = {
  days?: InputMaybe<Scalars['Int']['input']>;
};

export type AdminSocialSignInError = GeneralError;

export type AdminSocialSignInInput = {
  idToken: Scalars['String']['input'];
};

export type AdminSocialSignInResponse = {
  __typename?: 'AdminSocialSignInResponse';
  errors: Array<AdminSocialSignInError>;
  tokens?: Maybe<JwtTokens>;
  user?: Maybe<AdminUser>;
};

export type AdminTeamMutations = {
  __typename?: 'AdminTeamMutations';
  removeTeamLogo: RemoveTeamLogoResponse;
};


export type AdminTeamMutationsRemoveTeamLogoArgs = {
  id: Scalars['ObjectId']['input'];
};

export type AdminTeamQueries = {
  __typename?: 'AdminTeamQueries';
  suggestTeams: SuggestedTeamResponse;
};


export type AdminTeamQueriesSuggestTeamsArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
  exact?: InputMaybe<Scalars['Boolean']['input']>;
  league?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
};

export type AdminUser = {
  __typename?: 'AdminUser';
  email: Scalars['String']['output'];
  firstname?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  lastname?: Maybe<Scalars['String']['output']>;
};

export type AdminUserInfoQueries = {
  __typename?: 'AdminUserInfoQueries';
  userInfos: UserInfoResponse;
};


export type AdminUserInfoQueriesUserInfosArgs = {
  filter: UserInfoFilter;
  pagination?: InputMaybe<Pagination>;
};

export type AdminUserMutations = {
  __typename?: 'AdminUserMutations';
  addExternalConnection: AddExternalConnectionResponse;
  markUserAsDeleted: MarkUserAsDeletedResponse;
  removeExternalConnection: RemoveExternalConnectionResponse;
  syncAgencyClient: SyncAgencyClientsResponse;
  syncAgencyClients: SyncAgencyClientsResponse;
  uploadAvatar: UploadAvatarResponse;
};


export type AdminUserMutationsAddExternalConnectionArgs = {
  crawlerDomainConfigId: Scalars['ObjectId']['input'];
  externalId: Scalars['String']['input'];
  userId: Scalars['ObjectId']['input'];
};


export type AdminUserMutationsMarkUserAsDeletedArgs = {
  userId: Scalars['ObjectId']['input'];
};


export type AdminUserMutationsRemoveExternalConnectionArgs = {
  crawlerDomainConfigId: Scalars['ObjectId']['input'];
  externalId: Scalars['String']['input'];
  userId: Scalars['ObjectId']['input'];
};


export type AdminUserMutationsSyncAgencyClientArgs = {
  agencyId: Scalars['ObjectId']['input'];
  epLink: Scalars['String']['input'];
};


export type AdminUserMutationsSyncAgencyClientsArgs = {
  agencyId: Scalars['ObjectId']['input'];
  epAgencyId: Scalars['Int']['input'];
};


export type AdminUserMutationsUploadAvatarArgs = {
  input?: InputMaybe<UploadAvatarInput>;
  userId: Scalars['ObjectId']['input'];
};

export type AdminUserStatsFilter = {
  country?: InputMaybe<Scalars['String']['input']>;
  league?: InputMaybe<Scalars['ObjectId']['input']>;
  leagueMissing?: InputMaybe<Scalars['Boolean']['input']>;
  role?: InputMaybe<Role>;
  source?: InputMaybe<StatsSource>;
  team?: InputMaybe<Scalars['ObjectId']['input']>;
  teamMissing?: InputMaybe<Scalars['Boolean']['input']>;
  user?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type AdminUserStatsMutations = {
  __typename?: 'AdminUserStatsMutations';
  deleteAllUserStats: DeleteUserStatsResponse;
  migrate: MigrateUserStatsResponse;
  sync: UserStatsSyncResponse;
};


export type AdminUserStatsMutationsDeleteAllUserStatsArgs = {
  userId: Scalars['ObjectId']['input'];
};


export type AdminUserStatsMutationsMigrateArgs = {
  input: MigrateUserStatsInput;
};


export type AdminUserStatsMutationsSyncArgs = {
  id: Scalars['String']['input'];
};

export type AdminUserStatsQueries = {
  __typename?: 'AdminUserStatsQueries';
  list: UserStatsResponse;
};


export type AdminUserStatsQueriesListArgs = {
  filter?: InputMaybe<AdminUserStatsFilter>;
  pagination?: InputMaybe<Pagination>;
};

export type AdminUsersFilter = {
  agency?: InputMaybe<Scalars['ObjectId']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  inspired?: InputMaybe<Scalars['Boolean']['input']>;
  moreSelected?: InputMaybe<Scalars['Boolean']['input']>;
  possibleYearsOfBirth?: InputMaybe<Scalars['Float']['input']>;
  premium?: InputMaybe<Scalars['Boolean']['input']>;
  recommended?: InputMaybe<Scalars['Boolean']['input']>;
  role?: InputMaybe<Role>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  subrole?: InputMaybe<SubRole>;
  withoutPicture?: InputMaybe<Scalars['Boolean']['input']>;
  withoutTeam?: InputMaybe<Scalars['Boolean']['input']>;
  yearOfBirth?: InputMaybe<Scalars['Float']['input']>;
};

export type AdminUsersResponse = {
  __typename?: 'AdminUsersResponse';
  edges: Array<AminUserEdge>;
  pageInfo: PageInfo;
};

export type AdminWallVideosMutations = {
  __typename?: 'AdminWallVideosMutations';
  delete: DeleteWallVideoResponse;
  update: UpdateWallVideoResponse;
};


export type AdminWallVideosMutationsDeleteArgs = {
  id: Scalars['ObjectId']['input'];
};


export type AdminWallVideosMutationsUpdateArgs = {
  id: Scalars['ObjectId']['input'];
  input: UpdateWallVideoInput;
};

export type AdminWallVideosQueries = {
  __typename?: 'AdminWallVideosQueries';
  list: WallVideosResponse;
  videosCount: VideosCountByType;
  videosCountByTags: VideosCountByTags;
};


export type AdminWallVideosQueriesListArgs = {
  filter?: InputMaybe<VideoFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type AdminWallVideosQueriesVideosCountArgs = {
  days?: InputMaybe<Scalars['Int']['input']>;
};

export type AgenciesFilter = {
  category?: InputMaybe<SportCategory>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  integrated?: InputMaybe<Scalars['Boolean']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
};

export type AgenciesFollowingResponse = {
  __typename?: 'AgenciesFollowingResponse';
  edges: Array<AgencyFollowingEdge>;
  pageInfo: PageInfo;
};

export type AgenciesResponse = {
  __typename?: 'AgenciesResponse';
  edges: Array<AgencyEdge>;
  pageInfo: PageInfo;
};

export type Agency = {
  __typename?: 'Agency';
  address?: Maybe<Scalars['String']['output']>;
  affiliateAgencies?: Maybe<Array<Agency>>;
  agentsCount?: Maybe<Scalars['Float']['output']>;
  categories?: Maybe<Array<SportCategory>>;
  checked?: Maybe<Scalars['Boolean']['output']>;
  clientsCount?: Maybe<Scalars['Float']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  drafts?: Maybe<AgencyDrafts>;
  email?: Maybe<Scalars['String']['output']>;
  epUrl?: Maybe<Scalars['String']['output']>;
  externalNumberOfClients?: Maybe<Scalars['Int']['output']>;
  facebookUrl?: Maybe<Scalars['String']['output']>;
  founded?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  instagramFollowers?: Maybe<Scalars['Int']['output']>;
  instagramUrl?: Maybe<Scalars['String']['output']>;
  integrated?: Maybe<Scalars['Boolean']['output']>;
  licences?: Maybe<Array<Scalars['String']['output']>>;
  linkedinUrl?: Maybe<Scalars['String']['output']>;
  logo?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  ogLogo?: Maybe<Scalars['String']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  shadowClientsCount?: Maybe<Scalars['Float']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  stats?: Maybe<AgencyStats>;
  tiktokUrl?: Maybe<Scalars['String']['output']>;
  twitterUrl?: Maybe<Scalars['String']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type AgencyDrafts = {
  __typename?: 'AgencyDrafts';
  CHL: Scalars['Int']['output'];
  NCAA: Scalars['Int']['output'];
  NHL: Scalars['Int']['output'];
  USHL: Scalars['Int']['output'];
};

export type AgencyDraftsInput = {
  CHL: Scalars['Int']['input'];
  NCAA: Scalars['Int']['input'];
  NHL: Scalars['Int']['input'];
  USHL: Scalars['Int']['input'];
};

export type AgencyEdge = {
  __typename?: 'AgencyEdge';
  node: Agency;
};

export type AgencyFollowingEdge = {
  __typename?: 'AgencyFollowingEdge';
  node: Agency;
};

export type AgencyNotFoundError = IUserError & {
  __typename?: 'AgencyNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type AgencyStats = {
  __typename?: 'AgencyStats';
  followers: Scalars['Int']['output'];
  views: Scalars['Int']['output'];
};

export type AgencyUsersFilter = {
  agentV2?: InputMaybe<Scalars['ObjectId']['input']>;
  includeAggregation?: Scalars['Boolean']['input'];
  positions?: InputMaybe<Array<BioPosition>>;
  role?: InputMaybe<AgencyUsersRole>;
  years?: InputMaybe<Array<Scalars['String']['input']>>;
};

export enum AgencyUsersRole {
  Agent = 'AGENT',
  Player = 'PLAYER'
}

export type Agent = {
  __typename?: 'Agent';
  agency?: Maybe<Agency>;
  email: Scalars['String']['output'];
  firstname?: Maybe<Scalars['String']['output']>;
  lastname?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<PhoneNumber>;
};

export type AggregatedInfoGroup = {
  __typename?: 'AggregatedInfoGroup';
  items?: Maybe<Array<AggregatedInfoItem>>;
  name?: Maybe<Scalars['String']['output']>;
};

export type AggregatedInfoItem = {
  __typename?: 'AggregatedInfoItem';
  key?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['Float']['output']>;
};

export type AggregatedStats = {
  __typename?: 'AggregatedStats';
  career: AggregatedStatsItem;
  season: AggregatedStatsItem;
};

export type AggregatedStatsItem = {
  __typename?: 'AggregatedStatsItem';
  assists?: Maybe<Scalars['Int']['output']>;
  gaa?: Maybe<Scalars['Float']['output']>;
  gamesPlayed: Scalars['Int']['output'];
  goals?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  losses?: Maybe<Scalars['Float']['output']>;
  points?: Maybe<Scalars['Int']['output']>;
  pointsPerGame?: Maybe<Scalars['Float']['output']>;
  shutouts?: Maybe<Scalars['Int']['output']>;
  svp?: Maybe<Scalars['Float']['output']>;
  ties?: Maybe<Scalars['Float']['output']>;
  wins?: Maybe<Scalars['Float']['output']>;
};

export type AlternativeName = {
  __typename?: 'AlternativeName';
  name: Scalars['String']['output'];
  slug: Scalars['String']['output'];
};

export type AlternativeNameInput = {
  name: Scalars['String']['input'];
};

export type AminUserEdge = {
  __typename?: 'AminUserEdge';
  node: AdminUser;
};

export type Avatars = {
  __typename?: 'Avatars';
  large: Scalars['String']['output'];
  medium: Scalars['String']['output'];
  og?: Maybe<Scalars['String']['output']>;
  small: Scalars['String']['output'];
  tiny: Scalars['String']['output'];
};

export type BatchNotification = {
  __typename?: 'BatchNotification';
  destinationPath?: Maybe<Scalars['String']['output']>;
  filter: BatchNotificationFilter;
  forcePush?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  message: Scalars['String']['output'];
  numberOfSent: Scalars['Float']['output'];
  scheduledAt?: Maybe<Scalars['DateTimeISO']['output']>;
  state: Scalars['String']['output'];
  title: Scalars['String']['output'];
  totalUsers: Scalars['Float']['output'];
  type: Scalars['String']['output'];
};

export type BatchNotificationEdge = {
  __typename?: 'BatchNotificationEdge';
  node: BatchNotification;
};

export type BatchNotificationError = IUserError & {
  __typename?: 'BatchNotificationError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type BatchNotificationFilter = {
  __typename?: 'BatchNotificationFilter';
  firstNameInitials?: Maybe<Array<Scalars['String']['output']>>;
  nationalities?: Maybe<Array<Scalars['String']['output']>>;
  numberOfVideos?: Maybe<Array<Scalars['Float']['output']>>;
  role: Role;
  yearOfBirth?: Maybe<Array<Scalars['Float']['output']>>;
};

export type BatchNotificationInput = {
  destinationPath?: InputMaybe<Scalars['String']['input']>;
  firstNameInitials?: InputMaybe<Array<Scalars['String']['input']>>;
  forcePush?: InputMaybe<Scalars['Boolean']['input']>;
  message: Scalars['String']['input'];
  nationalities?: InputMaybe<Array<Scalars['String']['input']>>;
  numberOfVideos?: InputMaybe<Array<Scalars['Float']['input']>>;
  role: Role;
  scheduledAt?: InputMaybe<Scalars['DateTimeISO']['input']>;
  title: Scalars['String']['input'];
  type: PushNotificationType;
  yearOfBirth?: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type BatchNotificationListResponse = {
  __typename?: 'BatchNotificationListResponse';
  edges: Array<BatchNotificationEdge>;
  pageInfo: PageInfo;
};

export type BatchNotificationNotFoundError = IUserError & {
  __typename?: 'BatchNotificationNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type BatchNotificationResponse = {
  __typename?: 'BatchNotificationResponse';
  errors?: Maybe<Array<BatchNotificationResponseError>>;
  notification?: Maybe<BatchNotification>;
};

export type BatchNotificationResponseError = BatchNotificationError | BatchNotificationNotFoundError;

export enum BioGender {
  Female = 'FEMALE',
  Male = 'MALE',
  PreferNotToSay = 'PREFER_NOT_TO_SAY'
}

export enum BioHandedness {
  Left = 'LEFT',
  Right = 'RIGHT'
}

export enum BioPlayerType {
  Butterfly = 'BUTTERFLY',
  Defensive = 'DEFENSIVE',
  Enforcer = 'ENFORCER',
  Grinder = 'GRINDER',
  Hybrid = 'HYBRID',
  Offensive = 'OFFENSIVE',
  Playmaker = 'PLAYMAKER',
  PowerForward = 'POWER_FORWARD',
  Sniper = 'SNIPER',
  Standup = 'STANDUP',
  TwoWay = 'TWO_WAY'
}

export enum BioPosition {
  Center = 'CENTER',
  Defender = 'DEFENDER',
  Goaltender = 'GOALTENDER',
  LeftDefensive = 'LEFT_DEFENSIVE',
  LeftWing = 'LEFT_WING',
  RightDefensive = 'RIGHT_DEFENSIVE',
  RightWing = 'RIGHT_WING'
}

export enum BioSchoolType {
  College = 'COLLEGE',
  ElementarySchool = 'ELEMENTARY_SCHOOL',
  GraduateSchool = 'GRADUATE_SCHOOL',
  HighSchool = 'HIGH_SCHOOL',
  Homeschooling = 'HOMESCHOOLING',
  MiddleSchool = 'MIDDLE_SCHOOL',
  MilitaryAcademy = 'MILITARY_ACADEMY',
  Other = 'OTHER',
  ProfessionalSchool = 'PROFESSIONAL_SCHOOL',
  University = 'UNIVERSITY'
}

export type Boost = {
  __typename?: 'Boost';
  amount: Scalars['Float']['output'];
  createdAt: Scalars['DateTimeISO']['output'];
  currency: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  message?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  state: Scalars['String']['output'];
  user?: Maybe<User>;
};

export type BoostEdge = {
  __typename?: 'BoostEdge';
  node: Boost;
};

export type BoostGoal = {
  __typename?: 'BoostGoal';
  currency: Scalars['String']['output'];
  currentAmount?: Maybe<Scalars['Float']['output']>;
  goalAmount: Scalars['Float']['output'];
};

export type BoostInput = {
  currency: Scalars['String']['input'];
  goalAmount: Scalars['Float']['input'];
};

export type BoostMessageAlreadySetError = IUserError & {
  __typename?: 'BoostMessageAlreadySetError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type BoostMessageInput = {
  message: Scalars['String']['input'];
  name: Scalars['String']['input'];
  paymentIntentId: Scalars['String']['input'];
};

export type BoostMessageResponse = {
  __typename?: 'BoostMessageResponse';
  boost?: Maybe<Boost>;
  errors?: Maybe<Array<BoostMessagerError>>;
};

export type BoostMessagerError = BoostMessageAlreadySetError | BoostNotFoundError;

export type BoostNotFoundError = IUserError & {
  __typename?: 'BoostNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type BoostsFilter = {
  slug?: InputMaybe<Scalars['String']['input']>;
};

export type BoostsResponse = {
  __typename?: 'BoostsResponse';
  edges: Array<BoostEdge>;
  pageInfo: PageInfo;
};

export type CategoryCount = {
  __typename?: 'CategoryCount';
  category: Scalars['String']['output'];
  count: Scalars['Float']['output'];
};

export type ClientStaticConfig = {
  __typename?: 'ClientStaticConfig';
  inviteContest: InviteContest;
};

export type Contact = {
  __typename?: 'Contact';
  email: Scalars['String']['output'];
  firstname?: Maybe<Scalars['String']['output']>;
  lastname?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  phoneNumber?: Maybe<PhoneNumber>;
  type?: Maybe<ContactType>;
};

export type ContactInput = {
  email: Scalars['String']['input'];
  firstname: Scalars['String']['input'];
  lastname: Scalars['String']['input'];
  phoneNumber?: InputMaybe<PhoneNumberInput>;
  type: ContactType;
};

export type ContactMessageInput = {
  agencyId?: InputMaybe<Scalars['ObjectId']['input']>;
  email: Scalars['String']['input'];
  message: Scalars['String']['input'];
  name: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type ContactMessageResponse = {
  __typename?: 'ContactMessageResponse';
  errors?: Maybe<Array<ContactMessagerError>>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type ContactMessagerError = ContactNotFoundError;

export type ContactNotFoundError = IUserError & {
  __typename?: 'ContactNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export enum ContactType {
  Agent = 'AGENT',
  Father = 'FATHER',
  Mother = 'MOTHER',
  Other = 'OTHER',
  Player = 'PLAYER'
}

export type Country = {
  country: Scalars['String']['input'];
};

export type CountryYearStats = {
  __typename?: 'CountryYearStats';
  count: Scalars['Int']['output'];
  country: Scalars['String']['output'];
  percentageOfTarget: Scalars['Float']['output'];
  target: Scalars['Int']['output'];
  year: Scalars['String']['output'];
};

export type CrawlerDomainConfig = {
  __typename?: 'CrawlerDomainConfig';
  domain: Scalars['String']['output'];
  enabled: Scalars['Boolean']['output'];
  fetchType?: Maybe<FetchType>;
  id: Scalars['ID']['output'];
  parallelism?: Maybe<Scalars['Float']['output']>;
  relevantUrlPatterns?: Maybe<Array<Scalars['String']['output']>>;
  urlConfigs: Array<CrawlerUrlConfig>;
};

export type CrawlerDomainConfigCreateError = GeneralError;

export type CrawlerDomainConfigCreateInput = {
  domain: Scalars['String']['input'];
  enabled?: InputMaybe<Scalars['Boolean']['input']>;
  fetchType?: InputMaybe<FetchType>;
  parallelism?: InputMaybe<Scalars['Float']['input']>;
  relevantUrlPatterns?: InputMaybe<Array<Scalars['String']['input']>>;
  urlConfigs: Array<CrawlerUrlConfigInput>;
};

export type CrawlerDomainConfigCreateResponse = {
  __typename?: 'CrawlerDomainConfigCreateResponse';
  config: CrawlerDomainConfig;
  errors?: Maybe<Array<CrawlerDomainConfigCreateError>>;
};

export type CrawlerDomainConfigEdge = {
  __typename?: 'CrawlerDomainConfigEdge';
  node: CrawlerDomainConfig;
};

export type CrawlerDomainConfigListResponse = {
  __typename?: 'CrawlerDomainConfigListResponse';
  edges: Array<CrawlerDomainConfigEdge>;
  pageInfo: PageInfo;
};

export type CrawlerDomainConfigMutations = {
  __typename?: 'CrawlerDomainConfigMutations';
  create: CrawlerDomainConfigCreateResponse;
  update: CrawlerDomainConfigUpdateResponse;
};


export type CrawlerDomainConfigMutationsCreateArgs = {
  input: CrawlerDomainConfigCreateInput;
};


export type CrawlerDomainConfigMutationsUpdateArgs = {
  id: Scalars['ObjectId']['input'];
  input: CrawlerDomainConfigUpdateInput;
};

export type CrawlerDomainConfigQueries = {
  __typename?: 'CrawlerDomainConfigQueries';
  get?: Maybe<CrawlerDomainConfig>;
  list: CrawlerDomainConfigListResponse;
};


export type CrawlerDomainConfigQueriesGetArgs = {
  id: Scalars['ObjectId']['input'];
};


export type CrawlerDomainConfigQueriesListArgs = {
  pagination?: InputMaybe<Pagination>;
};

export type CrawlerDomainConfigUpdateError = GeneralError;

export type CrawlerDomainConfigUpdateInput = {
  domain?: InputMaybe<Scalars['String']['input']>;
  enabled?: InputMaybe<Scalars['Boolean']['input']>;
  fetchType?: InputMaybe<FetchType>;
  parallelism?: InputMaybe<Scalars['Float']['input']>;
  relevantUrlPatterns?: InputMaybe<Array<Scalars['String']['input']>>;
  urlConfigs?: InputMaybe<Array<CrawlerUrlConfigInput>>;
};

export type CrawlerDomainConfigUpdateResponse = {
  __typename?: 'CrawlerDomainConfigUpdateResponse';
  config: CrawlerDomainConfig;
  errors?: Maybe<Array<CrawlerDomainConfigUpdateError>>;
};

export enum CrawlerEntity {
  Game = 'GAME',
  League = 'LEAGUE',
  Player = 'PLAYER',
  PlayerStats = 'PLAYER_STATS'
}

export type CrawlerLogs = {
  __typename?: 'CrawlerLogs';
  collectionId: Scalars['ObjectId']['output'];
  collectionName: Scalars['String']['output'];
  createdAt: Scalars['DateTimeISO']['output'];
  id: Scalars['ID']['output'];
  modelName: Scalars['String']['output'];
  op: Scalars['String']['output'];
  patch: Array<CrawlerLogsPatchOperation>;
  updatedAt: Scalars['DateTimeISO']['output'];
  user: CrawlerLogsUser;
  version: Scalars['Float']['output'];
};

export type CrawlerLogsEdge = {
  __typename?: 'CrawlerLogsEdge';
  node: CrawlerLogs;
};

export type CrawlerLogsFilter = {
  collectionId?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type CrawlerLogsPatchOperation = {
  __typename?: 'CrawlerLogsPatchOperation';
  op: Scalars['String']['output'];
  path: Scalars['String']['output'];
  value?: Maybe<Scalars['JSONObject']['output']>;
};

export type CrawlerLogsResponse = {
  __typename?: 'CrawlerLogsResponse';
  edges: Array<CrawlerLogsEdge>;
  pageInfo: PageInfo;
};

export type CrawlerLogsUser = {
  __typename?: 'CrawlerLogsUser';
  admin: Scalars['Boolean']['output'];
  email: Scalars['String']['output'];
  exp: Scalars['Float']['output'];
  iat: Scalars['Float']['output'];
  id: Scalars['String']['output'];
  role: Scalars['String']['output'];
};

export type CrawlerRunStats = {
  __typename?: 'CrawlerRunStats';
  crawlerDomainConfig?: Maybe<CrawlerDomainConfig>;
  durationMs: Scalars['Float']['output'];
  finishedAt: Scalars['DateTimeISO']['output'];
  id: Scalars['ID']['output'];
  newlyDiscoveredUrls: Scalars['Float']['output'];
  startedAt: Scalars['DateTimeISO']['output'];
  totalDiscoveredUrls: Scalars['Float']['output'];
  totalVisitedUrls: Scalars['Float']['output'];
  urlPatternStats?: Maybe<Array<DomainUrlStats>>;
};

export type CrawlerRunStatsEdge = {
  __typename?: 'CrawlerRunStatsEdge';
  node: CrawlerRunStats;
};

export type CrawlerRunStatsFilter = {
  crawlerDomainConfig?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type CrawlerRunStatsResponse = {
  __typename?: 'CrawlerRunStatsResponse';
  edges: Array<CrawlerRunStatsEdge>;
  pageInfo: PageInfo;
};

export type CrawlerUrlConfig = {
  __typename?: 'CrawlerUrlConfig';
  addToQueue?: Maybe<Scalars['Boolean']['output']>;
  allowedQueryParams?: Maybe<Array<Scalars['String']['output']>>;
  entity: CrawlerEntity;
  externalIdPatternRegExp?: Maybe<Scalars['String']['output']>;
  extractionScript?: Maybe<Scalars['String']['output']>;
  fetchType?: Maybe<FetchType>;
  initialUrls: Array<Scalars['String']['output']>;
  paramExtractors?: Maybe<Array<ParamExtractor>>;
  urlPatternRegExp: Scalars['String']['output'];
};

export type CrawlerUrlConfigInput = {
  addToQueue?: InputMaybe<Scalars['Boolean']['input']>;
  allowedQueryParams?: InputMaybe<Array<Scalars['String']['input']>>;
  entity: CrawlerEntity;
  externalIdPatternRegExp: Scalars['String']['input'];
  extractionScript: Scalars['String']['input'];
  fetchType?: InputMaybe<FetchType>;
  initialUrls: Array<Scalars['String']['input']>;
  paramExtractors?: InputMaybe<Array<ParamExtractorInput>>;
  urlPatternRegExp: Scalars['String']['input'];
};

export type CreateAdminInput = {
  email: Scalars['String']['input'];
  firstname: Scalars['String']['input'];
  lastname: Scalars['String']['input'];
};

export type CreateAdminResponse = {
  __typename?: 'CreateAdminResponse';
  user?: Maybe<AdminUser>;
};

export type CreateAgencyAlreadyExistsError = IUserError & {
  __typename?: 'CreateAgencyAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type CreateAgencyError = CreateAgencyAlreadyExistsError;

export type CreateAgencyInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  affiliateAgencies?: InputMaybe<Array<Scalars['ObjectId']['input']>>;
  categories?: InputMaybe<Array<SportCategory>>;
  country?: InputMaybe<Scalars['String']['input']>;
  drafts?: InputMaybe<AgencyDraftsInput>;
  email?: InputMaybe<Scalars['String']['input']>;
  epUrl?: InputMaybe<Scalars['String']['input']>;
  externalNumberOfClients?: InputMaybe<Scalars['Int']['input']>;
  facebookUrl?: InputMaybe<Scalars['String']['input']>;
  founded?: InputMaybe<Scalars['Int']['input']>;
  instagramFollowers?: InputMaybe<Scalars['Int']['input']>;
  instagramUrl?: InputMaybe<Scalars['String']['input']>;
  licences?: InputMaybe<Array<Scalars['String']['input']>>;
  linkedinUrl?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone?: InputMaybe<Scalars['String']['input']>;
  tiktokUrl?: InputMaybe<Scalars['String']['input']>;
  twitterUrl?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type CreateAgencyResponse = {
  __typename?: 'CreateAgencyResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<CreateAgencyError>>;
};

export type CreateBoostError = StripeNotConnectedError;

export type CreateBoostInput = {
  amount: Scalars['Int']['input'];
  currency: Scalars['String']['input'];
  userId: Scalars['ObjectId']['input'];
};

export type CreateBoostResponse = {
  __typename?: 'CreateBoostResponse';
  boost?: Maybe<Boost>;
  errors?: Maybe<Array<CreateBoostError>>;
  paymentSheet?: Maybe<PaymentSheet>;
};

export type CreateChatUsersInput = {
  userIds: Array<Scalars['ObjectId']['input']>;
};

export type CreateExternalAccountInput = {
  accountNumber: Scalars['String']['input'];
  bankAccount: StripeBankAccountInput;
  routingNumber?: InputMaybe<Scalars['String']['input']>;
};

export type CreateLeagueAlreadyExistsError = IUserError & {
  __typename?: 'CreateLeagueAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type CreateLeagueError = CreateLeagueAlreadyExistsError;

export type CreateLeagueInput = {
  alternativeNames?: InputMaybe<Array<AlternativeNameInput>>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  countries: Array<Scalars['String']['input']>;
  eliteProspectSlug?: InputMaybe<Scalars['String']['input']>;
  facebookUrl?: InputMaybe<Scalars['String']['input']>;
  genderCategory?: InputMaybe<LeagueGenderCategory>;
  ignore?: InputMaybe<Scalars['Boolean']['input']>;
  instagramUrl?: InputMaybe<Scalars['String']['input']>;
  level?: InputMaybe<LeagueLevel>;
  logo?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  publicAlternativeName?: InputMaybe<Scalars['String']['input']>;
  scraped?: InputMaybe<Scalars['Boolean']['input']>;
  sourceUrl?: InputMaybe<Scalars['String']['input']>;
  teamAutoConnectRules?: InputMaybe<TeamAutoConnectRulesInput>;
  tiktokUrl?: InputMaybe<Scalars['String']['input']>;
  twitterUrl?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<LeagueType>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type CreateLeagueResponse = {
  __typename?: 'CreateLeagueResponse';
  errors?: Maybe<Array<CreateLeagueError>>;
  league?: Maybe<League>;
};

export type CreateOrganizationAlreadyExistsError = IUserError & {
  __typename?: 'CreateOrganizationAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type CreateOrganizationError = CreateOrganizationAlreadyExistsError;

export type CreateOrganizationInput = {
  abbreviation?: InputMaybe<Scalars['String']['input']>;
  alternativeNames?: InputMaybe<Array<AlternativeNameInput>>;
  arenaAddress?: InputMaybe<Scalars['String']['input']>;
  arenaName?: InputMaybe<Scalars['String']['input']>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  country: Scalars['String']['input'];
  facebookUrl?: InputMaybe<Scalars['String']['input']>;
  instagramUrl?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  province?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  tiktokUrl?: InputMaybe<Scalars['String']['input']>;
  twitterUrl?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type CreateOrganizationResponse = {
  __typename?: 'CreateOrganizationResponse';
  errors?: Maybe<Array<CreateOrganizationError>>;
  organization?: Maybe<Organization>;
};

export type CreatePassiveEventInput = {
  params: Scalars['String']['input'];
  pathname: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};

export type CreateStripeAccountError = AccountAlreadyExistsError | StripeAccountUserInputError;

export type CreateStripeAccountInput = {
  address?: InputMaybe<StripeAddressInput>;
  country?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  dob?: InputMaybe<Scalars['DateTimeISO']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  firstname?: InputMaybe<Scalars['String']['input']>;
  jobTitle?: InputMaybe<Scalars['String']['input']>;
  lastname?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  ssnLast4?: InputMaybe<Scalars['String']['input']>;
  verification?: InputMaybe<StripeAccountVerification>;
};

export type CreateStripeAccountResponse = {
  __typename?: 'CreateStripeAccountResponse';
  account?: Maybe<StripeAccount>;
  errors: Array<CreateStripeAccountError>;
};

export type CreateStripeExternalAccountError = AccountDoesNotExistsError | ExternalAccountAlreadyExistsError | ExternalAccountUserInputError;

export type CreateStripeExternalAccountResponse = {
  __typename?: 'CreateStripeExternalAccountResponse';
  errors: Array<CreateStripeExternalAccountError>;
  externalAccount?: Maybe<StripeExternalAccount>;
};

export type CreateTeamAlreadyExistsError = IUserError & {
  __typename?: 'CreateTeamAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type CreateTeamError = CreateTeamAlreadyExistsError;

export type CreateTeamInput = {
  abbreviation?: InputMaybe<Scalars['String']['input']>;
  alternativeNames?: InputMaybe<Array<AlternativeNameInput>>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  country: Scalars['String']['input'];
  eliteProspectLink?: InputMaybe<Scalars['String']['input']>;
  externalId?: InputMaybe<Scalars['String']['input']>;
  leagues?: InputMaybe<Array<Scalars['ObjectId']['input']>>;
  name: Scalars['String']['input'];
  organization?: InputMaybe<Scalars['String']['input']>;
  shortName?: InputMaybe<Scalars['String']['input']>;
};

export type CreateTeamResponse = {
  __typename?: 'CreateTeamResponse';
  errors?: Maybe<Array<CreateTeamError>>;
  team?: Maybe<Team>;
};

export type CreateUserStatsAlreadyExistsError = IUserError & {
  __typename?: 'CreateUserStatsAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type CreateUserStatsError = CreateUserStatsAlreadyExistsError;

export type CreateUserStatsInput = {
  assists?: InputMaybe<Scalars['Int']['input']>;
  gaa?: InputMaybe<Scalars['Float']['input']>;
  gamesPlayed?: InputMaybe<Scalars['Int']['input']>;
  goals?: InputMaybe<Scalars['Int']['input']>;
  league?: InputMaybe<Scalars['ObjectId']['input']>;
  losses?: InputMaybe<Scalars['Int']['input']>;
  orderInSeason?: InputMaybe<Scalars['Int']['input']>;
  season: Scalars['String']['input'];
  shutouts?: InputMaybe<Scalars['Int']['input']>;
  svp?: InputMaybe<Scalars['Float']['input']>;
  team?: InputMaybe<Scalars['ObjectId']['input']>;
  ties?: InputMaybe<Scalars['Int']['input']>;
  user: Scalars['ObjectId']['input'];
  wins?: InputMaybe<Scalars['Int']['input']>;
};

export type CreateUserStatsResponse = {
  __typename?: 'CreateUserStatsResponse';
  errors?: Maybe<Array<CreateUserStatsError>>;
  userStats?: Maybe<UserStats>;
};

export type CurrencyUpdateError = IUserError & {
  __typename?: 'CurrencyUpdateError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

/** Indicates which team the current user is on in a game */
export enum CurrentUserTeam {
  AwayTeam = 'AWAY_TEAM',
  HomeTeam = 'HOME_TEAM',
  Unknown = 'UNKNOWN'
}

export type DeleteUserStatsError = UserNotFoundError;

export type DeleteUserStatsResponse = {
  __typename?: 'DeleteUserStatsResponse';
  errors?: Maybe<Array<DeleteUserStatsError>>;
  success: Scalars['Boolean']['output'];
};

export type DeleteWallVideoError = FailedToDelete;

export type DeleteWallVideoResponse = {
  __typename?: 'DeleteWallVideoResponse';
  errors?: Maybe<Array<DeleteWallVideoError>>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type Device = {
  __typename?: 'Device';
  arn?: Maybe<Scalars['String']['output']>;
  platform: Platform;
  token: Scalars['String']['output'];
};

export type DeviceAlreadyRegisteredError = IUserError & {
  __typename?: 'DeviceAlreadyRegisteredError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type DeviceNotRegisteredError = IUserError & {
  __typename?: 'DeviceNotRegisteredError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type DiscoveredUrl = {
  __typename?: 'DiscoveredUrl';
  crawlerDomainConfig?: Maybe<CrawlerDomainConfig>;
  entity: CrawlerEntity;
  externalId?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  scraperInfo?: Maybe<DiscoveredUrlScraperInfo>;
  url: Scalars['String']['output'];
};

export type DiscoveredUrlEdge = {
  __typename?: 'DiscoveredUrlEdge';
  node: DiscoveredUrl;
};

export type DiscoveredUrlFilter = {
  crawlerDomainConfig?: InputMaybe<Scalars['ObjectId']['input']>;
  entity?: InputMaybe<CrawlerEntity>;
  scrapeSuccessful?: InputMaybe<Scalars['Boolean']['input']>;
};

export type DiscoveredUrlListResponse = {
  __typename?: 'DiscoveredUrlListResponse';
  edges: Array<DiscoveredUrlEdge>;
  pageInfo: PageInfo;
};

export type DiscoveredUrlScraperInfo = {
  __typename?: 'DiscoveredUrlScraperInfo';
  errorMessage?: Maybe<Scalars['String']['output']>;
  httpStatus?: Maybe<Scalars['Float']['output']>;
  lastScrapedAt: Scalars['DateTimeISO']['output'];
  scheduledForScrapeAt?: Maybe<Scalars['DateTimeISO']['output']>;
  scrapeAttempts?: Maybe<Scalars['Float']['output']>;
  scrapeSuccessful?: Maybe<Scalars['Boolean']['output']>;
  unsuccessfulScrapes?: Maybe<Scalars['Float']['output']>;
};

export type DomainUrlStats = {
  __typename?: 'DomainUrlStats';
  matchCount: Scalars['Float']['output'];
  urlPattern: Scalars['String']['output'];
};

export type EpAgencyNotFoundError = IUserError & {
  __typename?: 'EPAgencyNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type EpUserNotFoundError = IUserError & {
  __typename?: 'EPUserNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type EditHighlightsError = FailedToEdit;

export type EditHighlightsInput = {
  highlights: Array<Scalars['ObjectId']['input']>;
};

export type EditHighlightsResponse = {
  __typename?: 'EditHighlightsResponse';
  errors?: Maybe<Array<EditHighlightsError>>;
  user?: Maybe<User>;
};

export type EditWallVideoCategoryError = FailedToEdit | WallVideoNotFound;

export type EditWallVideoCategoryResponse = {
  __typename?: 'EditWallVideoCategoryResponse';
  errors?: Maybe<Array<EditWallVideoCategoryError>>;
  wallVideo?: Maybe<WallVideo>;
};

export type EditWallVideoInput = {
  id: Scalars['ObjectId']['input'];
  type: WallVideoType;
};

export type EliteProspectCountry = {
  __typename?: 'EliteProspectCountry';
  iso_3166_1_alpha_2?: Maybe<Scalars['String']['output']>;
};

export type EliteProspectLogo = {
  __typename?: 'EliteProspectLogo';
  large: Scalars['String']['output'];
};

export type EliteProspectTeam = {
  __typename?: 'EliteProspectTeam';
  country: EliteProspectCountry;
  id: Scalars['String']['output'];
  logo?: Maybe<EliteProspectLogo>;
  name: Scalars['String']['output'];
  slug: Scalars['String']['output'];
};

export type EntityCountsResponse = {
  __typename?: 'EntityCountsResponse';
  agencies: Scalars['Int']['output'];
  players: Scalars['Int']['output'];
  professionals: Scalars['Int']['output'];
};

export type EntityNotFoundError = IUserError & {
  __typename?: 'EntityNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type ExternalAccountAlreadyExistsError = IUserError & {
  __typename?: 'ExternalAccountAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type ExternalAccountUserInputError = IUserError & {
  __typename?: 'ExternalAccountUserInputError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type ExternalConnection = {
  __typename?: 'ExternalConnection';
  crawlerDomainConfig: CrawlerDomainConfig;
  externalId: Scalars['String']['output'];
  name?: Maybe<Scalars['String']['output']>;
};

export type ExternalConnectionError = IUserError & {
  __typename?: 'ExternalConnectionError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type ExternalInfo = {
  __typename?: 'ExternalInfo';
  externalLeagueCountry?: Maybe<Scalars['String']['output']>;
  externalLeagueName?: Maybe<Scalars['String']['output']>;
  externalPlayerName?: Maybe<Scalars['String']['output']>;
  externalTeamCountry?: Maybe<Scalars['String']['output']>;
  externalTeamName?: Maybe<Scalars['String']['output']>;
  stage?: Maybe<LeagueStage>;
};

export type ExternalLink = {
  __typename?: 'ExternalLink';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  scrapable?: Maybe<Scalars['Boolean']['output']>;
  type?: Maybe<ExternalLinkType>;
  url: Scalars['String']['output'];
};

export type ExternalLinkInput = {
  name: Scalars['String']['input'];
  scrapable?: InputMaybe<Scalars['Boolean']['input']>;
  type: ExternalLinkType;
  url: Scalars['String']['input'];
};

export enum ExternalLinkType {
  Custom = 'CUSTOM',
  EliteProspects = 'ELITE_PROSPECTS',
  Instagram = 'INSTAGRAM',
  Linkedin = 'LINKEDIN',
  Tiktok = 'TIKTOK',
  X = 'X'
}

/** StreamFeed group names */
export enum Feed_Names {
  Game = 'GAME',
  Notification = 'NOTIFICATION',
  Team = 'TEAM',
  Timeline = 'TIMELINE',
  User = 'USER'
}

export type FailedToCreatePresignedUrl = IUserError & {
  __typename?: 'FailedToCreatePresignedUrl';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type FailedToDelete = IUserError & {
  __typename?: 'FailedToDelete';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type FailedToEdit = IUserError & {
  __typename?: 'FailedToEdit';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type FailedToUpdateStats = {
  __typename?: 'FailedToUpdateStats';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type FamilyMemberAlreadyExistsError = IUserError & {
  __typename?: 'FamilyMemberAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type FamilyMemberError = FamilyMemberAlreadyExistsError | SelfFamilyMemberError | UserNotFoundError | UserNotInFamilyError | UserNotInPendingError;

export type FamilyMemberUpdateResponse = {
  __typename?: 'FamilyMemberUpdateResponse';
  errors?: Maybe<Array<FamilyMemberError>>;
  user?: Maybe<User>;
};

/** StreamFeed activity verbs */
export enum FeedActivityVerb {
  Add = 'ADD',
  BoostReceived = 'BOOST_RECEIVED',
  Follow = 'FOLLOW',
  Followed = 'FOLLOWED',
  FollowAgent = 'FOLLOW_AGENT',
  FollowCoach = 'FOLLOW_COACH',
  FollowOther = 'FOLLOW_OTHER',
  FollowScout = 'FOLLOW_SCOUT',
  Fire = 'Fire',
  FireAgent = 'FireAgent',
  FireCoach = 'FireCoach',
  FireScout = 'FireScout',
  GameResult = 'GameResult',
  IceHockeyMatch = 'IceHockeyMatch',
  Like = 'LIKE',
  LikeHighlight = 'LIKE_HIGHLIGHT',
  LikeVideo = 'LIKE_VIDEO',
  NewTeamMember = 'NEW_TEAM_MEMBER',
  View = 'VIEW',
  ViewAgent = 'VIEW_AGENT',
  ViewCoach = 'VIEW_COACH',
  ViewInsights = 'VIEW_INSIGHTS',
  ViewScout = 'VIEW_SCOUT'
}

export enum FetchType {
  Axios = 'Axios',
  Puppeteer = 'Puppeteer'
}

export type FetchUserDataEpError = IUserError & {
  __typename?: 'FetchUserDataEPError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type FetchUserDataEpResponse = {
  __typename?: 'FetchUserDataEPResponse';
  errors: Array<FetchUserDataEpError>;
  user?: Maybe<User>;
};

export type FollowAgencyError = FollowAgencyNotFoundError;

export type FollowAgencyNotFoundError = IUserError & {
  __typename?: 'FollowAgencyNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type FollowAgencyResponse = {
  __typename?: 'FollowAgencyResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<FollowAgencyError>>;
};

export type FollowTeamError = EntityNotFoundError;

export type FollowTeamResponse = {
  __typename?: 'FollowTeamResponse';
  errors?: Maybe<Array<FollowTeamError>>;
  team?: Maybe<Team>;
};

export type GameEdge = {
  __typename?: 'GameEdge';
  node: GameStats;
};

export type GameEvent = {
  __typename?: 'GameEvent';
  assists?: Maybe<Array<RosterPlayerExternalInfo>>;
  description?: Maybe<Scalars['String']['output']>;
  descriptionCode?: Maybe<Scalars['String']['output']>;
  externalInfo: GameEventExternalInfo;
  isHomeTeam?: Maybe<Scalars['Boolean']['output']>;
  penaltyTime?: Maybe<Scalars['String']['output']>;
  player?: Maybe<User>;
  score?: Maybe<Scalars['String']['output']>;
  time: Scalars['String']['output'];
  type: GameEventType;
};

export type GameEventExternalInfo = {
  __typename?: 'GameEventExternalInfo';
  playerExternalId?: Maybe<Scalars['String']['output']>;
  playerName?: Maybe<Scalars['String']['output']>;
  teamName: Scalars['String']['output'];
};

export enum GameEventType {
  Goal = 'Goal',
  GoalieEvent = 'GoalieEvent',
  Penalty = 'Penalty',
  Unknown = 'Unknown'
}

export type GameExternalInfo = {
  __typename?: 'GameExternalInfo';
  awayTeamName: Scalars['String']['output'];
  homeTeamName: Scalars['String']['output'];
  leagueName: Scalars['String']['output'];
};

export type GameOverallStats = {
  __typename?: 'GameOverallStats';
  awayTeam?: Maybe<GameTeamStats>;
  homeTeam?: Maybe<GameTeamStats>;
  periodScores?: Maybe<Scalars['String']['output']>;
  scores?: Maybe<Array<GameScore>>;
};

export type GameResponse = {
  __typename?: 'GameResponse';
  edges: Array<GameEdge>;
  pageInfo: PageInfo;
};

export enum GameResultType {
  Forfeit = 'Forfeit',
  Overtime = 'Overtime',
  Regular = 'Regular',
  Shootout = 'Shootout'
}

export type GameScore = {
  __typename?: 'GameScore';
  awayTeam?: Maybe<Scalars['Float']['output']>;
  homeTeam?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<GameScoreType>;
};

export enum GameScoreType {
  Overtime = 'Overtime',
  Regular = 'Regular',
  Shootout = 'Shootout'
}

export type GameStats = {
  __typename?: 'GameStats';
  awayTeam?: Maybe<Team>;
  awayTeamRoster?: Maybe<Array<RosterPlayer>>;
  date: Scalars['DateTimeISO']['output'];
  discoveredUrl: DiscoveredUrl;
  events?: Maybe<Array<GameEvent>>;
  eventsByPeriod?: Maybe<Array<Array<GameEvent>>>;
  externalId: Scalars['String']['output'];
  externalInfo: GameExternalInfo;
  gameResult?: Maybe<GameResultType>;
  homeTeam?: Maybe<Team>;
  homeTeamRoster?: Maybe<Array<RosterPlayer>>;
  id: Scalars['ID']['output'];
  ignore?: Maybe<Scalars['Boolean']['output']>;
  league?: Maybe<League>;
  season?: Maybe<Scalars['String']['output']>;
  stats?: Maybe<GameOverallStats>;
  streamFeedActivityId?: Maybe<Scalars['String']['output']>;
  syncedAt?: Maybe<Scalars['DateTimeISO']['output']>;
};

export type GameTeamStats = {
  __typename?: 'GameTeamStats';
  blockedShots?: Maybe<Scalars['Float']['output']>;
  faceoffs?: Maybe<Scalars['Float']['output']>;
  goalieSaves?: Maybe<Scalars['Float']['output']>;
  majorPenalties?: Maybe<Scalars['Float']['output']>;
  penalties?: Maybe<Scalars['Float']['output']>;
  penaltyMinutes?: Maybe<Scalars['Float']['output']>;
  powerPlayGoals?: Maybe<Scalars['Float']['output']>;
  score?: Maybe<Scalars['Float']['output']>;
  shortHandedGoals?: Maybe<Scalars['Float']['output']>;
  shotsOnGoal?: Maybe<Scalars['Float']['output']>;
};

export type GamesEdge = {
  __typename?: 'GamesEdge';
  currentUserPlayer?: Maybe<RosterPlayer>;
  currentUserTeam?: Maybe<CurrentUserTeam>;
  node: GameStats;
};

export type GamesFilter = {
  leagueId?: InputMaybe<Scalars['ObjectId']['input']>;
  period?: InputMaybe<GamesPeriod>;
  season?: InputMaybe<Scalars['String']['input']>;
  teamId?: InputMaybe<Scalars['ObjectId']['input']>;
  userId?: InputMaybe<Scalars['ObjectId']['input']>;
};

/** The period filter for games (ALL, HISTORY, FUTURE) */
export enum GamesPeriod {
  All = 'ALL',
  Future = 'FUTURE',
  History = 'HISTORY'
}

export type GamesPlayersInput = {
  addGamePerformanceLikes?: Scalars['Boolean']['input'];
  gameIds: Array<Scalars['ObjectId']['input']>;
};

export type GamesPlayersResponse = {
  __typename?: 'GamesPlayersResponse';
  games?: Maybe<Array<GameStats>>;
};

export type GamesResponse = {
  __typename?: 'GamesResponse';
  edges: Array<GamesEdge>;
  pageInfo: PageInfo;
};

export type GeneralError = IUserError & {
  __typename?: 'GeneralError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type GraetChatUser = {
  __typename?: 'GraetChatUser';
  firstname?: Maybe<Scalars['String']['output']>;
  graet_agent_agency_name?: Maybe<Scalars['String']['output']>;
  graet_role?: Maybe<Scalars['String']['output']>;
  graet_sub_role?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastname?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  slug?: Maybe<Scalars['String']['output']>;
};

export type Height = {
  __typename?: 'Height';
  centimeters?: Maybe<Scalars['Int']['output']>;
  feet?: Maybe<Scalars['Int']['output']>;
  inches?: Maybe<Scalars['Int']['output']>;
};

export type HeightInput = {
  centimeters: Scalars['Int']['input'];
  feet: Scalars['Int']['input'];
  inches: Scalars['Int']['input'];
};

export type IUserError = {
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type InvalidEpPlayerDataError = IUserError & {
  __typename?: 'InvalidEPPlayerDataError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type InvalidVerificationCodeError = IUserError & {
  __typename?: 'InvalidVerificationCodeError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type InviteAgentsAlreadyExistsError = IUserError & {
  __typename?: 'InviteAgentsAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type InviteAgentsError = InviteAgentsAlreadyExistsError;

export type InviteAgentsInput = {
  agencyId?: InputMaybe<Scalars['ID']['input']>;
  emails: Array<Scalars['String']['input']>;
};

export type InviteAgentsResponse = {
  __typename?: 'InviteAgentsResponse';
  errors?: Maybe<Array<InviteAgentsError>>;
  success: Scalars['Boolean']['output'];
};

export type InviteContest = {
  __typename?: 'InviteContest';
  enabled: Scalars['Boolean']['output'];
};

export type JwtTokens = {
  __typename?: 'JWTTokens';
  accessToken: Scalars['String']['output'];
  refreshToken: Scalars['String']['output'];
};

export type LatestClipsFilter = {
  agencyId?: InputMaybe<Scalars['ObjectId']['input']>;
  countries?: InputMaybe<Array<Scalars['String']['input']>>;
  positions?: InputMaybe<Array<BioPosition>>;
  teamId?: InputMaybe<Scalars['ObjectId']['input']>;
  yearOfBirth?: InputMaybe<Array<Scalars['Int']['input']>>;
};

export type League = {
  __typename?: 'League';
  alternativeNames?: Maybe<Array<AlternativeName>>;
  checked?: Maybe<Scalars['Boolean']['output']>;
  countries?: Maybe<Array<Scalars['String']['output']>>;
  /** @deprecated Use `countries` instead */
  country?: Maybe<Scalars['String']['output']>;
  eliteProspectSlug?: Maybe<Scalars['String']['output']>;
  facebookUrl?: Maybe<Scalars['String']['output']>;
  genderCategory?: Maybe<LeagueGenderCategory>;
  hasGames?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  ignore?: Maybe<Scalars['Boolean']['output']>;
  instagramUrl?: Maybe<Scalars['String']['output']>;
  level?: Maybe<LeagueLevel>;
  logo?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  ogLogo?: Maybe<Scalars['String']['output']>;
  publicAlternativeName?: Maybe<Scalars['String']['output']>;
  scraped?: Maybe<Scalars['Boolean']['output']>;
  slug: Scalars['String']['output'];
  sourceUrl?: Maybe<Scalars['String']['output']>;
  teamAutoConnectRules?: Maybe<TeamAutoConnectRules>;
  tiktokUrl?: Maybe<Scalars['String']['output']>;
  twitterUrl?: Maybe<Scalars['String']['output']>;
  type?: Maybe<LeagueType>;
  website?: Maybe<Scalars['String']['output']>;
};

export type LeagueAlreadyExistsError = IUserError & {
  __typename?: 'LeagueAlreadyExistsError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type LeagueEdge = {
  __typename?: 'LeagueEdge';
  node: League;
};

export enum LeagueGenderCategory {
  Men = 'MEN',
  Mixed = 'MIXED',
  Women = 'WOMEN'
}

export enum LeagueLevel {
  College = 'COLLEGE',
  Junior = 'JUNIOR',
  Professional = 'PROFESSIONAL',
  SemiProfessional = 'SEMI_PROFESSIONAL',
  Youth = 'YOUTH'
}

export type LeagueNotFoundError = IUserError & {
  __typename?: 'LeagueNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type LeagueStage = {
  __typename?: 'LeagueStage';
  label?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type LeagueStages = {
  __typename?: 'LeagueStages';
  league?: Maybe<League>;
  stages: Array<LeagueStage>;
};

export type LeagueStandingsGroup = {
  __typename?: 'LeagueStandingsGroup';
  group: Scalars['String']['output'];
  standings: Array<LeagueStats>;
};

export type LeagueStandingsResponse = {
  __typename?: 'LeagueStandingsResponse';
  groups: Array<LeagueStandingsGroup>;
  league: League;
};

export type LeagueStats = {
  __typename?: 'LeagueStats';
  discoveredUrl: DiscoveredUrl;
  externalId: Scalars['String']['output'];
  externalInfo: LeagueStatsExternalInfo;
  games: Scalars['Float']['output'];
  goalsAgainst: Scalars['Float']['output'];
  goalsFor: Scalars['Float']['output'];
  group?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  league?: Maybe<League>;
  losses: Scalars['Float']['output'];
  overtimeLosses: Scalars['Float']['output'];
  overtimeWins: Scalars['Float']['output'];
  points: Scalars['Float']['output'];
  season: Scalars['String']['output'];
  standing: Scalars['Float']['output'];
  syncedAt?: Maybe<Scalars['DateTimeISO']['output']>;
  team?: Maybe<Team>;
  ties: Scalars['Float']['output'];
  wins: Scalars['Float']['output'];
};

export type LeagueStatsExternalInfo = {
  __typename?: 'LeagueStatsExternalInfo';
  leagueName: Scalars['String']['output'];
  teamName: Scalars['String']['output'];
};

export enum LeagueType {
  League = 'LEAGUE',
  Tournament = 'TOURNAMENT'
}

export type LeaguesFilter = {
  searchQuery?: InputMaybe<Scalars['String']['input']>;
};

export type LeaguesResponse = {
  __typename?: 'LeaguesResponse';
  edges: Array<LeagueEdge>;
  pageInfo: PageInfo;
};

export type LikePlayerPerformanceError = EntityNotFoundError | UserNotFoundError;

export type LikePlayerPerformanceResponse = {
  __typename?: 'LikePlayerPerformanceResponse';
  errors: Array<LikePlayerPerformanceError>;
  player?: Maybe<User>;
};

export type LoginAdminError = UserNotFoundError;

export type LoginAdminResponse = {
  __typename?: 'LoginAdminResponse';
  errors: Array<LoginAdminError>;
};

export type MarkUserAsDeletedError = UserAlreadyMarkedForDeletionError | UserNotFoundError;

export type MarkUserAsDeletedResponse = {
  __typename?: 'MarkUserAsDeletedResponse';
  errors?: Maybe<Array<MarkUserAsDeletedError>>;
  success: Scalars['Boolean']['output'];
  user?: Maybe<User>;
};

export type MigrateUserStatsError = FailedToUpdateStats;

export type MigrateUserStatsInput = {
  from: Scalars['ObjectId']['input'];
  league?: InputMaybe<Scalars['ObjectId']['input']>;
  to: Scalars['ObjectId']['input'];
  type: MigrateUserStatsType;
};

export type MigrateUserStatsResponse = {
  __typename?: 'MigrateUserStatsResponse';
  errors?: Maybe<Array<MigrateUserStatsError>>;
  modifiedCount?: Maybe<Scalars['Int']['output']>;
  success: Scalars['Boolean']['output'];
};

/** The type of migration to perform */
export enum MigrateUserStatsType {
  League = 'LEAGUE',
  Team = 'TEAM'
}

export type Mutation = {
  __typename?: 'Mutation';
  acceptAgencyInvitation: AcceptAgencyInvitationResponse;
  addFamilyMember: FamilyMemberUpdateResponse;
  admin: AdminMutation;
  appleSignIn: SocialSignInResponse;
  changeTeamOrderDown: Scalars['Boolean']['output'];
  changeTeamOrderUp: Scalars['Boolean']['output'];
  confirmFamilyMember: FamilyMemberUpdateResponse;
  createAdmin: CreateAdminResponse;
  createAgency: CreateAgencyResponse;
  createBoost: CreateBoostResponse;
  createBoostMessage: BoostMessageResponse;
  createChatUserToken: Scalars['String']['output'];
  createChatUsers: Scalars['Boolean']['output'];
  createLeague: CreateLeagueResponse;
  createOrganization: CreateOrganizationResponse;
  createPassiveEvent: Scalars['Boolean']['output'];
  createStreamFeedUserToken: Scalars['String']['output'];
  createStripeAccount: CreateStripeAccountResponse;
  createStripeExternalAccount: CreateStripeExternalAccountResponse;
  createTeam: CreateTeamResponse;
  createUserStats: CreateUserStatsResponse;
  deleteAccount: Scalars['Boolean']['output'];
  deleteAdmin: Scalars['Boolean']['output'];
  deleteAgency: Scalars['Boolean']['output'];
  deleteLeague: Scalars['Boolean']['output'];
  deleteOrganization: Scalars['Boolean']['output'];
  deleteStripeExternalAccount: Scalars['Boolean']['output'];
  deleteTeam: Scalars['Boolean']['output'];
  deleteUserStats: Scalars['Boolean']['output'];
  deleteWallVideo: DeleteWallVideoResponse;
  editHighlights: EditHighlightsResponse;
  editWallVideoCategory: EditWallVideoCategoryResponse;
  fetchUserDataEP: FetchUserDataEpResponse;
  followAgency: FollowAgencyResponse;
  followTeam: FollowTeamResponse;
  followUser: User;
  googleSignIn: SocialSignInResponse;
  googleSignInAdmin: AdminSocialSignInResponse;
  inviteAgents: InviteAgentsResponse;
  likePlayerPerformance: LikePlayerPerformanceResponse;
  likeWallVideo: Scalars['Boolean']['output'];
  loginAdmin: LoginAdminResponse;
  pushNotifications: PushNotificationsResponse;
  registerDevice: RegisterDeviceResponse;
  registerOrLoginByEmail: RegisteryOrLoginByEmailResponse;
  removeFamilyMember: FamilyMemberUpdateResponse;
  removePendingFamilyMember: FamilyMemberUpdateResponse;
  sendContactMessage: ContactMessageResponse;
  syncUserStats: Scalars['Boolean']['output'];
  unfollowAgency: UnfollowAgencyResponse;
  unfollowTeam: UnfollowTeamResponse;
  unfollowUser: UnfollowUserResponse;
  unlikePlayerPerformance: UnlikePlayerPerformanceResponse;
  unregisterDevice: UnregisterDeviceResponse;
  updateAgency: UpdateAgencyResponse;
  updateLeague: UpdateLeagueResponse;
  updateOrganization: UpdateOrganizationResponse;
  updateStripeAccount: UpdateStripeAccountResponse;
  updateTeam: UpdateTeamResponse;
  updateUser: UpdateUserResponse;
  updateUserAdmin: UpdateUserAdminResponse;
  updateUserCurrency: UpdateDefaultCurrencyResponse;
  updateUserStats: UpdateUserStatsResponse;
  uploadAgencyLogo: UploadAgencyLogoResponse;
  uploadAvatar: UploadAvatarResponse;
  uploadOrganizationLogo: UploadOrganizationLogoResponse;
  uploadTeamLogo: UploadTeamLogoResponse;
  uploadWallVideo: UploadWallVideoResponse;
  uploadWallVideoSegmentation: UploadWallVideoSegmentationResponse;
  verifyAdminByEmail: VerifyAdminByEmailResponse;
  verifyByEmail: VerifyByEmailResponse;
  visitAgency: VisitAgencyResponse;
  visitTeam: VisitTeamResponse;
  visitUser: User;
};


export type MutationAcceptAgencyInvitationArgs = {
  agencyId: Scalars['ObjectId']['input'];
  token: Scalars['String']['input'];
};


export type MutationAddFamilyMemberArgs = {
  userId: Scalars['String']['input'];
};


export type MutationAppleSignInArgs = {
  input: SocialSignInInput;
};


export type MutationChangeTeamOrderDownArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationChangeTeamOrderUpArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationConfirmFamilyMemberArgs = {
  userId: Scalars['String']['input'];
};


export type MutationCreateAdminArgs = {
  input: CreateAdminInput;
};


export type MutationCreateAgencyArgs = {
  input: CreateAgencyInput;
};


export type MutationCreateBoostArgs = {
  input: CreateBoostInput;
};


export type MutationCreateBoostMessageArgs = {
  input?: InputMaybe<BoostMessageInput>;
};


export type MutationCreateChatUsersArgs = {
  input: CreateChatUsersInput;
};


export type MutationCreateLeagueArgs = {
  input: CreateLeagueInput;
};


export type MutationCreateOrganizationArgs = {
  input: CreateOrganizationInput;
};


export type MutationCreatePassiveEventArgs = {
  input: CreatePassiveEventInput;
};


export type MutationCreateStripeAccountArgs = {
  input: CreateStripeAccountInput;
};


export type MutationCreateStripeExternalAccountArgs = {
  input: CreateExternalAccountInput;
};


export type MutationCreateTeamArgs = {
  input: CreateTeamInput;
};


export type MutationCreateUserStatsArgs = {
  input: CreateUserStatsInput;
};


export type MutationDeleteAdminArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationDeleteAgencyArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationDeleteLeagueArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationDeleteOrganizationArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationDeleteTeamArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationDeleteUserStatsArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationDeleteWallVideoArgs = {
  id: Scalars['String']['input'];
};


export type MutationEditHighlightsArgs = {
  input: EditHighlightsInput;
};


export type MutationEditWallVideoCategoryArgs = {
  input: EditWallVideoInput;
};


export type MutationFollowAgencyArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationFollowTeamArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationFollowUserArgs = {
  id: Scalars['String']['input'];
};


export type MutationGoogleSignInArgs = {
  input: SocialSignInInput;
};


export type MutationGoogleSignInAdminArgs = {
  input: AdminSocialSignInInput;
};


export type MutationInviteAgentsArgs = {
  input: InviteAgentsInput;
};


export type MutationLikePlayerPerformanceArgs = {
  gameId: Scalars['ObjectId']['input'];
  playerId: Scalars['ObjectId']['input'];
};


export type MutationLikeWallVideoArgs = {
  id: Scalars['String']['input'];
  isHighlight?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationLoginAdminArgs = {
  email: Scalars['String']['input'];
};


export type MutationPushNotificationsArgs = {
  input: PushNotificationsInput;
};


export type MutationRegisterDeviceArgs = {
  input: RegisterDeviceInput;
};


export type MutationRegisterOrLoginByEmailArgs = {
  input: RegisterOrLoginByEmailInput;
  newUrl?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationRemoveFamilyMemberArgs = {
  userId: Scalars['String']['input'];
};


export type MutationRemovePendingFamilyMemberArgs = {
  userId: Scalars['String']['input'];
};


export type MutationSendContactMessageArgs = {
  input?: InputMaybe<ContactMessageInput>;
};


export type MutationSyncUserStatsArgs = {
  userId: Scalars['String']['input'];
};


export type MutationUnfollowAgencyArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationUnfollowTeamArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationUnfollowUserArgs = {
  id: Scalars['String']['input'];
};


export type MutationUnlikePlayerPerformanceArgs = {
  gameId: Scalars['ObjectId']['input'];
  playerId: Scalars['ObjectId']['input'];
};


export type MutationUnregisterDeviceArgs = {
  input: UnregisterDeviceInput;
};


export type MutationUpdateAgencyArgs = {
  id: Scalars['ObjectId']['input'];
  input: UpdateAgencyInput;
};


export type MutationUpdateLeagueArgs = {
  id: Scalars['ObjectId']['input'];
  input: UpdateLeagueInput;
};


export type MutationUpdateOrganizationArgs = {
  id: Scalars['ObjectId']['input'];
  input: UpdateOrganizationInput;
};


export type MutationUpdateStripeAccountArgs = {
  input: CreateStripeAccountInput;
};


export type MutationUpdateTeamArgs = {
  id: Scalars['ObjectId']['input'];
  input: UpdateTeamInput;
};


export type MutationUpdateUserArgs = {
  input: UpdateUserInput;
};


export type MutationUpdateUserAdminArgs = {
  id: Scalars['ObjectId']['input'];
  input: UpdateUserInputAdmin;
};


export type MutationUpdateUserCurrencyArgs = {
  currency: Scalars['String']['input'];
  id: Scalars['ObjectId']['input'];
};


export type MutationUpdateUserStatsArgs = {
  id: Scalars['ObjectId']['input'];
  input: UpdateUserStatsInput;
};


export type MutationUploadAgencyLogoArgs = {
  id: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadAgencyLogoInput>;
};


export type MutationUploadAvatarArgs = {
  input?: InputMaybe<UploadAvatarInput>;
};


export type MutationUploadOrganizationLogoArgs = {
  id: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadOrganizationLogoInput>;
};


export type MutationUploadTeamLogoArgs = {
  id: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadTeamLogoInput>;
};


export type MutationUploadWallVideoArgs = {
  input?: InputMaybe<UploadWallVideoInput>;
};


export type MutationUploadWallVideoSegmentationArgs = {
  input: UploadWallVideoSegmentationInput;
};


export type MutationVerifyAdminByEmailArgs = {
  code: Scalars['String']['input'];
  email: Scalars['String']['input'];
};


export type MutationVerifyByEmailArgs = {
  code: Scalars['String']['input'];
  email: Scalars['String']['input'];
};


export type MutationVisitAgencyArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationVisitTeamArgs = {
  id: Scalars['ObjectId']['input'];
};


export type MutationVisitUserArgs = {
  id: Scalars['ObjectId']['input'];
};

export type MyFollowerUserEdge = {
  __typename?: 'MyFollowerUserEdge';
  iFollow: Scalars['Boolean']['output'];
  node: User;
};

export type MyFollowersFilter = {
  role: Role;
};

export type MyFollowersResponse = {
  __typename?: 'MyFollowersResponse';
  edges: Array<MyFollowerUserEdge>;
  pageInfo: PageInfo;
};

export type NewUsersCountResponse = {
  __typename?: 'NewUsersCountResponse';
  count: Scalars['Int']['output'];
};

export type Notification = {
  __typename?: 'Notification';
  body: Scalars['String']['output'];
  destinationPath?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  sent: Scalars['Boolean']['output'];
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
  user?: Maybe<User>;
};

export type Organization = {
  __typename?: 'Organization';
  abbreviation?: Maybe<Scalars['String']['output']>;
  alternativeNames?: Maybe<Array<AlternativeName>>;
  arenaAddress?: Maybe<Scalars['String']['output']>;
  arenaName?: Maybe<Scalars['String']['output']>;
  checked?: Maybe<Scalars['Boolean']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  facebookUrl?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  instagramUrl?: Maybe<Scalars['String']['output']>;
  logo?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  ogLogo?: Maybe<Scalars['String']['output']>;
  province?: Maybe<Scalars['String']['output']>;
  slug: Scalars['String']['output'];
  state?: Maybe<Scalars['String']['output']>;
  tiktokUrl?: Maybe<Scalars['String']['output']>;
  twitterUrl?: Maybe<Scalars['String']['output']>;
  website?: Maybe<Scalars['String']['output']>;
};

export type OrganizationEdge = {
  __typename?: 'OrganizationEdge';
  node: Organization;
};

export type OrganizationNotFoundError = IUserError & {
  __typename?: 'OrganizationNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type OrganizationsFilter = {
  checked?: InputMaybe<StatusType>;
  country?: InputMaybe<Scalars['String']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  withoutLogo?: InputMaybe<Scalars['Boolean']['input']>;
};

export type OrganizationsResponse = {
  __typename?: 'OrganizationsResponse';
  edges: Array<OrganizationEdge>;
  pageInfo: PageInfo;
};

export type PageInfo = {
  __typename?: 'PageInfo';
  endCursor?: Maybe<Scalars['String']['output']>;
  hasNextPage: Scalars['Boolean']['output'];
  hasPreviousPage: Scalars['Boolean']['output'];
  startCursor?: Maybe<Scalars['String']['output']>;
  totalCount?: Maybe<Scalars['Float']['output']>;
};

export type Pagination = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first: Scalars['Float']['input'];
};

export type ParamExtractor = {
  __typename?: 'ParamExtractor';
  queryParam: Scalars['String']['output'];
  selector: Scalars['String']['output'];
};

export type ParamExtractorInput = {
  queryParam: Scalars['String']['input'];
  selector: Scalars['String']['input'];
};

export type PaymentSheet = {
  __typename?: 'PaymentSheet';
  accountId?: Maybe<Scalars['String']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  ephemeralKey?: Maybe<Scalars['String']['output']>;
  paymentIntent?: Maybe<Scalars['String']['output']>;
  publishableKey?: Maybe<Scalars['String']['output']>;
};

/** Period for which to get the views */
export enum Period {
  AllTime = 'ALL_TIME',
  Monthly = 'MONTHLY'
}

export type PhoneNumber = {
  __typename?: 'PhoneNumber';
  countryPrefix: Scalars['String']['output'];
  number: Scalars['String']['output'];
};

export type PhoneNumberInput = {
  countryPrefix: Scalars['String']['input'];
  number: Scalars['String']['input'];
};

export enum Platform {
  Android = 'ANDROID',
  Ios = 'IOS'
}

export type PlayersByGame = {
  __typename?: 'PlayersByGame';
  id: Scalars['ID']['output'];
  players?: Maybe<Array<User>>;
};

export type PlayersStatsFilter = {
  leagueId: Scalars['ObjectId']['input'];
  position?: InputMaybe<Scalars['String']['input']>;
  season?: InputMaybe<Scalars['String']['input']>;
  stage?: InputMaybe<Scalars['String']['input']>;
  teamId?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type PlayersStatsResponse = {
  __typename?: 'PlayersStatsResponse';
  edges: Array<UserStatsEdge>;
  leagueStages?: Maybe<Array<LeagueStages>>;
  pageInfo: PageInfo;
};

export type PostSeasonStats = {
  __typename?: 'PostSeasonStats';
  assists?: Maybe<Scalars['Float']['output']>;
  gaa?: Maybe<Scalars['Float']['output']>;
  gamesPlayed?: Maybe<Scalars['Float']['output']>;
  goals?: Maybe<Scalars['Float']['output']>;
  losses?: Maybe<Scalars['Float']['output']>;
  pim?: Maybe<Scalars['Float']['output']>;
  plusMinus?: Maybe<Scalars['Float']['output']>;
  shutouts?: Maybe<Scalars['Float']['output']>;
  svp?: Maybe<Scalars['Float']['output']>;
  ties?: Maybe<Scalars['Float']['output']>;
  wins?: Maybe<Scalars['Float']['output']>;
};

/** Enum for push notification types */
export enum PushNotificationType {
  BoostReceived = 'BOOST_RECEIVED',
  Follow = 'FOLLOW',
  FollowAgent = 'FOLLOW_AGENT',
  FollowCoach = 'FOLLOW_COACH',
  FollowOther = 'FOLLOW_OTHER',
  FollowScout = 'FOLLOW_SCOUT',
  Fire = 'Fire',
  FireAgent = 'FireAgent',
  FireCoach = 'FireCoach',
  FireScout = 'FireScout',
  GameStatsClipUpload = 'GAME_STATS_CLIP_UPLOAD',
  GameResult = 'GameResult',
  Marketing = 'Marketing',
  NewTeamMember = 'NEW_TEAM_MEMBER',
  StripeAccountVerification = 'STRIPE_ACCOUNT_VERIFICATION',
  VideoClipLike = 'VIDEO_CLIP_LIKE',
  VideoHighlightLike = 'VIDEO_HIGHLIGHT_LIKE',
  VideoSegmentationFinished = 'VIDEO_SEGMENTATION_FINISHED',
  ViewAgent = 'VIEW_AGENT',
  ViewCoach = 'VIEW_COACH',
  ViewInsights = 'VIEW_INSIGHTS',
  ViewScout = 'VIEW_SCOUT'
}

export type PushNotificationsChangeError = IUserError & {
  __typename?: 'PushNotificationsChangeError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type PushNotificationsError = PushNotificationsChangeError;

export type PushNotificationsInput = {
  enabled: Scalars['Boolean']['input'];
};

export type PushNotificationsResponse = {
  __typename?: 'PushNotificationsResponse';
  errors: Array<PushNotificationsError>;
  user?: Maybe<User>;
};

export type Query = {
  __typename?: 'Query';
  admin: AdminQuery;
  adminUserStats?: Maybe<UserStats>;
  adminUsers: AdminUsersResponse;
  agencies: AgenciesResponse;
  agency?: Maybe<Agency>;
  agencyUsers: UsersResponse;
  agentsClients: UsersResponse;
  boosts: BoostsResponse;
  currentAdminUser: AdminUser;
  currentUser: User;
  entityCounts: EntityCountsResponse;
  epTeam?: Maybe<EliteProspectTeam>;
  epTeams: Array<EliteProspectTeam>;
  exampleUsers: Array<User>;
  exampleWallVideos: Array<WallVideo>;
  game?: Maybe<GameStats>;
  games: GamesResponse;
  gamesPlayers?: Maybe<GamesPlayersResponse>;
  inspiredUsers: Array<User>;
  isFollowing: Scalars['Boolean']['output'];
  isFollowingAgency: Scalars['Boolean']['output'];
  isFollowingTeam: Scalars['Boolean']['output'];
  latestClips: WallVideosResponse;
  league?: Maybe<League>;
  leagueStandings: Array<LeagueStandingsResponse>;
  leagues: LeaguesResponse;
  moreSelectedUsers: Array<User>;
  myFollowers: MyFollowersResponse;
  myFollowing: UsersResponse;
  myFollowingAgencies: AgenciesFollowingResponse;
  myFollowingTeams: TeamsFollowingResponse;
  newUsersCount: NewUsersCountResponse;
  organization?: Maybe<Organization>;
  organizationTeams: TeamsResponse;
  organizations: OrganizationsResponse;
  playersStatsList: PlayersStatsResponse;
  randomUsers: RandomUsersResponse;
  recommendedUsers: Array<User>;
  searchUserStats: UserStatsResponse;
  searchUsers: UsersResponse;
  stripeAccount?: Maybe<StripeAccount>;
  stripeAccountBalance: StripeAccountBalance;
  stripeExternalAccount?: Maybe<StripeExternalAccount>;
  suggestInvites: SuggestInvitesResponse;
  team: TeamDetailsResponse;
  teams: TeamsResponse;
  topViewedUsers: UserViewsResponse;
  user?: Maybe<User>;
  userById?: Maybe<User>;
  userLikesPlayersGamesPerformance: UserLikesPlayersGamesPerformanceResponse;
  userLikesPlayersPerformance: UserLikesPlayersPerformanceResponse;
  userStats: UserStatsResponse;
  userViewsStats: UserViewStatsResponse;
  users: UsersResponse;
  usersByIds?: Maybe<Array<User>>;
  videosByIds: VideosByIdsResponse;
  wallVideos: WallVideosResponse;
};


export type QueryAdminUserStatsArgs = {
  id: Scalars['ObjectId']['input'];
};


export type QueryAdminUsersArgs = {
  pagination?: InputMaybe<Pagination>;
};


export type QueryAgenciesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<AgenciesFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  pagination?: InputMaybe<Pagination>;
  sort?: InputMaybe<SimpleSort>;
};


export type QueryAgencyArgs = {
  id?: InputMaybe<Scalars['ObjectId']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAgencyUsersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  agencyId: Scalars['ObjectId']['input'];
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<AgencyUsersFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryAgentsClientsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  slug: Scalars['String']['input'];
};


export type QueryBoostsArgs = {
  filter?: InputMaybe<BoostsFilter>;
  pagination?: InputMaybe<Pagination>;
  sort?: InputMaybe<SimpleSort>;
};


export type QueryEpTeamArgs = {
  id: Scalars['String']['input'];
};


export type QueryEpTeamsArgs = {
  searchQuery: Scalars['String']['input'];
};


export type QueryGameArgs = {
  id: Scalars['ObjectId']['input'];
};


export type QueryGamesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<GamesFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<SortInput>;
};


export type QueryGamesPlayersArgs = {
  input: GamesPlayersInput;
};


export type QueryInspiredUsersArgs = {
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryIsFollowingArgs = {
  userId: Scalars['String']['input'];
};


export type QueryIsFollowingAgencyArgs = {
  agencyId: Scalars['ObjectId']['input'];
};


export type QueryIsFollowingTeamArgs = {
  id: Scalars['ObjectId']['input'];
};


export type QueryLatestClipsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<LatestClipsFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryLeagueArgs = {
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryLeagueStandingsArgs = {
  leagueId?: InputMaybe<Scalars['ObjectId']['input']>;
  season?: InputMaybe<Scalars['String']['input']>;
  teamId?: InputMaybe<Scalars['ObjectId']['input']>;
};


export type QueryLeaguesArgs = {
  filter?: InputMaybe<LeaguesFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryMyFollowersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter: MyFollowersFilter;
  first?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryMyFollowingArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryMyFollowingAgenciesArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryMyFollowingTeamsArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryNewUsersCountArgs = {
  country: Scalars['String']['input'];
};


export type QueryOrganizationArgs = {
  id?: InputMaybe<Scalars['ObjectId']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryOrganizationTeamsArgs = {
  organizationId?: InputMaybe<Scalars['ObjectId']['input']>;
  pagination?: InputMaybe<Pagination>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryOrganizationsArgs = {
  filter?: InputMaybe<OrganizationsFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryPlayersStatsListArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter: PlayersStatsFilter;
  first?: InputMaybe<Scalars['Int']['input']>;
  sort?: InputMaybe<SortInput>;
};


export type QueryRecommendedUsersArgs = {
  activeUserId?: InputMaybe<Scalars['String']['input']>;
  country?: InputMaybe<Country>;
  inspired?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  yearOfBirth?: InputMaybe<YearArguments>;
};


export type QuerySearchUserStatsArgs = {
  filter: UserStatsFilter;
  pagination?: InputMaybe<Pagination>;
};


export type QuerySearchUsersArgs = {
  filter: UsersFilter;
  pagination?: InputMaybe<Pagination>;
};


export type QuerySuggestInvitesArgs = {
  teamId: Scalars['ObjectId']['input'];
};


export type QueryTeamArgs = {
  id?: InputMaybe<Scalars['ObjectId']['input']>;
  includeShadowPlayers?: InputMaybe<Scalars['Boolean']['input']>;
  orgSlug?: InputMaybe<Scalars['String']['input']>;
  pagination?: InputMaybe<Pagination>;
  season?: InputMaybe<Scalars['String']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTeamsArgs = {
  filter?: InputMaybe<TeamsFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryTopViewedUsersArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<TopViewedUsersFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  pagination?: InputMaybe<Pagination>;
  period: Period;
};


export type QueryUserArgs = {
  slug: Scalars['String']['input'];
};


export type QueryUserByIdArgs = {
  id: Scalars['ObjectId']['input'];
};


export type QueryUserLikesPlayersGamesPerformanceArgs = {
  input: UserLikesPlayersGamesPerformanceInput;
};


export type QueryUserLikesPlayersPerformanceArgs = {
  input: UserLikesPlayersPerformanceInput;
};


export type QueryUserStatsArgs = {
  filter?: InputMaybe<UserStatsFilter>;
  pagination?: InputMaybe<Pagination>;
};


export type QueryUsersArgs = {
  filter: AdminUsersFilter;
  pagination?: InputMaybe<Pagination>;
};


export type QueryUsersByIdsArgs = {
  userIds: Array<Scalars['ObjectId']['input']>;
};


export type QueryVideosByIdsArgs = {
  input: VideosByIdsInput;
};


export type QueryWallVideosArgs = {
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<WallVideosFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  includeCounts?: InputMaybe<Scalars['Boolean']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
};

export type RandomUsersResponse = {
  __typename?: 'RandomUsersResponse';
  edges: Array<UserEdge>;
  totalCount: Scalars['Int']['output'];
};

export type ReScrapeError = EntityNotFoundError;

export type ReScrapeResponse = {
  __typename?: 'ReScrapeResponse';
  errors?: Maybe<Array<ReScrapeError>>;
  success: Scalars['Boolean']['output'];
};

export type RegisterDeviceError = DeviceAlreadyRegisteredError;

export type RegisterDeviceInput = {
  platform: Platform;
  token: Scalars['String']['input'];
};

export type RegisterDeviceResponse = {
  __typename?: 'RegisterDeviceResponse';
  errors: Array<RegisterDeviceError>;
  user?: Maybe<User>;
};

export type RegisterOrLoginByEmailError = GeneralError;

export type RegisterOrLoginByEmailInput = {
  email: Scalars['String']['input'];
  role?: InputMaybe<Scalars['String']['input']>;
  subrole?: InputMaybe<Scalars['String']['input']>;
};

export type RegisteryOrLoginByEmailResponse = {
  __typename?: 'RegisteryOrLoginByEmailResponse';
  errors: Array<RegisterOrLoginByEmailError>;
  user?: Maybe<User>;
};

export type RemoveAgencyError = RemoveAgencyUserDoesNotBelongToAgencyError | RemoveAgencyUserNotFoundError;

export type RemoveAgencyUserDoesNotBelongToAgencyError = IUserError & {
  __typename?: 'RemoveAgencyUserDoesNotBelongToAgencyError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type RemoveAgencyUserNotFoundError = IUserError & {
  __typename?: 'RemoveAgencyUserNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type RemoveAgencyUserResponse = {
  __typename?: 'RemoveAgencyUserResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<RemoveAgencyError>>;
};

export type RemoveExternalConnectionError = ExternalConnectionError | UserInfoNotFoundError | UserNotFoundError;

export type RemoveExternalConnectionResponse = {
  __typename?: 'RemoveExternalConnectionResponse';
  errors?: Maybe<Array<RemoveExternalConnectionError>>;
  success: Scalars['Boolean']['output'];
};

export type RemoveLeagueLogoError = LeagueNotFoundError;

export type RemoveLeagueLogoResponse = {
  __typename?: 'RemoveLeagueLogoResponse';
  errors?: Maybe<Array<RemoveLeagueLogoError>>;
  success: Scalars['Boolean']['output'];
};

export type RemoveOrganizationLogoError = OrganizationNotFoundError;

export type RemoveOrganizationLogoResponse = {
  __typename?: 'RemoveOrganizationLogoResponse';
  errors?: Maybe<Array<RemoveOrganizationLogoError>>;
  success: Scalars['Boolean']['output'];
};

export type RemoveTeamLogoError = TeamNotFoundError;

export type RemoveTeamLogoResponse = {
  __typename?: 'RemoveTeamLogoResponse';
  errors?: Maybe<Array<RemoveTeamLogoError>>;
  success: Scalars['Boolean']['output'];
};

export enum Role {
  Parent = 'PARENT',
  Player = 'PLAYER',
  ShadowPlayer = 'SHADOW_PLAYER',
  SportsFan = 'SPORTS_FAN',
  SportsProfessional = 'SPORTS_PROFESSIONAL'
}

export type RosterPlayer = {
  __typename?: 'RosterPlayer';
  assists?: Maybe<Scalars['Float']['output']>;
  externalInfo: RosterPlayerExternalInfo;
  goals?: Maybe<Scalars['Float']['output']>;
  goalsAgainst?: Maybe<Scalars['Float']['output']>;
  jerseyNumber?: Maybe<Scalars['Float']['output']>;
  pim?: Maybe<Scalars['Float']['output']>;
  player?: Maybe<User>;
  plusMinus?: Maybe<Scalars['Float']['output']>;
  points?: Maybe<Scalars['Float']['output']>;
  position?: Maybe<RosterPosition>;
  saves?: Maybe<Scalars['Float']['output']>;
  svp?: Maybe<Scalars['Float']['output']>;
};

export type RosterPlayerExternalInfo = {
  __typename?: 'RosterPlayerExternalInfo';
  player?: Maybe<User>;
  playerExternalId?: Maybe<Scalars['String']['output']>;
  playerName: Scalars['String']['output'];
};

/** Enum for roster player positions */
export enum RosterPosition {
  Defenseman = 'Defenseman',
  Forward = 'Forward',
  Goaltender = 'Goaltender',
  Unknown = 'Unknown'
}

export enum SeasonType {
  Regular = 'REGULAR',
  Tournament = 'TOURNAMENT'
}

export type SelfFamilyMemberError = IUserError & {
  __typename?: 'SelfFamilyMemberError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type SendPushNotificationAdminResponse = {
  __typename?: 'SendPushNotificationAdminResponse';
  errors?: Maybe<Array<SendPushNotificationAdminError>>;
  success: Scalars['Boolean']['output'];
};

export type SimpleSort = {
  field: Scalars['String']['input'];
  order: SortOrder;
};

export type SocialSignInError = GeneralError;

export type SocialSignInInput = {
  firstname?: InputMaybe<Scalars['String']['input']>;
  idToken: Scalars['String']['input'];
  lastname?: InputMaybe<Scalars['String']['input']>;
  role?: InputMaybe<Scalars['String']['input']>;
  subrole?: InputMaybe<Scalars['String']['input']>;
};

export type SocialSignInResponse = {
  __typename?: 'SocialSignInResponse';
  errors: Array<SocialSignInError>;
  onboardingCompleted?: Maybe<Scalars['Boolean']['output']>;
  tokens?: Maybe<JwtTokens>;
  user?: Maybe<User>;
};

/** Sort setting */
export type SortInput = {
  fields: Array<SimpleSort>;
};

/** The sort order type */
export enum SortOrder {
  Asc = 'ASC',
  Desc = 'DESC'
}

export enum SportCategory {
  Baseball = 'BASEBALL',
  Basketball = 'BASKETBALL',
  Football = 'FOOTBALL',
  Hockey = 'HOCKEY',
  Soccer = 'SOCCER',
  Volleyball = 'VOLLEYBALL'
}

export enum StatsSource {
  Ep = 'EP',
  Leijonat = 'LEIJONAT',
  Manual = 'MANUAL',
  Scraper = 'SCRAPER'
}

export enum StatusType {
  False = 'FALSE',
  NotSet = 'NOT_SET',
  True = 'TRUE'
}

export type StreamFeedEnums = {
  __typename?: 'StreamFeedEnums';
  feedActivityVerb?: Maybe<FeedActivityVerb>;
  feedName?: Maybe<Feed_Names>;
};

export type StripeAccount = {
  __typename?: 'StripeAccount';
  address?: Maybe<StripeAddress>;
  country?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  dob?: Maybe<Scalars['DateTimeISO']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  firstname?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  jobTitle?: Maybe<Scalars['String']['output']>;
  lastname?: Maybe<Scalars['String']['output']>;
  paymentsEnabled?: Maybe<Scalars['Boolean']['output']>;
  payoutsEnabled?: Maybe<Scalars['Boolean']['output']>;
  phone?: Maybe<Scalars['String']['output']>;
  ssnLast4Provided?: Maybe<Scalars['Boolean']['output']>;
};

export type StripeAccountBalance = {
  __typename?: 'StripeAccountBalance';
  balance: StripeBalance;
  transactions: Array<StripeBalanceTransaction>;
};

export type StripeAccountRequirementError = {
  __typename?: 'StripeAccountRequirementError';
  code: Scalars['String']['output'];
  reason: Scalars['String']['output'];
  requirement: Scalars['String']['output'];
};

export type StripeAccountUserInputError = IUserError & {
  __typename?: 'StripeAccountUserInputError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type StripeAccountVerification = {
  document?: InputMaybe<StripeAccountVerificationDocument>;
};

export type StripeAccountVerificationDocument = {
  back?: InputMaybe<Scalars['String']['input']>;
  front?: InputMaybe<Scalars['String']['input']>;
  selfie?: InputMaybe<Scalars['String']['input']>;
};

export type StripeAddress = {
  __typename?: 'StripeAddress';
  city?: Maybe<Scalars['String']['output']>;
  line1?: Maybe<Scalars['String']['output']>;
  line2?: Maybe<Scalars['String']['output']>;
  postalCode?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
};

export type StripeAddressInput = {
  city?: InputMaybe<Scalars['String']['input']>;
  line1?: InputMaybe<Scalars['String']['input']>;
  line2?: InputMaybe<Scalars['String']['input']>;
  postalCode?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
};

export type StripeBalance = {
  __typename?: 'StripeBalance';
  available: StripeMoney;
  pending: StripeMoney;
};

export type StripeBalanceTransaction = {
  __typename?: 'StripeBalanceTransaction';
  createdAt: Scalars['DateTimeISO']['output'];
  id: Scalars['String']['output'];
  payout: StripeMoney;
  status: Scalars['String']['output'];
};

export type StripeBankAccount = {
  __typename?: 'StripeBankAccount';
  country?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  firstname?: Maybe<Scalars['String']['output']>;
  lastname?: Maybe<Scalars['String']['output']>;
};

export type StripeBankAccountInput = {
  country: Scalars['String']['input'];
  currency: Scalars['String']['input'];
  firstname: Scalars['String']['input'];
  lastname: Scalars['String']['input'];
};

export type StripeExternalAccount = {
  __typename?: 'StripeExternalAccount';
  accountNumber?: Maybe<Scalars['String']['output']>;
  bankAccount?: Maybe<StripeBankAccount>;
  id: Scalars['ID']['output'];
  routingNumber?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type StripeMoney = {
  __typename?: 'StripeMoney';
  amount: Scalars['Float']['output'];
  currency: Scalars['String']['output'];
};

export type StripeNotConnectedError = IUserError & {
  __typename?: 'StripeNotConnectedError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export enum SubRole {
  Agent = 'AGENT',
  Coach = 'COACH',
  Scout = 'SCOUT'
}

export type SubRoleSpecification = {
  __typename?: 'SubRoleSpecification';
  current?: Maybe<Scalars['Boolean']['output']>;
  season?: Maybe<Scalars['String']['output']>;
  team: Team;
  type: Scalars['String']['output'];
};

export type SubRoleSpecificationInput = {
  current?: InputMaybe<Scalars['Boolean']['input']>;
  team: Scalars['ID']['input'];
  type: SubRoleSpecificationType;
};

export enum SubRoleSpecificationType {
  AssistantCoach = 'AssistantCoach',
  AssistantGm = 'AssistantGm',
  AthleticTrainer = 'AthleticTrainer',
  ConditioningCoach = 'ConditioningCoach',
  EquipmentManager = 'EquipmentManager',
  GeneralManager = 'GeneralManager',
  GoalieCoach = 'GoalieCoach',
  HeadCoach = 'HeadCoach',
  HeadScout = 'HeadScout',
  HockeyOperations = 'HockeyOperations',
  MedicalStaff = 'MedicalStaff',
  Other = 'Other',
  PlayerDevelopmentManager = 'PlayerDevelopmentManager',
  Scout = 'Scout',
  SkillsCoach = 'SkillsCoach',
  TeamManager = 'TeamManager',
  VideoCoach = 'VideoCoach'
}

export type SuggestInvitesResponse = {
  __typename?: 'SuggestInvitesResponse';
  edges: Array<UserEdge>;
};

export type SuggestedLeague = {
  __typename?: 'SuggestedLeague';
  alternativeNames?: Maybe<Array<AlternativeName>>;
  countries: Array<Scalars['String']['output']>;
  id: Scalars['ObjectId']['output'];
  level: LeagueLevel;
  name: Scalars['String']['output'];
  score: Scalars['Float']['output'];
};

export type SuggestedLeagueResponse = {
  __typename?: 'SuggestedLeagueResponse';
  bestMatch?: Maybe<SuggestedLeague>;
  suggestions: Array<SuggestedLeague>;
};

export type SuggestedTeam = {
  __typename?: 'SuggestedTeam';
  abbreviation?: Maybe<Scalars['String']['output']>;
  alternativeNames?: Maybe<Array<AlternativeName>>;
  country?: Maybe<Scalars['String']['output']>;
  id: Scalars['ObjectId']['output'];
  leagues?: Maybe<Array<League>>;
  name: Scalars['String']['output'];
  organization?: Maybe<Organization>;
  score: Scalars['Float']['output'];
};

export type SuggestedTeamResponse = {
  __typename?: 'SuggestedTeamResponse';
  bestMatch?: Maybe<SuggestedTeam>;
  suggestions: Array<SuggestedTeam>;
};

export type SyncAgencyClientsError = AgencyNotFoundError | EpAgencyNotFoundError | EpUserNotFoundError | InvalidEpPlayerDataError;

export type SyncAgencyClientsResponse = {
  __typename?: 'SyncAgencyClientsResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<SyncAgencyClientsError>>;
};

export type SyncSingleGameError = EntityNotFoundError;

export type SyncSingleGameResponse = {
  __typename?: 'SyncSingleGameResponse';
  errors?: Maybe<Array<SyncSingleGameError>>;
  game?: Maybe<GameStats>;
};

export type SyncStripeAccountFailed = IUserError & {
  __typename?: 'SyncStripeAccountFailed';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type SyncStripeAccountResponse = {
  __typename?: 'SyncStripeAccountResponse';
  errors?: Maybe<Array<SyncStripeAccountResponseFailed>>;
  success: Scalars['Boolean']['output'];
};

export type SyncStripeAccountResponseFailed = SyncStripeAccountFailed;

export type TagCountWithPercentage = {
  __typename?: 'TagCountWithPercentage';
  count: Scalars['Int']['output'];
  percentage: Scalars['Float']['output'];
  tag: Scalars['String']['output'];
};

export type Team = {
  __typename?: 'Team';
  abbreviation?: Maybe<Scalars['String']['output']>;
  alternativeNames?: Maybe<Array<AlternativeName>>;
  checked?: Maybe<Scalars['Boolean']['output']>;
  country?: Maybe<Scalars['String']['output']>;
  eliteProspectLink?: Maybe<Scalars['String']['output']>;
  externalId?: Maybe<Scalars['String']['output']>;
  hasGames?: Maybe<Scalars['Boolean']['output']>;
  id: Scalars['ID']['output'];
  leagues?: Maybe<Array<League>>;
  logo?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  ogLogo?: Maybe<Scalars['String']['output']>;
  order?: Maybe<Scalars['Float']['output']>;
  organization?: Maybe<Organization>;
  playersCount: Scalars['Float']['output'];
  shortName?: Maybe<Scalars['String']['output']>;
  shortNameSlug?: Maybe<Scalars['String']['output']>;
  slug: Scalars['String']['output'];
  stats?: Maybe<TeamStats>;
};

export type TeamAutoConnectRules = {
  __typename?: 'TeamAutoConnectRules';
  country: Scalars['String']['output'];
  shortName: Scalars['String']['output'];
  shortNameInclude: Scalars['Boolean']['output'];
  shortNameSlug: Scalars['String']['output'];
};

export type TeamAutoConnectRulesInput = {
  country: Scalars['String']['input'];
  shortName: Scalars['String']['input'];
  shortNameInclude: Scalars['Boolean']['input'];
};

export type TeamDetailsError = TeamNotFoundError;

export type TeamDetailsResponse = {
  __typename?: 'TeamDetailsResponse';
  errors?: Maybe<Array<TeamDetailsError>>;
  team?: Maybe<Team>;
  users?: Maybe<UsersResponse>;
};

export type TeamEdge = {
  __typename?: 'TeamEdge';
  node: Team;
  /** @deprecated Use team.playersCount instead */
  playersCount: Scalars['Float']['output'];
};

export type TeamFollowingEdge = {
  __typename?: 'TeamFollowingEdge';
  node: Team;
};

export type TeamNotFoundError = IUserError & {
  __typename?: 'TeamNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type TeamStats = {
  __typename?: 'TeamStats';
  followers: Scalars['Int']['output'];
  views: Scalars['Int']['output'];
};

export type TeamsFilter = {
  checked?: InputMaybe<StatusType>;
  country?: InputMaybe<Scalars['String']['input']>;
  hasOrganization?: InputMaybe<Scalars['Boolean']['input']>;
  hasPlayers?: InputMaybe<Scalars['Boolean']['input']>;
  leagues?: InputMaybe<Array<Scalars['ObjectId']['input']>>;
  playersCount?: InputMaybe<Scalars['Float']['input']>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
  shortName?: InputMaybe<Scalars['String']['input']>;
};

export type TeamsFollowingResponse = {
  __typename?: 'TeamsFollowingResponse';
  edges: Array<TeamFollowingEdge>;
  pageInfo: PageInfo;
};

export type TeamsResponse = {
  __typename?: 'TeamsResponse';
  edges: Array<TeamEdge>;
  pageInfo: PageInfo;
};

/** Filter for top viewed users */
export enum TopViewedFilterType {
  All = 'ALL',
  New = 'NEW'
}

export type TopViewedUsersFilter = {
  country?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<TopViewedFilterType>;
  yearOfBirth?: InputMaybe<Scalars['Float']['input']>;
};

export type UnfollowAgencyError = UnfollowAgencyNotFoundError;

export type UnfollowAgencyNotFoundError = IUserError & {
  __typename?: 'UnfollowAgencyNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UnfollowAgencyResponse = {
  __typename?: 'UnfollowAgencyResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<UnfollowAgencyError>>;
};

export type UnfollowTeamError = EntityNotFoundError;

export type UnfollowTeamResponse = {
  __typename?: 'UnfollowTeamResponse';
  errors?: Maybe<Array<UnfollowTeamError>>;
  team?: Maybe<Team>;
};

export type UnfollowUserError = UserNotFoundError;

export type UnfollowUserResponse = {
  __typename?: 'UnfollowUserResponse';
  errors: Array<UnfollowUserError>;
  user?: Maybe<User>;
};

export type UnlikePlayerPerformanceError = EntityNotFoundError | UserNotFoundError;

export type UnlikePlayerPerformanceResponse = {
  __typename?: 'UnlikePlayerPerformanceResponse';
  errors: Array<UnlikePlayerPerformanceError>;
  player?: Maybe<User>;
};

export type UnregisterDeviceError = DeviceNotRegisteredError;

export type UnregisterDeviceInput = {
  platform: Platform;
  token: Scalars['String']['input'];
};

export type UnregisterDeviceResponse = {
  __typename?: 'UnregisterDeviceResponse';
  errors: Array<UnregisterDeviceError>;
  user?: Maybe<User>;
};

export type UpdateAgencyError = UpdateAgencyNotFoundError;

export type UpdateAgencyInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  affiliateAgencies?: InputMaybe<Array<Scalars['ObjectId']['input']>>;
  categories?: InputMaybe<Array<SportCategory>>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  drafts?: InputMaybe<AgencyDraftsInput>;
  email?: InputMaybe<Scalars['String']['input']>;
  epUrl?: InputMaybe<Scalars['String']['input']>;
  externalNumberOfClients?: InputMaybe<Scalars['Int']['input']>;
  facebookUrl?: InputMaybe<Scalars['String']['input']>;
  founded?: InputMaybe<Scalars['Int']['input']>;
  instagramFollowers?: InputMaybe<Scalars['Int']['input']>;
  instagramUrl?: InputMaybe<Scalars['String']['input']>;
  integrated?: InputMaybe<Scalars['Boolean']['input']>;
  licences?: InputMaybe<Array<Scalars['String']['input']>>;
  linkedinUrl?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
  tiktokUrl?: InputMaybe<Scalars['String']['input']>;
  twitterUrl?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateAgencyNotFoundError = IUserError & {
  __typename?: 'UpdateAgencyNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UpdateAgencyResponse = {
  __typename?: 'UpdateAgencyResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<UpdateAgencyError>>;
};

export type UpdateDefaultCurrencyError = CurrencyUpdateError | UserNotFoundError;

export type UpdateDefaultCurrencyResponse = {
  __typename?: 'UpdateDefaultCurrencyResponse';
  currency?: Maybe<Scalars['String']['output']>;
  errors: Array<UpdateDefaultCurrencyError>;
};

export type UpdateLeagueError = LeagueAlreadyExistsError | UpdateLeagueNotFoundError;

export type UpdateLeagueInput = {
  alternativeNames?: InputMaybe<Array<AlternativeNameInput>>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  countries?: InputMaybe<Array<Scalars['String']['input']>>;
  eliteProspectSlug?: InputMaybe<Scalars['String']['input']>;
  facebookUrl?: InputMaybe<Scalars['String']['input']>;
  genderCategory?: InputMaybe<LeagueGenderCategory>;
  ignore?: InputMaybe<Scalars['Boolean']['input']>;
  instagramUrl?: InputMaybe<Scalars['String']['input']>;
  level?: InputMaybe<LeagueLevel>;
  logo?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  publicAlternativeName?: InputMaybe<Scalars['String']['input']>;
  scraped?: InputMaybe<Scalars['Boolean']['input']>;
  sourceUrl?: InputMaybe<Scalars['String']['input']>;
  teamAutoConnectRules?: InputMaybe<TeamAutoConnectRulesInput>;
  tiktokUrl?: InputMaybe<Scalars['String']['input']>;
  twitterUrl?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<LeagueType>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateLeagueNotFoundError = IUserError & {
  __typename?: 'UpdateLeagueNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UpdateLeagueResponse = {
  __typename?: 'UpdateLeagueResponse';
  errors?: Maybe<Array<UpdateLeagueError>>;
  league?: Maybe<League>;
};

export type UpdateOrganizationError = UpdateOrganizationNotFoundError;

export type UpdateOrganizationInput = {
  abbreviation?: InputMaybe<Scalars['String']['input']>;
  alternativeNames?: InputMaybe<Array<AlternativeNameInput>>;
  arenaAddress?: InputMaybe<Scalars['String']['input']>;
  arenaName?: InputMaybe<Scalars['String']['input']>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  facebookUrl?: InputMaybe<Scalars['String']['input']>;
  instagramUrl?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  province?: InputMaybe<Scalars['String']['input']>;
  state?: InputMaybe<Scalars['String']['input']>;
  tiktokUrl?: InputMaybe<Scalars['String']['input']>;
  twitterUrl?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateOrganizationNotFoundError = IUserError & {
  __typename?: 'UpdateOrganizationNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UpdateOrganizationResponse = {
  __typename?: 'UpdateOrganizationResponse';
  errors?: Maybe<Array<UpdateOrganizationError>>;
  organization?: Maybe<Organization>;
};

export type UpdateStripeAccountError = AccountDoesNotExistsError | StripeAccountUserInputError;

export type UpdateStripeAccountResponse = {
  __typename?: 'UpdateStripeAccountResponse';
  account?: Maybe<StripeAccount>;
  errors: Array<UpdateStripeAccountError>;
};

export type UpdateTeamError = UpdateTeamNotFoundError;

export type UpdateTeamInput = {
  abbreviation?: InputMaybe<Scalars['String']['input']>;
  alternativeNames?: InputMaybe<Array<AlternativeNameInput>>;
  checked?: InputMaybe<Scalars['Boolean']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  eliteProspectLink?: InputMaybe<Scalars['String']['input']>;
  leagues?: InputMaybe<Array<Scalars['ObjectId']['input']>>;
  name?: InputMaybe<Scalars['String']['input']>;
  organization?: InputMaybe<Scalars['String']['input']>;
  shortName?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateTeamNotFoundError = IUserError & {
  __typename?: 'UpdateTeamNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UpdateTeamResponse = {
  __typename?: 'UpdateTeamResponse';
  errors?: Maybe<Array<UpdateTeamError>>;
  team?: Maybe<Team>;
};

export type UpdateUserAdminError = UserNotFoundError;

export type UpdateUserAdminResponse = {
  __typename?: 'UpdateUserAdminResponse';
  errors: Array<UpdateUserAdminError>;
  user?: Maybe<User>;
};

export type UpdateUserError = UserNotFoundError;

export type UpdateUserInput = {
  bio?: InputMaybe<UserBioInput>;
  boost?: InputMaybe<BoostInput>;
  boostMessage?: InputMaybe<Scalars['String']['input']>;
  contact?: InputMaybe<ContactInput>;
  country?: InputMaybe<Scalars['String']['input']>;
  currentTeam?: InputMaybe<Scalars['ID']['input']>;
  dateOfBirth?: InputMaybe<Scalars['DateTimeISO']['input']>;
  externalLinks?: InputMaybe<Array<ExternalLinkInput>>;
  firstname?: InputMaybe<Scalars['String']['input']>;
  lastname?: InputMaybe<Scalars['String']['input']>;
  phoneNumber?: InputMaybe<PhoneNumberInput>;
  role?: InputMaybe<Role>;
  subrole?: InputMaybe<SubRole>;
  subroleSpecifications?: InputMaybe<Array<SubRoleSpecificationInput>>;
};

export type UpdateUserInputAdmin = {
  agent?: InputMaybe<Scalars['ObjectId']['input']>;
  bio?: InputMaybe<UserBioInput>;
  boost?: InputMaybe<BoostInput>;
  boostMessage?: InputMaybe<Scalars['String']['input']>;
  contact?: InputMaybe<ContactInput>;
  country?: InputMaybe<Scalars['String']['input']>;
  currentTeam?: InputMaybe<Scalars['ID']['input']>;
  dateOfBirth?: InputMaybe<Scalars['DateTimeISO']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  externalLinks?: InputMaybe<Array<ExternalLinkInput>>;
  firstname?: InputMaybe<Scalars['String']['input']>;
  inspired?: InputMaybe<Scalars['Boolean']['input']>;
  lastname?: InputMaybe<Scalars['String']['input']>;
  moreSelected?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phoneNumber?: InputMaybe<PhoneNumberInput>;
  premium?: InputMaybe<Scalars['Boolean']['input']>;
  public?: InputMaybe<Scalars['Boolean']['input']>;
  recommended?: InputMaybe<Scalars['Boolean']['input']>;
  role?: InputMaybe<Role>;
  searchEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
  subrole?: InputMaybe<Scalars['String']['input']>;
  subroleSpecifications?: InputMaybe<Array<SubRoleSpecificationInput>>;
};

export type UpdateUserResponse = {
  __typename?: 'UpdateUserResponse';
  errors: Array<UpdateUserError>;
  user?: Maybe<User>;
};

export type UpdateUserStatsError = UpdateUserStatsNotFoundError;

export type UpdateUserStatsInput = {
  assists?: InputMaybe<Scalars['Int']['input']>;
  gaa?: InputMaybe<Scalars['Float']['input']>;
  gamesPlayed?: InputMaybe<Scalars['Int']['input']>;
  goals?: InputMaybe<Scalars['Int']['input']>;
  league?: InputMaybe<Scalars['ObjectId']['input']>;
  losses?: InputMaybe<Scalars['Int']['input']>;
  orderInSeason?: InputMaybe<Scalars['Int']['input']>;
  season?: InputMaybe<Scalars['String']['input']>;
  shutouts?: InputMaybe<Scalars['Int']['input']>;
  svp?: InputMaybe<Scalars['Float']['input']>;
  team?: InputMaybe<Scalars['ObjectId']['input']>;
  ties?: InputMaybe<Scalars['Int']['input']>;
  user?: InputMaybe<Scalars['ObjectId']['input']>;
  wins?: InputMaybe<Scalars['Int']['input']>;
};

export type UpdateUserStatsNotFoundError = IUserError & {
  __typename?: 'UpdateUserStatsNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UpdateUserStatsResponse = {
  __typename?: 'UpdateUserStatsResponse';
  errors?: Maybe<Array<UpdateUserStatsError>>;
  userStats?: Maybe<UserStats>;
};

export type UpdateWallVideoError = WallVideoNotFoundError;

export type UpdateWallVideoInput = {
  tags?: InputMaybe<Array<WallVideoTags>>;
};

export type UpdateWallVideoResponse = {
  __typename?: 'UpdateWallVideoResponse';
  errors: Array<UpdateWallVideoError>;
  wallVideo?: Maybe<WallVideo>;
};

export type UploadAgencyLogoError = FailedToCreatePresignedUrl;

export type UploadAgencyLogoInput = {
  uploadSuccessful: Scalars['Boolean']['input'];
};

export type UploadAgencyLogoResponse = {
  __typename?: 'UploadAgencyLogoResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<UploadAgencyLogoError>>;
  preSignedUrl?: Maybe<Scalars['String']['output']>;
};

export type UploadAvatarError = FailedToCreatePresignedUrl;

export type UploadAvatarInput = {
  uploadSuccessful: Scalars['Boolean']['input'];
};

export type UploadAvatarResponse = {
  __typename?: 'UploadAvatarResponse';
  errors?: Maybe<Array<UploadAvatarError>>;
  preSignedUrl?: Maybe<Scalars['String']['output']>;
  user?: Maybe<User>;
};

export type UploadLeagueLogoError = FailedToCreatePresignedUrl;

export type UploadLeagueLogoInput = {
  uploadSuccessful: Scalars['Boolean']['input'];
};

export type UploadLeagueLogoResponse = {
  __typename?: 'UploadLeagueLogoResponse';
  errors?: Maybe<Array<UploadLeagueLogoError>>;
  league?: Maybe<League>;
  preSignedUrl?: Maybe<Scalars['String']['output']>;
};

export type UploadOrganizationLogoError = FailedToCreatePresignedUrl;

export type UploadOrganizationLogoInput = {
  uploadSuccessful: Scalars['Boolean']['input'];
};

export type UploadOrganizationLogoResponse = {
  __typename?: 'UploadOrganizationLogoResponse';
  errors?: Maybe<Array<UploadOrganizationLogoError>>;
  organization?: Maybe<Organization>;
  preSignedUrl?: Maybe<Scalars['String']['output']>;
};

export type UploadTeamLogoError = FailedToCreatePresignedUrl;

export type UploadTeamLogoInput = {
  uploadSuccessful: Scalars['Boolean']['input'];
};

export type UploadTeamLogoResponse = {
  __typename?: 'UploadTeamLogoResponse';
  errors?: Maybe<Array<UploadTeamLogoError>>;
  preSignedUrl?: Maybe<Scalars['String']['output']>;
  team?: Maybe<Team>;
};

export type UploadWallVideoConfirmationInput = {
  id: Scalars['ObjectId']['input'];
  uploadSuccessful: Scalars['Boolean']['input'];
};

export type UploadWallVideoDataInput = {
  duration?: InputMaybe<Scalars['Float']['input']>;
  frameRate?: InputMaybe<Scalars['Float']['input']>;
  height?: InputMaybe<Scalars['Float']['input']>;
  repeat?: InputMaybe<UploadWallVideoRepeatInput>;
  segmentation?: InputMaybe<UploadWallVideoDataSegmentationInput>;
  size?: InputMaybe<Scalars['Float']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
  type: WallVideoType;
  width?: InputMaybe<Scalars['Float']['input']>;
};

export type UploadWallVideoDataSegmentationInput = {
  frame: Scalars['Float']['input'];
  selectionX: Scalars['Float']['input'];
  selectionY: Scalars['Float']['input'];
};

export type UploadWallVideoError = FailedToCreatePresignedUrl;

export type UploadWallVideoInput = {
  confirmation?: InputMaybe<UploadWallVideoConfirmationInput>;
  data?: InputMaybe<UploadWallVideoDataInput>;
};

export type UploadWallVideoRepeatInput = {
  end: Scalars['Float']['input'];
  start: Scalars['Float']['input'];
};

export type UploadWallVideoResponse = {
  __typename?: 'UploadWallVideoResponse';
  errors?: Maybe<Array<UploadWallVideoError>>;
  preSignedUrl?: Maybe<Scalars['String']['output']>;
  thumbnailPreSignedUrl?: Maybe<Scalars['String']['output']>;
  wallVideo?: Maybe<WallVideo>;
};

export type UploadWallVideoSegmentationConfirmationInput = {
  segmentationSuccessful: Scalars['Boolean']['input'];
};

export type UploadWallVideoSegmentationError = FailedToEdit | WallVideoNotFound;

export type UploadWallVideoSegmentationInput = {
  confirmation?: InputMaybe<UploadWallVideoSegmentationConfirmationInput>;
  id: Scalars['ObjectId']['input'];
};

export type UploadWallVideoSegmentationResponse = {
  __typename?: 'UploadWallVideoSegmentationResponse';
  errors?: Maybe<Array<UploadWallVideoSegmentationError>>;
  preSignedUrl?: Maybe<Scalars['String']['output']>;
  wallVideo?: Maybe<WallVideo>;
};

export type User = {
  __typename?: 'User';
  accountUpdateErrors?: Maybe<Array<StripeAccountRequirementError>>;
  agent?: Maybe<Agent>;
  agentV2?: Maybe<User>;
  avatar?: Maybe<Scalars['String']['output']>;
  avatars?: Maybe<Avatars>;
  bio?: Maybe<UserBio>;
  boost?: Maybe<BoostGoal>;
  boostMessage?: Maybe<Scalars['String']['output']>;
  clientStaticConfig: ClientStaticConfig;
  contact?: Maybe<Contact>;
  country?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['DateTimeISO']['output'];
  currentTeam?: Maybe<Team>;
  dateOfBirth?: Maybe<Scalars['DateTimeISO']['output']>;
  email: Scalars['String']['output'];
  epCurrentTeam?: Maybe<Scalars['ObjectId']['output']>;
  epViews?: Maybe<Scalars['Int']['output']>;
  experimentGroup?: Maybe<Scalars['Float']['output']>;
  externalConnections?: Maybe<Array<ExternalConnection>>;
  externalLinks?: Maybe<Array<ExternalLink>>;
  familyMembers?: Maybe<Array<User>>;
  firstname?: Maybe<Scalars['String']['output']>;
  following: Scalars['Boolean']['output'];
  hasGames?: Maybe<Scalars['Boolean']['output']>;
  highlights?: Maybe<Array<WallVideo>>;
  id: Scalars['ID']['output'];
  inspired?: Maybe<Scalars['Boolean']['output']>;
  isAdult: Scalars['Boolean']['output'];
  lastActiveAt?: Maybe<Scalars['DateTimeISO']['output']>;
  lastname?: Maybe<Scalars['String']['output']>;
  moreSelected?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  numberOfVideos?: Maybe<Scalars['Float']['output']>;
  originalFirstname?: Maybe<Scalars['String']['output']>;
  originalLastname?: Maybe<Scalars['String']['output']>;
  paymentsEnabled: Scalars['Boolean']['output'];
  pendingFamilyMembers?: Maybe<Array<User>>;
  phoneNumber?: Maybe<PhoneNumber>;
  possibleYearsOfBirth?: Maybe<Array<Scalars['Int']['output']>>;
  premium?: Maybe<Scalars['Boolean']['output']>;
  premiumSince?: Maybe<Scalars['DateTimeISO']['output']>;
  public?: Maybe<Scalars['Boolean']['output']>;
  pushEnabled?: Maybe<Scalars['Boolean']['output']>;
  recommended?: Maybe<Scalars['Boolean']['output']>;
  registeredDevices?: Maybe<Array<Device>>;
  role?: Maybe<Role>;
  searchEnabled?: Maybe<Scalars['Boolean']['output']>;
  shadowSlug?: Maybe<Scalars['String']['output']>;
  slug?: Maybe<Scalars['String']['output']>;
  stats?: Maybe<AggregatedStats>;
  stripeAccountId?: Maybe<Scalars['String']['output']>;
  stripeExternalAccountId?: Maybe<Scalars['String']['output']>;
  subrole?: Maybe<SubRole>;
  subroleSpecifications?: Maybe<Array<SubRoleSpecification>>;
  userStats?: Maybe<UserBasicStats>;
  verificationNeeded?: Maybe<Scalars['Boolean']['output']>;
  verificationPending?: Maybe<Scalars['Boolean']['output']>;
};

export type UserAlreadyMarkedForDeletionError = IUserError & {
  __typename?: 'UserAlreadyMarkedForDeletionError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UserBasicStats = {
  __typename?: 'UserBasicStats';
  boosts: Scalars['Int']['output'];
  followers: Scalars['Int']['output'];
  followings: Scalars['Int']['output'];
  views: Scalars['Int']['output'];
};

export type UserBio = {
  __typename?: 'UserBio';
  actScore?: Maybe<Scalars['Int']['output']>;
  agency?: Maybe<Agency>;
  agencyUnverified?: Maybe<Agency>;
  birthplace?: Maybe<Scalars['String']['output']>;
  coreGPA?: Maybe<Scalars['Float']['output']>;
  cumulativeGPA?: Maybe<Scalars['Float']['output']>;
  gender?: Maybe<BioGender>;
  graduationYear?: Maybe<Scalars['Int']['output']>;
  handedness?: Maybe<BioHandedness>;
  height?: Maybe<Height>;
  intro?: Maybe<Scalars['String']['output']>;
  naiaEligibility?: Maybe<Scalars['Boolean']['output']>;
  nameOfSchool?: Maybe<Scalars['String']['output']>;
  ncaaEligibility?: Maybe<Scalars['Boolean']['output']>;
  playerType?: Maybe<BioPlayerType>;
  position?: Maybe<BioPosition>;
  satScore?: Maybe<Scalars['Int']['output']>;
  schoolType?: Maybe<BioSchoolType>;
  toeflScore?: Maybe<Scalars['Int']['output']>;
  unweightedGPA?: Maybe<Scalars['Float']['output']>;
  weight?: Maybe<Weight>;
  weightedGPA?: Maybe<Scalars['Float']['output']>;
};

export type UserBioInput = {
  actScore?: InputMaybe<Scalars['Int']['input']>;
  agency?: InputMaybe<Scalars['ID']['input']>;
  agencyUnverified?: InputMaybe<Scalars['ID']['input']>;
  birthplace?: InputMaybe<Scalars['String']['input']>;
  coreGPA?: InputMaybe<Scalars['Float']['input']>;
  cumulativeGPA?: InputMaybe<Scalars['Float']['input']>;
  gender?: InputMaybe<BioGender>;
  graduationYear?: InputMaybe<Scalars['Int']['input']>;
  handedness?: InputMaybe<BioHandedness>;
  height?: InputMaybe<HeightInput>;
  intro?: InputMaybe<Scalars['String']['input']>;
  naiaEligibility?: InputMaybe<Scalars['Boolean']['input']>;
  nameOfSchool?: InputMaybe<Scalars['String']['input']>;
  ncaaEligibility?: InputMaybe<Scalars['Boolean']['input']>;
  playerType?: InputMaybe<BioPlayerType>;
  position?: InputMaybe<BioPosition>;
  satScore?: InputMaybe<Scalars['Int']['input']>;
  schoolType?: InputMaybe<BioSchoolType>;
  toeflScore?: InputMaybe<Scalars['Int']['input']>;
  unweightedGPA?: InputMaybe<Scalars['Float']['input']>;
  weight?: InputMaybe<WeightInput>;
  weightedGPA?: InputMaybe<Scalars['Float']['input']>;
};

export type UserCount = {
  __typename?: 'UserCount';
  parentsCount: Scalars['Int']['output'];
  playersCount: Scalars['Int']['output'];
  shadowPlayersCount: Scalars['Int']['output'];
  sportsFansCount: Scalars['Int']['output'];
  sportsProfsCount: Scalars['Int']['output'];
};

export type UserDoesNotHaveRegisteredDeviceError = {
  __typename?: 'UserDoesNotHaveRegisteredDeviceError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UserEdge = {
  __typename?: 'UserEdge';
  node: User;
};

export type UserInfo = {
  __typename?: 'UserInfo';
  crawlerDomainConfig?: Maybe<CrawlerDomainConfig>;
  externalId: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  possibleYearsOfBirth?: Maybe<Array<Scalars['Float']['output']>>;
};

export type UserInfoEdge = {
  __typename?: 'UserInfoEdge';
  node: UserInfo;
};

export type UserInfoFilter = {
  searchQuery: Scalars['String']['input'];
};

export type UserInfoNotFoundError = IUserError & {
  __typename?: 'UserInfoNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UserInfoResponse = {
  __typename?: 'UserInfoResponse';
  edges: Array<UserInfoEdge>;
  pageInfo: PageInfo;
};

export type UserLikesPlayersGamesPerformanceError = EntityNotFoundError | UserNotFoundError;

export type UserLikesPlayersGamesPerformanceInput = {
  gameIds: Array<Scalars['ObjectId']['input']>;
  playerIds?: InputMaybe<Array<Scalars['ObjectId']['input']>>;
};

export type UserLikesPlayersGamesPerformanceResponse = {
  __typename?: 'UserLikesPlayersGamesPerformanceResponse';
  errors?: Maybe<Array<UserLikesPlayersGamesPerformanceError>>;
  playersByGame?: Maybe<Array<PlayersByGame>>;
};

export type UserLikesPlayersPerformanceError = EntityNotFoundError | UserNotFoundError;

export type UserLikesPlayersPerformanceInput = {
  gameId: Scalars['ObjectId']['input'];
  playerIds?: InputMaybe<Array<Scalars['ObjectId']['input']>>;
};

export type UserLikesPlayersPerformanceResponse = {
  __typename?: 'UserLikesPlayersPerformanceResponse';
  errors?: Maybe<Array<UserLikesPlayersPerformanceError>>;
  playersByGame?: Maybe<PlayersByGame>;
};

export type UserNotFoundError = IUserError & {
  __typename?: 'UserNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UserNotInFamilyError = IUserError & {
  __typename?: 'UserNotInFamilyError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UserNotInPendingError = IUserError & {
  __typename?: 'UserNotInPendingError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type UserStatisticsByYearAndCountry = {
  __typename?: 'UserStatisticsByYearAndCountry';
  totalUsers: Scalars['Int']['output'];
  usersByCountryAndYear: Array<CountryYearStats>;
};

export type UserStats = {
  __typename?: 'UserStats';
  assists?: Maybe<Scalars['Float']['output']>;
  crawlerDomainConfig?: Maybe<CrawlerDomainConfig>;
  discoveredUrl?: Maybe<DiscoveredUrl>;
  externalInfo?: Maybe<ExternalInfo>;
  ga?: Maybe<Scalars['Float']['output']>;
  gaa?: Maybe<Scalars['Float']['output']>;
  gamesPlayed?: Maybe<Scalars['Float']['output']>;
  goals?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  key?: Maybe<Scalars['String']['output']>;
  league?: Maybe<League>;
  losses?: Maybe<Scalars['Float']['output']>;
  orderInSeason?: Maybe<Scalars['Float']['output']>;
  pim?: Maybe<Scalars['Float']['output']>;
  plusMinus?: Maybe<Scalars['Float']['output']>;
  points?: Maybe<Scalars['Float']['output']>;
  position?: Maybe<UserStatsPosition>;
  postSeasonStats?: Maybe<PostSeasonStats>;
  saves?: Maybe<Scalars['Float']['output']>;
  season: Scalars['String']['output'];
  seasonType?: Maybe<SeasonType>;
  shutouts?: Maybe<Scalars['Float']['output']>;
  source?: Maybe<StatsSource>;
  svp?: Maybe<Scalars['Float']['output']>;
  team?: Maybe<Team>;
  ties?: Maybe<Scalars['Float']['output']>;
  toi?: Maybe<Scalars['String']['output']>;
  total?: Maybe<Scalars['Boolean']['output']>;
  user?: Maybe<User>;
  userExternalId?: Maybe<Scalars['String']['output']>;
  wins?: Maybe<Scalars['Float']['output']>;
};

export type UserStatsEdge = {
  __typename?: 'UserStatsEdge';
  node: UserStats;
};

export type UserStatsFilter = {
  slug?: InputMaybe<Scalars['String']['input']>;
  user?: InputMaybe<Scalars['ObjectId']['input']>;
};

export type UserStatsNotFoundError = IUserError & {
  __typename?: 'UserStatsNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

/** Enum for user stats player positions */
export enum UserStatsPosition {
  Goalie = 'Goalie',
  Skater = 'Skater',
  Unknown = 'Unknown'
}

export type UserStatsResponse = {
  __typename?: 'UserStatsResponse';
  edges: Array<UserStatsEdge>;
  pageInfo: PageInfo;
};

export type UserStatsSyncError = UserStatsNotFoundError;

export type UserStatsSyncResponse = {
  __typename?: 'UserStatsSyncResponse';
  errors?: Maybe<Array<UserStatsSyncError>>;
  userStats?: Maybe<UserStats>;
};

export type UserViewEdge = {
  __typename?: 'UserViewEdge';
  node: User;
  views: Scalars['Int']['output'];
};

export type UserViewStatsResponse = {
  __typename?: 'UserViewStatsResponse';
  weeklyStats: Array<WeeklyViewStats>;
};

export type UserViewsResponse = {
  __typename?: 'UserViewsResponse';
  edges: Array<UserViewEdge>;
  myPosition: Scalars['Int']['output'];
  pageInfo: PageInfo;
};

export type UsersFilter = {
  country?: InputMaybe<Scalars['String']['input']>;
  role?: InputMaybe<Scalars['String']['input']>;
  roles?: InputMaybe<Array<Role>>;
  searchQuery?: InputMaybe<Scalars['String']['input']>;
};

export type UsersResponse = {
  __typename?: 'UsersResponse';
  aggregatedInfo?: Maybe<Array<AggregatedInfoGroup>>;
  edges: Array<UserEdge>;
  pageInfo: PageInfo;
};

export type VerifyAdminByEmailError = InvalidVerificationCodeError | UserNotFoundError;

export type VerifyAdminByEmailResponse = {
  __typename?: 'VerifyAdminByEmailResponse';
  errors: Array<VerifyAdminByEmailError>;
  tokens?: Maybe<JwtTokens>;
  user?: Maybe<AdminUser>;
};

export type VerifyByEmailError = InvalidVerificationCodeError | UserNotFoundError;

export type VerifyByEmailResponse = {
  __typename?: 'VerifyByEmailResponse';
  errors: Array<VerifyByEmailError>;
  tokens?: Maybe<JwtTokens>;
  user?: Maybe<User>;
};

export type VideoFilter = {
  segmentation?: InputMaybe<Scalars['Boolean']['input']>;
  tags?: InputMaybe<Array<WallVideoTags>>;
  type?: InputMaybe<Array<WallVideoType>>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type VideosByIdsInput = {
  videoIds: Array<Scalars['String']['input']>;
};

export type VideosByIdsResponse = {
  __typename?: 'VideosByIdsResponse';
  videos: Array<WallVideo>;
};

export type VideosCountByTags = {
  __typename?: 'VideosCountByTags';
  totalTaggedVideosCount: Scalars['Int']['output'];
  videoTagsCount: Array<TagCountWithPercentage>;
};

export type VideosCountByType = {
  __typename?: 'VideosCountByType';
  defensiveVideosCount: Scalars['Int']['output'];
  gameSaveVideosCount: Scalars['Int']['output'];
  highlightVideosCount: Scalars['Int']['output'];
  interviewVideosCount: Scalars['Int']['output'];
  offensiveVideosCount: Scalars['Int']['output'];
  otherVideosCount: Scalars['Int']['output'];
  puckHandlingVideosCount: Scalars['Int']['output'];
  trainingVideosCount: Scalars['Int']['output'];
};

export type VisitAgencyError = VisitAgencyNotFoundError;

export type VisitAgencyNotFoundError = IUserError & {
  __typename?: 'VisitAgencyNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type VisitAgencyResponse = {
  __typename?: 'VisitAgencyResponse';
  agency?: Maybe<Agency>;
  errors?: Maybe<Array<VisitAgencyError>>;
};

export type VisitTeamError = EntityNotFoundError;

export type VisitTeamResponse = {
  __typename?: 'VisitTeamResponse';
  errors?: Maybe<Array<VisitTeamError>>;
  team?: Maybe<Team>;
};

export type WallVideo = {
  __typename?: 'WallVideo';
  createdAt: Scalars['DateTimeISO']['output'];
  deleted?: Maybe<Scalars['Boolean']['output']>;
  deletedBy?: Maybe<Scalars['String']['output']>;
  duration: Scalars['Float']['output'];
  frameRate?: Maybe<Scalars['Float']['output']>;
  height?: Maybe<Scalars['Float']['output']>;
  hidden?: Maybe<Scalars['Boolean']['output']>;
  hls?: Maybe<Scalars['Boolean']['output']>;
  hlsPath?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  path: Scalars['String']['output'];
  repeat?: Maybe<WallVideoRepeat>;
  segmentation?: Maybe<WallVideoSegmentation>;
  size: Scalars['Float']['output'];
  streamFeedActivityId?: Maybe<Scalars['String']['output']>;
  tags?: Maybe<Array<WallVideoTags>>;
  thumbnail: Scalars['String']['output'];
  title: Scalars['String']['output'];
  type: WallVideoType;
  user?: Maybe<User>;
  width?: Maybe<Scalars['Float']['output']>;
};

export type WallVideoEdge = {
  __typename?: 'WallVideoEdge';
  node: WallVideo;
};

export type WallVideoNotFound = IUserError & {
  __typename?: 'WallVideoNotFound';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type WallVideoNotFoundError = IUserError & {
  __typename?: 'WallVideoNotFoundError';
  message: Scalars['String']['output'];
  path: Scalars['String']['output'];
};

export type WallVideoRepeat = {
  __typename?: 'WallVideoRepeat';
  end: Scalars['Float']['output'];
  start: Scalars['Float']['output'];
};

export type WallVideoSegmentation = {
  __typename?: 'WallVideoSegmentation';
  createdAt?: Maybe<Scalars['DateTimeISO']['output']>;
  finished?: Maybe<Scalars['Boolean']['output']>;
  path?: Maybe<Scalars['String']['output']>;
  videoHlsPath?: Maybe<Scalars['String']['output']>;
  videoPath?: Maybe<Scalars['String']['output']>;
};

/** The wall video tags */
export enum WallVideoTags {
  Correct = 'CORRECT',
  DidntSegment = 'DIDNT_SEGMENT',
  IncorrectObject = 'INCORRECT_OBJECT',
  MissingAtEnd = 'MISSING_AT_END',
  MissingInBeginning = 'MISSING_IN_BEGINNING',
  MissingInReplay = 'MISSING_IN_REPLAY',
  NotAccuratePosition = 'NOT_ACCURATE_POSITION',
  SegmentedMultiplePlayers = 'SEGMENTED_MULTIPLE_PLAYERS'
}

/** The wall video type */
export enum WallVideoType {
  Defensive = 'DEFENSIVE',
  GameSave = 'GAME_SAVE',
  Highlight = 'HIGHLIGHT',
  Interview = 'INTERVIEW',
  Offensive = 'OFFENSIVE',
  Other = 'OTHER',
  PuckHandling = 'PUCK_HANDLING',
  Training = 'TRAINING'
}

export type WallVideosFilter = {
  onlyLandscape?: InputMaybe<Scalars['Boolean']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<WallVideoType>;
  userId?: InputMaybe<Scalars['ObjectId']['input']>;
  videoId?: InputMaybe<Scalars['String']['input']>;
};

export type WallVideosResponse = {
  __typename?: 'WallVideosResponse';
  categoryCounts?: Maybe<Array<CategoryCount>>;
  edges: Array<WallVideoEdge>;
  pageInfo: PageInfo;
};

export type WeeklyViewStats = {
  __typename?: 'WeeklyViewStats';
  from: Scalars['DateTimeISO']['output'];
  to: Scalars['DateTimeISO']['output'];
  views: Scalars['Int']['output'];
};

export type Weight = {
  __typename?: 'Weight';
  kilograms?: Maybe<Scalars['Int']['output']>;
  pounds?: Maybe<Scalars['Int']['output']>;
};

export type WeightInput = {
  kilograms: Scalars['Int']['input'];
  pounds: Scalars['Int']['input'];
};

export type YearArguments = {
  year: Scalars['Int']['input'];
};

export type SendPushNotificationAdminError = UserDoesNotHaveRegisteredDeviceError | UserNotFoundError;

export type AgencyFragment = { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null };

export type AggregatedStatsItemFragment = { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null };

export type AggregatedStatsFragment = { __typename?: 'AggregatedStats', career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } };

export type BatchNotificationFragment = { __typename?: 'BatchNotification', id: string, state: string, title: string, message: string, type: string, forcePush?: boolean | null, destinationPath?: string | null, numberOfSent: number, totalUsers: number, scheduledAt?: any | null, filter: { __typename?: 'BatchNotificationFilter', role: Role, nationalities?: Array<string> | null, numberOfVideos?: Array<number> | null, firstNameInitials?: Array<string> | null, yearOfBirth?: Array<number> | null } };

export type BoostFragment = { __typename?: 'Boost', id: string, message?: string | null, name?: string | null, createdAt: any, currency: string, amount: number, state: string };

export type CrawlerDomainConfigFragment = { __typename?: 'CrawlerDomainConfig', id: string, domain: string, relevantUrlPatterns?: Array<string> | null, fetchType?: FetchType | null, enabled: boolean, parallelism?: number | null, urlConfigs: Array<{ __typename?: 'CrawlerUrlConfig', urlPatternRegExp: string, entity: CrawlerEntity, initialUrls: Array<string>, externalIdPatternRegExp?: string | null, extractionScript?: string | null, fetchType?: FetchType | null, addToQueue?: boolean | null, paramExtractors?: Array<{ __typename?: 'ParamExtractor', selector: string, queryParam: string }> | null }> };

export type CrawlerLogsFragment = { __typename?: 'CrawlerLogs', id: string, op: string, modelName: string, collectionName: string, collectionId: any, version: number, createdAt: any, updatedAt: any, patch: Array<{ __typename?: 'CrawlerLogsPatchOperation', op: string, path: string, value?: any | null }>, user: { __typename?: 'CrawlerLogsUser', id: string, email: string, admin: boolean, role: string, iat: number, exp: number } };

export type DiscoveredUrlListFragment = { __typename?: 'DiscoveredUrl', id: string, url: string, entity: CrawlerEntity, externalId?: string | null, crawlerDomainConfig?: { __typename?: 'CrawlerDomainConfig', id: string, domain: string, urlConfigs: Array<{ __typename?: 'CrawlerUrlConfig', urlPatternRegExp: string, entity: CrawlerEntity, initialUrls: Array<string>, externalIdPatternRegExp?: string | null, allowedQueryParams?: Array<string> | null, extractionScript?: string | null, paramExtractors?: Array<{ __typename?: 'ParamExtractor', selector: string, queryParam: string }> | null }> } | null, scraperInfo?: { __typename?: 'DiscoveredUrlScraperInfo', lastScrapedAt: any, scheduledForScrapeAt?: any | null, scrapeAttempts?: number | null, unsuccessfulScrapes?: number | null, scrapeSuccessful?: boolean | null } | null };

export type ExternalConnectionFragment = { __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } };

export type GameResponseFragment = { __typename?: 'GameStats', id: string, date: any, season?: string | null, externalId: string, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string }, stats?: { __typename?: 'GameOverallStats', periodScores?: string | null, homeTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, awayTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, scores?: Array<{ __typename?: 'GameScore', homeTeam?: number | null, awayTeam?: number | null, type?: GameScoreType | null }> | null } | null, events?: Array<{ __typename?: 'GameEvent', time: string, isHomeTeam?: boolean | null, type: GameEventType, score?: string | null, penaltyTime?: string | null, description?: string | null, descriptionCode?: string | null, assists?: Array<{ __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }> | null, externalInfo: { __typename?: 'GameEventExternalInfo', teamName: string, playerName?: string | null, playerExternalId?: string | null }, player?: { __typename?: 'User', id: string, name?: string | null } | null }> | null, league?: { __typename?: 'League', id: string, name: string } | null, homeTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, awayTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, homeTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null, id: string } | null }> | null, awayTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null }> | null };

export type GameListItemFragment = { __typename?: 'GameStats', id: string, date: any, externalId: string, ignore?: boolean | null, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string }, stats?: { __typename?: 'GameOverallStats', periodScores?: string | null, homeTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, awayTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null } | null, league?: { __typename?: 'League', id: string, name: string } | null, homeTeam?: { __typename?: 'Team', id: string, name: string } | null, awayTeam?: { __typename?: 'Team', id: string, name: string } | null };

export type GameDetailWithPlayersFragment = { __typename?: 'GameStats', id: string, homeTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null, id: string } | null }> | null, awayTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null }> | null };

export type LeagueFragment = { __typename?: 'League', id: string, name: string, slug: string, countries?: Array<string> | null, genderCategory?: LeagueGenderCategory | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, level?: LeagueLevel | null, type?: LeagueType | null, eliteProspectSlug?: string | null, sourceUrl?: string | null, checked?: boolean | null, hasGames?: boolean | null, publicAlternativeName?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null, teamAutoConnectRules?: { __typename?: 'TeamAutoConnectRules', country: string, shortName: string, shortNameSlug: string, shortNameInclude: boolean } | null };

export type LeagueStatsListItemFragment = { __typename?: 'LeagueStats', id: string, standing: number, games: number, wins: number, losses: number, overtimeWins: number, overtimeLosses: number, goalsFor: number, goalsAgainst: number, points: number, group?: string | null, league?: { __typename?: 'League', id: string, name: string } | null, team?: { __typename?: 'Team', id: string, name: string } | null };

export type NotificationsFragment = { __typename?: 'Notification', id: string, destinationPath?: string | null, body: string, sent: boolean, title: string, type: string, user?: { __typename?: 'User', name?: string | null } | null };

export type OrganizationFragment = { __typename?: 'Organization', country?: string | null, id: string, logo?: string | null, name: string, slug: string, state?: string | null, province?: string | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, arenaName?: string | null, arenaAddress?: string | null, checked?: boolean | null, abbreviation?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null };

export type TeamFragment = { __typename?: 'Team', id: string, name: string, stats?: { __typename?: 'TeamStats', followers: number, views: number } | null };

export type UserFragment = { __typename?: 'User', id: string, role?: Role | null, subrole?: SubRole | null, email: string, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, email: string, type?: ContactType | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, intro?: string | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, clientStaticConfig: { __typename?: 'ClientStaticConfig', inviteContest: { __typename?: 'InviteContest', enabled: boolean } }, subroleSpecifications?: Array<{ __typename?: 'SubRoleSpecification', type: string, current?: boolean | null, team: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } }> | null };

export type UserWithAgencyUnverifiedFragment = { __typename?: 'User', id: string, bio?: { __typename?: 'UserBio', agency?: { __typename?: 'Agency', id: string, name: string } | null, agencyUnverified?: { __typename?: 'Agency', id: string, name: string } | null } | null };

export type PublicUserFragment = { __typename?: 'User', id: string, role?: Role | null, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, inspired?: boolean | null, createdAt: any, following: boolean, hasGames?: boolean | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, type?: ContactType | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, externalConnections?: Array<{ __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, stats?: { __typename?: 'AggregatedStats', season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null };

export type ExampleUserFragment = { __typename?: 'User', id: string, name?: string | null, slug?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, avatars?: { __typename?: 'Avatars', small: string, large: string } | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null };

export type CurrentUserFragment = { __typename?: 'User', id: string, role?: Role | null, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, inspired?: boolean | null, createdAt: any, following: boolean, hasGames?: boolean | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, familyMembers?: Array<{ __typename?: 'User', id: string, name?: string | null, slug?: string | null, role?: Role | null, subrole?: SubRole | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null, agency?: { __typename?: 'Agency', id: string, name: string } | null } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, country?: string | null, shortNameSlug?: string | null, organization?: { __typename?: 'Organization', id: string, name: string, slug: string, logo?: string | null, country?: string | null } | null } | null }> | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, type?: ContactType | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, externalConnections?: Array<{ __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, stats?: { __typename?: 'AggregatedStats', season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null };

export type UserStatsFragment = { __typename?: 'UserStats', id: string, seasonType?: SeasonType | null, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null, orderInSeason?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null } | null, team?: { __typename?: 'Team', name: string, id: string, country?: string | null, shortName?: string | null, shortNameSlug?: string | null, slug: string } | null, league?: { __typename?: 'League', name: string, id: string, country?: string | null } | null, postSeasonStats?: { __typename?: 'PostSeasonStats', gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null } | null, externalInfo?: { __typename?: 'ExternalInfo', externalTeamName?: string | null, externalTeamCountry?: string | null, externalLeagueName?: string | null, externalLeagueCountry?: string | null } | null };

export type VideosByIdsVideoFragment = { __typename?: 'WallVideo', id: string, path: string, thumbnail: string, width?: number | null, height?: number | null, hls?: boolean | null, hlsPath?: string | null, size: number, title: string, duration: number, type: WallVideoType, createdAt: any, frameRate?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, following: boolean, role?: Role | null, subrole?: SubRole | null, slug?: string | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null } | null, stats?: { __typename?: 'AggregatedStats', career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null } | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null } | null };

export type VideosByIdsUserFragment = { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, following: boolean, role?: Role | null, subrole?: SubRole | null, slug?: string | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null } | null, stats?: { __typename?: 'AggregatedStats', career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null };

export type WallVideoFragment = { __typename?: 'WallVideo', id: string, type: WallVideoType, path: string, size: number, thumbnail: string, frameRate?: number | null, width?: number | null, height?: number | null, duration: number, hls?: boolean | null, hlsPath?: string | null, createdAt: any, tags?: Array<WallVideoTags> | null, streamFeedActivityId?: string | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null, createdAt?: any | null } | null, user?: { __typename?: 'User', id: string, name?: string | null, slug?: string | null } | null };

export type WallVideoReelFragment = { __typename?: 'WallVideo', id: string, path: string, thumbnail: string, width?: number | null, height?: number | null, hls?: boolean | null, hlsPath?: string | null, size: number, title: string, duration: number, type: WallVideoType, createdAt: any, frameRate?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, following: boolean, role?: Role | null, subrole?: SubRole | null, slug?: string | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null } | null, stats?: { __typename?: 'AggregatedStats', career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null } | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null } | null };

export type WallVideoReelUserFragment = { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, following: boolean, role?: Role | null, subrole?: SubRole | null, slug?: string | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null } | null, stats?: { __typename?: 'AggregatedStats', career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null };

export type SyncAdminUserStatsMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type SyncAdminUserStatsMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', userStats: { __typename?: 'AdminUserStatsMutations', sync: { __typename?: 'UserStatsSyncResponse', userStats?: { __typename?: 'UserStats', id: string, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, team?: { __typename?: 'Team', id: string, name: string } | null, league?: { __typename?: 'League', id: string, name: string } | null } | null, errors?: Array<{ __typename?: 'UserStatsNotFoundError', message: string, path: string }> | null } } } };

export type AcceptAgencyInvitationMutationVariables = Exact<{
  token: Scalars['String']['input'];
  agencyId: Scalars['ObjectId']['input'];
}>;


export type AcceptAgencyInvitationMutation = { __typename?: 'Mutation', acceptAgencyInvitation: { __typename?: 'AcceptAgencyInvitationResponse', success: boolean, errors?: Array<{ __typename?: 'AcceptAgencyInvitationAlreadyAcceptedError', message: string, path: string } | { __typename?: 'AcceptAgencyInvitationExpiredError', message: string, path: string } | { __typename?: 'AcceptAgencyInvitationInvalidTokenError', message: string, path: string }> | null } };

export type AddAgencyUserMutationVariables = Exact<{
  agencyId: Scalars['ObjectId']['input'];
  userId: Scalars['ObjectId']['input'];
}>;


export type AddAgencyUserMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', agency: { __typename?: 'AdminAgencyMutations', addUser: { __typename?: 'AddAgencyUserResponse', agency?: { __typename?: 'Agency', id: string, agentsCount?: number | null, clientsCount?: number | null } | null, errors?: Array<{ __typename?: 'AddAgencyUserAlreadyBelongsToAgencyError', message: string } | { __typename?: 'AddAgencyUserNotFoundError', message: string }> | null } } } };

export type AddFamilyMemberMutationVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type AddFamilyMemberMutation = { __typename?: 'Mutation', addFamilyMember: { __typename?: 'FamilyMemberUpdateResponse', user?: { __typename?: 'User', id: string, familyMembers?: Array<{ __typename?: 'User', id: string }> | null, pendingFamilyMembers?: Array<{ __typename?: 'User', id: string }> | null } | null, errors?: Array<{ __typename?: 'FamilyMemberAlreadyExistsError', message: string, path: string } | { __typename?: 'SelfFamilyMemberError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string } | { __typename?: 'UserNotInFamilyError' } | { __typename?: 'UserNotInPendingError' }> | null } };

export type CancelBatchNotificationMutationVariables = Exact<{
  batchId: Scalars['ObjectId']['input'];
}>;


export type CancelBatchNotificationMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', batchNotification: { __typename?: 'AdminBatchNotificationMutations', cancel: { __typename?: 'BatchNotificationResponse', errors?: Array<{ __typename?: 'BatchNotificationError', message: string, path: string } | { __typename?: 'BatchNotificationNotFoundError', message: string, path: string }> | null, notification?: { __typename?: 'BatchNotification', id: string, state: string } | null } } } };

export type CreateBatchNotificationMutationVariables = Exact<{
  input: BatchNotificationInput;
}>;


export type CreateBatchNotificationMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', batchNotification: { __typename?: 'AdminBatchNotificationMutations', create: { __typename?: 'BatchNotificationResponse', notification?: { __typename?: 'BatchNotification', id: string, state: string, title: string, message: string, type: string, forcePush?: boolean | null, destinationPath?: string | null, numberOfSent: number, totalUsers: number, scheduledAt?: any | null, filter: { __typename?: 'BatchNotificationFilter', role: Role, nationalities?: Array<string> | null, numberOfVideos?: Array<number> | null, firstNameInitials?: Array<string> | null, yearOfBirth?: Array<number> | null } } | null, errors?: Array<{ __typename?: 'BatchNotificationError', message: string } | { __typename?: 'BatchNotificationNotFoundError', message: string }> | null } } } };

export type CreateCrawlerDomainConfigMutationVariables = Exact<{
  input: CrawlerDomainConfigCreateInput;
}>;


export type CreateCrawlerDomainConfigMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', crawlerDomainConfig: { __typename?: 'CrawlerDomainConfigMutations', create: { __typename?: 'CrawlerDomainConfigCreateResponse', config: { __typename?: 'CrawlerDomainConfig', id: string, domain: string, relevantUrlPatterns?: Array<string> | null, fetchType?: FetchType | null, enabled: boolean, parallelism?: number | null, urlConfigs: Array<{ __typename?: 'CrawlerUrlConfig', urlPatternRegExp: string, entity: CrawlerEntity, initialUrls: Array<string>, externalIdPatternRegExp?: string | null, extractionScript?: string | null, fetchType?: FetchType | null, addToQueue?: boolean | null, paramExtractors?: Array<{ __typename?: 'ParamExtractor', selector: string, queryParam: string }> | null }> } } } } };

export type UpdateCrawlerDomainConfigMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
  input: CrawlerDomainConfigUpdateInput;
}>;


export type UpdateCrawlerDomainConfigMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', crawlerDomainConfig: { __typename?: 'CrawlerDomainConfigMutations', update: { __typename?: 'CrawlerDomainConfigUpdateResponse', config: { __typename?: 'CrawlerDomainConfig', id: string, domain: string, relevantUrlPatterns?: Array<string> | null, fetchType?: FetchType | null, enabled: boolean, parallelism?: number | null, urlConfigs: Array<{ __typename?: 'CrawlerUrlConfig', urlPatternRegExp: string, entity: CrawlerEntity, initialUrls: Array<string>, externalIdPatternRegExp?: string | null, extractionScript?: string | null, fetchType?: FetchType | null, addToQueue?: boolean | null, paramExtractors?: Array<{ __typename?: 'ParamExtractor', selector: string, queryParam: string }> | null }> } } } } };

export type DeleteAllUserStatsMutationVariables = Exact<{
  userId: Scalars['ObjectId']['input'];
}>;


export type DeleteAllUserStatsMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', userStats: { __typename?: 'AdminUserStatsMutations', deleteAllUserStats: { __typename?: 'DeleteUserStatsResponse', success: boolean, errors?: Array<{ __typename?: 'UserNotFoundError', message: string }> | null } } } };

export type ReScrapeMutationVariables = Exact<{
  reScrapeId: Scalars['ObjectId']['input'];
}>;


export type ReScrapeMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', discoveredUrl: { __typename?: 'AdminDiscoveredUrlMutations', reScrape: { __typename?: 'ReScrapeResponse', success: boolean, errors?: Array<{ __typename?: 'EntityNotFoundError', path: string, message: string }> | null } } } };

export type RemoveLeagueLogoMutationVariables = Exact<{
  removeLeagueLogoId: Scalars['ObjectId']['input'];
}>;


export type RemoveLeagueLogoMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', league: { __typename?: 'AdminLeagueMutations', removeLeagueLogo: { __typename?: 'RemoveLeagueLogoResponse', success: boolean, errors?: Array<{ __typename?: 'LeagueNotFoundError', message: string, path: string }> | null } } } };

export type UploadLeagueLogoMutationVariables = Exact<{
  uploadLeagueLogoId: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadLeagueLogoInput>;
}>;


export type UploadLeagueLogoMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', league: { __typename?: 'AdminLeagueMutations', uploadLeagueLogo: { __typename?: 'UploadLeagueLogoResponse', preSignedUrl?: string | null, league?: { __typename?: 'League', id: string, name: string, slug: string, logo?: string | null, countries?: Array<string> | null } | null, errors?: Array<{ __typename?: 'FailedToCreatePresignedUrl', message: string, path: string }> | null } } } };

export type MarkUserAsDeletedMutationVariables = Exact<{
  userId: Scalars['ObjectId']['input'];
}>;


export type MarkUserAsDeletedMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', user: { __typename?: 'AdminUserMutations', markUserAsDeleted: { __typename?: 'MarkUserAsDeletedResponse', success: boolean, errors?: Array<{ __typename?: 'UserAlreadyMarkedForDeletionError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }> | null } } } };

export type SendPushNotificationMutationVariables = Exact<{
  type: PushNotificationType;
  message: Scalars['String']['input'];
  title: Scalars['String']['input'];
  userId: Scalars['ObjectId']['input'];
  destinationPath?: InputMaybe<Scalars['String']['input']>;
  forcePush?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type SendPushNotificationMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', notifications: { __typename?: 'AdminNotificationsMutations', sendPushNotification: { __typename?: 'SendPushNotificationAdminResponse', success: boolean, errors?: Array<{ __typename?: 'UserDoesNotHaveRegisteredDeviceError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }> | null } } } };

export type CreateOrganizationMutationVariables = Exact<{
  input: CreateOrganizationInput;
}>;


export type CreateOrganizationMutation = { __typename?: 'Mutation', createOrganization: { __typename?: 'CreateOrganizationResponse', organization?: { __typename?: 'Organization', country?: string | null, id: string, logo?: string | null, name: string, slug: string, state?: string | null, province?: string | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, arenaName?: string | null, arenaAddress?: string | null, checked?: boolean | null, abbreviation?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null } | null, errors?: Array<{ __typename?: 'CreateOrganizationAlreadyExistsError', message: string, path: string }> | null } };

export type RemoveOrganizationLogoMutationVariables = Exact<{
  removeOrganizationLogoId: Scalars['ObjectId']['input'];
}>;


export type RemoveOrganizationLogoMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', organization: { __typename?: 'AdminOrganizationMutations', removeOrganizationLogo: { __typename?: 'RemoveOrganizationLogoResponse', success: boolean, errors?: Array<{ __typename?: 'OrganizationNotFoundError', message: string, path: string }> | null } } } };

export type UpdateOrganizationMutationVariables = Exact<{
  input: UpdateOrganizationInput;
  updateOrganizationId: Scalars['ObjectId']['input'];
}>;


export type UpdateOrganizationMutation = { __typename?: 'Mutation', updateOrganization: { __typename?: 'UpdateOrganizationResponse', organization?: { __typename?: 'Organization', country?: string | null, id: string, logo?: string | null, name: string, slug: string, state?: string | null, province?: string | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, arenaName?: string | null, arenaAddress?: string | null, checked?: boolean | null, abbreviation?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null } | null, errors?: Array<{ __typename?: 'UpdateOrganizationNotFoundError', message: string, path: string }> | null } };

export type SyncAgencyClientMutationVariables = Exact<{
  agencyId: Scalars['ObjectId']['input'];
  epLink: Scalars['String']['input'];
}>;


export type SyncAgencyClientMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', user: { __typename?: 'AdminUserMutations', syncAgencyClient: { __typename?: 'SyncAgencyClientsResponse', agency?: { __typename?: 'Agency', id: string, clientsCount?: number | null, shadowClientsCount?: number | null } | null, errors?: Array<{ __typename?: 'AgencyNotFoundError', message: string } | { __typename?: 'EPAgencyNotFoundError', message: string } | { __typename?: 'EPUserNotFoundError', message: string } | { __typename?: 'InvalidEPPlayerDataError', message: string }> | null } } } };

export type SyncSingleGameStatsMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type SyncSingleGameStatsMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', game: { __typename?: 'AdminGameMutations', syncSingle: { __typename?: 'SyncSingleGameResponse', game?: { __typename?: 'GameStats', id: string, date: any, externalId: string, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string } } | null, errors?: Array<{ __typename?: 'EntityNotFoundError', message: string, path: string }> | null } } } };

export type CreateTeamMutationVariables = Exact<{
  input: CreateTeamInput;
}>;


export type CreateTeamMutation = { __typename?: 'Mutation', createTeam: { __typename?: 'CreateTeamResponse', team?: { __typename?: 'Team', id: string, name: string, logo?: string | null, eliteProspectLink?: string | null, checked?: boolean | null, abbreviation?: string | null, shortName?: string | null, shortNameSlug?: string | null, externalId?: string | null, country?: string | null, order?: number | null, organization?: { __typename?: 'Organization', id: string, name: string, country?: string | null, slug: string, logo?: string | null } | null } | null, errors?: Array<{ __typename?: 'CreateTeamAlreadyExistsError', message: string, path: string }> | null } };

export type RemoveTeamLogoMutationVariables = Exact<{
  removeTeamLogoId: Scalars['ObjectId']['input'];
}>;


export type RemoveTeamLogoMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', team: { __typename?: 'AdminTeamMutations', removeTeamLogo: { __typename?: 'RemoveTeamLogoResponse', success: boolean, errors?: Array<{ __typename?: 'TeamNotFoundError', message: string, path: string }> | null } } } };

export type UpdateTeamMutationVariables = Exact<{
  input: UpdateTeamInput;
  updateTeamId: Scalars['ObjectId']['input'];
}>;


export type UpdateTeamMutation = { __typename?: 'Mutation', updateTeam: { __typename?: 'UpdateTeamResponse', errors?: Array<{ __typename?: 'UpdateTeamNotFoundError', message: string, path: string }> | null, team?: { __typename?: 'Team', id: string, name: string, slug: string, logo?: string | null, country?: string | null, eliteProspectLink?: string | null, checked?: boolean | null, abbreviation?: string | null, order?: number | null, externalId?: string | null, shortName?: string | null, shortNameSlug?: string | null, organization?: { __typename?: 'Organization', id: string, name: string, slug: string, country?: string | null, logo?: string | null } | null } | null } };

export type UpdateBoostMessageMutationVariables = Exact<{
  message: Scalars['String']['input'];
  boostId: Scalars['ObjectId']['input'];
}>;


export type UpdateBoostMessageMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', boost: { __typename?: 'AdminBoostMutations', updateBoostMessage: { __typename?: 'BoostMessageResponse', boost?: { __typename?: 'Boost', id: string, message?: string | null, name?: string | null, createdAt: any, currency: string, amount: number, state: string } | null, errors?: Array<{ __typename?: 'BoostMessageAlreadySetError', message: string, path: string } | { __typename?: 'BoostNotFoundError', message: string, path: string }> | null } } } };

export type AddExternalConnectionMutationVariables = Exact<{
  crawlerDomainConfigId: Scalars['ObjectId']['input'];
  externalId: Scalars['String']['input'];
  userId: Scalars['ObjectId']['input'];
}>;


export type AddExternalConnectionMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', user: { __typename?: 'AdminUserMutations', addExternalConnection: { __typename?: 'AddExternalConnectionResponse', success: boolean, errors?: Array<{ __typename?: 'ExternalConnectionError', message: string } | { __typename?: 'UserInfoNotFoundError', message: string } | { __typename?: 'UserNotFoundError', message: string }> | null } } } };

export type RemoveExternalConnectionMutationVariables = Exact<{
  crawlerDomainConfigId: Scalars['ObjectId']['input'];
  externalId: Scalars['String']['input'];
  userId: Scalars['ObjectId']['input'];
}>;


export type RemoveExternalConnectionMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', user: { __typename?: 'AdminUserMutations', removeExternalConnection: { __typename?: 'RemoveExternalConnectionResponse', success: boolean, errors?: Array<{ __typename?: 'ExternalConnectionError', message: string } | { __typename?: 'UserInfoNotFoundError', message: string } | { __typename?: 'UserNotFoundError', message: string }> | null } } } };

export type UploadAvatarAdminMutationVariables = Exact<{
  userId: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadAvatarInput>;
}>;


export type UploadAvatarAdminMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', user: { __typename?: 'AdminUserMutations', uploadAvatar: { __typename?: 'UploadAvatarResponse', preSignedUrl?: string | null, errors?: Array<{ __typename?: 'FailedToCreatePresignedUrl', message: string, path: string }> | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null, slug?: string | null, avatar?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, avatars?: { __typename?: 'Avatars', large: string, medium: string, small: string, tiny: string } | null } | null } } } };

export type DeleteWallVideoAdminMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type DeleteWallVideoAdminMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', wallVideo: { __typename?: 'AdminWallVideosMutations', delete: { __typename?: 'DeleteWallVideoResponse', success?: boolean | null, errors?: Array<{ __typename?: 'FailedToDelete', message: string, path: string }> | null } } } };

export type AppleSignInMutationVariables = Exact<{
  input: SocialSignInInput;
}>;


export type AppleSignInMutation = { __typename?: 'Mutation', appleSignIn: { __typename?: 'SocialSignInResponse', onboardingCompleted?: boolean | null, user?: { __typename?: 'User', id: string, name?: string | null, email: string, currentTeam?: { __typename?: 'Team', name: string } | null } | null, tokens?: { __typename?: 'JWTTokens', accessToken: string, refreshToken: string } | null, errors: Array<{ __typename?: 'GeneralError', message: string, path: string }> } };

export type ChangeTeamOrderDownMutationVariables = Exact<{
  changeTeamOrderDownId: Scalars['ObjectId']['input'];
}>;


export type ChangeTeamOrderDownMutation = { __typename?: 'Mutation', changeTeamOrderDown: boolean };

export type ChangeTeamOrderUpMutationVariables = Exact<{
  changeTeamOrderUpId: Scalars['ObjectId']['input'];
}>;


export type ChangeTeamOrderUpMutation = { __typename?: 'Mutation', changeTeamOrderUp: boolean };

export type ConfirmFamilyMemberMutationVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type ConfirmFamilyMemberMutation = { __typename?: 'Mutation', confirmFamilyMember: { __typename?: 'FamilyMemberUpdateResponse', user?: { __typename?: 'User', id: string, familyMembers?: Array<{ __typename?: 'User', id: string }> | null, pendingFamilyMembers?: Array<{ __typename?: 'User', id: string }> | null } | null, errors?: Array<{ __typename?: 'FamilyMemberAlreadyExistsError', message: string, path: string } | { __typename?: 'SelfFamilyMemberError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string } | { __typename?: 'UserNotInFamilyError' } | { __typename?: 'UserNotInPendingError', message: string, path: string }> | null } };

export type CreateAdminMutationVariables = Exact<{
  input: CreateAdminInput;
}>;


export type CreateAdminMutation = { __typename?: 'Mutation', createAdmin: { __typename?: 'CreateAdminResponse', user?: { __typename?: 'AdminUser', id: string, firstname?: string | null, lastname?: string | null, email: string } | null } };

export type CreateAgencyMutationVariables = Exact<{
  input: CreateAgencyInput;
}>;


export type CreateAgencyMutation = { __typename?: 'Mutation', createAgency: { __typename?: 'CreateAgencyResponse', agency?: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } | null, errors?: Array<{ __typename?: 'CreateAgencyAlreadyExistsError', message: string, path: string }> | null } };

export type CreateBoostMutationVariables = Exact<{
  input: CreateBoostInput;
}>;


export type CreateBoostMutation = { __typename?: 'Mutation', createBoost: { __typename?: 'CreateBoostResponse', boost?: { __typename?: 'Boost', id: string, name?: string | null, amount: number, currency: string, message?: string | null, state: string, createdAt: any } | null, errors?: Array<{ __typename?: 'StripeNotConnectedError', message: string, path: string }> | null, paymentSheet?: { __typename?: 'PaymentSheet', accountId?: string | null, customerId?: string | null, ephemeralKey?: string | null, paymentIntent?: string | null, publishableKey?: string | null } | null } };

export type CreateBoostMessageMutationVariables = Exact<{
  input?: InputMaybe<BoostMessageInput>;
}>;


export type CreateBoostMessageMutation = { __typename?: 'Mutation', createBoostMessage: { __typename?: 'BoostMessageResponse', errors?: Array<{ __typename?: 'BoostMessageAlreadySetError', message: string, path: string } | { __typename?: 'BoostNotFoundError', message: string, path: string }> | null, boost?: { __typename?: 'Boost', id: string, currency: string, amount: number, name?: string | null, message?: string | null, state: string } | null } };

export type CreateChatUserTokenMutationVariables = Exact<{ [key: string]: never; }>;


export type CreateChatUserTokenMutation = { __typename?: 'Mutation', createChatUserToken: string };

export type CreateChatUsersMutationVariables = Exact<{
  input: CreateChatUsersInput;
}>;


export type CreateChatUsersMutation = { __typename?: 'Mutation', createChatUsers: boolean };

export type CreateLeagueMutationVariables = Exact<{
  input: CreateLeagueInput;
}>;


export type CreateLeagueMutation = { __typename?: 'Mutation', createLeague: { __typename?: 'CreateLeagueResponse', errors?: Array<{ __typename?: 'CreateLeagueAlreadyExistsError', message: string, path: string }> | null, league?: { __typename?: 'League', id: string, name: string, slug: string, countries?: Array<string> | null, genderCategory?: LeagueGenderCategory | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, level?: LeagueLevel | null, type?: LeagueType | null, eliteProspectSlug?: string | null, sourceUrl?: string | null, checked?: boolean | null, hasGames?: boolean | null, publicAlternativeName?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null, teamAutoConnectRules?: { __typename?: 'TeamAutoConnectRules', country: string, shortName: string, shortNameSlug: string, shortNameInclude: boolean } | null } | null } };

export type CreatePassiveEventMutationVariables = Exact<{
  input: CreatePassiveEventInput;
}>;


export type CreatePassiveEventMutation = { __typename?: 'Mutation', createPassiveEvent: boolean };

export type CreateStreamFeedUserTokenMutationVariables = Exact<{ [key: string]: never; }>;


export type CreateStreamFeedUserTokenMutation = { __typename?: 'Mutation', createStreamFeedUserToken: string };

export type CreateStripeAccountMutationVariables = Exact<{
  input: CreateStripeAccountInput;
}>;


export type CreateStripeAccountMutation = { __typename?: 'Mutation', createStripeAccount: { __typename?: 'CreateStripeAccountResponse', account?: { __typename?: 'StripeAccount', id: string, firstname?: string | null, email?: string | null, lastname?: string | null, paymentsEnabled?: boolean | null, payoutsEnabled?: boolean | null, phone?: string | null, ssnLast4Provided?: boolean | null, dob?: any | null, currency?: string | null, country?: string | null, jobTitle?: string | null, address?: { __typename?: 'StripeAddress', city?: string | null, line1?: string | null, line2?: string | null, postalCode?: string | null, state?: string | null } | null } | null, errors: Array<{ __typename?: 'AccountAlreadyExistsError', message: string, path: string } | { __typename?: 'StripeAccountUserInputError', message: string, path: string }> } };

export type CreateStripeExternalAccountMutationVariables = Exact<{
  input: CreateExternalAccountInput;
}>;


export type CreateStripeExternalAccountMutation = { __typename?: 'Mutation', createStripeExternalAccount: { __typename?: 'CreateStripeExternalAccountResponse', errors: Array<{ __typename?: 'AccountDoesNotExistsError', message: string, path: string } | { __typename?: 'ExternalAccountAlreadyExistsError', message: string, path: string } | { __typename?: 'ExternalAccountUserInputError', message: string, path: string }>, externalAccount?: { __typename?: 'StripeExternalAccount', id: string, routingNumber?: string | null, accountNumber?: string | null, status?: string | null, bankAccount?: { __typename?: 'StripeBankAccount', country?: string | null, currency?: string | null, firstname?: string | null, lastname?: string | null } | null } | null } };

export type CreateUserStatsMutationVariables = Exact<{
  input: CreateUserStatsInput;
}>;


export type CreateUserStatsMutation = { __typename?: 'Mutation', createUserStats: { __typename?: 'CreateUserStatsResponse', userStats?: { __typename?: 'UserStats', id: string, seasonType?: SeasonType | null, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null, orderInSeason?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null } | null, team?: { __typename?: 'Team', name: string, id: string, country?: string | null, shortName?: string | null, shortNameSlug?: string | null, slug: string } | null, league?: { __typename?: 'League', name: string, id: string, country?: string | null } | null, postSeasonStats?: { __typename?: 'PostSeasonStats', gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null } | null, externalInfo?: { __typename?: 'ExternalInfo', externalTeamName?: string | null, externalTeamCountry?: string | null, externalLeagueName?: string | null, externalLeagueCountry?: string | null } | null } | null, errors?: Array<{ __typename?: 'CreateUserStatsAlreadyExistsError', message: string, path: string }> | null } };

export type DeleteAccountMutationVariables = Exact<{ [key: string]: never; }>;


export type DeleteAccountMutation = { __typename?: 'Mutation', deleteAccount: boolean };

export type DeleteAdminMutationVariables = Exact<{
  deleteAdminId: Scalars['ObjectId']['input'];
}>;


export type DeleteAdminMutation = { __typename?: 'Mutation', deleteAdmin: boolean };

export type DeleteAgencyMutationVariables = Exact<{
  deleteAgencyId: Scalars['ObjectId']['input'];
}>;


export type DeleteAgencyMutation = { __typename?: 'Mutation', deleteAgency: boolean };

export type DeleteLeagueMutationVariables = Exact<{
  deleteLeagueId: Scalars['ObjectId']['input'];
}>;


export type DeleteLeagueMutation = { __typename?: 'Mutation', deleteLeague: boolean };

export type DeleteOrganizationMutationVariables = Exact<{
  deleteOrganizationId: Scalars['ObjectId']['input'];
}>;


export type DeleteOrganizationMutation = { __typename?: 'Mutation', deleteOrganization: boolean };

export type DeleteStripeExternalAccountMutationVariables = Exact<{ [key: string]: never; }>;


export type DeleteStripeExternalAccountMutation = { __typename?: 'Mutation', deleteStripeExternalAccount: boolean };

export type DeleteTeamMutationVariables = Exact<{
  deleteTeamId: Scalars['ObjectId']['input'];
}>;


export type DeleteTeamMutation = { __typename?: 'Mutation', deleteTeam: boolean };

export type DeleteUserStatsMutationVariables = Exact<{
  deleteUserStatsId: Scalars['ObjectId']['input'];
}>;


export type DeleteUserStatsMutation = { __typename?: 'Mutation', deleteUserStats: boolean };

export type DeleteWallVideoMutationVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type DeleteWallVideoMutation = { __typename?: 'Mutation', deleteWallVideo: { __typename?: 'DeleteWallVideoResponse', success?: boolean | null, errors?: Array<{ __typename?: 'FailedToDelete', path: string, message: string }> | null } };

export type EditHighlightsMutationVariables = Exact<{
  input: EditHighlightsInput;
}>;


export type EditHighlightsMutation = { __typename?: 'Mutation', editHighlights: { __typename?: 'EditHighlightsResponse', user?: { __typename?: 'User', highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null } | null, errors?: Array<{ __typename?: 'FailedToEdit', message: string }> | null } };

export type EditWallVideoCategoryMutationVariables = Exact<{
  input: EditWallVideoInput;
}>;


export type EditWallVideoCategoryMutation = { __typename?: 'Mutation', editWallVideoCategory: { __typename?: 'EditWallVideoCategoryResponse', errors?: Array<{ __typename?: 'FailedToEdit', message: string, path: string } | { __typename?: 'WallVideoNotFound', message: string, path: string }> | null, wallVideo?: { __typename?: 'WallVideo', id: string, type: WallVideoType, path: string, width?: number | null, height?: number | null, hls?: boolean | null, hlsPath?: string | null, createdAt: any, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null, createdAt?: any | null } | null } | null } };

export type FetchUserDataEpMutationVariables = Exact<{ [key: string]: never; }>;


export type FetchUserDataEpMutation = { __typename?: 'Mutation', fetchUserDataEP: { __typename?: 'FetchUserDataEPResponse', errors: Array<{ __typename?: 'FetchUserDataEPError', message: string, path: string }>, user?: { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, externalLinks?: Array<{ __typename?: 'ExternalLink', id: string, name: string, type?: ExternalLinkType | null, url: string }> | null, currentTeam?: { __typename?: 'Team', id: string } | null } | null } };

export type FollowAgencyMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type FollowAgencyMutation = { __typename?: 'Mutation', followAgency: { __typename?: 'FollowAgencyResponse', agency?: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } | null, errors?: Array<{ __typename?: 'FollowAgencyNotFoundError', message: string }> | null } };

export type FollowTeamMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type FollowTeamMutation = { __typename?: 'Mutation', followTeam: { __typename?: 'FollowTeamResponse', team?: { __typename?: 'Team', id: string, name: string, stats?: { __typename?: 'TeamStats', followers: number, views: number } | null } | null, errors?: Array<{ __typename?: 'EntityNotFoundError', message: string }> | null } };

export type FollowUserMutationVariables = Exact<{
  followUserId: Scalars['String']['input'];
}>;


export type FollowUserMutation = { __typename?: 'Mutation', followUser: { __typename?: 'User', id: string, userStats?: { __typename?: 'UserBasicStats', followers: number } | null } };

export type GoogleSignInMutationVariables = Exact<{
  input: SocialSignInInput;
}>;


export type GoogleSignInMutation = { __typename?: 'Mutation', googleSignIn: { __typename?: 'SocialSignInResponse', onboardingCompleted?: boolean | null, tokens?: { __typename?: 'JWTTokens', accessToken: string, refreshToken: string } | null, user?: { __typename?: 'User', id: string, name?: string | null, email: string, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, email: string } | null } | null, errors: Array<{ __typename?: 'GeneralError', message: string, path: string }> } };

export type GoogleSignInAdminMutationVariables = Exact<{
  input: AdminSocialSignInInput;
}>;


export type GoogleSignInAdminMutation = { __typename?: 'Mutation', googleSignInAdmin: { __typename?: 'AdminSocialSignInResponse', tokens?: { __typename?: 'JWTTokens', accessToken: string, refreshToken: string } | null, user?: { __typename?: 'AdminUser', id: string, firstname?: string | null, lastname?: string | null, email: string } | null, errors: Array<{ __typename?: 'GeneralError', message: string, path: string }> } };

export type InviteAgentsMutationVariables = Exact<{
  input: InviteAgentsInput;
}>;


export type InviteAgentsMutation = { __typename?: 'Mutation', inviteAgents: { __typename?: 'InviteAgentsResponse', success: boolean, errors?: Array<{ __typename?: 'InviteAgentsAlreadyExistsError', message: string, path: string }> | null } };

export type LikePlayerPerformanceMutationVariables = Exact<{
  gameId: Scalars['ObjectId']['input'];
  playerId: Scalars['ObjectId']['input'];
}>;


export type LikePlayerPerformanceMutation = { __typename?: 'Mutation', likePlayerPerformance: { __typename?: 'LikePlayerPerformanceResponse', errors: Array<{ __typename?: 'EntityNotFoundError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }>, player?: { __typename?: 'User', id: string } | null } };

export type LikeWallVideoMutationVariables = Exact<{
  id: Scalars['String']['input'];
  isHighlight?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type LikeWallVideoMutation = { __typename?: 'Mutation', likeWallVideo: boolean };

export type LoginAdminMutationVariables = Exact<{
  email: Scalars['String']['input'];
}>;


export type LoginAdminMutation = { __typename?: 'Mutation', loginAdmin: { __typename?: 'LoginAdminResponse', errors: Array<{ __typename?: 'UserNotFoundError', message: string, path: string }> } };

export type MigrateUserStatsMutationVariables = Exact<{
  input: MigrateUserStatsInput;
}>;


export type MigrateUserStatsMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', userStats: { __typename?: 'AdminUserStatsMutations', migrate: { __typename?: 'MigrateUserStatsResponse', success: boolean, modifiedCount?: number | null, errors?: Array<{ __typename?: 'FailedToUpdateStats', message: string }> | null } } } };

export type PushNotificationsMutationVariables = Exact<{
  input: PushNotificationsInput;
}>;


export type PushNotificationsMutation = { __typename?: 'Mutation', pushNotifications: { __typename?: 'PushNotificationsResponse', errors: Array<{ __typename?: 'PushNotificationsChangeError', message: string, path: string }>, user?: { __typename?: 'User', id: string, firstname?: string | null, lastname?: string | null, pushEnabled?: boolean | null, role?: Role | null, currentTeam?: { __typename?: 'Team', id: string } | null } | null } };

export type RegisterDeviceMutationVariables = Exact<{
  input: RegisterDeviceInput;
}>;


export type RegisterDeviceMutation = { __typename?: 'Mutation', registerDevice: { __typename?: 'RegisterDeviceResponse', user?: { __typename?: 'User', registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null } | null, errors: Array<{ __typename?: 'DeviceAlreadyRegisteredError', message: string }> } };

export type RegisterOrLoginByEmailMutationVariables = Exact<{
  input: RegisterOrLoginByEmailInput;
  newUrl?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type RegisterOrLoginByEmailMutation = { __typename?: 'Mutation', registerOrLoginByEmail: { __typename?: 'RegisteryOrLoginByEmailResponse', user?: { __typename?: 'User', id: string, role?: Role | null, subrole?: SubRole | null, email: string, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, email: string, type?: ContactType | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, intro?: string | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, clientStaticConfig: { __typename?: 'ClientStaticConfig', inviteContest: { __typename?: 'InviteContest', enabled: boolean } }, subroleSpecifications?: Array<{ __typename?: 'SubRoleSpecification', type: string, current?: boolean | null, team: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } }> | null } | null, errors: Array<{ __typename?: 'GeneralError', message: string }> } };

export type RemoveAgencyUserMutationVariables = Exact<{
  agencyId: Scalars['ObjectId']['input'];
  userId: Scalars['ObjectId']['input'];
}>;


export type RemoveAgencyUserMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', agency: { __typename?: 'AdminAgencyMutations', removeAgencyUser: { __typename?: 'RemoveAgencyUserResponse', agency?: { __typename?: 'Agency', id: string, agentsCount?: number | null, clientsCount?: number | null } | null, errors?: Array<{ __typename?: 'RemoveAgencyUserDoesNotBelongToAgencyError', message: string } | { __typename?: 'RemoveAgencyUserNotFoundError', message: string }> | null } } } };

export type RemoveFamilyMemberMutationVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type RemoveFamilyMemberMutation = { __typename?: 'Mutation', removeFamilyMember: { __typename?: 'FamilyMemberUpdateResponse', user?: { __typename?: 'User', id: string, familyMembers?: Array<{ __typename?: 'User', id: string }> | null, pendingFamilyMembers?: Array<{ __typename?: 'User', id: string }> | null } | null, errors?: Array<{ __typename?: 'FamilyMemberAlreadyExistsError' } | { __typename?: 'SelfFamilyMemberError' } | { __typename?: 'UserNotFoundError', message: string, path: string } | { __typename?: 'UserNotInFamilyError', message: string, path: string } | { __typename?: 'UserNotInPendingError' }> | null } };

export type RemovePendingFamilyMemberMutationVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type RemovePendingFamilyMemberMutation = { __typename?: 'Mutation', removePendingFamilyMember: { __typename?: 'FamilyMemberUpdateResponse', user?: { __typename?: 'User', id: string, familyMembers?: Array<{ __typename?: 'User', id: string }> | null, pendingFamilyMembers?: Array<{ __typename?: 'User', id: string }> | null } | null, errors?: Array<{ __typename?: 'FamilyMemberAlreadyExistsError' } | { __typename?: 'SelfFamilyMemberError' } | { __typename?: 'UserNotFoundError', message: string, path: string } | { __typename?: 'UserNotInFamilyError' } | { __typename?: 'UserNotInPendingError', message: string, path: string }> | null } };

export type SendContactMessageMutationVariables = Exact<{
  input?: InputMaybe<ContactMessageInput>;
}>;


export type SendContactMessageMutation = { __typename?: 'Mutation', sendContactMessage: { __typename?: 'ContactMessageResponse', success?: boolean | null, errors?: Array<{ __typename?: 'ContactNotFoundError', message: string, path: string }> | null } };

export type SyncAgencyClientsMutationVariables = Exact<{
  epAgencyId: Scalars['Int']['input'];
  agencyId: Scalars['ObjectId']['input'];
}>;


export type SyncAgencyClientsMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', user: { __typename?: 'AdminUserMutations', syncAgencyClients: { __typename?: 'SyncAgencyClientsResponse', agency?: { __typename?: 'Agency', id: string, clientsCount?: number | null } | null, errors?: Array<{ __typename?: 'AgencyNotFoundError', message: string } | { __typename?: 'EPAgencyNotFoundError', message: string } | { __typename?: 'EPUserNotFoundError', message: string } | { __typename?: 'InvalidEPPlayerDataError', message: string }> | null } } } };

export type SyncUserStatsMutationVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type SyncUserStatsMutation = { __typename?: 'Mutation', syncUserStats: boolean };

export type UnfollowAgencyMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type UnfollowAgencyMutation = { __typename?: 'Mutation', unfollowAgency: { __typename?: 'UnfollowAgencyResponse', agency?: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } | null, errors?: Array<{ __typename?: 'UnfollowAgencyNotFoundError', message: string }> | null } };

export type UnfollowTeamMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type UnfollowTeamMutation = { __typename?: 'Mutation', unfollowTeam: { __typename?: 'UnfollowTeamResponse', team?: { __typename?: 'Team', id: string, name: string, stats?: { __typename?: 'TeamStats', followers: number, views: number } | null } | null, errors?: Array<{ __typename?: 'EntityNotFoundError', message: string }> | null } };

export type UnfollowUserMutationVariables = Exact<{
  unfollowUserId: Scalars['String']['input'];
}>;


export type UnfollowUserMutation = { __typename?: 'Mutation', unfollowUser: { __typename?: 'UnfollowUserResponse', errors: Array<{ __typename?: 'UserNotFoundError', message: string, path: string }>, user?: { __typename?: 'User', id: string, userStats?: { __typename?: 'UserBasicStats', followers: number } | null } | null } };

export type UnlikePlayerPerformanceMutationVariables = Exact<{
  gameId: Scalars['ObjectId']['input'];
  playerId: Scalars['ObjectId']['input'];
}>;


export type UnlikePlayerPerformanceMutation = { __typename?: 'Mutation', unlikePlayerPerformance: { __typename?: 'UnlikePlayerPerformanceResponse', errors: Array<{ __typename?: 'EntityNotFoundError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }>, player?: { __typename?: 'User', id: string } | null } };

export type UnregisterDeviceMutationVariables = Exact<{
  input: UnregisterDeviceInput;
}>;


export type UnregisterDeviceMutation = { __typename?: 'Mutation', unregisterDevice: { __typename?: 'UnregisterDeviceResponse', user?: { __typename?: 'User', registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null } | null, errors: Array<{ __typename?: 'DeviceNotRegisteredError', message: string }> } };

export type UpdateAgencyMutationVariables = Exact<{
  input: UpdateAgencyInput;
  updateAgencyId: Scalars['ObjectId']['input'];
}>;


export type UpdateAgencyMutation = { __typename?: 'Mutation', updateAgency: { __typename?: 'UpdateAgencyResponse', agency?: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } | null, errors?: Array<{ __typename?: 'UpdateAgencyNotFoundError', message: string, path: string }> | null } };

export type UpdateLeagueMutationVariables = Exact<{
  input: UpdateLeagueInput;
  updateLeagueId: Scalars['ObjectId']['input'];
}>;


export type UpdateLeagueMutation = { __typename?: 'Mutation', updateLeague: { __typename?: 'UpdateLeagueResponse', errors?: Array<{ __typename?: 'LeagueAlreadyExistsError' } | { __typename?: 'UpdateLeagueNotFoundError', message: string, path: string }> | null, league?: { __typename?: 'League', id: string, name: string, slug: string, countries?: Array<string> | null, genderCategory?: LeagueGenderCategory | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, level?: LeagueLevel | null, type?: LeagueType | null, eliteProspectSlug?: string | null, sourceUrl?: string | null, checked?: boolean | null, hasGames?: boolean | null, publicAlternativeName?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null, teamAutoConnectRules?: { __typename?: 'TeamAutoConnectRules', country: string, shortName: string, shortNameSlug: string, shortNameInclude: boolean } | null } | null } };

export type UpdateStripeAccountMutationVariables = Exact<{
  input: CreateStripeAccountInput;
}>;


export type UpdateStripeAccountMutation = { __typename?: 'Mutation', updateStripeAccount: { __typename?: 'UpdateStripeAccountResponse', account?: { __typename?: 'StripeAccount', id: string, firstname?: string | null, lastname?: string | null, dob?: any | null, currency?: string | null, phone?: string | null, email?: string | null, country?: string | null, payoutsEnabled?: boolean | null, paymentsEnabled?: boolean | null, ssnLast4Provided?: boolean | null, jobTitle?: string | null, address?: { __typename?: 'StripeAddress', city?: string | null, line1?: string | null, line2?: string | null, postalCode?: string | null, state?: string | null } | null } | null, errors: Array<{ __typename?: 'AccountDoesNotExistsError', message: string, path: string } | { __typename?: 'StripeAccountUserInputError', message: string, path: string }> } };

export type UpdateUserMutationVariables = Exact<{
  input: UpdateUserInput;
}>;


export type UpdateUserMutation = { __typename?: 'Mutation', updateUser: { __typename?: 'UpdateUserResponse', user?: { __typename?: 'User', id: string, role?: Role | null, subrole?: SubRole | null, email: string, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, email: string, type?: ContactType | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, intro?: string | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, clientStaticConfig: { __typename?: 'ClientStaticConfig', inviteContest: { __typename?: 'InviteContest', enabled: boolean } }, subroleSpecifications?: Array<{ __typename?: 'SubRoleSpecification', type: string, current?: boolean | null, team: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } }> | null } | null, errors: Array<{ __typename?: 'UserNotFoundError', message: string }> } };

export type UpdateUserAdminMutationVariables = Exact<{
  input: UpdateUserInputAdmin;
  updateUserAdminId: Scalars['ObjectId']['input'];
}>;


export type UpdateUserAdminMutation = { __typename?: 'Mutation', updateUserAdmin: { __typename?: 'UpdateUserAdminResponse', user?: { __typename?: 'User', id: string, name?: string | null, originalFirstname?: string | null, firstname?: string | null, originalLastname?: string | null, lastname?: string | null, email: string, slug?: string | null, dateOfBirth?: any | null, country?: string | null, avatar?: string | null, boostMessage?: string | null, epCurrentTeam?: any | null, stripeAccountId?: string | null, stripeExternalAccountId?: string | null, recommended?: boolean | null, inspired?: boolean | null, moreSelected?: boolean | null, role?: Role | null, subrole?: SubRole | null, public?: boolean | null, premium?: boolean | null, premiumSince?: any | null, searchEnabled?: boolean | null, isAdult: boolean, pushEnabled?: boolean | null, numberOfVideos?: number | null, createdAt: any, contact?: { __typename?: 'Contact', name?: string | null, firstname?: string | null, lastname?: string | null, email: string, type?: ContactType | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null } | null, agentV2?: { __typename?: 'User', name?: string | null, firstname?: string | null, lastname?: string | null, email: string, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null } | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string, name: string, country?: string | null, logo?: string | null, checked?: boolean | null } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, avatars?: { __typename?: 'Avatars', tiny: string, small: string, medium: string, large: string } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', id: string, name: string, url: string, type?: ExternalLinkType | null }> | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number, currentAmount?: number | null } | null, userStats?: { __typename?: 'UserBasicStats', followers: number, boosts: number, views: number } | null, registeredDevices?: Array<{ __typename?: 'Device', token: string, platform: Platform, arn?: string | null }> | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, type: WallVideoType, title: string, path: string, thumbnail: string, size: number, width?: number | null, height?: number | null, duration: number, hls?: boolean | null, hlsPath?: string | null, createdAt: any }> | null } | null, errors: Array<{ __typename?: 'UserNotFoundError', message: string, path: string }> } };

export type UpdateUserCurrencyMutationVariables = Exact<{
  currency: Scalars['String']['input'];
  updateUserCurrencyId: Scalars['ObjectId']['input'];
}>;


export type UpdateUserCurrencyMutation = { __typename?: 'Mutation', updateUserCurrency: { __typename?: 'UpdateDefaultCurrencyResponse', currency?: string | null, errors: Array<{ __typename?: 'CurrencyUpdateError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }> } };

export type UpdateUserStatsMutationVariables = Exact<{
  input: UpdateUserStatsInput;
  updateUserStatsId: Scalars['ObjectId']['input'];
}>;


export type UpdateUserStatsMutation = { __typename?: 'Mutation', updateUserStats: { __typename?: 'UpdateUserStatsResponse', userStats?: { __typename?: 'UserStats', id: string, seasonType?: SeasonType | null, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null, orderInSeason?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null } | null, team?: { __typename?: 'Team', name: string, id: string, country?: string | null, shortName?: string | null, shortNameSlug?: string | null, slug: string } | null, league?: { __typename?: 'League', name: string, id: string, country?: string | null } | null, postSeasonStats?: { __typename?: 'PostSeasonStats', gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null } | null, externalInfo?: { __typename?: 'ExternalInfo', externalTeamName?: string | null, externalTeamCountry?: string | null, externalLeagueName?: string | null, externalLeagueCountry?: string | null } | null } | null, errors?: Array<{ __typename?: 'UpdateUserStatsNotFoundError', message: string, path: string }> | null } };

export type UpdateUserWithAgencyUnverifiedMutationVariables = Exact<{
  input: UpdateUserInput;
}>;


export type UpdateUserWithAgencyUnverifiedMutation = { __typename?: 'Mutation', updateUser: { __typename?: 'UpdateUserResponse', user?: { __typename?: 'User', id: string, bio?: { __typename?: 'UserBio', agency?: { __typename?: 'Agency', id: string, name: string } | null, agencyUnverified?: { __typename?: 'Agency', id: string, name: string } | null } | null } | null, errors: Array<{ __typename: 'UserNotFoundError', message: string }> } };

export type UpdateWallVideoMutationVariables = Exact<{
  updateId: Scalars['ObjectId']['input'];
  input: UpdateWallVideoInput;
}>;


export type UpdateWallVideoMutation = { __typename?: 'Mutation', admin: { __typename?: 'AdminMutation', wallVideo: { __typename?: 'AdminWallVideosMutations', update: { __typename?: 'UpdateWallVideoResponse', wallVideo?: { __typename?: 'WallVideo', id: string, type: WallVideoType, path: string, size: number, thumbnail: string, frameRate?: number | null, width?: number | null, height?: number | null, duration: number, hls?: boolean | null, hlsPath?: string | null, createdAt: any, tags?: Array<WallVideoTags> | null, streamFeedActivityId?: string | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null, createdAt?: any | null } | null, user?: { __typename?: 'User', id: string, name?: string | null, slug?: string | null } | null } | null, errors: Array<{ __typename?: 'WallVideoNotFoundError', message: string, path: string }> } } } };

export type UploadAgencyLogoMutationVariables = Exact<{
  uploadAgencyLogoId: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadAgencyLogoInput>;
}>;


export type UploadAgencyLogoMutation = { __typename?: 'Mutation', uploadAgencyLogo: { __typename?: 'UploadAgencyLogoResponse', preSignedUrl?: string | null, agency?: { __typename?: 'Agency', id: string, name: string, country?: string | null, logo?: string | null, checked?: boolean | null } | null, errors?: Array<{ __typename?: 'FailedToCreatePresignedUrl', message: string, path: string }> | null } };

export type UploadAvatarMutationVariables = Exact<{
  input?: InputMaybe<UploadAvatarInput>;
}>;


export type UploadAvatarMutation = { __typename?: 'Mutation', uploadAvatar: { __typename?: 'UploadAvatarResponse', preSignedUrl?: string | null, errors?: Array<{ __typename?: 'FailedToCreatePresignedUrl', message: string, path: string }> | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null, slug?: string | null, avatar?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, avatars?: { __typename?: 'Avatars', large: string, medium: string, small: string, tiny: string } | null } | null } };

export type UploadOrganizationLogoMutationVariables = Exact<{
  uploadOrganizationLogoId: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadOrganizationLogoInput>;
}>;


export type UploadOrganizationLogoMutation = { __typename?: 'Mutation', uploadOrganizationLogo: { __typename?: 'UploadOrganizationLogoResponse', preSignedUrl?: string | null, organization?: { __typename?: 'Organization', id: string, name: string, slug: string, country?: string | null, logo?: string | null } | null, errors?: Array<{ __typename?: 'FailedToCreatePresignedUrl', message: string, path: string }> | null } };

export type UploadTeamLogoMutationVariables = Exact<{
  uploadTeamLogoId: Scalars['ObjectId']['input'];
  input?: InputMaybe<UploadTeamLogoInput>;
}>;


export type UploadTeamLogoMutation = { __typename?: 'Mutation', uploadTeamLogo: { __typename?: 'UploadTeamLogoResponse', preSignedUrl?: string | null, team?: { __typename?: 'Team', id: string, name: string, slug: string, logo?: string | null, country?: string | null, order?: number | null, externalId?: string | null, shortName?: string | null, shortNameSlug?: string | null } | null, errors?: Array<{ __typename?: 'FailedToCreatePresignedUrl', message: string, path: string }> | null } };

export type UploadWallVideoMutationVariables = Exact<{
  input?: InputMaybe<UploadWallVideoInput>;
}>;


export type UploadWallVideoMutation = { __typename?: 'Mutation', uploadWallVideo: { __typename?: 'UploadWallVideoResponse', preSignedUrl?: string | null, thumbnailPreSignedUrl?: string | null, errors?: Array<{ __typename?: 'FailedToCreatePresignedUrl', message: string, path: string }> | null, wallVideo?: { __typename?: 'WallVideo', id: string, hls?: boolean | null, thumbnail: string, createdAt: any, duration: number, height?: number | null, size: number, title: string, type: WallVideoType, width?: number | null } | null } };

export type UploadWallVideoSegmentationMutationVariables = Exact<{
  input: UploadWallVideoSegmentationInput;
}>;


export type UploadWallVideoSegmentationMutation = { __typename?: 'Mutation', uploadWallVideoSegmentation: { __typename?: 'UploadWallVideoSegmentationResponse', preSignedUrl?: string | null, wallVideo?: { __typename?: 'WallVideo', id: string, path: string, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null } | null } | null, errors?: Array<{ __typename?: 'FailedToEdit', message: string } | { __typename?: 'WallVideoNotFound', message: string }> | null } };

export type VerifyAdminByEmailMutationVariables = Exact<{
  code: Scalars['String']['input'];
  email: Scalars['String']['input'];
}>;


export type VerifyAdminByEmailMutation = { __typename?: 'Mutation', verifyAdminByEmail: { __typename?: 'VerifyAdminByEmailResponse', tokens?: { __typename?: 'JWTTokens', accessToken: string, refreshToken: string } | null, user?: { __typename?: 'AdminUser', id: string, firstname?: string | null, lastname?: string | null, email: string } | null, errors: Array<{ __typename?: 'InvalidVerificationCodeError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }> } };

export type VerifyByEmailMutationVariables = Exact<{
  email: Scalars['String']['input'];
  code: Scalars['String']['input'];
}>;


export type VerifyByEmailMutation = { __typename?: 'Mutation', verifyByEmail: { __typename?: 'VerifyByEmailResponse', tokens?: { __typename?: 'JWTTokens', accessToken: string, refreshToken: string } | null, user?: { __typename?: 'User', id: string, role?: Role | null, subrole?: SubRole | null, email: string, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, email: string, type?: ContactType | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, intro?: string | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, clientStaticConfig: { __typename?: 'ClientStaticConfig', inviteContest: { __typename?: 'InviteContest', enabled: boolean } }, subroleSpecifications?: Array<{ __typename?: 'SubRoleSpecification', type: string, current?: boolean | null, team: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } }> | null } | null, errors: Array<{ __typename?: 'InvalidVerificationCodeError', message: string } | { __typename?: 'UserNotFoundError', message: string }> } };

export type VisitAgencyMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type VisitAgencyMutation = { __typename?: 'Mutation', visitAgency: { __typename?: 'VisitAgencyResponse', agency?: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } | null, errors?: Array<{ __typename?: 'VisitAgencyNotFoundError', message: string }> | null } };

export type VisitTeamMutationVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type VisitTeamMutation = { __typename?: 'Mutation', visitTeam: { __typename?: 'VisitTeamResponse', team?: { __typename?: 'Team', id: string, name: string, stats?: { __typename?: 'TeamStats', followers: number, views: number } | null } | null, errors?: Array<{ __typename?: 'EntityNotFoundError', message: string }> | null } };

export type VisitUserMutationVariables = Exact<{
  visitUserId: Scalars['ObjectId']['input'];
}>;


export type VisitUserMutation = { __typename?: 'Mutation', visitUser: { __typename?: 'User', id: string, userStats?: { __typename?: 'UserBasicStats', views: number } | null } };

export type GetBatchNotificationsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
}>;


export type GetBatchNotificationsQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', batchNotification: { __typename?: 'AdminBatchNotificationQueries', list: { __typename?: 'BatchNotificationListResponse', edges: Array<{ __typename?: 'BatchNotificationEdge', node: { __typename?: 'BatchNotification', id: string, state: string, title: string, message: string, type: string, forcePush?: boolean | null, destinationPath?: string | null, numberOfSent: number, totalUsers: number, scheduledAt?: any | null, filter: { __typename?: 'BatchNotificationFilter', role: Role, nationalities?: Array<string> | null, numberOfVideos?: Array<number> | null, firstNameInitials?: Array<string> | null, yearOfBirth?: Array<number> | null } } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type BoostsQueryVariables = Exact<{
  filter?: InputMaybe<AdminBoostFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type BoostsQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', boost: { __typename?: 'AdminBoostQueries', list: { __typename?: 'BoostsResponse', edges: Array<{ __typename?: 'BoostEdge', node: { __typename?: 'Boost', id: string, message?: string | null, name?: string | null, createdAt: any, currency: string, amount: number, state: string, user?: { __typename?: 'User', id: string, name?: string | null } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type GetCrawlerDomainConfigQueryVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type GetCrawlerDomainConfigQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', crawlerDomainConfig: { __typename?: 'CrawlerDomainConfigQueries', get?: { __typename?: 'CrawlerDomainConfig', id: string, domain: string, relevantUrlPatterns?: Array<string> | null, fetchType?: FetchType | null, enabled: boolean, parallelism?: number | null, urlConfigs: Array<{ __typename?: 'CrawlerUrlConfig', urlPatternRegExp: string, entity: CrawlerEntity, initialUrls: Array<string>, externalIdPatternRegExp?: string | null, extractionScript?: string | null, fetchType?: FetchType | null, addToQueue?: boolean | null, paramExtractors?: Array<{ __typename?: 'ParamExtractor', selector: string, queryParam: string }> | null }> } | null } } };

export type GetCrawlerDomainConfigsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
}>;


export type GetCrawlerDomainConfigsQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', crawlerDomainConfig: { __typename?: 'CrawlerDomainConfigQueries', list: { __typename?: 'CrawlerDomainConfigListResponse', edges: Array<{ __typename?: 'CrawlerDomainConfigEdge', node: { __typename?: 'CrawlerDomainConfig', id: string, domain: string, relevantUrlPatterns?: Array<string> | null, fetchType?: FetchType | null, enabled: boolean, parallelism?: number | null, urlConfigs: Array<{ __typename?: 'CrawlerUrlConfig', urlPatternRegExp: string, entity: CrawlerEntity, initialUrls: Array<string>, externalIdPatternRegExp?: string | null, extractionScript?: string | null, fetchType?: FetchType | null, addToQueue?: boolean | null, paramExtractors?: Array<{ __typename?: 'ParamExtractor', selector: string, queryParam: string }> | null }> } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type GetCrawlerLogsItemQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type GetCrawlerLogsItemQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', crawlerLogs: { __typename?: 'AdminCrawlerLogsQueries', get?: { __typename?: 'CrawlerLogs', id: string, op: string, modelName: string, collectionName: string, collectionId: any, version: number, createdAt: any, updatedAt: any, patch: Array<{ __typename?: 'CrawlerLogsPatchOperation', op: string, path: string, value?: any | null }>, user: { __typename?: 'CrawlerLogsUser', id: string, email: string, admin: boolean, role: string, iat: number, exp: number } } | null } } };

export type GetCrawlerLogsQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<CrawlerLogsFilter>;
}>;


export type GetCrawlerLogsQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', crawlerLogs: { __typename?: 'AdminCrawlerLogsQueries', list: { __typename?: 'CrawlerLogsResponse', edges: Array<{ __typename?: 'CrawlerLogsEdge', node: { __typename?: 'CrawlerLogs', id: string, op: string, modelName: string, collectionName: string, collectionId: any, version: number, createdAt: any, updatedAt: any, patch: Array<{ __typename?: 'CrawlerLogsPatchOperation', op: string, path: string, value?: any | null }>, user: { __typename?: 'CrawlerLogsUser', id: string, email: string, admin: boolean, role: string, iat: number, exp: number } } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type Admin_CrawlerRunStats_GetQueryVariables = Exact<{
  id: Scalars['String']['input'];
}>;


export type Admin_CrawlerRunStats_GetQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', crawlerRunStats: { __typename?: 'AdminCrawlerRunStatsQueries', get?: { __typename?: 'CrawlerRunStats', id: string, startedAt: any, finishedAt: any, durationMs: number, totalVisitedUrls: number, totalDiscoveredUrls: number, newlyDiscoveredUrls: number, urlPatternStats?: Array<{ __typename?: 'DomainUrlStats', urlPattern: string, matchCount: number }> | null, crawlerDomainConfig?: { __typename?: 'CrawlerDomainConfig', id: string, domain: string } | null } | null } } };

export type Admin_CrawlerRunStats_ListQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  filter?: InputMaybe<CrawlerRunStatsFilter>;
}>;


export type Admin_CrawlerRunStats_ListQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', crawlerRunStats: { __typename?: 'AdminCrawlerRunStatsQueries', list: { __typename?: 'CrawlerRunStatsResponse', edges: Array<{ __typename?: 'CrawlerRunStatsEdge', node: { __typename?: 'CrawlerRunStats', id: string, startedAt: any, finishedAt: any, durationMs: number, totalVisitedUrls: number, totalDiscoveredUrls: number, newlyDiscoveredUrls: number, urlPatternStats?: Array<{ __typename?: 'DomainUrlStats', urlPattern: string, matchCount: number }> | null, crawlerDomainConfig?: { __typename?: 'CrawlerDomainConfig', id: string, domain: string } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, hasPreviousPage: boolean, startCursor?: string | null, endCursor?: string | null, totalCount?: number | null } } } } };

export type DiscoveredUrlListQueryVariables = Exact<{
  filter?: InputMaybe<DiscoveredUrlFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type DiscoveredUrlListQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', discoveredUrl: { __typename?: 'AdminDiscoveredUrlQueries', list: { __typename?: 'DiscoveredUrlListResponse', edges: Array<{ __typename?: 'DiscoveredUrlEdge', node: { __typename?: 'DiscoveredUrl', id: string, url: string, entity: CrawlerEntity, externalId?: string | null, crawlerDomainConfig?: { __typename?: 'CrawlerDomainConfig', id: string, domain: string, urlConfigs: Array<{ __typename?: 'CrawlerUrlConfig', urlPatternRegExp: string, entity: CrawlerEntity, initialUrls: Array<string>, externalIdPatternRegExp?: string | null, allowedQueryParams?: Array<string> | null, extractionScript?: string | null, paramExtractors?: Array<{ __typename?: 'ParamExtractor', selector: string, queryParam: string }> | null }> } | null, scraperInfo?: { __typename?: 'DiscoveredUrlScraperInfo', lastScrapedAt: any, scheduledForScrapeAt?: any | null, scrapeAttempts?: number | null, unsuccessfulScrapes?: number | null, scrapeSuccessful?: boolean | null } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type AdminGameGetQueryVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type AdminGameGetQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', game: { __typename?: 'AdminGameQueries', get: { __typename?: 'GameStats', id: string, date: any, season?: string | null, externalId: string, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string }, stats?: { __typename?: 'GameOverallStats', periodScores?: string | null, homeTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, awayTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, scores?: Array<{ __typename?: 'GameScore', homeTeam?: number | null, awayTeam?: number | null, type?: GameScoreType | null }> | null } | null, events?: Array<{ __typename?: 'GameEvent', time: string, isHomeTeam?: boolean | null, type: GameEventType, score?: string | null, penaltyTime?: string | null, description?: string | null, descriptionCode?: string | null, assists?: Array<{ __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }> | null, externalInfo: { __typename?: 'GameEventExternalInfo', teamName: string, playerName?: string | null, playerExternalId?: string | null }, player?: { __typename?: 'User', id: string, name?: string | null } | null }> | null, league?: { __typename?: 'League', id: string, name: string } | null, homeTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, awayTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, homeTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null, id: string } | null }> | null, awayTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null }> | null } } } };

export type AdminGameListQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  filter?: InputMaybe<AdminGameFilter>;
  sort?: InputMaybe<SortInput>;
}>;


export type AdminGameListQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', game: { __typename?: 'AdminGameQueries', list: { __typename?: 'GameResponse', edges: Array<{ __typename?: 'GameEdge', node: { __typename?: 'GameStats', id: string, date: any, externalId: string, ignore?: boolean | null, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string }, stats?: { __typename?: 'GameOverallStats', periodScores?: string | null, homeTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, awayTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null } | null, league?: { __typename?: 'League', id: string, name: string } | null, homeTeam?: { __typename?: 'Team', id: string, name: string } | null, awayTeam?: { __typename?: 'Team', id: string, name: string } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type GetLeagueQueryVariables = Exact<{
  leagueId: Scalars['ObjectId']['input'];
}>;


export type GetLeagueQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', league: { __typename?: 'AdminLeagueQueries', get?: { __typename?: 'League', id: string, name: string, slug: string, countries?: Array<string> | null, genderCategory?: LeagueGenderCategory | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, level?: LeagueLevel | null, type?: LeagueType | null, eliteProspectSlug?: string | null, sourceUrl?: string | null, checked?: boolean | null, hasGames?: boolean | null, publicAlternativeName?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null, teamAutoConnectRules?: { __typename?: 'TeamAutoConnectRules', country: string, shortName: string, shortNameSlug: string, shortNameInclude: boolean } | null } | null } } };

export type AdminLeagueListQueryVariables = Exact<{
  filter?: InputMaybe<AdminLeaguesFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type AdminLeagueListQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', league: { __typename?: 'AdminLeagueQueries', list: { __typename?: 'AdminLeaguesResponse', edges: Array<{ __typename?: 'AdminLeagueEdge', used: boolean, node: { __typename?: 'League', id: string, name: string, slug: string, countries?: Array<string> | null, genderCategory?: LeagueGenderCategory | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, level?: LeagueLevel | null, type?: LeagueType | null, eliteProspectSlug?: string | null, sourceUrl?: string | null, checked?: boolean | null, hasGames?: boolean | null, publicAlternativeName?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null, teamAutoConnectRules?: { __typename?: 'TeamAutoConnectRules', country: string, shortName: string, shortNameSlug: string, shortNameInclude: boolean } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type AdminLeagueStatsListQueryVariables = Exact<{
  filter: AdminLeagueStatsFilter;
}>;


export type AdminLeagueStatsListQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', leagueStats: { __typename?: 'AdminLeagueStatsQueries', list: { __typename?: 'AdminLeagueStatsResponse', edges: Array<{ __typename?: 'AdminLeagueStatsEdge', node: { __typename?: 'LeagueStats', id: string, standing: number, games: number, wins: number, losses: number, overtimeWins: number, overtimeLosses: number, goalsFor: number, goalsAgainst: number, points: number, group?: string | null, league?: { __typename?: 'League', id: string, name: string } | null, team?: { __typename?: 'Team', id: string, name: string } | null } }> } } } };

export type GetNotificationsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  filter?: InputMaybe<AdminNotificationFilter>;
}>;


export type GetNotificationsQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', notifications: { __typename?: 'AdminNotificationsQueries', getNotifications: { __typename?: 'AdminNotificationResponse', edges: Array<{ __typename?: 'AdminNotificationEdges', node: { __typename?: 'Notification', id: string, destinationPath?: string | null, body: string, sent: boolean, title: string, type: string, user?: { __typename?: 'User', name?: string | null } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type UserInfoQueryVariables = Exact<{
  filter: UserInfoFilter;
  pagination?: InputMaybe<Pagination>;
}>;


export type UserInfoQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', userInfo: { __typename?: 'AdminUserInfoQueries', userInfos: { __typename?: 'UserInfoResponse', edges: Array<{ __typename?: 'UserInfoEdge', node: { __typename?: 'UserInfo', id: string, name: string, externalId: string, crawlerDomainConfig?: { __typename?: 'CrawlerDomainConfig', id: string, domain: string } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type AdminWallVideosQueryVariables = Exact<{
  filter?: InputMaybe<VideoFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type AdminWallVideosQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', wallVideo: { __typename?: 'AdminWallVideosQueries', list: { __typename?: 'WallVideosResponse', edges: Array<{ __typename?: 'WallVideoEdge', node: { __typename?: 'WallVideo', id: string, type: WallVideoType, path: string, size: number, thumbnail: string, frameRate?: number | null, width?: number | null, height?: number | null, duration: number, hls?: boolean | null, hlsPath?: string | null, createdAt: any, tags?: Array<WallVideoTags> | null, streamFeedActivityId?: string | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null, createdAt?: any | null } | null, user?: { __typename?: 'User', id: string, name?: string | null, slug?: string | null } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type AdminUserQueryVariables = Exact<{
  userId: Scalars['ObjectId']['input'];
}>;


export type AdminUserQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', user?: { __typename?: 'User', firstname?: string | null, lastname?: string | null, currentTeam?: { __typename?: 'Team', name: string } | null, bio?: { __typename?: 'UserBio', agency?: { __typename?: 'Agency', id: string, name: string } | null, agencyUnverified?: { __typename?: 'Agency', id: string, name: string } | null } | null } | null } };

export type AdminUserStatsQueryVariables = Exact<{
  adminUserStatsId: Scalars['ObjectId']['input'];
}>;


export type AdminUserStatsQuery = { __typename?: 'Query', adminUserStats?: { __typename?: 'UserStats', id: string, seasonType?: SeasonType | null, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null, orderInSeason?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null } | null, team?: { __typename?: 'Team', name: string, id: string, country?: string | null, shortName?: string | null, shortNameSlug?: string | null, slug: string } | null, league?: { __typename?: 'League', name: string, id: string, country?: string | null } | null, postSeasonStats?: { __typename?: 'PostSeasonStats', gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null } | null, externalInfo?: { __typename?: 'ExternalInfo', externalTeamName?: string | null, externalTeamCountry?: string | null, externalLeagueName?: string | null, externalLeagueCountry?: string | null } | null } | null };

export type AdminUserStatsListQueryVariables = Exact<{
  filter?: InputMaybe<AdminUserStatsFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type AdminUserStatsListQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', userStats: { __typename?: 'AdminUserStatsQueries', list: { __typename?: 'UserStatsResponse', edges: Array<{ __typename?: 'UserStatsEdge', node: { __typename?: 'UserStats', id: string, seasonType?: SeasonType | null, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null, orderInSeason?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null } | null, team?: { __typename?: 'Team', name: string, id: string, country?: string | null, shortName?: string | null, shortNameSlug?: string | null, slug: string } | null, league?: { __typename?: 'League', name: string, id: string, country?: string | null } | null, postSeasonStats?: { __typename?: 'PostSeasonStats', gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null } | null, externalInfo?: { __typename?: 'ExternalInfo', externalTeamName?: string | null, externalTeamCountry?: string | null, externalLeagueName?: string | null, externalLeagueCountry?: string | null } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } } } };

export type AdminUsersQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
}>;


export type AdminUsersQuery = { __typename?: 'Query', adminUsers: { __typename?: 'AdminUsersResponse', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'AminUserEdge', node: { __typename?: 'AdminUser', id: string, firstname?: string | null, lastname?: string | null, email: string } }> } };

export type AgenciesQueryVariables = Exact<{
  filter?: InputMaybe<AgenciesFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type AgenciesQuery = { __typename?: 'Query', agencies: { __typename?: 'AgenciesResponse', edges: Array<{ __typename?: 'AgencyEdge', node: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } };

export type AgencyQueryVariables = Exact<{
  agencyId?: InputMaybe<Scalars['ObjectId']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
}>;


export type AgencyQuery = { __typename?: 'Query', agency?: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } | null };

export type AgencyUsersQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  agencyId: Scalars['ObjectId']['input'];
  filter?: InputMaybe<AgencyUsersFilter>;
}>;


export type AgencyUsersQuery = { __typename?: 'Query', agencyUsers: { __typename?: 'UsersResponse', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', id: string, name?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, agentV2?: { __typename?: 'User', id: string, name?: string | null } | null } }>, aggregatedInfo?: Array<{ __typename?: 'AggregatedInfoGroup', name?: string | null, items?: Array<{ __typename?: 'AggregatedInfoItem', key?: string | null, value?: number | null }> | null }> | null } };

export type AgentsClientsQueryVariables = Exact<{
  slug: Scalars['String']['input'];
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
}>;


export type AgentsClientsQuery = { __typename?: 'Query', agentsClients: { __typename?: 'UsersResponse', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', name?: string | null, currentTeam?: { __typename?: 'Team', name: string } | null } }>, aggregatedInfo?: Array<{ __typename?: 'AggregatedInfoGroup', name?: string | null, items?: Array<{ __typename?: 'AggregatedInfoItem', key?: string | null, value?: number | null }> | null }> | null } };

export type BoostsQueryQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  filter?: InputMaybe<BoostsFilter>;
}>;


export type BoostsQueryQuery = { __typename?: 'Query', boosts: { __typename?: 'BoostsResponse', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'BoostEdge', node: { __typename?: 'Boost', id: string, name?: string | null, amount: number, currency: string } }> } };

export type CurrentAdminUserQueryVariables = Exact<{ [key: string]: never; }>;


export type CurrentAdminUserQuery = { __typename?: 'Query', currentAdminUser: { __typename?: 'AdminUser', id: string, firstname?: string | null, lastname?: string | null, email: string } };

export type CurrentUserQueryVariables = Exact<{ [key: string]: never; }>;


export type CurrentUserQuery = { __typename?: 'Query', currentUser: { __typename?: 'User', id: string, role?: Role | null, subrole?: SubRole | null, email: string, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, email: string, type?: ContactType | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, intro?: string | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, phoneNumber?: { __typename?: 'PhoneNumber', countryPrefix: string, number: string } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, clientStaticConfig: { __typename?: 'ClientStaticConfig', inviteContest: { __typename?: 'InviteContest', enabled: boolean } }, subroleSpecifications?: Array<{ __typename?: 'SubRoleSpecification', type: string, current?: boolean | null, team: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } }> | null } };

export type CurrentUserFollowingsQueryVariables = Exact<{ [key: string]: never; }>;


export type CurrentUserFollowingsQuery = { __typename?: 'Query', currentUser: { __typename?: 'User', id: string, userStats?: { __typename?: 'UserBasicStats', followings: number } | null } };

export type CurrentUserWithAgencyUnverifiedQueryVariables = Exact<{ [key: string]: never; }>;


export type CurrentUserWithAgencyUnverifiedQuery = { __typename?: 'Query', currentUser: { __typename?: 'User', id: string, bio?: { __typename?: 'UserBio', agency?: { __typename?: 'Agency', id: string, name: string } | null, agencyUnverified?: { __typename?: 'Agency', id: string, name: string } | null } | null } };

export type EntityCountsQueryVariables = Exact<{ [key: string]: never; }>;


export type EntityCountsQuery = { __typename?: 'Query', entityCounts: { __typename?: 'EntityCountsResponse', players: number, professionals: number, agencies: number } };

export type EpTeamQueryVariables = Exact<{
  epTeamId: Scalars['String']['input'];
}>;


export type EpTeamQuery = { __typename?: 'Query', epTeam?: { __typename?: 'EliteProspectTeam', id: string, slug: string, name: string, country: { __typename?: 'EliteProspectCountry', iso_3166_1_alpha_2?: string | null }, logo?: { __typename?: 'EliteProspectLogo', large: string } | null } | null };

export type EpTeamsQueryVariables = Exact<{
  searchQuery: Scalars['String']['input'];
}>;


export type EpTeamsQuery = { __typename?: 'Query', epTeams: Array<{ __typename?: 'EliteProspectTeam', id: string, slug: string, name: string, country: { __typename?: 'EliteProspectCountry', iso_3166_1_alpha_2?: string | null }, logo?: { __typename?: 'EliteProspectLogo', large: string } | null }> };

export type ExampleUsersQueryVariables = Exact<{ [key: string]: never; }>;


export type ExampleUsersQuery = { __typename?: 'Query', exampleUsers: Array<{ __typename?: 'User', id: string, name?: string | null, slug?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, avatars?: { __typename?: 'Avatars', small: string, large: string } | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null }> };

export type ExampleWallVideosQueryVariables = Exact<{ [key: string]: never; }>;


export type ExampleWallVideosQuery = { __typename?: 'Query', exampleWallVideos: Array<{ __typename?: 'WallVideo', id: string, type: WallVideoType, path: string, size: number, thumbnail: string, frameRate?: number | null, width?: number | null, height?: number | null, duration: number, hls?: boolean | null, hlsPath?: string | null, createdAt: any, tags?: Array<WallVideoTags> | null, streamFeedActivityId?: string | null, user?: { __typename?: 'User', id: string, name?: string | null, slug?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, avatars?: { __typename?: 'Avatars', small: string, large: string } | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null } | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null, createdAt?: any | null } | null }> };

export type GameQueryVariables = Exact<{
  gameId: Scalars['ObjectId']['input'];
}>;


export type GameQuery = { __typename?: 'Query', game?: { __typename?: 'GameStats', id: string, date: any, externalId: string, syncedAt?: any | null, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string }, league?: { __typename?: 'League', id: string, name: string } | null, awayTeam?: { __typename?: 'Team', id: string, name: string, shortNameSlug?: string | null, organization?: { __typename?: 'Organization', slug: string } | null } | null, homeTeam?: { __typename?: 'Team', id: string, name: string, shortNameSlug?: string | null, organization?: { __typename?: 'Organization', slug: string } | null } | null, stats?: { __typename?: 'GameOverallStats', periodScores?: string | null, homeTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, awayTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null } | null, eventsByPeriod?: Array<Array<{ __typename?: 'GameEvent', time: string, type: GameEventType, isHomeTeam?: boolean | null, externalInfo: { __typename?: 'GameEventExternalInfo', teamName: string, playerName?: string | null, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null, assists?: Array<{ __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }> | null }>> | null, homeTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null, id: string } | null }> | null, awayTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null }> | null } | null };

export type GameEventsQueryVariables = Exact<{
  gameId: Scalars['ObjectId']['input'];
}>;


export type GameEventsQuery = { __typename?: 'Query', game?: { __typename?: 'GameStats', id: string, date: any, season?: string | null, externalId: string, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string }, stats?: { __typename?: 'GameOverallStats', periodScores?: string | null, homeTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, awayTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, scores?: Array<{ __typename?: 'GameScore', homeTeam?: number | null, awayTeam?: number | null, type?: GameScoreType | null }> | null } | null, events?: Array<{ __typename?: 'GameEvent', time: string, isHomeTeam?: boolean | null, type: GameEventType, score?: string | null, penaltyTime?: string | null, description?: string | null, descriptionCode?: string | null, assists?: Array<{ __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }> | null, externalInfo: { __typename?: 'GameEventExternalInfo', teamName: string, playerName?: string | null, playerExternalId?: string | null }, player?: { __typename?: 'User', id: string, name?: string | null } | null }> | null, league?: { __typename?: 'League', id: string, name: string } | null, homeTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, awayTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, homeTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null, id: string } | null }> | null, awayTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null }> | null } | null };

export type GamesQueryVariables = Exact<{
  filter?: InputMaybe<GamesFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
  sort?: InputMaybe<SortInput>;
}>;


export type GamesQuery = { __typename?: 'Query', games: { __typename?: 'GamesResponse', edges: Array<{ __typename?: 'GamesEdge', currentUserTeam?: CurrentUserTeam | null, node: { __typename?: 'GameStats', id: string, date: any, season?: string | null, externalId: string, externalInfo: { __typename?: 'GameExternalInfo', homeTeamName: string, awayTeamName: string, leagueName: string }, stats?: { __typename?: 'GameOverallStats', periodScores?: string | null, homeTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, awayTeam?: { __typename?: 'GameTeamStats', score?: number | null, shotsOnGoal?: number | null, blockedShots?: number | null, faceoffs?: number | null, powerPlayGoals?: number | null, shortHandedGoals?: number | null, penalties?: number | null, majorPenalties?: number | null, penaltyMinutes?: number | null, goalieSaves?: number | null } | null, scores?: Array<{ __typename?: 'GameScore', homeTeam?: number | null, awayTeam?: number | null, type?: GameScoreType | null }> | null } | null, events?: Array<{ __typename?: 'GameEvent', time: string, isHomeTeam?: boolean | null, type: GameEventType, score?: string | null, penaltyTime?: string | null, description?: string | null, descriptionCode?: string | null, assists?: Array<{ __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }> | null, externalInfo: { __typename?: 'GameEventExternalInfo', teamName: string, playerName?: string | null, playerExternalId?: string | null }, player?: { __typename?: 'User', id: string, name?: string | null } | null }> | null, league?: { __typename?: 'League', id: string, name: string } | null, homeTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, awayTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, slug: string, organization?: { __typename?: 'Organization', id: string, name: string, logo?: string | null } | null } | null, homeTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null, id: string } | null }> | null, awayTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null }> | null }, currentUserPlayer?: { __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, saves?: number | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', id: string } | null } | null }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } };

export type GamesPlayersQueryVariables = Exact<{
  input: GamesPlayersInput;
}>;


export type GamesPlayersQuery = { __typename?: 'Query', gamesPlayers?: { __typename?: 'GamesPlayersResponse', games?: Array<{ __typename?: 'GameStats', id: string, homeTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null, id: string } | null }> | null, awayTeamRoster?: Array<{ __typename?: 'RosterPlayer', goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, plusMinus?: number | null, saves?: number | null, goalsAgainst?: number | null, svp?: number | null, jerseyNumber?: number | null, position?: RosterPosition | null, externalInfo: { __typename?: 'RosterPlayerExternalInfo', playerName: string, playerExternalId?: string | null }, player?: { __typename?: 'User', name?: string | null } | null }> | null }> | null } | null };

export type InspiredUsersQueryVariables = Exact<{
  first?: InputMaybe<Scalars['Int']['input']>;
}>;


export type InspiredUsersQuery = { __typename?: 'Query', inspiredUsers: Array<{ __typename?: 'User', id: string, name?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null }> };

export type IsFollowingQueryVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type IsFollowingQuery = { __typename?: 'Query', isFollowing: boolean };

export type IsFollowingAgencyQueryVariables = Exact<{
  agencyId: Scalars['ObjectId']['input'];
}>;


export type IsFollowingAgencyQuery = { __typename?: 'Query', isFollowingAgency: boolean };

export type IsFollowingTeamQueryVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type IsFollowingTeamQuery = { __typename?: 'Query', isFollowingTeam: boolean };

export type LatestClipsQueryVariables = Exact<{
  filter?: InputMaybe<LatestClipsFilter>;
  before?: InputMaybe<Scalars['String']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
}>;


export type LatestClipsQuery = { __typename?: 'Query', latestClips: { __typename?: 'WallVideosResponse', edges: Array<{ __typename?: 'WallVideoEdge', node: { __typename?: 'WallVideo', id: string, path: string, createdAt: any, user?: { __typename?: 'User', id: string, name?: string | null, dateOfBirth?: any | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null } | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null } | null } }>, pageInfo: { __typename?: 'PageInfo', startCursor?: string | null, endCursor?: string | null, hasNextPage: boolean, hasPreviousPage: boolean } } };

export type LeagueQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type LeagueQuery = { __typename?: 'Query', league?: { __typename?: 'League', id: string, name: string, slug: string, countries?: Array<string> | null, genderCategory?: LeagueGenderCategory | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, level?: LeagueLevel | null, type?: LeagueType | null, eliteProspectSlug?: string | null, sourceUrl?: string | null, checked?: boolean | null, hasGames?: boolean | null, publicAlternativeName?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null, teamAutoConnectRules?: { __typename?: 'TeamAutoConnectRules', country: string, shortName: string, shortNameSlug: string, shortNameInclude: boolean } | null } | null };

export type LeagueStandingsQueryVariables = Exact<{
  teamId?: InputMaybe<Scalars['ObjectId']['input']>;
  leagueId?: InputMaybe<Scalars['ObjectId']['input']>;
  season?: InputMaybe<Scalars['String']['input']>;
}>;


export type LeagueStandingsQuery = { __typename?: 'Query', leagueStandings: Array<{ __typename?: 'LeagueStandingsResponse', league: { __typename?: 'League', name: string }, groups: Array<{ __typename?: 'LeagueStandingsGroup', group: string, standings: Array<{ __typename?: 'LeagueStats', standing: number, season: string, points: number, overtimeWins: number, overtimeLosses: number, losses: number, games: number, team?: { __typename?: 'Team', name: string, shortName?: string | null } | null }> }> }> };

export type LeaguesQueryVariables = Exact<{
  filter?: InputMaybe<LeaguesFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type LeaguesQuery = { __typename?: 'Query', leagues: { __typename?: 'LeaguesResponse', edges: Array<{ __typename?: 'LeagueEdge', node: { __typename?: 'League', id: string, name: string, slug: string, countries?: Array<string> | null, genderCategory?: LeagueGenderCategory | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, level?: LeagueLevel | null, type?: LeagueType | null, eliteProspectSlug?: string | null, sourceUrl?: string | null, checked?: boolean | null, hasGames?: boolean | null, publicAlternativeName?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null, teamAutoConnectRules?: { __typename?: 'TeamAutoConnectRules', country: string, shortName: string, shortNameSlug: string, shortNameInclude: boolean } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } };

export type MoreSelectedUsersQueryVariables = Exact<{ [key: string]: never; }>;


export type MoreSelectedUsersQuery = { __typename?: 'Query', moreSelectedUsers: Array<{ __typename?: 'User', id: string, name?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null }> };

export type MyFollowersQueryVariables = Exact<{
  filter: MyFollowersFilter;
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
}>;


export type MyFollowersQuery = { __typename?: 'Query', myFollowers: { __typename?: 'MyFollowersResponse', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'MyFollowerUserEdge', iFollow: boolean, node: { __typename?: 'User', name?: string | null, firstname?: string | null, lastname?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null } }> } };

export type MyFollowingQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
}>;


export type MyFollowingQuery = { __typename?: 'Query', myFollowing: { __typename?: 'UsersResponse', edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', id: string, name?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null } }>, pageInfo: { __typename?: 'PageInfo', totalCount?: number | null, startCursor?: string | null, hasPreviousPage: boolean, endCursor?: string | null, hasNextPage: boolean } } };

export type MyFollowingAgenciesQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
}>;


export type MyFollowingAgenciesQuery = { __typename?: 'Query', myFollowingAgencies: { __typename?: 'AgenciesFollowingResponse', edges: Array<{ __typename?: 'AgencyFollowingEdge', node: { __typename?: 'Agency', id: string, name: string, slug?: string | null, country?: string | null, logo?: string | null, website?: string | null, address?: string | null, categories?: Array<SportCategory> | null, phone?: string | null, email?: string | null, epUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, linkedinUrl?: string | null, instagramUrl?: string | null, tiktokUrl?: string | null, externalNumberOfClients?: number | null, instagramFollowers?: number | null, checked?: boolean | null, integrated?: boolean | null, clientsCount?: number | null, agentsCount?: number | null, shadowClientsCount?: number | null, founded?: number | null, licences?: Array<string> | null, stats?: { __typename?: 'AgencyStats', views: number, followers: number } | null, drafts?: { __typename?: 'AgencyDrafts', NHL: number, NCAA: number, CHL: number, USHL: number } | null, affiliateAgencies?: Array<{ __typename?: 'Agency', name: string, logo?: string | null, id: string, slug?: string | null }> | null } }>, pageInfo: { __typename?: 'PageInfo', totalCount?: number | null, startCursor?: string | null, hasPreviousPage: boolean, endCursor?: string | null, hasNextPage: boolean } } };

export type MyFollowingTeamsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
}>;


export type MyFollowingTeamsQuery = { __typename?: 'Query', myFollowingTeams: { __typename?: 'TeamsFollowingResponse', edges: Array<{ __typename?: 'TeamFollowingEdge', node: { __typename?: 'Team', id: string, name: string, stats?: { __typename?: 'TeamStats', followers: number, views: number } | null } }>, pageInfo: { __typename?: 'PageInfo', totalCount?: number | null, startCursor?: string | null, hasPreviousPage: boolean, endCursor?: string | null, hasNextPage: boolean } } };

export type NewUsersCountQueryVariables = Exact<{
  country: Scalars['String']['input'];
}>;


export type NewUsersCountQuery = { __typename?: 'Query', newUsersCount: { __typename?: 'NewUsersCountResponse', count: number } };

export type OrganizationQueryVariables = Exact<{
  slug?: InputMaybe<Scalars['String']['input']>;
  organizationId?: InputMaybe<Scalars['ObjectId']['input']>;
}>;


export type OrganizationQuery = { __typename?: 'Query', organization?: { __typename?: 'Organization', country?: string | null, id: string, logo?: string | null, name: string, slug: string, state?: string | null, province?: string | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, arenaName?: string | null, arenaAddress?: string | null, checked?: boolean | null, abbreviation?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null } | null };

export type OrganizationTeamsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  slug?: InputMaybe<Scalars['String']['input']>;
  organizationId?: InputMaybe<Scalars['ObjectId']['input']>;
}>;


export type OrganizationTeamsQuery = { __typename?: 'Query', organizationTeams: { __typename?: 'TeamsResponse', edges: Array<{ __typename?: 'TeamEdge', playersCount: number, node: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string, country?: string | null } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } };

export type OrganizationsQueryVariables = Exact<{
  filter?: InputMaybe<OrganizationsFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type OrganizationsQuery = { __typename?: 'Query', organizations: { __typename?: 'OrganizationsResponse', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'OrganizationEdge', node: { __typename?: 'Organization', country?: string | null, id: string, logo?: string | null, name: string, slug: string, state?: string | null, province?: string | null, website?: string | null, instagramUrl?: string | null, facebookUrl?: string | null, twitterUrl?: string | null, tiktokUrl?: string | null, arenaName?: string | null, arenaAddress?: string | null, checked?: boolean | null, abbreviation?: string | null, alternativeNames?: Array<{ __typename?: 'AlternativeName', name: string, slug: string }> | null } }> } };

export type PlayersStatsListQueryVariables = Exact<{
  filter: PlayersStatsFilter;
  first?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  sort?: InputMaybe<SortInput>;
}>;


export type PlayersStatsListQuery = { __typename?: 'Query', playersStatsList: { __typename?: 'PlayersStatsResponse', edges: Array<{ __typename?: 'UserStatsEdge', node: { __typename?: 'UserStats', id: string, goals?: number | null, assists?: number | null, points?: number | null, pim?: number | null, gamesPlayed?: number | null, svp?: number | null, gaa?: number | null, shutouts?: number | null, toi?: string | null, saves?: number | null, ga?: number | null, season: string, position?: UserStatsPosition | null, user?: { __typename?: 'User', id: string, name?: string | null, slug?: string | null, firstname?: string | null, lastname?: string | null, role?: Role | null, country?: string | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null } | null } | null, team?: { __typename?: 'Team', id: string, name: string } | null, league?: { __typename?: 'League', id: string, name: string } | null, externalInfo?: { __typename?: 'ExternalInfo', externalLeagueCountry?: string | null, externalLeagueName?: string | null, externalTeamCountry?: string | null, externalTeamName?: string | null, externalPlayerName?: string | null, stage?: { __typename?: 'LeagueStage', value?: string | null, label?: string | null } | null } | null } }>, pageInfo: { __typename?: 'PageInfo', totalCount?: number | null }, leagueStages?: Array<{ __typename?: 'LeagueStages', league?: { __typename?: 'League', id: string, name: string } | null, stages: Array<{ __typename?: 'LeagueStage', value?: string | null, label?: string | null }> }> | null } };

export type RandomUsersQueryVariables = Exact<{ [key: string]: never; }>;


export type RandomUsersQuery = { __typename?: 'Query', randomUsers: { __typename?: 'RandomUsersResponse', totalCount: number, edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', id: string, role?: Role | null, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, inspired?: boolean | null, createdAt: any, following: boolean, hasGames?: boolean | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, type?: ContactType | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, externalConnections?: Array<{ __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, stats?: { __typename?: 'AggregatedStats', season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null } }> } };

export type RecommendedUsersQueryVariables = Exact<{
  country?: InputMaybe<Country>;
  yearOfBirth?: InputMaybe<YearArguments>;
  inspired?: InputMaybe<Scalars['Boolean']['input']>;
  activeUserId?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
}>;


export type RecommendedUsersQuery = { __typename?: 'Query', recommendedUsers: Array<{ __typename?: 'User', id: string, role?: Role | null, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, inspired?: boolean | null, createdAt: any, following: boolean, hasGames?: boolean | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, type?: ContactType | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, externalConnections?: Array<{ __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, stats?: { __typename?: 'AggregatedStats', season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null }> };

export type SearchUserStatsQueryVariables = Exact<{
  filter: UserStatsFilter;
  pagination?: InputMaybe<Pagination>;
}>;


export type SearchUserStatsQuery = { __typename?: 'Query', searchUserStats: { __typename?: 'UserStatsResponse', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'UserStatsEdge', node: { __typename?: 'UserStats', id: string, seasonType?: SeasonType | null, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null, orderInSeason?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null } | null, team?: { __typename?: 'Team', name: string, id: string, country?: string | null, shortName?: string | null, shortNameSlug?: string | null, slug: string } | null, league?: { __typename?: 'League', name: string, id: string, country?: string | null } | null, postSeasonStats?: { __typename?: 'PostSeasonStats', gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null } | null, externalInfo?: { __typename?: 'ExternalInfo', externalTeamName?: string | null, externalTeamCountry?: string | null, externalLeagueName?: string | null, externalLeagueCountry?: string | null } | null } }> } };

export type SearchUsersQueryVariables = Exact<{
  filter: UsersFilter;
  pagination?: InputMaybe<Pagination>;
}>;


export type SearchUsersQuery = { __typename?: 'Query', searchUsers: { __typename?: 'UsersResponse', edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', id: string, name?: string | null, lastname?: string | null, role?: Role | null, country?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } };

export type StripeAccountQueryVariables = Exact<{ [key: string]: never; }>;


export type StripeAccountQuery = { __typename?: 'Query', stripeAccount?: { __typename?: 'StripeAccount', id: string, firstname?: string | null, lastname?: string | null, dob?: any | null, currency?: string | null, phone?: string | null, email?: string | null, country?: string | null, payoutsEnabled?: boolean | null, paymentsEnabled?: boolean | null, ssnLast4Provided?: boolean | null, jobTitle?: string | null, address?: { __typename?: 'StripeAddress', city?: string | null, line1?: string | null, line2?: string | null, postalCode?: string | null, state?: string | null } | null } | null };

export type StripeAccountBalanceQueryVariables = Exact<{ [key: string]: never; }>;


export type StripeAccountBalanceQuery = { __typename?: 'Query', stripeAccountBalance: { __typename?: 'StripeAccountBalance', balance: { __typename?: 'StripeBalance', available: { __typename?: 'StripeMoney', amount: number, currency: string }, pending: { __typename?: 'StripeMoney', amount: number, currency: string } }, transactions: Array<{ __typename?: 'StripeBalanceTransaction', id: string, createdAt: any, status: string, payout: { __typename?: 'StripeMoney', amount: number, currency: string } }> } };

export type StripeExternalAccountQueryVariables = Exact<{ [key: string]: never; }>;


export type StripeExternalAccountQuery = { __typename?: 'Query', stripeExternalAccount?: { __typename?: 'StripeExternalAccount', id: string, routingNumber?: string | null, accountNumber?: string | null, status?: string | null, bankAccount?: { __typename?: 'StripeBankAccount', country?: string | null, currency?: string | null, firstname?: string | null, lastname?: string | null } | null } | null };

export type TeamQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  orgSlug?: InputMaybe<Scalars['String']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
  includeShadowPlayers?: InputMaybe<Scalars['Boolean']['input']>;
  season?: InputMaybe<Scalars['String']['input']>;
  teamId?: InputMaybe<Scalars['ObjectId']['input']>;
}>;


export type TeamQuery = { __typename?: 'Query', team: { __typename?: 'TeamDetailsResponse', errors?: Array<{ __typename?: 'TeamNotFoundError', message: string, path: string }> | null, team?: { __typename?: 'Team', id: string, name: string, hasGames?: boolean | null, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, users?: { __typename?: 'UsersResponse', edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', id: string, name?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } | null } };

export type TeamWithoutUserFieldsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  orgSlug?: InputMaybe<Scalars['String']['input']>;
  slug?: InputMaybe<Scalars['String']['input']>;
  teamId?: InputMaybe<Scalars['ObjectId']['input']>;
}>;


export type TeamWithoutUserFieldsQuery = { __typename?: 'Query', team: { __typename?: 'TeamDetailsResponse', errors?: Array<{ __typename?: 'TeamNotFoundError', message: string, path: string }> | null, team?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null } };

export type TeamsQueryVariables = Exact<{
  pagination?: InputMaybe<Pagination>;
  filter?: InputMaybe<TeamsFilter>;
}>;


export type TeamsQuery = { __typename?: 'Query', teams: { __typename?: 'TeamsResponse', edges: Array<{ __typename?: 'TeamEdge', playersCount: number, node: { __typename?: 'Team', id: string, name: string, country?: string | null, playersCount: number, checked?: boolean | null, organization?: { __typename?: 'Organization', name: string } | null } }> } };

export type TopViewedUsersQueryVariables = Exact<{
  period: Period;
  filter?: InputMaybe<TopViewedUsersFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type TopViewedUsersQuery = { __typename?: 'Query', topViewedUsers: { __typename?: 'UserViewsResponse', myPosition: number, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, edges: Array<{ __typename?: 'UserViewEdge', views: number, node: { __typename?: 'User', id: string, name?: string | null, country?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null } }> } };

export type UserByIdQueryQueryVariables = Exact<{
  id: Scalars['ObjectId']['input'];
}>;


export type UserByIdQueryQuery = { __typename?: 'Query', userById?: { __typename?: 'User', id: string, role?: Role | null, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, inspired?: boolean | null, createdAt: any, following: boolean, hasGames?: boolean | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, type?: ContactType | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, externalConnections?: Array<{ __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, stats?: { __typename?: 'AggregatedStats', season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null } | null };

export type UserCountQueryVariables = Exact<{
  days?: InputMaybe<Scalars['Int']['input']>;
}>;


export type UserCountQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', userCount?: { __typename?: 'UserCount', playersCount: number, sportsProfsCount: number, parentsCount: number, sportsFansCount: number, shadowPlayersCount: number } | null } };

export type UserLikesPlayersGamesPerformanceQueryVariables = Exact<{
  input: UserLikesPlayersGamesPerformanceInput;
}>;


export type UserLikesPlayersGamesPerformanceQuery = { __typename?: 'Query', userLikesPlayersGamesPerformance: { __typename?: 'UserLikesPlayersGamesPerformanceResponse', playersByGame?: Array<{ __typename?: 'PlayersByGame', id: string, players?: Array<{ __typename?: 'User', id: string }> | null }> | null, errors?: Array<{ __typename?: 'EntityNotFoundError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }> | null } };

export type UserLikesPlayersPerformanceQueryVariables = Exact<{
  input: UserLikesPlayersPerformanceInput;
}>;


export type UserLikesPlayersPerformanceQuery = { __typename?: 'Query', userLikesPlayersPerformance: { __typename?: 'UserLikesPlayersPerformanceResponse', playersByGame?: { __typename?: 'PlayersByGame', id: string, players?: Array<{ __typename?: 'User', id: string }> | null } | null, errors?: Array<{ __typename?: 'EntityNotFoundError', message: string, path: string } | { __typename?: 'UserNotFoundError', message: string, path: string }> | null } };

export type UserQueryQueryVariables = Exact<{
  slug: Scalars['String']['input'];
}>;


export type UserQueryQuery = { __typename?: 'Query', user?: { __typename?: 'User', id: string, role?: Role | null, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, inspired?: boolean | null, createdAt: any, following: boolean, hasGames?: boolean | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, familyMembers?: Array<{ __typename?: 'User', id: string, name?: string | null, slug?: string | null, role?: Role | null, subrole?: SubRole | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null, agency?: { __typename?: 'Agency', id: string, name: string } | null } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, logo?: string | null, country?: string | null, shortNameSlug?: string | null, organization?: { __typename?: 'Organization', id: string, name: string, slug: string, logo?: string | null, country?: string | null } | null } | null }> | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, type?: ContactType | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, externalConnections?: Array<{ __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, stats?: { __typename?: 'AggregatedStats', season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null } | null };

export type UserStatisticsByYearAndCountryQueryVariables = Exact<{ [key: string]: never; }>;


export type UserStatisticsByYearAndCountryQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', userStatisticsByYearAndCountry: { __typename?: 'UserStatisticsByYearAndCountry', totalUsers: number, usersByCountryAndYear: Array<{ __typename?: 'CountryYearStats', country: string, year: string, count: number, target: number, percentageOfTarget: number }> } } };

export type UserStatsQueryVariables = Exact<{
  filter?: InputMaybe<UserStatsFilter>;
  pagination?: InputMaybe<Pagination>;
}>;


export type UserStatsQuery = { __typename?: 'Query', userStats: { __typename?: 'UserStatsResponse', edges: Array<{ __typename?: 'UserStatsEdge', node: { __typename?: 'UserStats', id: string, seasonType?: SeasonType | null, season: string, gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null, orderInSeason?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, role?: Role | null } | null, team?: { __typename?: 'Team', name: string, id: string, country?: string | null, shortName?: string | null, shortNameSlug?: string | null, slug: string } | null, league?: { __typename?: 'League', name: string, id: string, country?: string | null } | null, postSeasonStats?: { __typename?: 'PostSeasonStats', gamesPlayed?: number | null, assists?: number | null, goals?: number | null, pim?: number | null, plusMinus?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, gaa?: number | null, svp?: number | null, shutouts?: number | null } | null, externalInfo?: { __typename?: 'ExternalInfo', externalTeamName?: string | null, externalTeamCountry?: string | null, externalLeagueName?: string | null, externalLeagueCountry?: string | null } | null } }>, pageInfo: { __typename?: 'PageInfo', endCursor?: string | null, hasNextPage: boolean, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } };

export type UserViewStatsQueryVariables = Exact<{ [key: string]: never; }>;


export type UserViewStatsQuery = { __typename?: 'Query', userViewsStats: { __typename?: 'UserViewStatsResponse', weeklyStats: Array<{ __typename?: 'WeeklyViewStats', from: any, to: any, views: number }> } };

export type UsersByIdsQueryQueryVariables = Exact<{
  userIds: Array<Scalars['ObjectId']['input']> | Scalars['ObjectId']['input'];
}>;


export type UsersByIdsQueryQuery = { __typename?: 'Query', usersByIds?: Array<{ __typename?: 'User', id: string, role?: Role | null, name?: string | null, firstname?: string | null, lastname?: string | null, slug?: string | null, dateOfBirth?: any | null, country?: string | null, inspired?: boolean | null, createdAt: any, following: boolean, hasGames?: boolean | null, boostMessage?: string | null, verificationNeeded?: boolean | null, verificationPending?: boolean | null, highlights?: Array<{ __typename?: 'WallVideo', id: string, path: string }> | null, contact?: { __typename?: 'Contact', firstname?: string | null, lastname?: string | null, type?: ContactType | null } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null, schoolType?: BioSchoolType | null, gender?: BioGender | null, birthplace?: string | null, nameOfSchool?: string | null, graduationYear?: number | null, cumulativeGPA?: number | null, coreGPA?: number | null, unweightedGPA?: number | null, weightedGPA?: number | null, satScore?: number | null, actScore?: number | null, toeflScore?: number | null, ncaaEligibility?: boolean | null, naiaEligibility?: boolean | null, agency?: { __typename?: 'Agency', id: string } | null, height?: { __typename?: 'Height', feet?: number | null, inches?: number | null, centimeters?: number | null } | null, weight?: { __typename?: 'Weight', pounds?: number | null, kilograms?: number | null } | null } | null, externalLinks?: Array<{ __typename?: 'ExternalLink', name: string, url: string, type?: ExternalLinkType | null }> | null, externalConnections?: Array<{ __typename?: 'ExternalConnection', externalId: string, name?: string | null, crawlerDomainConfig: { __typename?: 'CrawlerDomainConfig', id: string } }> | null, boost?: { __typename?: 'BoostGoal', currency: string, goalAmount: number } | null, currentTeam?: { __typename?: 'Team', id: string, name: string, organization?: { __typename?: 'Organization', id: string, name: string } | null } | null, registeredDevices?: Array<{ __typename?: 'Device', arn?: string | null, platform: Platform, token: string }> | null, accountUpdateErrors?: Array<{ __typename?: 'StripeAccountRequirementError', code: string, reason: string, requirement: string }> | null, stats?: { __typename?: 'AggregatedStats', season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null }> | null };

export type UsersQueryVariables = Exact<{
  filter: AdminUsersFilter;
  pagination?: InputMaybe<Pagination>;
}>;


export type UsersQuery = { __typename?: 'Query', users: { __typename?: 'UsersResponse', edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', id: string, firstname?: string | null, lastname?: string | null, email: string, country?: string | null, recommended?: boolean | null, inspired?: boolean | null, moreSelected?: boolean | null, dateOfBirth?: any | null, avatar?: string | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null } }>, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null } } };

export type VideosByIdsQueryVariables = Exact<{
  input: VideosByIdsInput;
}>;


export type VideosByIdsQuery = { __typename?: 'Query', videosByIds: { __typename?: 'VideosByIdsResponse', videos: Array<{ __typename?: 'WallVideo', id: string, path: string, thumbnail: string, width?: number | null, height?: number | null, hls?: boolean | null, hlsPath?: string | null, size: number, title: string, duration: number, type: WallVideoType, createdAt: any, frameRate?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, following: boolean, role?: Role | null, subrole?: SubRole | null, slug?: string | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null } | null, stats?: { __typename?: 'AggregatedStats', career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null } | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null } | null }> } };

export type VideosCountQueryVariables = Exact<{
  days?: InputMaybe<Scalars['Int']['input']>;
}>;


export type VideosCountQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', wallVideo: { __typename?: 'AdminWallVideosQueries', videosCount: { __typename?: 'VideosCountByType', defensiveVideosCount: number, gameSaveVideosCount: number, highlightVideosCount: number, interviewVideosCount: number, offensiveVideosCount: number, otherVideosCount: number, puckHandlingVideosCount: number, trainingVideosCount: number } } } };

export type VideosCountByTagsQueryVariables = Exact<{ [key: string]: never; }>;


export type VideosCountByTagsQuery = { __typename?: 'Query', admin: { __typename?: 'AdminQuery', wallVideo: { __typename?: 'AdminWallVideosQueries', videosCountByTags: { __typename?: 'VideosCountByTags', totalTaggedVideosCount: number, videoTagsCount: Array<{ __typename?: 'TagCountWithPercentage', count: number, percentage: number, tag: string }> } } } };

export type WallVideosQueryVariables = Exact<{
  filter?: InputMaybe<WallVideosFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
}>;


export type WallVideosQuery = { __typename?: 'Query', wallVideos: { __typename?: 'WallVideosResponse', edges: Array<{ __typename?: 'WallVideoEdge', node: { __typename?: 'WallVideo', id: string, path: string, width?: number | null, height?: number | null } }>, categoryCounts?: Array<{ __typename?: 'CategoryCount', category: string, count: number }> | null, pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, hasPreviousPage: boolean, endCursor?: string | null, startCursor?: string | null } } };

export type WallVideosReelQueryVariables = Exact<{
  filter?: InputMaybe<WallVideosFilter>;
  first?: InputMaybe<Scalars['Int']['input']>;
  last?: InputMaybe<Scalars['Int']['input']>;
  after?: InputMaybe<Scalars['String']['input']>;
  before?: InputMaybe<Scalars['String']['input']>;
}>;


export type WallVideosReelQuery = { __typename?: 'Query', wallVideosReel: { __typename?: 'WallVideosResponse', edges: Array<{ __typename?: 'WallVideoEdge', node: { __typename?: 'WallVideo', id: string, path: string, thumbnail: string, width?: number | null, height?: number | null, hls?: boolean | null, hlsPath?: string | null, size: number, title: string, duration: number, type: WallVideoType, createdAt: any, frameRate?: number | null, user?: { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, following: boolean, role?: Role | null, subrole?: SubRole | null, slug?: string | null, country?: string | null, dateOfBirth?: any | null, avatars?: { __typename?: 'Avatars', small: string } | null, currentTeam?: { __typename?: 'Team', id: string, name: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, playerType?: BioPlayerType | null } | null, stats?: { __typename?: 'AggregatedStats', career: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null }, season: { __typename?: 'AggregatedStatsItem', id: string, gamesPlayed: number, goals?: number | null, assists?: number | null, points?: number | null, pointsPerGame?: number | null, wins?: number | null, losses?: number | null, ties?: number | null, shutouts?: number | null, gaa?: number | null, svp?: number | null } } | null } | null, segmentation?: { __typename?: 'WallVideoSegmentation', path?: string | null, finished?: boolean | null } | null } }>, pageInfo: { __typename?: 'PageInfo', endCursor?: string | null, hasNextPage: boolean, hasPreviousPage: boolean, startCursor?: string | null, totalCount?: number | null }, categoryCounts?: Array<{ __typename?: 'CategoryCount', category: string, count: number }> | null } };

export type SuggestInvitesQueryVariables = Exact<{
  teamId: Scalars['ObjectId']['input'];
}>;


export type SuggestInvitesQuery = { __typename?: 'Query', suggestInvites: { __typename?: 'SuggestInvitesResponse', edges: Array<{ __typename?: 'UserEdge', node: { __typename?: 'User', id: string, name?: string | null, firstname?: string | null, lastname?: string | null, country?: string | null, role?: Role | null, avatar?: string | null, dateOfBirth?: any | null, possibleYearsOfBirth?: Array<number> | null, avatars?: { __typename?: 'Avatars', tiny: string, small: string, medium: string, large: string } | null, bio?: { __typename?: 'UserBio', position?: BioPosition | null, handedness?: BioHandedness | null, playerType?: BioPlayerType | null } | null } }> } };

export const AgencyFragmentDoc = gql`
    fragment Agency on Agency {
  id
  name
  slug
  country
  logo
  website
  address
  categories
  phone
  email
  epUrl
  facebookUrl
  twitterUrl
  linkedinUrl
  instagramUrl
  tiktokUrl
  externalNumberOfClients
  instagramFollowers
  checked
  integrated
  clientsCount
  agentsCount
  shadowClientsCount
  stats {
    views
    followers
  }
  founded
  licences
  drafts {
    NHL
    NCAA
    CHL
    USHL
  }
  affiliateAgencies {
    name
    logo
    id
    slug
  }
}
    `;
export const BatchNotificationFragmentDoc = gql`
    fragment BatchNotification on BatchNotification {
  id
  state
  title
  message
  type
  forcePush
  destinationPath
  numberOfSent
  totalUsers
  scheduledAt
  filter {
    role
    nationalities
    numberOfVideos
    firstNameInitials
    yearOfBirth
  }
}
    `;
export const BoostFragmentDoc = gql`
    fragment Boost on Boost {
  id
  message
  name
  createdAt
  currency
  amount
  state
}
    `;
export const CrawlerDomainConfigFragmentDoc = gql`
    fragment CrawlerDomainConfig on CrawlerDomainConfig {
  id
  domain
  relevantUrlPatterns
  fetchType
  enabled
  parallelism
  urlConfigs {
    urlPatternRegExp
    entity
    initialUrls
    externalIdPatternRegExp
    extractionScript
    fetchType
    addToQueue
    paramExtractors {
      selector
      queryParam
    }
  }
}
    `;
export const CrawlerLogsFragmentDoc = gql`
    fragment CrawlerLogs on CrawlerLogs {
  id
  op
  modelName
  collectionName
  collectionId
  patch {
    op
    path
    value
  }
  user {
    id
    email
    admin
    role
    iat
    exp
  }
  version
  createdAt
  updatedAt
}
    `;
export const DiscoveredUrlListFragmentDoc = gql`
    fragment DiscoveredUrlList on DiscoveredUrl {
  id
  url
  entity
  crawlerDomainConfig {
    id
    domain
    urlConfigs {
      urlPatternRegExp
      entity
      initialUrls
      externalIdPatternRegExp
      paramExtractors {
        selector
        queryParam
      }
      allowedQueryParams
      extractionScript
    }
  }
  externalId
  scraperInfo {
    lastScrapedAt
    scheduledForScrapeAt
    scrapeAttempts
    unsuccessfulScrapes
    scrapeSuccessful
  }
}
    `;
export const GameResponseFragmentDoc = gql`
    fragment GameResponse on GameStats {
  id
  date
  season
  externalId
  externalInfo {
    homeTeamName
    awayTeamName
    leagueName
  }
  stats {
    homeTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    awayTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    periodScores
    scores {
      homeTeam
      awayTeam
      type
    }
  }
  events {
    time
    isHomeTeam
    type
    score
    penaltyTime
    description
    descriptionCode
    assists {
      playerName
      playerExternalId
    }
    externalInfo {
      teamName
      playerName
      playerExternalId
    }
    player {
      id
      name
    }
  }
  league {
    id
    name
  }
  homeTeam {
    id
    name
    logo
    slug
    organization {
      id
      name
      logo
    }
  }
  awayTeam {
    id
    name
    logo
    slug
    organization {
      id
      name
      logo
    }
  }
  homeTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
      id
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
  awayTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
}
    `;
export const GameListItemFragmentDoc = gql`
    fragment GameListItem on GameStats {
  id
  date
  externalId
  ignore
  externalInfo {
    homeTeamName
    awayTeamName
    leagueName
  }
  stats {
    homeTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    awayTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    periodScores
  }
  league {
    id
    name
  }
  homeTeam {
    id
    name
  }
  awayTeam {
    id
    name
  }
}
    `;
export const GameDetailWithPlayersFragmentDoc = gql`
    fragment GameDetailWithPlayers on GameStats {
  id
  homeTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
      id
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
  awayTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
}
    `;
export const LeagueFragmentDoc = gql`
    fragment League on League {
  id
  name
  slug
  countries
  genderCategory
  website
  instagramUrl
  facebookUrl
  twitterUrl
  tiktokUrl
  level
  type
  eliteProspectSlug
  sourceUrl
  checked
  hasGames
  alternativeNames {
    name
    slug
  }
  publicAlternativeName
  teamAutoConnectRules {
    country
    shortName
    shortNameSlug
    shortNameInclude
  }
}
    `;
export const LeagueStatsListItemFragmentDoc = gql`
    fragment LeagueStatsListItem on LeagueStats {
  id
  league {
    id
    name
  }
  team {
    id
    name
  }
  standing
  games
  wins
  losses
  overtimeWins
  overtimeLosses
  goalsFor
  goalsAgainst
  points
  group
}
    `;
export const NotificationsFragmentDoc = gql`
    fragment Notifications on Notification {
  id
  destinationPath
  body
  sent
  title
  type
  user {
    name
  }
}
    `;
export const OrganizationFragmentDoc = gql`
    fragment Organization on Organization {
  country
  id
  logo
  name
  slug
  state
  province
  alternativeNames {
    name
    slug
  }
  website
  instagramUrl
  facebookUrl
  twitterUrl
  tiktokUrl
  arenaName
  arenaAddress
  checked
  abbreviation
}
    `;
export const TeamFragmentDoc = gql`
    fragment Team on Team {
  id
  name
  stats {
    followers
    views
  }
}
    `;
export const UserFragmentDoc = gql`
    fragment User on User {
  id
  role
  subrole
  email
  name
  firstname
  lastname
  slug
  dateOfBirth
  country
  highlights {
    id
    path
  }
  contact {
    firstname
    lastname
    email
    phoneNumber {
      countryPrefix
      number
    }
    type
  }
  bio {
    position
    handedness
    playerType
    schoolType
    agency {
      id
    }
    gender
    height {
      feet
      inches
      centimeters
    }
    weight {
      pounds
      kilograms
    }
    birthplace
    nameOfSchool
    graduationYear
    intro
    cumulativeGPA
    coreGPA
    unweightedGPA
    weightedGPA
    satScore
    actScore
    toeflScore
    ncaaEligibility
    naiaEligibility
  }
  phoneNumber {
    countryPrefix
    number
  }
  externalLinks {
    name
    url
    type
  }
  boostMessage
  boost {
    currency
    goalAmount
  }
  role
  currentTeam {
    id
    name
    organization {
      id
      name
    }
  }
  registeredDevices {
    arn
    platform
    token
  }
  verificationNeeded
  verificationPending
  accountUpdateErrors {
    code
    reason
    requirement
  }
  clientStaticConfig {
    inviteContest {
      enabled
    }
  }
  subroleSpecifications {
    type
    team {
      id
      name
      organization {
        id
        name
      }
    }
    current
  }
}
    `;
export const UserWithAgencyUnverifiedFragmentDoc = gql`
    fragment UserWithAgencyUnverified on User {
  id
  bio {
    agency {
      id
      name
    }
    agencyUnverified {
      id
      name
    }
  }
}
    `;
export const ExampleUserFragmentDoc = gql`
    fragment ExampleUser on User {
  id
  name
  currentTeam {
    id
    name
  }
  avatars {
    small
    large
  }
  slug
  highlights {
    id
    path
  }
}
    `;
export const ExternalConnectionFragmentDoc = gql`
    fragment ExternalConnection on ExternalConnection {
  crawlerDomainConfig {
    id
  }
  externalId
  name
}
    `;
export const AggregatedStatsItemFragmentDoc = gql`
    fragment AggregatedStatsItem on AggregatedStatsItem {
  id
  gamesPlayed
  goals
  assists
  points
  pointsPerGame
  wins
  losses
  ties
  shutouts
  gaa
  svp
}
    `;
export const PublicUserFragmentDoc = gql`
    fragment PublicUser on User {
  id
  role
  name
  firstname
  lastname
  slug
  dateOfBirth
  country
  inspired
  createdAt
  following
  hasGames
  highlights {
    id
    path
  }
  contact {
    firstname
    lastname
    type
  }
  bio {
    position
    handedness
    playerType
    schoolType
    agency {
      id
    }
    gender
    height {
      feet
      inches
      centimeters
    }
    weight {
      pounds
      kilograms
    }
    birthplace
    nameOfSchool
    graduationYear
    cumulativeGPA
    coreGPA
    unweightedGPA
    weightedGPA
    satScore
    actScore
    toeflScore
    ncaaEligibility
    naiaEligibility
  }
  externalLinks {
    name
    url
    type
  }
  externalConnections {
    ...ExternalConnection
  }
  boostMessage
  boost {
    currency
    goalAmount
  }
  role
  currentTeam {
    id
    name
    organization {
      id
      name
    }
  }
  registeredDevices {
    arn
    platform
    token
  }
  verificationNeeded
  verificationPending
  accountUpdateErrors {
    code
    reason
    requirement
  }
  stats {
    season {
      ...AggregatedStatsItem
    }
    career {
      ...AggregatedStatsItem
    }
  }
}
    ${ExternalConnectionFragmentDoc}
${AggregatedStatsItemFragmentDoc}`;
export const CurrentUserFragmentDoc = gql`
    fragment CurrentUser on User {
  ...PublicUser
  familyMembers {
    id
    name
    avatars {
      small
    }
    slug
    role
    subrole
    country
    dateOfBirth
    bio {
      position
      playerType
      agency {
        id
        name
      }
    }
    currentTeam {
      id
      name
      logo
      country
      shortNameSlug
      organization {
        id
        name
        slug
        logo
        country
      }
    }
  }
}
    ${PublicUserFragmentDoc}`;
export const UserStatsFragmentDoc = gql`
    fragment UserStats on UserStats {
  id
  user {
    id
    name
    role
  }
  team {
    name
    id
    country
    shortName
    shortNameSlug
    slug
  }
  league {
    name
    id
    country
  }
  seasonType
  season
  gamesPlayed
  assists
  goals
  pim
  plusMinus
  wins
  losses
  ties
  gaa
  svp
  shutouts
  orderInSeason
  postSeasonStats {
    gamesPlayed
    assists
    goals
    pim
    plusMinus
    wins
    losses
    ties
    gaa
    svp
    shutouts
  }
  externalInfo {
    externalTeamName
    externalTeamCountry
    externalLeagueName
    externalLeagueCountry
  }
}
    `;
export const AggregatedStatsFragmentDoc = gql`
    fragment AggregatedStats on AggregatedStats {
  career {
    ...AggregatedStatsItem
  }
  season {
    ...AggregatedStatsItem
  }
}
    ${AggregatedStatsItemFragmentDoc}`;
export const VideosByIdsUserFragmentDoc = gql`
    fragment VideosByIdsUser on User {
  id
  name
  firstname
  lastname
  following
  role
  subrole
  slug
  country
  dateOfBirth
  avatars {
    small
  }
  currentTeam {
    id
    name
  }
  bio {
    position
    playerType
  }
  stats {
    ...AggregatedStats
  }
}
    ${AggregatedStatsFragmentDoc}`;
export const VideosByIdsVideoFragmentDoc = gql`
    fragment VideosByIdsVideo on WallVideo {
  id
  path
  thumbnail
  width
  height
  hls
  hlsPath
  size
  title
  duration
  type
  createdAt
  frameRate
  user {
    ...VideosByIdsUser
  }
  segmentation {
    path
    finished
  }
}
    ${VideosByIdsUserFragmentDoc}`;
export const WallVideoFragmentDoc = gql`
    fragment WallVideo on WallVideo {
  id
  type
  path
  size
  thumbnail
  frameRate
  width
  height
  duration
  hls
  hlsPath
  segmentation {
    path
    finished
    createdAt
  }
  createdAt
  tags
  user {
    id
    name
    slug
  }
  streamFeedActivityId
}
    `;
export const WallVideoReelUserFragmentDoc = gql`
    fragment WallVideoReelUser on User {
  id
  name
  firstname
  lastname
  following
  role
  subrole
  slug
  country
  dateOfBirth
  avatars {
    small
  }
  currentTeam {
    id
    name
  }
  bio {
    position
    playerType
  }
  stats {
    ...AggregatedStats
  }
}
    ${AggregatedStatsFragmentDoc}`;
export const WallVideoReelFragmentDoc = gql`
    fragment WallVideoReel on WallVideo {
  id
  path
  thumbnail
  width
  height
  hls
  hlsPath
  size
  title
  duration
  type
  createdAt
  frameRate
  user {
    ...WallVideoReelUser
  }
  segmentation {
    path
    finished
  }
}
    ${WallVideoReelUserFragmentDoc}`;
export const SyncAdminUserStatsDocument = gql`
    mutation syncAdminUserStats($id: String!) {
  admin {
    userStats {
      sync(id: $id) {
        userStats {
          id
          season
          gamesPlayed
          assists
          goals
          team {
            id
            name
          }
          league {
            id
            name
          }
        }
        errors {
          ... on UserStatsNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const AcceptAgencyInvitationDocument = gql`
    mutation acceptAgencyInvitation($token: String!, $agencyId: ObjectId!) {
  acceptAgencyInvitation(token: $token, agencyId: $agencyId) {
    success
    errors {
      ... on AcceptAgencyInvitationInvalidTokenError {
        message
        path
      }
      ... on AcceptAgencyInvitationExpiredError {
        message
        path
      }
      ... on AcceptAgencyInvitationAlreadyAcceptedError {
        message
        path
      }
    }
  }
}
    `;
export const AddAgencyUserDocument = gql`
    mutation addAgencyUser($agencyId: ObjectId!, $userId: ObjectId!) {
  admin {
    agency {
      addUser(agencyId: $agencyId, userId: $userId) {
        agency {
          id
          agentsCount
          clientsCount
        }
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    `;
export const AddFamilyMemberDocument = gql`
    mutation addFamilyMember($userId: String!) {
  addFamilyMember(userId: $userId) {
    user {
      id
      familyMembers {
        id
      }
      pendingFamilyMembers {
        id
      }
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on FamilyMemberAlreadyExistsError {
        message
        path
      }
      ... on SelfFamilyMemberError {
        message
        path
      }
    }
  }
}
    `;
export const CancelBatchNotificationDocument = gql`
    mutation cancelBatchNotification($batchId: ObjectId!) {
  admin {
    batchNotification {
      cancel(id: $batchId) {
        errors {
          ... on BatchNotificationError {
            message
            path
          }
          ... on BatchNotificationNotFoundError {
            message
            path
          }
        }
        notification {
          id
          state
        }
      }
    }
  }
}
    `;
export const CreateBatchNotificationDocument = gql`
    mutation createBatchNotification($input: BatchNotificationInput!) {
  admin {
    batchNotification {
      create(input: $input) {
        notification {
          ...BatchNotification
        }
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    ${BatchNotificationFragmentDoc}`;
export const CreateCrawlerDomainConfigDocument = gql`
    mutation createCrawlerDomainConfig($input: CrawlerDomainConfigCreateInput!) {
  admin {
    crawlerDomainConfig {
      create(input: $input) {
        config {
          ...CrawlerDomainConfig
        }
      }
    }
  }
}
    ${CrawlerDomainConfigFragmentDoc}`;
export const UpdateCrawlerDomainConfigDocument = gql`
    mutation updateCrawlerDomainConfig($id: ObjectId!, $input: CrawlerDomainConfigUpdateInput!) {
  admin {
    crawlerDomainConfig {
      update(id: $id, input: $input) {
        config {
          ...CrawlerDomainConfig
        }
      }
    }
  }
}
    ${CrawlerDomainConfigFragmentDoc}`;
export const DeleteAllUserStatsDocument = gql`
    mutation deleteAllUserStats($userId: ObjectId!) {
  admin {
    userStats {
      deleteAllUserStats(userId: $userId) {
        success
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    `;
export const ReScrapeDocument = gql`
    mutation reScrape($reScrapeId: ObjectId!) {
  admin {
    discoveredUrl {
      reScrape(id: $reScrapeId) {
        success
        errors {
          ... on EntityNotFoundError {
            path
            message
          }
        }
      }
    }
  }
}
    `;
export const RemoveLeagueLogoDocument = gql`
    mutation removeLeagueLogo($removeLeagueLogoId: ObjectId!) {
  admin {
    league {
      removeLeagueLogo(id: $removeLeagueLogoId) {
        success
        errors {
          ... on LeagueNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const UploadLeagueLogoDocument = gql`
    mutation uploadLeagueLogo($uploadLeagueLogoId: ObjectId!, $input: UploadLeagueLogoInput) {
  admin {
    league {
      uploadLeagueLogo(id: $uploadLeagueLogoId, input: $input) {
        preSignedUrl
        league {
          id
          name
          slug
          logo
          countries
        }
        errors {
          ... on FailedToCreatePresignedUrl {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const MarkUserAsDeletedDocument = gql`
    mutation markUserAsDeleted($userId: ObjectId!) {
  admin {
    user {
      markUserAsDeleted(userId: $userId) {
        success
        errors {
          ... on UserAlreadyMarkedForDeletionError {
            message
            path
          }
          ... on UserNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const SendPushNotificationDocument = gql`
    mutation sendPushNotification($type: PushNotificationType!, $message: String!, $title: String!, $userId: ObjectId!, $destinationPath: String, $forcePush: Boolean) {
  admin {
    notifications {
      sendPushNotification(
        type: $type
        message: $message
        title: $title
        userId: $userId
        destinationPath: $destinationPath
        forcePush: $forcePush
      ) {
        success
        errors {
          ... on UserDoesNotHaveRegisteredDeviceError {
            message
            path
          }
          ... on UserNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const CreateOrganizationDocument = gql`
    mutation createOrganization($input: CreateOrganizationInput!) {
  createOrganization(input: $input) {
    organization {
      ...Organization
    }
    errors {
      ... on CreateOrganizationAlreadyExistsError {
        message
        path
      }
    }
  }
}
    ${OrganizationFragmentDoc}`;
export const RemoveOrganizationLogoDocument = gql`
    mutation removeOrganizationLogo($removeOrganizationLogoId: ObjectId!) {
  admin {
    organization {
      removeOrganizationLogo(id: $removeOrganizationLogoId) {
        success
        errors {
          ... on OrganizationNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const UpdateOrganizationDocument = gql`
    mutation updateOrganization($input: UpdateOrganizationInput!, $updateOrganizationId: ObjectId!) {
  updateOrganization(input: $input, id: $updateOrganizationId) {
    organization {
      ...Organization
    }
    errors {
      ... on UpdateOrganizationNotFoundError {
        message
        path
      }
    }
  }
}
    ${OrganizationFragmentDoc}`;
export const SyncAgencyClientDocument = gql`
    mutation syncAgencyClient($agencyId: ObjectId!, $epLink: String!) {
  admin {
    user {
      syncAgencyClient(agencyId: $agencyId, epLink: $epLink) {
        agency {
          id
          clientsCount
          shadowClientsCount
        }
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    `;
export const SyncSingleGameStatsDocument = gql`
    mutation syncSingleGameStats($id: ObjectId!) {
  admin {
    game {
      syncSingle(id: $id) {
        game {
          id
          date
          externalId
          externalInfo {
            homeTeamName
            awayTeamName
            leagueName
          }
        }
        errors {
          ... on EntityNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const CreateTeamDocument = gql`
    mutation createTeam($input: CreateTeamInput!) {
  createTeam(input: $input) {
    team {
      id
      name
      logo
      eliteProspectLink
      checked
      abbreviation
      organization {
        id
        name
        country
        slug
        logo
      }
      shortName
      shortNameSlug
      externalId
      country
      order
    }
    errors {
      ... on CreateTeamAlreadyExistsError {
        message
        path
      }
    }
  }
}
    `;
export const RemoveTeamLogoDocument = gql`
    mutation removeTeamLogo($removeTeamLogoId: ObjectId!) {
  admin {
    team {
      removeTeamLogo(id: $removeTeamLogoId) {
        success
        errors {
          ... on TeamNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    `;
export const UpdateTeamDocument = gql`
    mutation updateTeam($input: UpdateTeamInput!, $updateTeamId: ObjectId!) {
  updateTeam(input: $input, id: $updateTeamId) {
    errors {
      ... on UpdateTeamNotFoundError {
        message
        path
      }
    }
    team {
      id
      name
      slug
      logo
      country
      eliteProspectLink
      checked
      abbreviation
      organization {
        id
        name
        slug
        country
        logo
      }
      order
      externalId
      shortName
      shortNameSlug
    }
  }
}
    `;
export const UpdateBoostMessageDocument = gql`
    mutation updateBoostMessage($message: String!, $boostId: ObjectId!) {
  admin {
    boost {
      updateBoostMessage(message: $message, boostId: $boostId) {
        boost {
          ...Boost
        }
        errors {
          ... on BoostNotFoundError {
            message
            path
          }
          ... on BoostMessageAlreadySetError {
            message
            path
          }
        }
      }
    }
  }
}
    ${BoostFragmentDoc}`;
export const AddExternalConnectionDocument = gql`
    mutation addExternalConnection($crawlerDomainConfigId: ObjectId!, $externalId: String!, $userId: ObjectId!) {
  admin {
    user {
      addExternalConnection(
        crawlerDomainConfigId: $crawlerDomainConfigId
        externalId: $externalId
        userId: $userId
      ) {
        success
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    `;
export const RemoveExternalConnectionDocument = gql`
    mutation removeExternalConnection($crawlerDomainConfigId: ObjectId!, $externalId: String!, $userId: ObjectId!) {
  admin {
    user {
      removeExternalConnection(
        crawlerDomainConfigId: $crawlerDomainConfigId
        externalId: $externalId
        userId: $userId
      ) {
        success
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    `;
export const UploadAvatarAdminDocument = gql`
    mutation uploadAvatarAdmin($userId: ObjectId!, $input: UploadAvatarInput) {
  admin {
    user {
      uploadAvatar(userId: $userId, input: $input) {
        errors {
          ... on FailedToCreatePresignedUrl {
            message
            path
          }
        }
        preSignedUrl
        user {
          id
          name
          role
          slug
          currentTeam {
            id
            name
          }
          avatar
          avatars {
            large
            medium
            small
            tiny
          }
        }
      }
    }
  }
}
    `;
export const DeleteWallVideoAdminDocument = gql`
    mutation deleteWallVideoAdmin($id: ObjectId!) {
  admin {
    wallVideo {
      delete(id: $id) {
        errors {
          ... on FailedToDelete {
            message
            path
          }
        }
        success
      }
    }
  }
}
    `;
export const AppleSignInDocument = gql`
    mutation appleSignIn($input: SocialSignInInput!) {
  appleSignIn(input: $input) {
    user {
      id
      name
      email
      currentTeam {
        name
      }
    }
    tokens {
      accessToken
      refreshToken
    }
    onboardingCompleted
    errors {
      ... on GeneralError {
        message
        path
      }
    }
  }
}
    `;
export const ChangeTeamOrderDownDocument = gql`
    mutation changeTeamOrderDown($changeTeamOrderDownId: ObjectId!) {
  changeTeamOrderDown(id: $changeTeamOrderDownId)
}
    `;
export const ChangeTeamOrderUpDocument = gql`
    mutation changeTeamOrderUp($changeTeamOrderUpId: ObjectId!) {
  changeTeamOrderUp(id: $changeTeamOrderUpId)
}
    `;
export const ConfirmFamilyMemberDocument = gql`
    mutation confirmFamilyMember($userId: String!) {
  confirmFamilyMember(userId: $userId) {
    user {
      id
      familyMembers {
        id
      }
      pendingFamilyMembers {
        id
      }
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on UserNotInPendingError {
        message
        path
      }
      ... on SelfFamilyMemberError {
        message
        path
      }
      ... on FamilyMemberAlreadyExistsError {
        message
        path
      }
    }
  }
}
    `;
export const CreateAdminDocument = gql`
    mutation createAdmin($input: CreateAdminInput!) {
  createAdmin(input: $input) {
    user {
      id
      firstname
      lastname
      email
    }
  }
}
    `;
export const CreateAgencyDocument = gql`
    mutation createAgency($input: CreateAgencyInput!) {
  createAgency(input: $input) {
    agency {
      ...Agency
    }
    errors {
      ... on CreateAgencyAlreadyExistsError {
        message
        path
      }
    }
  }
}
    ${AgencyFragmentDoc}`;
export const CreateBoostDocument = gql`
    mutation createBoost($input: CreateBoostInput!) {
  createBoost(input: $input) {
    boost {
      id
      name
      amount
      currency
      message
      state
      createdAt
    }
    errors {
      ... on StripeNotConnectedError {
        message
        path
      }
    }
    paymentSheet {
      accountId
      customerId
      ephemeralKey
      paymentIntent
      publishableKey
    }
  }
}
    `;
export const CreateBoostMessageDocument = gql`
    mutation createBoostMessage($input: BoostMessageInput) {
  createBoostMessage(input: $input) {
    errors {
      ... on BoostNotFoundError {
        message
        path
      }
      ... on BoostMessageAlreadySetError {
        message
        path
      }
    }
    boost {
      id
      currency
      amount
      name
      message
      state
    }
  }
}
    `;
export const CreateChatUserTokenDocument = gql`
    mutation createChatUserToken {
  createChatUserToken
}
    `;
export const CreateChatUsersDocument = gql`
    mutation createChatUsers($input: CreateChatUsersInput!) {
  createChatUsers(input: $input)
}
    `;
export const CreateLeagueDocument = gql`
    mutation createLeague($input: CreateLeagueInput!) {
  createLeague(input: $input) {
    errors {
      ... on CreateLeagueAlreadyExistsError {
        message
        path
      }
    }
    league {
      ...League
    }
  }
}
    ${LeagueFragmentDoc}`;
export const CreatePassiveEventDocument = gql`
    mutation createPassiveEvent($input: CreatePassiveEventInput!) {
  createPassiveEvent(input: $input)
}
    `;
export const CreateStreamFeedUserTokenDocument = gql`
    mutation createStreamFeedUserToken {
  createStreamFeedUserToken
}
    `;
export const CreateStripeAccountDocument = gql`
    mutation createStripeAccount($input: CreateStripeAccountInput!) {
  createStripeAccount(input: $input) {
    account {
      id
      firstname
      email
      lastname
      paymentsEnabled
      payoutsEnabled
      phone
      ssnLast4Provided
      dob
      currency
      country
      address {
        city
        line1
        line2
        postalCode
        state
      }
      jobTitle
    }
    errors {
      ... on AccountAlreadyExistsError {
        message
        path
      }
      ... on StripeAccountUserInputError {
        message
        path
      }
    }
  }
}
    `;
export const CreateStripeExternalAccountDocument = gql`
    mutation createStripeExternalAccount($input: CreateExternalAccountInput!) {
  createStripeExternalAccount(input: $input) {
    errors {
      ... on ExternalAccountAlreadyExistsError {
        message
        path
      }
      ... on ExternalAccountUserInputError {
        message
        path
      }
      ... on AccountDoesNotExistsError {
        message
        path
      }
    }
    externalAccount {
      id
      bankAccount {
        country
        currency
        firstname
        lastname
      }
      routingNumber
      accountNumber
      status
    }
  }
}
    `;
export const CreateUserStatsDocument = gql`
    mutation createUserStats($input: CreateUserStatsInput!) {
  createUserStats(input: $input) {
    userStats {
      ...UserStats
    }
    errors {
      ... on CreateUserStatsAlreadyExistsError {
        message
        path
      }
    }
  }
}
    ${UserStatsFragmentDoc}`;
export const DeleteAccountDocument = gql`
    mutation deleteAccount {
  deleteAccount
}
    `;
export const DeleteAdminDocument = gql`
    mutation deleteAdmin($deleteAdminId: ObjectId!) {
  deleteAdmin(id: $deleteAdminId)
}
    `;
export const DeleteAgencyDocument = gql`
    mutation deleteAgency($deleteAgencyId: ObjectId!) {
  deleteAgency(id: $deleteAgencyId)
}
    `;
export const DeleteLeagueDocument = gql`
    mutation deleteLeague($deleteLeagueId: ObjectId!) {
  deleteLeague(id: $deleteLeagueId)
}
    `;
export const DeleteOrganizationDocument = gql`
    mutation deleteOrganization($deleteOrganizationId: ObjectId!) {
  deleteOrganization(id: $deleteOrganizationId)
}
    `;
export const DeleteStripeExternalAccountDocument = gql`
    mutation deleteStripeExternalAccount {
  deleteStripeExternalAccount
}
    `;
export const DeleteTeamDocument = gql`
    mutation deleteTeam($deleteTeamId: ObjectId!) {
  deleteTeam(id: $deleteTeamId)
}
    `;
export const DeleteUserStatsDocument = gql`
    mutation deleteUserStats($deleteUserStatsId: ObjectId!) {
  deleteUserStats(id: $deleteUserStatsId)
}
    `;
export const DeleteWallVideoDocument = gql`
    mutation deleteWallVideo($id: String!) {
  deleteWallVideo(id: $id) {
    errors {
      ... on FailedToDelete {
        path
        message
      }
    }
    success
  }
}
    `;
export const EditHighlightsDocument = gql`
    mutation editHighlights($input: EditHighlightsInput!) {
  editHighlights(input: $input) {
    user {
      highlights {
        id
        path
      }
    }
    errors {
      ... on FailedToEdit {
        message
      }
    }
  }
}
    `;
export const EditWallVideoCategoryDocument = gql`
    mutation editWallVideoCategory($input: EditWallVideoInput!) {
  editWallVideoCategory(input: $input) {
    errors {
      ... on WallVideoNotFound {
        message
        path
      }
      ... on FailedToEdit {
        message
        path
      }
    }
    wallVideo {
      id
      type
      path
      width
      height
      hls
      hlsPath
      segmentation {
        path
        finished
        createdAt
      }
      createdAt
    }
  }
}
    `;
export const FetchUserDataEpDocument = gql`
    mutation fetchUserDataEP {
  fetchUserDataEP {
    errors {
      message
      path
    }
    user {
      id
      name
      firstname
      lastname
      externalLinks {
        id
        name
        type
        url
      }
      currentTeam {
        id
      }
    }
  }
}
    `;
export const FollowAgencyDocument = gql`
    mutation followAgency($id: ObjectId!) {
  followAgency(id: $id) {
    agency {
      ...Agency
    }
    errors {
      ... on FollowAgencyNotFoundError {
        message
      }
    }
  }
}
    ${AgencyFragmentDoc}`;
export const FollowTeamDocument = gql`
    mutation followTeam($id: ObjectId!) {
  followTeam(id: $id) {
    team {
      ...Team
    }
    errors {
      ... on EntityNotFoundError {
        message
      }
    }
  }
}
    ${TeamFragmentDoc}`;
export const FollowUserDocument = gql`
    mutation followUser($followUserId: String!) {
  followUser(id: $followUserId) {
    userStats {
      followers
    }
    id
  }
}
    `;
export const GoogleSignInDocument = gql`
    mutation googleSignIn($input: SocialSignInInput!) {
  googleSignIn(input: $input) {
    tokens {
      accessToken
      refreshToken
    }
    user {
      id
      name
      email
      contact {
        firstname
        lastname
        email
      }
    }
    onboardingCompleted
    errors {
      ... on GeneralError {
        message
        path
      }
    }
  }
}
    `;
export const GoogleSignInAdminDocument = gql`
    mutation googleSignInAdmin($input: AdminSocialSignInInput!) {
  googleSignInAdmin(input: $input) {
    tokens {
      accessToken
      refreshToken
    }
    user {
      id
      firstname
      lastname
      email
    }
    errors {
      ... on GeneralError {
        message
        path
      }
    }
  }
}
    `;
export const InviteAgentsDocument = gql`
    mutation inviteAgents($input: InviteAgentsInput!) {
  inviteAgents(input: $input) {
    success
    errors {
      ... on InviteAgentsAlreadyExistsError {
        message
        path
      }
    }
  }
}
    `;
export const LikePlayerPerformanceDocument = gql`
    mutation likePlayerPerformance($gameId: ObjectId!, $playerId: ObjectId!) {
  likePlayerPerformance(gameId: $gameId, playerId: $playerId) {
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on EntityNotFoundError {
        message
        path
      }
    }
    player {
      id
    }
  }
}
    `;
export const LikeWallVideoDocument = gql`
    mutation likeWallVideo($id: String!, $isHighlight: Boolean) {
  likeWallVideo(id: $id, isHighlight: $isHighlight)
}
    `;
export const LoginAdminDocument = gql`
    mutation loginAdmin($email: String!) {
  loginAdmin(email: $email) {
    errors {
      ... on UserNotFoundError {
        message
        path
      }
    }
  }
}
    `;
export const MigrateUserStatsDocument = gql`
    mutation migrateUserStats($input: MigrateUserStatsInput!) {
  admin {
    userStats {
      migrate(input: $input) {
        success
        modifiedCount
        errors {
          ... on FailedToUpdateStats {
            message
          }
        }
      }
    }
  }
}
    `;
export const PushNotificationsDocument = gql`
    mutation pushNotifications($input: PushNotificationsInput!) {
  pushNotifications(input: $input) {
    errors {
      ... on PushNotificationsChangeError {
        message
        path
      }
    }
    user {
      id
      firstname
      lastname
      pushEnabled
      role
      currentTeam {
        id
      }
    }
  }
}
    `;
export const RegisterDeviceDocument = gql`
    mutation registerDevice($input: RegisterDeviceInput!) {
  registerDevice(input: $input) {
    user {
      registeredDevices {
        arn
        platform
        token
      }
    }
    errors {
      ... on IUserError {
        message
      }
    }
  }
}
    `;
export const RegisterOrLoginByEmailDocument = gql`
    mutation registerOrLoginByEmail($input: RegisterOrLoginByEmailInput!, $newUrl: Boolean) {
  registerOrLoginByEmail(input: $input, newUrl: $newUrl) {
    user {
      ...User
    }
    errors {
      ... on IUserError {
        message
      }
    }
  }
}
    ${UserFragmentDoc}`;
export const RemoveAgencyUserDocument = gql`
    mutation removeAgencyUser($agencyId: ObjectId!, $userId: ObjectId!) {
  admin {
    agency {
      removeAgencyUser(agencyId: $agencyId, userId: $userId) {
        agency {
          id
          agentsCount
          clientsCount
        }
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    `;
export const RemoveFamilyMemberDocument = gql`
    mutation removeFamilyMember($userId: String!) {
  removeFamilyMember(userId: $userId) {
    user {
      id
      familyMembers {
        id
      }
      pendingFamilyMembers {
        id
      }
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on UserNotInFamilyError {
        message
        path
      }
    }
  }
}
    `;
export const RemovePendingFamilyMemberDocument = gql`
    mutation removePendingFamilyMember($userId: String!) {
  removePendingFamilyMember(userId: $userId) {
    user {
      id
      familyMembers {
        id
      }
      pendingFamilyMembers {
        id
      }
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on UserNotInPendingError {
        message
        path
      }
    }
  }
}
    `;
export const SendContactMessageDocument = gql`
    mutation sendContactMessage($input: ContactMessageInput) {
  sendContactMessage(input: $input) {
    errors {
      ... on ContactNotFoundError {
        message
        path
      }
    }
    success
  }
}
    `;
export const SyncAgencyClientsDocument = gql`
    mutation syncAgencyClients($epAgencyId: Int!, $agencyId: ObjectId!) {
  admin {
    user {
      syncAgencyClients(epAgencyId: $epAgencyId, agencyId: $agencyId) {
        agency {
          id
          clientsCount
        }
        errors {
          ... on IUserError {
            message
          }
        }
      }
    }
  }
}
    `;
export const SyncUserStatsDocument = gql`
    mutation syncUserStats($userId: String!) {
  syncUserStats(userId: $userId)
}
    `;
export const UnfollowAgencyDocument = gql`
    mutation unfollowAgency($id: ObjectId!) {
  unfollowAgency(id: $id) {
    agency {
      ...Agency
    }
    errors {
      ... on UnfollowAgencyNotFoundError {
        message
      }
    }
  }
}
    ${AgencyFragmentDoc}`;
export const UnfollowTeamDocument = gql`
    mutation unfollowTeam($id: ObjectId!) {
  unfollowTeam(id: $id) {
    team {
      ...Team
    }
    errors {
      ... on EntityNotFoundError {
        message
      }
    }
  }
}
    ${TeamFragmentDoc}`;
export const UnfollowUserDocument = gql`
    mutation unfollowUser($unfollowUserId: String!) {
  unfollowUser(id: $unfollowUserId) {
    errors {
      ... on UserNotFoundError {
        message
        path
      }
    }
    user {
      id
      userStats {
        followers
      }
    }
  }
}
    `;
export const UnlikePlayerPerformanceDocument = gql`
    mutation unlikePlayerPerformance($gameId: ObjectId!, $playerId: ObjectId!) {
  unlikePlayerPerformance(gameId: $gameId, playerId: $playerId) {
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on EntityNotFoundError {
        message
        path
      }
    }
    player {
      id
    }
  }
}
    `;
export const UnregisterDeviceDocument = gql`
    mutation unregisterDevice($input: UnregisterDeviceInput!) {
  unregisterDevice(input: $input) {
    user {
      registeredDevices {
        arn
        platform
        token
      }
    }
    errors {
      ... on IUserError {
        message
      }
    }
  }
}
    `;
export const UpdateAgencyDocument = gql`
    mutation updateAgency($input: UpdateAgencyInput!, $updateAgencyId: ObjectId!) {
  updateAgency(input: $input, id: $updateAgencyId) {
    agency {
      ...Agency
    }
    errors {
      ... on UpdateAgencyNotFoundError {
        message
        path
      }
    }
  }
}
    ${AgencyFragmentDoc}`;
export const UpdateLeagueDocument = gql`
    mutation updateLeague($input: UpdateLeagueInput!, $updateLeagueId: ObjectId!) {
  updateLeague(input: $input, id: $updateLeagueId) {
    errors {
      ... on UpdateLeagueNotFoundError {
        message
        path
      }
    }
    league {
      ...League
    }
  }
}
    ${LeagueFragmentDoc}`;
export const UpdateStripeAccountDocument = gql`
    mutation updateStripeAccount($input: CreateStripeAccountInput!) {
  updateStripeAccount(input: $input) {
    account {
      id
      firstname
      lastname
      dob
      address {
        city
        line1
        line2
        postalCode
        state
      }
      currency
      phone
      email
      country
      payoutsEnabled
      paymentsEnabled
      ssnLast4Provided
      jobTitle
    }
    errors {
      ... on AccountDoesNotExistsError {
        message
        path
      }
      ... on StripeAccountUserInputError {
        message
        path
      }
    }
  }
}
    `;
export const UpdateUserDocument = gql`
    mutation updateUser($input: UpdateUserInput!) {
  updateUser(input: $input) {
    user {
      ...User
    }
    errors {
      ... on UserNotFoundError {
        message
      }
    }
  }
}
    ${UserFragmentDoc}`;
export const UpdateUserAdminDocument = gql`
    mutation updateUserAdmin($input: UpdateUserInputAdmin!, $updateUserAdminId: ObjectId!) {
  updateUserAdmin(input: $input, id: $updateUserAdminId) {
    user {
      id
      name
      originalFirstname
      firstname
      originalLastname
      lastname
      email
      slug
      dateOfBirth
      country
      contact {
        name
        firstname
        lastname
        email
        phoneNumber {
          countryPrefix
          number
        }
        type
      }
      agentV2 {
        name
        firstname
        lastname
        email
        phoneNumber {
          countryPrefix
          number
        }
      }
      phoneNumber {
        countryPrefix
        number
      }
      bio {
        position
        handedness
        playerType
        schoolType
        agency {
          id
          name
          country
          logo
          checked
        }
        gender
        height {
          feet
          inches
          centimeters
        }
        weight {
          pounds
          kilograms
        }
        birthplace
        nameOfSchool
        graduationYear
        cumulativeGPA
        coreGPA
        unweightedGPA
        weightedGPA
        satScore
        actScore
        toeflScore
        ncaaEligibility
        naiaEligibility
      }
      avatar
      avatars {
        tiny
        small
        medium
        large
      }
      externalLinks {
        id
        name
        url
        type
      }
      boostMessage
      currentTeam {
        id
        name
      }
      epCurrentTeam
      stripeAccountId
      stripeExternalAccountId
      boost {
        currency
        goalAmount
        currentAmount
      }
      recommended
      inspired
      moreSelected
      role
      subrole
      userStats {
        followers
        boosts
        views
      }
      public
      premium
      premiumSince
      searchEnabled
      isAdult
      registeredDevices {
        token
        platform
        arn
      }
      pushEnabled
      highlights {
        id
        type
        title
        path
        thumbnail
        size
        width
        height
        duration
        hls
        hlsPath
        createdAt
      }
      numberOfVideos
      createdAt
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
    }
  }
}
    `;
export const UpdateUserCurrencyDocument = gql`
    mutation updateUserCurrency($currency: String!, $updateUserCurrencyId: ObjectId!) {
  updateUserCurrency(currency: $currency, id: $updateUserCurrencyId) {
    currency
    errors {
      ... on CurrencyUpdateError {
        message
        path
      }
      ... on UserNotFoundError {
        message
        path
      }
    }
  }
}
    `;
export const UpdateUserStatsDocument = gql`
    mutation updateUserStats($input: UpdateUserStatsInput!, $updateUserStatsId: ObjectId!) {
  updateUserStats(input: $input, id: $updateUserStatsId) {
    userStats {
      ...UserStats
    }
    errors {
      ... on UpdateUserStatsNotFoundError {
        message
        path
      }
    }
  }
}
    ${UserStatsFragmentDoc}`;
export const UpdateUserWithAgencyUnverifiedDocument = gql`
    mutation updateUserWithAgencyUnverified($input: UpdateUserInput!) {
  updateUser(input: $input) {
    user {
      ...UserWithAgencyUnverified
    }
    errors {
      ... on UserNotFoundError {
        __typename
        message
      }
      ... on IUserError {
        __typename
        message
      }
    }
  }
}
    ${UserWithAgencyUnverifiedFragmentDoc}`;
export const UpdateWallVideoDocument = gql`
    mutation updateWallVideo($updateId: ObjectId!, $input: UpdateWallVideoInput!) {
  admin {
    wallVideo {
      update(id: $updateId, input: $input) {
        wallVideo {
          ...WallVideo
        }
        errors {
          ... on WallVideoNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
    ${WallVideoFragmentDoc}`;
export const UploadAgencyLogoDocument = gql`
    mutation uploadAgencyLogo($uploadAgencyLogoId: ObjectId!, $input: UploadAgencyLogoInput) {
  uploadAgencyLogo(id: $uploadAgencyLogoId, input: $input) {
    preSignedUrl
    agency {
      id
      name
      country
      logo
      checked
    }
    errors {
      ... on FailedToCreatePresignedUrl {
        message
        path
      }
    }
  }
}
    `;
export const UploadAvatarDocument = gql`
    mutation uploadAvatar($input: UploadAvatarInput) {
  uploadAvatar(input: $input) {
    errors {
      ... on FailedToCreatePresignedUrl {
        message
        path
      }
    }
    preSignedUrl
    user {
      id
      name
      role
      slug
      currentTeam {
        id
        name
      }
      avatar
      avatars {
        large
        medium
        small
        tiny
      }
    }
  }
}
    `;
export const UploadOrganizationLogoDocument = gql`
    mutation uploadOrganizationLogo($uploadOrganizationLogoId: ObjectId!, $input: UploadOrganizationLogoInput) {
  uploadOrganizationLogo(id: $uploadOrganizationLogoId, input: $input) {
    organization {
      id
      name
      slug
      country
      logo
    }
    errors {
      ... on FailedToCreatePresignedUrl {
        message
        path
      }
    }
    preSignedUrl
  }
}
    `;
export const UploadTeamLogoDocument = gql`
    mutation uploadTeamLogo($uploadTeamLogoId: ObjectId!, $input: UploadTeamLogoInput) {
  uploadTeamLogo(id: $uploadTeamLogoId, input: $input) {
    preSignedUrl
    team {
      id
      name
      slug
      logo
      country
      order
      externalId
      shortName
      shortNameSlug
    }
    errors {
      ... on FailedToCreatePresignedUrl {
        message
        path
      }
    }
  }
}
    `;
export const UploadWallVideoDocument = gql`
    mutation uploadWallVideo($input: UploadWallVideoInput) {
  uploadWallVideo(input: $input) {
    errors {
      ... on FailedToCreatePresignedUrl {
        message
        path
      }
    }
    preSignedUrl
    thumbnailPreSignedUrl
    wallVideo {
      id
      hls
      thumbnail
      createdAt
      duration
      height
      size
      title
      type
      width
    }
  }
}
    `;
export const UploadWallVideoSegmentationDocument = gql`
    mutation uploadWallVideoSegmentation($input: UploadWallVideoSegmentationInput!) {
  uploadWallVideoSegmentation(input: $input) {
    preSignedUrl
    wallVideo {
      id
      path
      segmentation {
        path
        finished
      }
    }
    errors {
      ... on WallVideoNotFound {
        message
      }
      ... on FailedToEdit {
        message
      }
    }
  }
}
    `;
export const VerifyAdminByEmailDocument = gql`
    mutation verifyAdminByEmail($code: String!, $email: String!) {
  verifyAdminByEmail(code: $code, email: $email) {
    tokens {
      accessToken
      refreshToken
    }
    user {
      id
      firstname
      lastname
      email
    }
    errors {
      ... on InvalidVerificationCodeError {
        message
        path
      }
      ... on UserNotFoundError {
        message
        path
      }
    }
  }
}
    `;
export const VerifyByEmailDocument = gql`
    mutation verifyByEmail($email: String!, $code: String!) {
  verifyByEmail(email: $email, code: $code) {
    tokens {
      accessToken
      refreshToken
    }
    user {
      ...User
    }
    errors {
      ... on InvalidVerificationCodeError {
        message
      }
      ... on UserNotFoundError {
        message
      }
      ... on IUserError {
        message
      }
    }
  }
}
    ${UserFragmentDoc}`;
export const VisitAgencyDocument = gql`
    mutation visitAgency($id: ObjectId!) {
  visitAgency(id: $id) {
    agency {
      ...Agency
    }
    errors {
      ... on VisitAgencyNotFoundError {
        message
      }
    }
  }
}
    ${AgencyFragmentDoc}`;
export const VisitTeamDocument = gql`
    mutation visitTeam($id: ObjectId!) {
  visitTeam(id: $id) {
    team {
      ...Team
    }
    errors {
      ... on EntityNotFoundError {
        message
      }
    }
  }
}
    ${TeamFragmentDoc}`;
export const VisitUserDocument = gql`
    mutation visitUser($visitUserId: ObjectId!) {
  visitUser(id: $visitUserId) {
    id
    userStats {
      views
    }
  }
}
    `;
export const GetBatchNotificationsDocument = gql`
    query getBatchNotifications($pagination: Pagination) {
  admin {
    batchNotification {
      list(pagination: $pagination) {
        edges {
          node {
            ...BatchNotification
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${BatchNotificationFragmentDoc}`;
export const BoostsDocument = gql`
    query boosts($filter: AdminBoostFilter, $pagination: Pagination) {
  admin {
    boost {
      list(filter: $filter, pagination: $pagination) {
        edges {
          node {
            ...Boost
            user {
              id
              name
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${BoostFragmentDoc}`;
export const GetCrawlerDomainConfigDocument = gql`
    query getCrawlerDomainConfig($id: ObjectId!) {
  admin {
    crawlerDomainConfig {
      get(id: $id) {
        ...CrawlerDomainConfig
      }
    }
  }
}
    ${CrawlerDomainConfigFragmentDoc}`;
export const GetCrawlerDomainConfigsDocument = gql`
    query getCrawlerDomainConfigs($pagination: Pagination) {
  admin {
    crawlerDomainConfig {
      list(pagination: $pagination) {
        edges {
          node {
            ...CrawlerDomainConfig
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${CrawlerDomainConfigFragmentDoc}`;
export const GetCrawlerLogsItemDocument = gql`
    query getCrawlerLogsItem($id: String!) {
  admin {
    crawlerLogs {
      get(id: $id) {
        id
        op
        modelName
        collectionName
        collectionId
        patch {
          op
          path
          value
        }
        user {
          id
          email
          admin
          role
          iat
          exp
        }
        version
        createdAt
        updatedAt
      }
    }
  }
}
    `;
export const GetCrawlerLogsDocument = gql`
    query getCrawlerLogs($first: Int, $after: String, $before: String, $filter: CrawlerLogsFilter) {
  admin {
    crawlerLogs {
      list(first: $first, after: $after, before: $before, filter: $filter) {
        edges {
          node {
            id
            op
            modelName
            collectionName
            collectionId
            patch {
              op
              path
              value
            }
            user {
              id
              email
              admin
              role
              iat
              exp
            }
            version
            createdAt
            updatedAt
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    `;
export const Admin_CrawlerRunStats_GetDocument = gql`
    query admin_crawlerRunStats_get($id: String!) {
  admin {
    crawlerRunStats {
      get(id: $id) {
        id
        startedAt
        finishedAt
        durationMs
        totalVisitedUrls
        totalDiscoveredUrls
        newlyDiscoveredUrls
        urlPatternStats {
          urlPattern
          matchCount
        }
        crawlerDomainConfig {
          id
          domain
        }
      }
    }
  }
}
    `;
export const Admin_CrawlerRunStats_ListDocument = gql`
    query admin_crawlerRunStats_list($first: Int, $after: String, $before: String, $filter: CrawlerRunStatsFilter) {
  admin {
    crawlerRunStats {
      list(first: $first, after: $after, before: $before, filter: $filter) {
        edges {
          node {
            id
            startedAt
            finishedAt
            durationMs
            totalVisitedUrls
            totalDiscoveredUrls
            newlyDiscoveredUrls
            urlPatternStats {
              urlPattern
              matchCount
            }
            crawlerDomainConfig {
              id
              domain
            }
          }
        }
        pageInfo {
          hasNextPage
          hasPreviousPage
          startCursor
          endCursor
          totalCount
        }
      }
    }
  }
}
    `;
export const DiscoveredUrlListDocument = gql`
    query discoveredUrlList($filter: DiscoveredUrlFilter, $pagination: Pagination) {
  admin {
    discoveredUrl {
      list(filter: $filter, pagination: $pagination) {
        edges {
          node {
            ...DiscoveredUrlList
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${DiscoveredUrlListFragmentDoc}`;
export const AdminGameGetDocument = gql`
    query adminGameGet($id: ObjectId!) {
  admin {
    game {
      get(id: $id) {
        ...GameResponse
      }
    }
  }
}
    ${GameResponseFragmentDoc}`;
export const AdminGameListDocument = gql`
    query adminGameList($pagination: Pagination, $filter: AdminGameFilter, $sort: SortInput) {
  admin {
    game {
      list(filter: $filter, pagination: $pagination, sort: $sort) {
        edges {
          node {
            ...GameListItem
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${GameListItemFragmentDoc}`;
export const GetLeagueDocument = gql`
    query getLeague($leagueId: ObjectId!) {
  admin {
    league {
      get(id: $leagueId) {
        ...League
      }
    }
  }
}
    ${LeagueFragmentDoc}`;
export const AdminLeagueListDocument = gql`
    query adminLeagueList($filter: AdminLeaguesFilter, $pagination: Pagination) {
  admin {
    league {
      list(filter: $filter, pagination: $pagination) {
        edges {
          node {
            ...League
          }
          used
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${LeagueFragmentDoc}`;
export const AdminLeagueStatsListDocument = gql`
    query adminLeagueStatsList($filter: AdminLeagueStatsFilter!) {
  admin {
    leagueStats {
      list(filter: $filter) {
        edges {
          node {
            ...LeagueStatsListItem
          }
        }
      }
    }
  }
}
    ${LeagueStatsListItemFragmentDoc}`;
export const GetNotificationsDocument = gql`
    query getNotifications($pagination: Pagination, $filter: AdminNotificationFilter) {
  admin {
    notifications {
      getNotifications(pagination: $pagination, filter: $filter) {
        edges {
          node {
            ...Notifications
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${NotificationsFragmentDoc}`;
export const UserInfoDocument = gql`
    query userInfo($filter: UserInfoFilter!, $pagination: Pagination) {
  admin {
    userInfo {
      userInfos(filter: $filter, pagination: $pagination) {
        edges {
          node {
            id
            name
            externalId
            crawlerDomainConfig {
              id
              domain
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    `;
export const AdminWallVideosDocument = gql`
    query adminWallVideos($filter: VideoFilter, $pagination: Pagination) {
  admin {
    wallVideo {
      list(filter: $filter, pagination: $pagination) {
        edges {
          node {
            ...WallVideo
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${WallVideoFragmentDoc}`;
export const AdminUserDocument = gql`
    query adminUser($userId: ObjectId!) {
  admin {
    user(id: $userId) {
      currentTeam {
        name
      }
      firstname
      lastname
      bio {
        agency {
          id
          name
        }
        agencyUnverified {
          id
          name
        }
      }
    }
  }
}
    `;
export const AdminUserStatsDocument = gql`
    query adminUserStats($adminUserStatsId: ObjectId!) {
  adminUserStats(id: $adminUserStatsId) {
    ...UserStats
  }
}
    ${UserStatsFragmentDoc}`;
export const AdminUserStatsListDocument = gql`
    query adminUserStatsList($filter: AdminUserStatsFilter, $pagination: Pagination) {
  admin {
    userStats {
      list(filter: $filter, pagination: $pagination) {
        edges {
          node {
            ...UserStats
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
    ${UserStatsFragmentDoc}`;
export const AdminUsersDocument = gql`
    query adminUsers($pagination: Pagination) {
  adminUsers(pagination: $pagination) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        id
        firstname
        lastname
        email
      }
    }
  }
}
    `;
export const AgenciesDocument = gql`
    query agencies($filter: AgenciesFilter, $pagination: Pagination) {
  agencies(filter: $filter, pagination: $pagination) {
    edges {
      node {
        ...Agency
      }
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
    ${AgencyFragmentDoc}`;
export const AgencyDocument = gql`
    query agency($agencyId: ObjectId, $slug: String) {
  agency(id: $agencyId, slug: $slug) {
    ...Agency
  }
}
    ${AgencyFragmentDoc}`;
export const AgencyUsersDocument = gql`
    query agencyUsers($pagination: Pagination, $agencyId: ObjectId!, $filter: AgencyUsersFilter) {
  agencyUsers(pagination: $pagination, agencyId: $agencyId, filter: $filter) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        id
        name
        currentTeam {
          id
          name
        }
        agentV2 {
          id
          name
        }
      }
    }
    aggregatedInfo {
      name
      items {
        key
        value
      }
    }
  }
}
    `;
export const AgentsClientsDocument = gql`
    query agentsClients($slug: String!, $after: String, $first: Int) {
  agentsClients(slug: $slug, after: $after, first: $first) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        name
        currentTeam {
          name
        }
      }
    }
    aggregatedInfo {
      name
      items {
        key
        value
      }
    }
  }
}
    `;
export const BoostsQueryDocument = gql`
    query boostsQuery($pagination: Pagination, $filter: BoostsFilter) {
  boosts(pagination: $pagination, filter: $filter) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        id
        name
        amount
        currency
      }
    }
  }
}
    `;
export const CurrentAdminUserDocument = gql`
    query currentAdminUser {
  currentAdminUser {
    id
    firstname
    lastname
    email
  }
}
    `;
export const CurrentUserDocument = gql`
    query currentUser {
  currentUser {
    ...User
  }
}
    ${UserFragmentDoc}`;
export const CurrentUserFollowingsDocument = gql`
    query currentUserFollowings {
  currentUser {
    id
    userStats {
      followings
    }
  }
}
    `;
export const CurrentUserWithAgencyUnverifiedDocument = gql`
    query currentUserWithAgencyUnverified {
  currentUser {
    ...UserWithAgencyUnverified
  }
}
    ${UserWithAgencyUnverifiedFragmentDoc}`;
export const EntityCountsDocument = gql`
    query entityCounts {
  entityCounts {
    players
    professionals
    agencies
  }
}
    `;
export const EpTeamDocument = gql`
    query epTeam($epTeamId: String!) {
  epTeam(id: $epTeamId) {
    id
    slug
    name
    country {
      iso_3166_1_alpha_2
    }
    logo {
      large
    }
  }
}
    `;
export const EpTeamsDocument = gql`
    query epTeams($searchQuery: String!) {
  epTeams(searchQuery: $searchQuery) {
    id
    slug
    name
    country {
      iso_3166_1_alpha_2
    }
    logo {
      large
    }
  }
}
    `;
export const ExampleUsersDocument = gql`
    query exampleUsers {
  exampleUsers {
    ...ExampleUser
  }
}
    ${ExampleUserFragmentDoc}`;
export const ExampleWallVideosDocument = gql`
    query exampleWallVideos {
  exampleWallVideos {
    ...WallVideo
    user {
      ...ExampleUser
    }
  }
}
    ${WallVideoFragmentDoc}
${ExampleUserFragmentDoc}`;
export const GameDocument = gql`
    query game($gameId: ObjectId!) {
  game(id: $gameId) {
    id
    date
    externalId
    externalInfo {
      homeTeamName
      awayTeamName
      leagueName
    }
    league {
      id
      name
    }
    awayTeam {
      id
      name
      shortNameSlug
      organization {
        slug
      }
    }
    homeTeam {
      id
      name
      shortNameSlug
      organization {
        slug
      }
    }
    stats {
      homeTeam {
        score
        shotsOnGoal
        blockedShots
        faceoffs
        powerPlayGoals
        shortHandedGoals
        penalties
        majorPenalties
        penaltyMinutes
        goalieSaves
      }
      awayTeam {
        score
        shotsOnGoal
        blockedShots
        faceoffs
        powerPlayGoals
        shortHandedGoals
        penalties
        majorPenalties
        penaltyMinutes
        goalieSaves
      }
      periodScores
    }
    eventsByPeriod {
      time
      type
      isHomeTeam
      externalInfo {
        teamName
        playerName
        playerExternalId
      }
      player {
        name
      }
      assists {
        playerName
        playerExternalId
      }
    }
    homeTeamRoster {
      externalInfo {
        playerName
        playerExternalId
      }
      player {
        name
        id
      }
      goals
      assists
      points
      pim
      plusMinus
      saves
      goalsAgainst
      svp
      jerseyNumber
      position
    }
    awayTeamRoster {
      externalInfo {
        playerName
        playerExternalId
      }
      player {
        name
      }
      goals
      assists
      points
      pim
      plusMinus
      saves
      goalsAgainst
      svp
      jerseyNumber
      position
    }
    syncedAt
  }
}
    `;
export const GameEventsDocument = gql`
    query gameEvents($gameId: ObjectId!) {
  game(id: $gameId) {
    ...GameResponse
  }
}
    ${GameResponseFragmentDoc}`;
export const GamesDocument = gql`
    query games($filter: GamesFilter, $first: Int, $after: String, $before: String, $sort: SortInput) {
  games(
    filter: $filter
    first: $first
    after: $after
    before: $before
    sort: $sort
  ) {
    edges {
      node {
        ...GameResponse
      }
      currentUserPlayer {
        goals
        assists
        saves
        externalInfo {
          playerName
          playerExternalId
        }
        player {
          id
        }
      }
      currentUserTeam
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
    ${GameResponseFragmentDoc}`;
export const GamesPlayersDocument = gql`
    query gamesPlayers($input: GamesPlayersInput!) {
  gamesPlayers(input: $input) {
    games {
      ...GameDetailWithPlayers
    }
  }
}
    ${GameDetailWithPlayersFragmentDoc}`;
export const InspiredUsersDocument = gql`
    query inspiredUsers($first: Int) {
  inspiredUsers(first: $first) {
    id
    name
    currentTeam {
      id
      name
    }
  }
}
    `;
export const IsFollowingDocument = gql`
    query isFollowing($userId: String!) {
  isFollowing(userId: $userId)
}
    `;
export const IsFollowingAgencyDocument = gql`
    query isFollowingAgency($agencyId: ObjectId!) {
  isFollowingAgency(agencyId: $agencyId)
}
    `;
export const IsFollowingTeamDocument = gql`
    query isFollowingTeam($id: ObjectId!) {
  isFollowingTeam(id: $id)
}
    `;
export const LatestClipsDocument = gql`
    query latestClips($filter: LatestClipsFilter, $before: String, $after: String, $first: Int, $last: Int) {
  latestClips(
    filter: $filter
    before: $before
    after: $after
    first: $first
    last: $last
  ) {
    edges {
      node {
        id
        path
        createdAt
        user {
          id
          name
          dateOfBirth
          bio {
            position
          }
          currentTeam {
            id
            name
          }
        }
      }
    }
    pageInfo {
      startCursor
      endCursor
      hasNextPage
      hasPreviousPage
    }
  }
}
    `;
export const LeagueDocument = gql`
    query league($slug: String!) {
  league(slug: $slug) {
    ...League
  }
}
    ${LeagueFragmentDoc}`;
export const LeagueStandingsDocument = gql`
    query leagueStandings($teamId: ObjectId, $leagueId: ObjectId, $season: String) {
  leagueStandings(teamId: $teamId, leagueId: $leagueId, season: $season) {
    league {
      name
    }
    groups {
      group
      standings {
        standing
        team {
          name
          shortName
        }
        season
        points
        overtimeWins
        overtimeLosses
        losses
        games
      }
    }
  }
}
    `;
export const LeaguesDocument = gql`
    query leagues($filter: LeaguesFilter, $pagination: Pagination) {
  leagues(filter: $filter, pagination: $pagination) {
    edges {
      node {
        ...League
      }
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
    ${LeagueFragmentDoc}`;
export const MoreSelectedUsersDocument = gql`
    query moreSelectedUsers {
  moreSelectedUsers {
    id
    name
    currentTeam {
      id
      name
    }
  }
}
    `;
export const MyFollowersDocument = gql`
    query myFollowers($filter: MyFollowersFilter!, $first: Int, $after: String, $before: String) {
  myFollowers(filter: $filter, first: $first, after: $after, before: $before) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      iFollow
      node {
        name
        firstname
        lastname
        currentTeam {
          id
          name
        }
      }
    }
  }
}
    `;
export const MyFollowingDocument = gql`
    query myFollowing($pagination: Pagination) {
  myFollowing(pagination: $pagination) {
    edges {
      node {
        id
        name
        currentTeam {
          id
          name
        }
      }
    }
    pageInfo {
      totalCount
      startCursor
      hasPreviousPage
      endCursor
      hasNextPage
    }
  }
}
    `;
export const MyFollowingAgenciesDocument = gql`
    query myFollowingAgencies($pagination: Pagination) {
  myFollowingAgencies(pagination: $pagination) {
    edges {
      node {
        id
        ...Agency
      }
    }
    pageInfo {
      totalCount
      startCursor
      hasPreviousPage
      endCursor
      hasNextPage
    }
  }
}
    ${AgencyFragmentDoc}`;
export const MyFollowingTeamsDocument = gql`
    query myFollowingTeams($pagination: Pagination) {
  myFollowingTeams(pagination: $pagination) {
    edges {
      node {
        id
        ...Team
      }
    }
    pageInfo {
      totalCount
      startCursor
      hasPreviousPage
      endCursor
      hasNextPage
    }
  }
}
    ${TeamFragmentDoc}`;
export const NewUsersCountDocument = gql`
    query newUsersCount($country: String!) {
  newUsersCount(country: $country) {
    count
  }
}
    `;
export const OrganizationDocument = gql`
    query organization($slug: String, $organizationId: ObjectId) {
  organization(slug: $slug, id: $organizationId) {
    ...Organization
  }
}
    ${OrganizationFragmentDoc}`;
export const OrganizationTeamsDocument = gql`
    query organizationTeams($pagination: Pagination, $slug: String, $organizationId: ObjectId) {
  organizationTeams(
    pagination: $pagination
    slug: $slug
    organizationId: $organizationId
  ) {
    edges {
      node {
        id
        name
        organization {
          id
          name
          country
        }
      }
      playersCount
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
    `;
export const OrganizationsDocument = gql`
    query organizations($filter: OrganizationsFilter, $pagination: Pagination) {
  organizations(filter: $filter, pagination: $pagination) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        ...Organization
      }
    }
  }
}
    ${OrganizationFragmentDoc}`;
export const PlayersStatsListDocument = gql`
    query playersStatsList($filter: PlayersStatsFilter!, $first: Int, $after: String, $sort: SortInput) {
  playersStatsList(filter: $filter, first: $first, after: $after, sort: $sort) {
    edges {
      node {
        id
        user {
          id
          name
          slug
          firstname
          lastname
          role
          country
          bio {
            position
          }
        }
        team {
          id
          name
        }
        league {
          id
          name
        }
        goals
        assists
        points
        pim
        gamesPlayed
        svp
        gaa
        shutouts
        toi
        saves
        ga
        season
        externalInfo {
          externalLeagueCountry
          externalLeagueName
          externalTeamCountry
          externalTeamName
          externalPlayerName
          stage {
            value
            label
          }
        }
        position
      }
    }
    pageInfo {
      totalCount
    }
    leagueStages {
      league {
        id
        name
      }
      stages {
        value
        label
      }
    }
  }
}
    `;
export const RandomUsersDocument = gql`
    query randomUsers {
  randomUsers {
    edges {
      node {
        ...PublicUser
      }
    }
    totalCount
  }
}
    ${PublicUserFragmentDoc}`;
export const RecommendedUsersDocument = gql`
    query recommendedUsers($country: Country, $yearOfBirth: YearArguments, $inspired: Boolean, $activeUserId: String, $limit: Int) {
  recommendedUsers(
    country: $country
    yearOfBirth: $yearOfBirth
    inspired: $inspired
    activeUserId: $activeUserId
    limit: $limit
  ) {
    ...PublicUser
  }
}
    ${PublicUserFragmentDoc}`;
export const SearchUserStatsDocument = gql`
    query searchUserStats($filter: UserStatsFilter!, $pagination: Pagination) {
  searchUserStats(filter: $filter, pagination: $pagination) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        ...UserStats
      }
    }
  }
}
    ${UserStatsFragmentDoc}`;
export const SearchUsersDocument = gql`
    query searchUsers($filter: UsersFilter!, $pagination: Pagination) {
  searchUsers(filter: $filter, pagination: $pagination) {
    edges {
      node {
        id
        name
        lastname
        currentTeam {
          id
          name
        }
        role
        country
      }
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
    `;
export const StripeAccountDocument = gql`
    query stripeAccount {
  stripeAccount {
    id
    firstname
    lastname
    dob
    address {
      city
      line1
      line2
      postalCode
      state
    }
    currency
    phone
    email
    country
    payoutsEnabled
    paymentsEnabled
    ssnLast4Provided
    jobTitle
  }
}
    `;
export const StripeAccountBalanceDocument = gql`
    query stripeAccountBalance {
  stripeAccountBalance {
    balance {
      available {
        amount
        currency
      }
      pending {
        amount
        currency
      }
    }
    transactions {
      id
      createdAt
      payout {
        amount
        currency
      }
      status
    }
  }
}
    `;
export const StripeExternalAccountDocument = gql`
    query stripeExternalAccount {
  stripeExternalAccount {
    id
    bankAccount {
      country
      currency
      firstname
      lastname
    }
    routingNumber
    accountNumber
    status
  }
}
    `;
export const TeamDocument = gql`
    query team($pagination: Pagination, $orgSlug: String, $slug: String, $includeShadowPlayers: Boolean, $season: String, $teamId: ObjectId) {
  team(
    pagination: $pagination
    orgSlug: $orgSlug
    slug: $slug
    includeShadowPlayers: $includeShadowPlayers
    season: $season
    id: $teamId
  ) {
    errors {
      ... on TeamNotFoundError {
        message
        path
      }
    }
    team {
      id
      name
      hasGames
      organization {
        id
        name
      }
    }
    users {
      edges {
        node {
          id
          name
          currentTeam {
            id
            name
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
        hasPreviousPage
        startCursor
        totalCount
      }
    }
  }
}
    `;
export const TeamWithoutUserFieldsDocument = gql`
    query teamWithoutUserFields($pagination: Pagination, $orgSlug: String, $slug: String, $teamId: ObjectId) {
  team(pagination: $pagination, orgSlug: $orgSlug, slug: $slug, id: $teamId) {
    errors {
      ... on TeamNotFoundError {
        message
        path
      }
    }
    team {
      id
      name
      organization {
        id
        name
      }
    }
  }
}
    `;
export const TeamsDocument = gql`
    query teams($pagination: Pagination, $filter: TeamsFilter) {
  teams(pagination: $pagination, filter: $filter) {
    edges {
      node {
        id
        name
        country
        playersCount
        checked
        organization {
          name
        }
      }
      playersCount
    }
  }
}
    `;
export const TopViewedUsersDocument = gql`
    query topViewedUsers($period: Period!, $filter: TopViewedUsersFilter, $pagination: Pagination) {
  topViewedUsers(period: $period, filter: $filter, pagination: $pagination) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        id
        name
        country
        currentTeam {
          id
          name
        }
      }
      views
    }
    myPosition
  }
}
    `;
export const UserByIdQueryDocument = gql`
    query userByIdQuery($id: ObjectId!) {
  userById(id: $id) {
    ...PublicUser
  }
}
    ${PublicUserFragmentDoc}`;
export const UserCountDocument = gql`
    query userCount($days: Int) {
  admin {
    userCount(days: $days) {
      playersCount
      sportsProfsCount
      parentsCount
      sportsFansCount
      shadowPlayersCount
    }
  }
}
    `;
export const UserLikesPlayersGamesPerformanceDocument = gql`
    query userLikesPlayersGamesPerformance($input: UserLikesPlayersGamesPerformanceInput!) {
  userLikesPlayersGamesPerformance(input: $input) {
    playersByGame {
      id
      players {
        id
      }
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on EntityNotFoundError {
        message
        path
      }
    }
  }
}
    `;
export const UserLikesPlayersPerformanceDocument = gql`
    query userLikesPlayersPerformance($input: UserLikesPlayersPerformanceInput!) {
  userLikesPlayersPerformance(input: $input) {
    playersByGame {
      id
      players {
        id
      }
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
      ... on EntityNotFoundError {
        message
        path
      }
    }
  }
}
    `;
export const UserQueryDocument = gql`
    query userQuery($slug: String!) {
  user(slug: $slug) {
    ...CurrentUser
  }
}
    ${CurrentUserFragmentDoc}`;
export const UserStatisticsByYearAndCountryDocument = gql`
    query userStatisticsByYearAndCountry {
  admin {
    userStatisticsByYearAndCountry {
      usersByCountryAndYear {
        country
        year
        count
        target
        percentageOfTarget
      }
      totalUsers
    }
  }
}
    `;
export const UserStatsDocument = gql`
    query userStats($filter: UserStatsFilter, $pagination: Pagination) {
  userStats(filter: $filter, pagination: $pagination) {
    edges {
      node {
        ...UserStats
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
    ${UserStatsFragmentDoc}`;
export const UserViewStatsDocument = gql`
    query userViewStats {
  userViewsStats {
    weeklyStats {
      from
      to
      views
    }
  }
}
    `;
export const UsersByIdsQueryDocument = gql`
    query usersByIdsQuery($userIds: [ObjectId!]!) {
  usersByIds(userIds: $userIds) {
    ...PublicUser
  }
}
    ${PublicUserFragmentDoc}`;
export const UsersDocument = gql`
    query users($filter: AdminUsersFilter!, $pagination: Pagination) {
  users(filter: $filter, pagination: $pagination) {
    edges {
      node {
        id
        firstname
        lastname
        email
        country
        currentTeam {
          id
          name
        }
        recommended
        inspired
        moreSelected
        dateOfBirth
        avatar
      }
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
    `;
export const VideosByIdsDocument = gql`
    query videosByIds($input: VideosByIdsInput!) {
  videosByIds(input: $input) {
    videos {
      ...VideosByIdsVideo
    }
  }
}
    ${VideosByIdsVideoFragmentDoc}`;
export const VideosCountDocument = gql`
    query videosCount($days: Int) {
  admin {
    wallVideo {
      videosCount(days: $days) {
        defensiveVideosCount
        gameSaveVideosCount
        highlightVideosCount
        interviewVideosCount
        offensiveVideosCount
        otherVideosCount
        puckHandlingVideosCount
        trainingVideosCount
      }
    }
  }
}
    `;
export const VideosCountByTagsDocument = gql`
    query videosCountByTags {
  admin {
    wallVideo {
      videosCountByTags {
        videoTagsCount {
          count
          percentage
          tag
        }
        totalTaggedVideosCount
      }
    }
  }
}
    `;
export const WallVideosDocument = gql`
    query wallVideos($filter: WallVideosFilter, $first: Int, $last: Int, $after: String, $before: String) {
  wallVideos(
    filter: $filter
    includeCounts: true
    first: $first
    last: $last
    after: $after
    before: $before
  ) {
    edges {
      node {
        id
        path
        width
        height
      }
    }
    categoryCounts {
      category
      count
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      endCursor
      startCursor
    }
  }
}
    `;
export const WallVideosReelDocument = gql`
    query wallVideosReel($filter: WallVideosFilter, $first: Int, $last: Int, $after: String, $before: String) {
  wallVideosReel: wallVideos(
    filter: $filter
    includeCounts: true
    first: $first
    last: $last
    after: $after
    before: $before
  ) {
    edges {
      node {
        ...WallVideoReel
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
      totalCount
    }
    categoryCounts {
      category
      count
    }
  }
}
    ${WallVideoReelFragmentDoc}`;
export const SuggestInvitesDocument = gql`
    query suggestInvites($teamId: ObjectId!) {
  suggestInvites(teamId: $teamId) {
    edges {
      node {
        id
        name
        firstname
        lastname
        country
        role
        avatar
        avatars {
          tiny
          small
          medium
          large
        }
        dateOfBirth
        possibleYearsOfBirth
        bio {
          position
          handedness
          playerType
        }
      }
    }
  }
}
    `;

export type SdkFunctionWrapper = <T>(action: (requestHeaders?:Record<string, string>) => Promise<T>, operationName: string, operationType?: string, variables?: any) => Promise<T>;


const defaultWrapper: SdkFunctionWrapper = (action, _operationName, _operationType, _variables) => action();

export function getSdk(client: GraphQLClient, withWrapper: SdkFunctionWrapper = defaultWrapper) {
  return {
    syncAdminUserStats(variables: SyncAdminUserStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SyncAdminUserStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<SyncAdminUserStatsMutation>(SyncAdminUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'syncAdminUserStats', 'mutation', variables);
    },
    acceptAgencyInvitation(variables: AcceptAgencyInvitationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AcceptAgencyInvitationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<AcceptAgencyInvitationMutation>(AcceptAgencyInvitationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'acceptAgencyInvitation', 'mutation', variables);
    },
    addAgencyUser(variables: AddAgencyUserMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AddAgencyUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<AddAgencyUserMutation>(AddAgencyUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'addAgencyUser', 'mutation', variables);
    },
    addFamilyMember(variables: AddFamilyMemberMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AddFamilyMemberMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<AddFamilyMemberMutation>(AddFamilyMemberDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'addFamilyMember', 'mutation', variables);
    },
    cancelBatchNotification(variables: CancelBatchNotificationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CancelBatchNotificationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CancelBatchNotificationMutation>(CancelBatchNotificationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'cancelBatchNotification', 'mutation', variables);
    },
    createBatchNotification(variables: CreateBatchNotificationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateBatchNotificationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateBatchNotificationMutation>(CreateBatchNotificationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createBatchNotification', 'mutation', variables);
    },
    createCrawlerDomainConfig(variables: CreateCrawlerDomainConfigMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateCrawlerDomainConfigMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateCrawlerDomainConfigMutation>(CreateCrawlerDomainConfigDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createCrawlerDomainConfig', 'mutation', variables);
    },
    updateCrawlerDomainConfig(variables: UpdateCrawlerDomainConfigMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateCrawlerDomainConfigMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateCrawlerDomainConfigMutation>(UpdateCrawlerDomainConfigDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateCrawlerDomainConfig', 'mutation', variables);
    },
    deleteAllUserStats(variables: DeleteAllUserStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteAllUserStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteAllUserStatsMutation>(DeleteAllUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteAllUserStats', 'mutation', variables);
    },
    reScrape(variables: ReScrapeMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<ReScrapeMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<ReScrapeMutation>(ReScrapeDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'reScrape', 'mutation', variables);
    },
    removeLeagueLogo(variables: RemoveLeagueLogoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RemoveLeagueLogoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RemoveLeagueLogoMutation>(RemoveLeagueLogoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'removeLeagueLogo', 'mutation', variables);
    },
    uploadLeagueLogo(variables: UploadLeagueLogoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadLeagueLogoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadLeagueLogoMutation>(UploadLeagueLogoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadLeagueLogo', 'mutation', variables);
    },
    markUserAsDeleted(variables: MarkUserAsDeletedMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<MarkUserAsDeletedMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<MarkUserAsDeletedMutation>(MarkUserAsDeletedDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'markUserAsDeleted', 'mutation', variables);
    },
    sendPushNotification(variables: SendPushNotificationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SendPushNotificationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<SendPushNotificationMutation>(SendPushNotificationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'sendPushNotification', 'mutation', variables);
    },
    createOrganization(variables: CreateOrganizationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateOrganizationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateOrganizationMutation>(CreateOrganizationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createOrganization', 'mutation', variables);
    },
    removeOrganizationLogo(variables: RemoveOrganizationLogoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RemoveOrganizationLogoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RemoveOrganizationLogoMutation>(RemoveOrganizationLogoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'removeOrganizationLogo', 'mutation', variables);
    },
    updateOrganization(variables: UpdateOrganizationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateOrganizationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateOrganizationMutation>(UpdateOrganizationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateOrganization', 'mutation', variables);
    },
    syncAgencyClient(variables: SyncAgencyClientMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SyncAgencyClientMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<SyncAgencyClientMutation>(SyncAgencyClientDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'syncAgencyClient', 'mutation', variables);
    },
    syncSingleGameStats(variables: SyncSingleGameStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SyncSingleGameStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<SyncSingleGameStatsMutation>(SyncSingleGameStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'syncSingleGameStats', 'mutation', variables);
    },
    createTeam(variables: CreateTeamMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateTeamMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateTeamMutation>(CreateTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createTeam', 'mutation', variables);
    },
    removeTeamLogo(variables: RemoveTeamLogoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RemoveTeamLogoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RemoveTeamLogoMutation>(RemoveTeamLogoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'removeTeamLogo', 'mutation', variables);
    },
    updateTeam(variables: UpdateTeamMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateTeamMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateTeamMutation>(UpdateTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateTeam', 'mutation', variables);
    },
    updateBoostMessage(variables: UpdateBoostMessageMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateBoostMessageMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateBoostMessageMutation>(UpdateBoostMessageDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateBoostMessage', 'mutation', variables);
    },
    addExternalConnection(variables: AddExternalConnectionMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AddExternalConnectionMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<AddExternalConnectionMutation>(AddExternalConnectionDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'addExternalConnection', 'mutation', variables);
    },
    removeExternalConnection(variables: RemoveExternalConnectionMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RemoveExternalConnectionMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RemoveExternalConnectionMutation>(RemoveExternalConnectionDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'removeExternalConnection', 'mutation', variables);
    },
    uploadAvatarAdmin(variables: UploadAvatarAdminMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadAvatarAdminMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadAvatarAdminMutation>(UploadAvatarAdminDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadAvatarAdmin', 'mutation', variables);
    },
    deleteWallVideoAdmin(variables: DeleteWallVideoAdminMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteWallVideoAdminMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteWallVideoAdminMutation>(DeleteWallVideoAdminDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteWallVideoAdmin', 'mutation', variables);
    },
    appleSignIn(variables: AppleSignInMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AppleSignInMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<AppleSignInMutation>(AppleSignInDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'appleSignIn', 'mutation', variables);
    },
    changeTeamOrderDown(variables: ChangeTeamOrderDownMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<ChangeTeamOrderDownMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<ChangeTeamOrderDownMutation>(ChangeTeamOrderDownDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'changeTeamOrderDown', 'mutation', variables);
    },
    changeTeamOrderUp(variables: ChangeTeamOrderUpMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<ChangeTeamOrderUpMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<ChangeTeamOrderUpMutation>(ChangeTeamOrderUpDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'changeTeamOrderUp', 'mutation', variables);
    },
    confirmFamilyMember(variables: ConfirmFamilyMemberMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<ConfirmFamilyMemberMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<ConfirmFamilyMemberMutation>(ConfirmFamilyMemberDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'confirmFamilyMember', 'mutation', variables);
    },
    createAdmin(variables: CreateAdminMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateAdminMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateAdminMutation>(CreateAdminDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createAdmin', 'mutation', variables);
    },
    createAgency(variables: CreateAgencyMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateAgencyMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateAgencyMutation>(CreateAgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createAgency', 'mutation', variables);
    },
    createBoost(variables: CreateBoostMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateBoostMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateBoostMutation>(CreateBoostDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createBoost', 'mutation', variables);
    },
    createBoostMessage(variables?: CreateBoostMessageMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateBoostMessageMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateBoostMessageMutation>(CreateBoostMessageDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createBoostMessage', 'mutation', variables);
    },
    createChatUserToken(variables?: CreateChatUserTokenMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateChatUserTokenMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateChatUserTokenMutation>(CreateChatUserTokenDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createChatUserToken', 'mutation', variables);
    },
    createChatUsers(variables: CreateChatUsersMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateChatUsersMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateChatUsersMutation>(CreateChatUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createChatUsers', 'mutation', variables);
    },
    createLeague(variables: CreateLeagueMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateLeagueMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateLeagueMutation>(CreateLeagueDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createLeague', 'mutation', variables);
    },
    createPassiveEvent(variables: CreatePassiveEventMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreatePassiveEventMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreatePassiveEventMutation>(CreatePassiveEventDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createPassiveEvent', 'mutation', variables);
    },
    createStreamFeedUserToken(variables?: CreateStreamFeedUserTokenMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateStreamFeedUserTokenMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateStreamFeedUserTokenMutation>(CreateStreamFeedUserTokenDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createStreamFeedUserToken', 'mutation', variables);
    },
    createStripeAccount(variables: CreateStripeAccountMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateStripeAccountMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateStripeAccountMutation>(CreateStripeAccountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createStripeAccount', 'mutation', variables);
    },
    createStripeExternalAccount(variables: CreateStripeExternalAccountMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateStripeExternalAccountMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateStripeExternalAccountMutation>(CreateStripeExternalAccountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createStripeExternalAccount', 'mutation', variables);
    },
    createUserStats(variables: CreateUserStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CreateUserStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<CreateUserStatsMutation>(CreateUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'createUserStats', 'mutation', variables);
    },
    deleteAccount(variables?: DeleteAccountMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteAccountMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteAccountMutation>(DeleteAccountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteAccount', 'mutation', variables);
    },
    deleteAdmin(variables: DeleteAdminMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteAdminMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteAdminMutation>(DeleteAdminDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteAdmin', 'mutation', variables);
    },
    deleteAgency(variables: DeleteAgencyMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteAgencyMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteAgencyMutation>(DeleteAgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteAgency', 'mutation', variables);
    },
    deleteLeague(variables: DeleteLeagueMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteLeagueMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteLeagueMutation>(DeleteLeagueDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteLeague', 'mutation', variables);
    },
    deleteOrganization(variables: DeleteOrganizationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteOrganizationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteOrganizationMutation>(DeleteOrganizationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteOrganization', 'mutation', variables);
    },
    deleteStripeExternalAccount(variables?: DeleteStripeExternalAccountMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteStripeExternalAccountMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteStripeExternalAccountMutation>(DeleteStripeExternalAccountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteStripeExternalAccount', 'mutation', variables);
    },
    deleteTeam(variables: DeleteTeamMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteTeamMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteTeamMutation>(DeleteTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteTeam', 'mutation', variables);
    },
    deleteUserStats(variables: DeleteUserStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteUserStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteUserStatsMutation>(DeleteUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteUserStats', 'mutation', variables);
    },
    deleteWallVideo(variables: DeleteWallVideoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DeleteWallVideoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<DeleteWallVideoMutation>(DeleteWallVideoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'deleteWallVideo', 'mutation', variables);
    },
    editHighlights(variables: EditHighlightsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<EditHighlightsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<EditHighlightsMutation>(EditHighlightsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'editHighlights', 'mutation', variables);
    },
    editWallVideoCategory(variables: EditWallVideoCategoryMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<EditWallVideoCategoryMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<EditWallVideoCategoryMutation>(EditWallVideoCategoryDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'editWallVideoCategory', 'mutation', variables);
    },
    fetchUserDataEP(variables?: FetchUserDataEpMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<FetchUserDataEpMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<FetchUserDataEpMutation>(FetchUserDataEpDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'fetchUserDataEP', 'mutation', variables);
    },
    followAgency(variables: FollowAgencyMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<FollowAgencyMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<FollowAgencyMutation>(FollowAgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'followAgency', 'mutation', variables);
    },
    followTeam(variables: FollowTeamMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<FollowTeamMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<FollowTeamMutation>(FollowTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'followTeam', 'mutation', variables);
    },
    followUser(variables: FollowUserMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<FollowUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<FollowUserMutation>(FollowUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'followUser', 'mutation', variables);
    },
    googleSignIn(variables: GoogleSignInMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GoogleSignInMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<GoogleSignInMutation>(GoogleSignInDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'googleSignIn', 'mutation', variables);
    },
    googleSignInAdmin(variables: GoogleSignInAdminMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GoogleSignInAdminMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<GoogleSignInAdminMutation>(GoogleSignInAdminDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'googleSignInAdmin', 'mutation', variables);
    },
    inviteAgents(variables: InviteAgentsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<InviteAgentsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<InviteAgentsMutation>(InviteAgentsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'inviteAgents', 'mutation', variables);
    },
    likePlayerPerformance(variables: LikePlayerPerformanceMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<LikePlayerPerformanceMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<LikePlayerPerformanceMutation>(LikePlayerPerformanceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'likePlayerPerformance', 'mutation', variables);
    },
    likeWallVideo(variables: LikeWallVideoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<LikeWallVideoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<LikeWallVideoMutation>(LikeWallVideoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'likeWallVideo', 'mutation', variables);
    },
    loginAdmin(variables: LoginAdminMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<LoginAdminMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<LoginAdminMutation>(LoginAdminDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'loginAdmin', 'mutation', variables);
    },
    migrateUserStats(variables: MigrateUserStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<MigrateUserStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<MigrateUserStatsMutation>(MigrateUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'migrateUserStats', 'mutation', variables);
    },
    pushNotifications(variables: PushNotificationsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<PushNotificationsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<PushNotificationsMutation>(PushNotificationsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'pushNotifications', 'mutation', variables);
    },
    registerDevice(variables: RegisterDeviceMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RegisterDeviceMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RegisterDeviceMutation>(RegisterDeviceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'registerDevice', 'mutation', variables);
    },
    registerOrLoginByEmail(variables: RegisterOrLoginByEmailMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RegisterOrLoginByEmailMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RegisterOrLoginByEmailMutation>(RegisterOrLoginByEmailDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'registerOrLoginByEmail', 'mutation', variables);
    },
    removeAgencyUser(variables: RemoveAgencyUserMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RemoveAgencyUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RemoveAgencyUserMutation>(RemoveAgencyUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'removeAgencyUser', 'mutation', variables);
    },
    removeFamilyMember(variables: RemoveFamilyMemberMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RemoveFamilyMemberMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RemoveFamilyMemberMutation>(RemoveFamilyMemberDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'removeFamilyMember', 'mutation', variables);
    },
    removePendingFamilyMember(variables: RemovePendingFamilyMemberMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RemovePendingFamilyMemberMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<RemovePendingFamilyMemberMutation>(RemovePendingFamilyMemberDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'removePendingFamilyMember', 'mutation', variables);
    },
    sendContactMessage(variables?: SendContactMessageMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SendContactMessageMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<SendContactMessageMutation>(SendContactMessageDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'sendContactMessage', 'mutation', variables);
    },
    syncAgencyClients(variables: SyncAgencyClientsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SyncAgencyClientsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<SyncAgencyClientsMutation>(SyncAgencyClientsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'syncAgencyClients', 'mutation', variables);
    },
    syncUserStats(variables: SyncUserStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SyncUserStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<SyncUserStatsMutation>(SyncUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'syncUserStats', 'mutation', variables);
    },
    unfollowAgency(variables: UnfollowAgencyMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UnfollowAgencyMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UnfollowAgencyMutation>(UnfollowAgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'unfollowAgency', 'mutation', variables);
    },
    unfollowTeam(variables: UnfollowTeamMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UnfollowTeamMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UnfollowTeamMutation>(UnfollowTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'unfollowTeam', 'mutation', variables);
    },
    unfollowUser(variables: UnfollowUserMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UnfollowUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UnfollowUserMutation>(UnfollowUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'unfollowUser', 'mutation', variables);
    },
    unlikePlayerPerformance(variables: UnlikePlayerPerformanceMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UnlikePlayerPerformanceMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UnlikePlayerPerformanceMutation>(UnlikePlayerPerformanceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'unlikePlayerPerformance', 'mutation', variables);
    },
    unregisterDevice(variables: UnregisterDeviceMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UnregisterDeviceMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UnregisterDeviceMutation>(UnregisterDeviceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'unregisterDevice', 'mutation', variables);
    },
    updateAgency(variables: UpdateAgencyMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateAgencyMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateAgencyMutation>(UpdateAgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateAgency', 'mutation', variables);
    },
    updateLeague(variables: UpdateLeagueMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateLeagueMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateLeagueMutation>(UpdateLeagueDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateLeague', 'mutation', variables);
    },
    updateStripeAccount(variables: UpdateStripeAccountMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateStripeAccountMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateStripeAccountMutation>(UpdateStripeAccountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateStripeAccount', 'mutation', variables);
    },
    updateUser(variables: UpdateUserMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateUserMutation>(UpdateUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateUser', 'mutation', variables);
    },
    updateUserAdmin(variables: UpdateUserAdminMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateUserAdminMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateUserAdminMutation>(UpdateUserAdminDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateUserAdmin', 'mutation', variables);
    },
    updateUserCurrency(variables: UpdateUserCurrencyMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateUserCurrencyMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateUserCurrencyMutation>(UpdateUserCurrencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateUserCurrency', 'mutation', variables);
    },
    updateUserStats(variables: UpdateUserStatsMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateUserStatsMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateUserStatsMutation>(UpdateUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateUserStats', 'mutation', variables);
    },
    updateUserWithAgencyUnverified(variables: UpdateUserWithAgencyUnverifiedMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateUserWithAgencyUnverifiedMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateUserWithAgencyUnverifiedMutation>(UpdateUserWithAgencyUnverifiedDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateUserWithAgencyUnverified', 'mutation', variables);
    },
    updateWallVideo(variables: UpdateWallVideoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UpdateWallVideoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UpdateWallVideoMutation>(UpdateWallVideoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'updateWallVideo', 'mutation', variables);
    },
    uploadAgencyLogo(variables: UploadAgencyLogoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadAgencyLogoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadAgencyLogoMutation>(UploadAgencyLogoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadAgencyLogo', 'mutation', variables);
    },
    uploadAvatar(variables?: UploadAvatarMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadAvatarMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadAvatarMutation>(UploadAvatarDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadAvatar', 'mutation', variables);
    },
    uploadOrganizationLogo(variables: UploadOrganizationLogoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadOrganizationLogoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadOrganizationLogoMutation>(UploadOrganizationLogoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadOrganizationLogo', 'mutation', variables);
    },
    uploadTeamLogo(variables: UploadTeamLogoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadTeamLogoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadTeamLogoMutation>(UploadTeamLogoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadTeamLogo', 'mutation', variables);
    },
    uploadWallVideo(variables?: UploadWallVideoMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadWallVideoMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadWallVideoMutation>(UploadWallVideoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadWallVideo', 'mutation', variables);
    },
    uploadWallVideoSegmentation(variables: UploadWallVideoSegmentationMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UploadWallVideoSegmentationMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<UploadWallVideoSegmentationMutation>(UploadWallVideoSegmentationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'uploadWallVideoSegmentation', 'mutation', variables);
    },
    verifyAdminByEmail(variables: VerifyAdminByEmailMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VerifyAdminByEmailMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<VerifyAdminByEmailMutation>(VerifyAdminByEmailDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'verifyAdminByEmail', 'mutation', variables);
    },
    verifyByEmail(variables: VerifyByEmailMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VerifyByEmailMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<VerifyByEmailMutation>(VerifyByEmailDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'verifyByEmail', 'mutation', variables);
    },
    visitAgency(variables: VisitAgencyMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VisitAgencyMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<VisitAgencyMutation>(VisitAgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'visitAgency', 'mutation', variables);
    },
    visitTeam(variables: VisitTeamMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VisitTeamMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<VisitTeamMutation>(VisitTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'visitTeam', 'mutation', variables);
    },
    visitUser(variables: VisitUserMutationVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VisitUserMutation> {
      return withWrapper((wrappedRequestHeaders) => client.request<VisitUserMutation>(VisitUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'visitUser', 'mutation', variables);
    },
    getBatchNotifications(variables?: GetBatchNotificationsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetBatchNotificationsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetBatchNotificationsQuery>(GetBatchNotificationsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getBatchNotifications', 'query', variables);
    },
    boosts(variables?: BoostsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<BoostsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<BoostsQuery>(BoostsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'boosts', 'query', variables);
    },
    getCrawlerDomainConfig(variables: GetCrawlerDomainConfigQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetCrawlerDomainConfigQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetCrawlerDomainConfigQuery>(GetCrawlerDomainConfigDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getCrawlerDomainConfig', 'query', variables);
    },
    getCrawlerDomainConfigs(variables?: GetCrawlerDomainConfigsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetCrawlerDomainConfigsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetCrawlerDomainConfigsQuery>(GetCrawlerDomainConfigsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getCrawlerDomainConfigs', 'query', variables);
    },
    getCrawlerLogsItem(variables: GetCrawlerLogsItemQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetCrawlerLogsItemQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetCrawlerLogsItemQuery>(GetCrawlerLogsItemDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getCrawlerLogsItem', 'query', variables);
    },
    getCrawlerLogs(variables?: GetCrawlerLogsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetCrawlerLogsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetCrawlerLogsQuery>(GetCrawlerLogsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getCrawlerLogs', 'query', variables);
    },
    admin_crawlerRunStats_get(variables: Admin_CrawlerRunStats_GetQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<Admin_CrawlerRunStats_GetQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<Admin_CrawlerRunStats_GetQuery>(Admin_CrawlerRunStats_GetDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'admin_crawlerRunStats_get', 'query', variables);
    },
    admin_crawlerRunStats_list(variables?: Admin_CrawlerRunStats_ListQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<Admin_CrawlerRunStats_ListQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<Admin_CrawlerRunStats_ListQuery>(Admin_CrawlerRunStats_ListDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'admin_crawlerRunStats_list', 'query', variables);
    },
    discoveredUrlList(variables?: DiscoveredUrlListQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<DiscoveredUrlListQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<DiscoveredUrlListQuery>(DiscoveredUrlListDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'discoveredUrlList', 'query', variables);
    },
    adminGameGet(variables: AdminGameGetQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminGameGetQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminGameGetQuery>(AdminGameGetDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminGameGet', 'query', variables);
    },
    adminGameList(variables?: AdminGameListQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminGameListQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminGameListQuery>(AdminGameListDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminGameList', 'query', variables);
    },
    getLeague(variables: GetLeagueQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetLeagueQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetLeagueQuery>(GetLeagueDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getLeague', 'query', variables);
    },
    adminLeagueList(variables?: AdminLeagueListQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminLeagueListQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminLeagueListQuery>(AdminLeagueListDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminLeagueList', 'query', variables);
    },
    adminLeagueStatsList(variables: AdminLeagueStatsListQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminLeagueStatsListQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminLeagueStatsListQuery>(AdminLeagueStatsListDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminLeagueStatsList', 'query', variables);
    },
    getNotifications(variables?: GetNotificationsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GetNotificationsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GetNotificationsQuery>(GetNotificationsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'getNotifications', 'query', variables);
    },
    userInfo(variables: UserInfoQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserInfoQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserInfoQuery>(UserInfoDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userInfo', 'query', variables);
    },
    adminWallVideos(variables?: AdminWallVideosQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminWallVideosQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminWallVideosQuery>(AdminWallVideosDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminWallVideos', 'query', variables);
    },
    adminUser(variables: AdminUserQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminUserQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminUserQuery>(AdminUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminUser', 'query', variables);
    },
    adminUserStats(variables: AdminUserStatsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminUserStatsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminUserStatsQuery>(AdminUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminUserStats', 'query', variables);
    },
    adminUserStatsList(variables?: AdminUserStatsListQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminUserStatsListQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminUserStatsListQuery>(AdminUserStatsListDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminUserStatsList', 'query', variables);
    },
    adminUsers(variables?: AdminUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AdminUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AdminUsersQuery>(AdminUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'adminUsers', 'query', variables);
    },
    agencies(variables?: AgenciesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AgenciesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AgenciesQuery>(AgenciesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'agencies', 'query', variables);
    },
    agency(variables?: AgencyQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AgencyQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AgencyQuery>(AgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'agency', 'query', variables);
    },
    agencyUsers(variables: AgencyUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AgencyUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AgencyUsersQuery>(AgencyUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'agencyUsers', 'query', variables);
    },
    agentsClients(variables: AgentsClientsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<AgentsClientsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<AgentsClientsQuery>(AgentsClientsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'agentsClients', 'query', variables);
    },
    boostsQuery(variables?: BoostsQueryQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<BoostsQueryQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<BoostsQueryQuery>(BoostsQueryDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'boostsQuery', 'query', variables);
    },
    currentAdminUser(variables?: CurrentAdminUserQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CurrentAdminUserQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<CurrentAdminUserQuery>(CurrentAdminUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'currentAdminUser', 'query', variables);
    },
    currentUser(variables?: CurrentUserQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CurrentUserQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<CurrentUserQuery>(CurrentUserDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'currentUser', 'query', variables);
    },
    currentUserFollowings(variables?: CurrentUserFollowingsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CurrentUserFollowingsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<CurrentUserFollowingsQuery>(CurrentUserFollowingsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'currentUserFollowings', 'query', variables);
    },
    currentUserWithAgencyUnverified(variables?: CurrentUserWithAgencyUnverifiedQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<CurrentUserWithAgencyUnverifiedQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<CurrentUserWithAgencyUnverifiedQuery>(CurrentUserWithAgencyUnverifiedDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'currentUserWithAgencyUnverified', 'query', variables);
    },
    entityCounts(variables?: EntityCountsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<EntityCountsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<EntityCountsQuery>(EntityCountsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'entityCounts', 'query', variables);
    },
    epTeam(variables: EpTeamQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<EpTeamQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<EpTeamQuery>(EpTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'epTeam', 'query', variables);
    },
    epTeams(variables: EpTeamsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<EpTeamsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<EpTeamsQuery>(EpTeamsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'epTeams', 'query', variables);
    },
    exampleUsers(variables?: ExampleUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<ExampleUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<ExampleUsersQuery>(ExampleUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'exampleUsers', 'query', variables);
    },
    exampleWallVideos(variables?: ExampleWallVideosQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<ExampleWallVideosQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<ExampleWallVideosQuery>(ExampleWallVideosDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'exampleWallVideos', 'query', variables);
    },
    game(variables: GameQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GameQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GameQuery>(GameDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'game', 'query', variables);
    },
    gameEvents(variables: GameEventsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GameEventsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GameEventsQuery>(GameEventsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'gameEvents', 'query', variables);
    },
    games(variables?: GamesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GamesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GamesQuery>(GamesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'games', 'query', variables);
    },
    gamesPlayers(variables: GamesPlayersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<GamesPlayersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<GamesPlayersQuery>(GamesPlayersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'gamesPlayers', 'query', variables);
    },
    inspiredUsers(variables?: InspiredUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<InspiredUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<InspiredUsersQuery>(InspiredUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'inspiredUsers', 'query', variables);
    },
    isFollowing(variables: IsFollowingQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<IsFollowingQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<IsFollowingQuery>(IsFollowingDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'isFollowing', 'query', variables);
    },
    isFollowingAgency(variables: IsFollowingAgencyQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<IsFollowingAgencyQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<IsFollowingAgencyQuery>(IsFollowingAgencyDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'isFollowingAgency', 'query', variables);
    },
    isFollowingTeam(variables: IsFollowingTeamQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<IsFollowingTeamQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<IsFollowingTeamQuery>(IsFollowingTeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'isFollowingTeam', 'query', variables);
    },
    latestClips(variables?: LatestClipsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<LatestClipsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<LatestClipsQuery>(LatestClipsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'latestClips', 'query', variables);
    },
    league(variables: LeagueQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<LeagueQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<LeagueQuery>(LeagueDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'league', 'query', variables);
    },
    leagueStandings(variables?: LeagueStandingsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<LeagueStandingsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<LeagueStandingsQuery>(LeagueStandingsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'leagueStandings', 'query', variables);
    },
    leagues(variables?: LeaguesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<LeaguesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<LeaguesQuery>(LeaguesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'leagues', 'query', variables);
    },
    moreSelectedUsers(variables?: MoreSelectedUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<MoreSelectedUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MoreSelectedUsersQuery>(MoreSelectedUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'moreSelectedUsers', 'query', variables);
    },
    myFollowers(variables: MyFollowersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<MyFollowersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MyFollowersQuery>(MyFollowersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'myFollowers', 'query', variables);
    },
    myFollowing(variables?: MyFollowingQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<MyFollowingQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MyFollowingQuery>(MyFollowingDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'myFollowing', 'query', variables);
    },
    myFollowingAgencies(variables?: MyFollowingAgenciesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<MyFollowingAgenciesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MyFollowingAgenciesQuery>(MyFollowingAgenciesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'myFollowingAgencies', 'query', variables);
    },
    myFollowingTeams(variables?: MyFollowingTeamsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<MyFollowingTeamsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<MyFollowingTeamsQuery>(MyFollowingTeamsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'myFollowingTeams', 'query', variables);
    },
    newUsersCount(variables: NewUsersCountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<NewUsersCountQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<NewUsersCountQuery>(NewUsersCountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'newUsersCount', 'query', variables);
    },
    organization(variables?: OrganizationQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<OrganizationQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<OrganizationQuery>(OrganizationDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'organization', 'query', variables);
    },
    organizationTeams(variables?: OrganizationTeamsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<OrganizationTeamsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<OrganizationTeamsQuery>(OrganizationTeamsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'organizationTeams', 'query', variables);
    },
    organizations(variables?: OrganizationsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<OrganizationsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<OrganizationsQuery>(OrganizationsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'organizations', 'query', variables);
    },
    playersStatsList(variables: PlayersStatsListQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<PlayersStatsListQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<PlayersStatsListQuery>(PlayersStatsListDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'playersStatsList', 'query', variables);
    },
    randomUsers(variables?: RandomUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RandomUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<RandomUsersQuery>(RandomUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'randomUsers', 'query', variables);
    },
    recommendedUsers(variables?: RecommendedUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<RecommendedUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<RecommendedUsersQuery>(RecommendedUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'recommendedUsers', 'query', variables);
    },
    searchUserStats(variables: SearchUserStatsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SearchUserStatsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<SearchUserStatsQuery>(SearchUserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'searchUserStats', 'query', variables);
    },
    searchUsers(variables: SearchUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SearchUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<SearchUsersQuery>(SearchUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'searchUsers', 'query', variables);
    },
    stripeAccount(variables?: StripeAccountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<StripeAccountQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<StripeAccountQuery>(StripeAccountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'stripeAccount', 'query', variables);
    },
    stripeAccountBalance(variables?: StripeAccountBalanceQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<StripeAccountBalanceQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<StripeAccountBalanceQuery>(StripeAccountBalanceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'stripeAccountBalance', 'query', variables);
    },
    stripeExternalAccount(variables?: StripeExternalAccountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<StripeExternalAccountQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<StripeExternalAccountQuery>(StripeExternalAccountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'stripeExternalAccount', 'query', variables);
    },
    team(variables?: TeamQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<TeamQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<TeamQuery>(TeamDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'team', 'query', variables);
    },
    teamWithoutUserFields(variables?: TeamWithoutUserFieldsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<TeamWithoutUserFieldsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<TeamWithoutUserFieldsQuery>(TeamWithoutUserFieldsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'teamWithoutUserFields', 'query', variables);
    },
    teams(variables?: TeamsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<TeamsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<TeamsQuery>(TeamsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'teams', 'query', variables);
    },
    topViewedUsers(variables: TopViewedUsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<TopViewedUsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<TopViewedUsersQuery>(TopViewedUsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'topViewedUsers', 'query', variables);
    },
    userByIdQuery(variables: UserByIdQueryQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserByIdQueryQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserByIdQueryQuery>(UserByIdQueryDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userByIdQuery', 'query', variables);
    },
    userCount(variables?: UserCountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserCountQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserCountQuery>(UserCountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userCount', 'query', variables);
    },
    userLikesPlayersGamesPerformance(variables: UserLikesPlayersGamesPerformanceQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserLikesPlayersGamesPerformanceQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserLikesPlayersGamesPerformanceQuery>(UserLikesPlayersGamesPerformanceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userLikesPlayersGamesPerformance', 'query', variables);
    },
    userLikesPlayersPerformance(variables: UserLikesPlayersPerformanceQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserLikesPlayersPerformanceQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserLikesPlayersPerformanceQuery>(UserLikesPlayersPerformanceDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userLikesPlayersPerformance', 'query', variables);
    },
    userQuery(variables: UserQueryQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserQueryQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserQueryQuery>(UserQueryDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userQuery', 'query', variables);
    },
    userStatisticsByYearAndCountry(variables?: UserStatisticsByYearAndCountryQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserStatisticsByYearAndCountryQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserStatisticsByYearAndCountryQuery>(UserStatisticsByYearAndCountryDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userStatisticsByYearAndCountry', 'query', variables);
    },
    userStats(variables?: UserStatsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserStatsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserStatsQuery>(UserStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userStats', 'query', variables);
    },
    userViewStats(variables?: UserViewStatsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UserViewStatsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UserViewStatsQuery>(UserViewStatsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'userViewStats', 'query', variables);
    },
    usersByIdsQuery(variables: UsersByIdsQueryQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UsersByIdsQueryQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UsersByIdsQueryQuery>(UsersByIdsQueryDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'usersByIdsQuery', 'query', variables);
    },
    users(variables: UsersQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<UsersQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<UsersQuery>(UsersDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'users', 'query', variables);
    },
    videosByIds(variables: VideosByIdsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VideosByIdsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<VideosByIdsQuery>(VideosByIdsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'videosByIds', 'query', variables);
    },
    videosCount(variables?: VideosCountQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VideosCountQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<VideosCountQuery>(VideosCountDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'videosCount', 'query', variables);
    },
    videosCountByTags(variables?: VideosCountByTagsQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<VideosCountByTagsQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<VideosCountByTagsQuery>(VideosCountByTagsDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'videosCountByTags', 'query', variables);
    },
    wallVideos(variables?: WallVideosQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<WallVideosQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<WallVideosQuery>(WallVideosDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'wallVideos', 'query', variables);
    },
    wallVideosReel(variables?: WallVideosReelQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<WallVideosReelQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<WallVideosReelQuery>(WallVideosReelDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'wallVideosReel', 'query', variables);
    },
    suggestInvites(variables: SuggestInvitesQueryVariables, requestHeaders?: GraphQLClientRequestHeaders): Promise<SuggestInvitesQuery> {
      return withWrapper((wrappedRequestHeaders) => client.request<SuggestInvitesQuery>(SuggestInvitesDocument, variables, {...requestHeaders, ...wrappedRequestHeaders}), 'suggestInvites', 'query', variables);
    }
  };
}
export type Sdk = ReturnType<typeof getSdk>;