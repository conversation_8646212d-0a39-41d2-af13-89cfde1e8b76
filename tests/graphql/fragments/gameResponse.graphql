fragment GameResponse on GameStats {
  id
  date
  season
  externalId
  externalInfo {
    homeTeamName
    awayTeamName
    leagueName
  }
  stats {
    homeTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    awayTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    periodScores
    scores {
      homeTeam
      awayTeam
      type
    }
  }
  events {
    time
    isHomeTeam
    type
    score
    penaltyTime
    description
    descriptionCode
    assists {
      playerName
      playerExternalId
    }
    externalInfo {
      teamName
      playerName
      playerExternalId
    }
    player {
      id
      name
    }
  }
  league {
    id
    name
  }
  homeTeam {
    id
    name
    logo
    slug
    organization {
      id
      name
      logo
    }
  }
  awayTeam {
    id
    name
    logo
    slug
    organization {
      id
      name
      logo
    }
  }
  homeTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
      id
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
  awayTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
}
