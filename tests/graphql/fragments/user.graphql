fragment User on User {
  id
  role
  subrole
  email
  name
  firstname
  lastname
  slug
  dateOfBirth
  country
  highlights {
    id
    path
  }
  contact {
    firstname
    lastname
    email
    phoneNumber {
      countryPrefix
      number
    }
    type
  }
  bio {
    position
    handedness
    playerType
    schoolType
    agency {
      id
    }
    gender
    height {
      feet
      inches
      centimeters
    }
    weight {
      pounds
      kilograms
    }
    birthplace
    nameOfSchool
    graduationYear
    intro
    cumulativeGPA
    coreGPA
    unweightedGPA
    weightedGPA
    satScore
    actScore
    toeflScore
    ncaaEligibility
    naiaEligibility
  }
  phoneNumber {
    countryPrefix
    number
  }
  externalLinks {
    name
    url
    type
  }
  boostMessage
  boost {
    currency
    goalAmount
  }
  role
  currentTeam {
    id
    name
    organization {
      id
      name
    }
  }
  registeredDevices {
    arn
    platform
    token
  }
  verificationNeeded
  verificationPending
  accountUpdateErrors {
    code
    reason
    requirement
  }
  clientStaticConfig {
    inviteContest {
      enabled
    }
  }
  subroleSpecifications {
    type
    team {
      id
      name
      organization {
        id
        name
      }
    }
    current
  }
}

fragment UserWithAgencyUnverified on User {
  id
  bio {
    agency {
      id
      name
    }
    agencyUnverified {
      id
      name
    }
  }
}

fragment PublicUser on User {
  id
  role
  name
  firstname
  lastname
  slug
  dateOfBirth
  country
  inspired
  createdAt
  following
  hasGames
  highlights {
    id
    path
  }
  contact {
    firstname
    lastname
    type
  }
  bio {
    position
    handedness
    playerType
    schoolType
    agency {
      id
    }
    gender
    height {
      feet
      inches
      centimeters
    }
    weight {
      pounds
      kilograms
    }
    birthplace
    nameOfSchool
    graduationYear
    cumulativeGPA
    coreGPA
    unweightedGPA
    weightedGPA
    satScore
    actScore
    toeflScore
    ncaaEligibility
    naiaEligibility
  }

  externalLinks {
    name
    url
    type
  }
  externalConnections {
    ...ExternalConnection
  }
  boostMessage
  boost {
    currency
    goalAmount
  }
  role
  currentTeam {
    id
    name
    organization {
      id
      name
    }
  }
  registeredDevices {
    arn
    platform
    token
  }
  verificationNeeded
  verificationPending
  accountUpdateErrors {
    code
    reason
    requirement
  }

  stats {
    season {
      ...AggregatedStatsItem
    }
    career {
      ...AggregatedStatsItem
    }
  }
}

fragment AggregatedStatsItem on AggregatedStatsItem {
  id
  gamesPlayed
  goals
  assists
  points
  pointsPerGame

  wins
  losses
  ties
  shutouts
  gaa
  svp
}

fragment ExampleUser on User {
  id
  name
  currentTeam {
    id
    name
  }
  avatars {
    small
    large
  }
  slug

  highlights {
    id
    path
  }
}


fragment CurrentUser on User {
  ...PublicUser

  familyMembers {
    id
    name
    avatars {
      small
    }
    slug
    role
    subrole
    country
    dateOfBirth

    bio {
      position
      playerType
      agency {
        id
        name
      }
    }

    currentTeam {
      id
      name
      logo
      country
      shortNameSlug
      organization {
        id
        name
        slug
        logo
        country
      }
    }
  }
}