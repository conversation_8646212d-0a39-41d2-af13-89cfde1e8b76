fragment GameListItem on GameStats {
  id
  date
  externalId
  ignore
  externalInfo {
    homeTeamName
    awayTeamName
    leagueName
  }
  stats {
    homeTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    awayTeam {
      score
      shotsOnGoal
      blockedShots
      faceoffs
      powerPlayGoals
      shortHandedGoals
      penalties
      majorPenalties
      penaltyMinutes
      goalieSaves
    }
    periodScores
  }
  league {
    id
    name
  }
  homeTeam {
    id
    name
  }
  awayTeam {
    id
    name
  }
}


fragment GameDetailWithPlayers on GameStats {
  id
  homeTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
      id
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
  awayTeamRoster {
    externalInfo {
      playerName
      playerExternalId
    }
    player {
      name
    }
    goals
    assists
    points
    pim
    plusMinus
    saves
    goalsAgainst
    svp
    jerseyNumber
    position
  }
}