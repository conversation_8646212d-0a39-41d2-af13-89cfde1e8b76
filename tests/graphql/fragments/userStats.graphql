fragment UserStats on UserStats {
  id
  user {
    id
    name
    role
  }
  team {
    name
    id
    country
    shortName
    shortNameSlug
    slug
  }
  league {
    name
    id
    country
  }
  seasonType
  season
  gamesPlayed
  assists
  goals
  pim
  plusMinus
  wins
  losses
  ties
  gaa
  svp
  shutouts
  orderInSeason
  postSeasonStats {
    gamesPlayed
    assists
    goals
    pim
    plusMinus
    wins
    losses
    ties
    gaa
    svp
    shutouts
  }
  externalInfo {
    externalTeamName
    externalTeamCountry
    externalLeagueName
    externalLeagueCountry
  }
}