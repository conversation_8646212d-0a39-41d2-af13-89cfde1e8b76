fragment VideosByIdsVideo on WallVideo {
  id
  path
  thumbnail
  width
  height
  hls
  hlsPath
  size
  title
  duration
  type
  createdAt
  frameRate
  user {
    ...VideosByIdsUser
  }
  segmentation {
    path
    finished
  }
}

fragment VideosByIdsUser on User {
  id
  name
  firstname
  lastname
  following
  role
  subrole
  slug
  country
  dateOfBirth
  avatars {
    small
  }
  currentTeam {
    id
    name
  }
  bio {
    position
    playerType
  }
  stats {
    ...AggregatedStats
  }
}
