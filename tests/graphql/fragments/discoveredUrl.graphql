fragment DiscoveredUrlList on DiscoveredUrl {
    id
    url
    entity
    crawlerDomainConfig {
              id
              domain
              urlConfigs {
                urlPatternRegExp
                entity
                initialUrls
                externalIdPatternRegExp
                paramExtractors {
                  selector
                  queryParam
                }
                allowedQueryParams
                extractionScript
              }
            }
            externalId
            scraperInfo {
              lastScrapedAt
              scheduledForScrapeAt
              scrapeAttempts
              unsuccessfulScrapes
              scrapeSuccessful
    }
}