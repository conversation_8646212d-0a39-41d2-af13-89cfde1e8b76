mutation updateUserAdmin($input: UpdateUserInputAdmin!, $updateUserAdminId: ObjectId!) {
  updateUserAdmin(input: $input, id: $updateUserAdminId) {
    user {
      id
      name
      originalFirstname
      firstname
      originalLastname
      lastname
      email
      slug
      dateOfBirth
      country
      contact {
        name
        firstname
        lastname
        email
        phoneNumber {
          countryPrefix
          number
        }
        type
      }
      agentV2 {
        name
        firstname
        lastname
        email
        phoneNumber {
          countryPrefix
          number
        }
      }
      phoneNumber {
        countryPrefix
        number
      }
      bio {
        position
        handedness
        playerType
        schoolType
        agency {
          id
          name
          country
          logo
          checked
        }
        gender
        height {
          feet
          inches
          centimeters
        }
        weight {
          pounds
          kilograms
        }
        birthplace
        nameOfSchool
        graduationYear
        cumulativeGPA
        coreGPA
        unweightedGPA
        weightedGPA
        satScore
        actScore
        toeflScore
        ncaaEligibility
        naiaEligibility
      }
      avatar
      avatars {
        tiny
        small
        medium
        large
      }
      externalLinks {
        id
        name
        url
        type
      }
      boostMessage
      currentTeam {
        id
        name
      }
      epCurrentTeam
      stripeAccountId
      stripeExternalAccountId
      boost {
        currency
        goalAmount
        currentAmount
      }
      recommended
      inspired
      moreSelected
      role
      subrole
      userStats {
        followers
        boosts
        views
      }
      public
      premium
      premiumSince
      searchEnabled
      isAdult
      registeredDevices {
        token
        platform
        arn
      }
      pushEnabled
      highlights {
        id
        type
        title
        path
        thumbnail
        size
        width
        height
        duration
        hls
        hlsPath
        createdAt
      }
      numberOfVideos
      createdAt
    }
    errors {
      ... on UserNotFoundError {
        message
        path
      }
    }
  }
}
