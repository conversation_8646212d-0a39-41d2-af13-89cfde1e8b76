mutation acceptAgencyInvitation($token: String!, $agencyId: ObjectId!) {
  acceptAgencyInvitation(token: $token, agencyId: $agencyId) {
    success
    errors {
      ... on AcceptAgencyInvitationInvalidTokenError {
        message
        path
      }
      ... on AcceptAgencyInvitationExpiredError {
        message
        path
      }
      ... on AcceptAgencyInvitationAlreadyAcceptedError {
        message
        path
      }
    }
  }
}