mutation createStripeAccount($input: CreateStripeAccountInput!) {
  createStripeAccount(input: $input) {
    account {
      id
      firstname
      email
      lastname
      paymentsEnabled
      payoutsEnabled
      phone
      ssnLast4Provided
      dob
      currency
      country
      address {
        city
        line1
        line2
        postalCode
        state
      }
      jobTitle
    }
    errors {
      ... on AccountAlreadyExistsError {
        message
        path
      }
      ... on StripeAccountUserInputError {
        message
        path
      }
    }
  }
}