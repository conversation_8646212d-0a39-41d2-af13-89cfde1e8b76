mutation uploadTeamLogo($uploadTeamLogoId: ObjectId!, $input: UploadTeamLogoInput) {
  uploadTeamLogo(id: $uploadTeamLogoId, input: $input) {
    preSignedUrl
    team {
      id
      name
      slug
      logo
      country      
      order
      externalId
      shortName
      shortNameSlug
    }
    errors {
      ... on FailedToCreatePresignedUrl {
        message
        path
      }
    }
  }
}