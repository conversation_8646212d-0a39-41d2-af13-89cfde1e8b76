mutation updateStripeAccount($input: CreateStripeAccountInput!) {
  updateStripeAccount(input: $input) {
    account {
      id
      firstname
      lastname
      dob
      address {
        city
        line1
        line2
        postalCode
        state
      }
      currency
      phone
      email
      country
      payoutsEnabled
      paymentsEnabled
      ssnLast4Provided
      jobTitle
    }
    errors {
      ... on AccountDoesNotExistsError {
        message
        path
      }
      ... on StripeAccountUserInputError {
        message
        path
      }
    }
  }
}