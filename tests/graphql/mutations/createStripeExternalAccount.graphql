mutation createStripeExternalAccount($input: CreateExternalAccountInput!) {
  createStripeExternalAccount(input: $input) {
    errors {
      ... on ExternalAccountAlreadyExistsError {
        message
        path
      }
      ... on ExternalAccountUserInputError {
        message
        path
      }
      ... on AccountDoesNotExistsError {
        message
        path
      }
    }
    externalAccount {
      id
      bankAccount {
        country
        currency
        firstname
        lastname
      }
      routingNumber
      accountNumber
      status
    }
  }
}