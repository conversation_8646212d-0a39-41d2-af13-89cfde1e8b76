mutation verifyAdminByEmail($code: String!, $email: String!) {
  verifyAdminByEmail(code: $code, email: $email) {
    tokens {
      accessToken
      refreshToken
    }
    user {
      id
      firstname
      lastname
      email
    }
    errors {
      ... on InvalidVerificationCodeError {
        message
        path
      }
      ... on UserNotFoundError {
        message
        path
      }
    }
  }
}