mutation uploadAvatarAdmin($userId: ObjectId!, $input: UploadAvatarInput) {
  admin {
    user {
      uploadAvatar(userId: $userId, input: $input) {
       errors {
      ... on FailedToCreatePresignedUrl {
        message
        path
      }
    }
    preSignedUrl
    user {
      id
      name
      role
      slug
      currentTeam {
        id
        name
      }
      avatar
      avatars {
        large
        medium
        small
        tiny
      }
    }
  }
}

}}