mutation syncSingleGameStats($id: ObjectId!) {
  admin {
    game {
      syncSingle(id: $id) {
        game {
          id
          date
          externalId
          externalInfo {
            homeTeamName
            awayTeamName
            leagueName
          }
        }
        errors {
          ... on EntityNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}
