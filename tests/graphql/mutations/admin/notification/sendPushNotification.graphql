mutation sendPushNotification($type: PushNotificationType!, $message: String!, $title: String!, $userId: ObjectId!, $destinationPath: String, $forcePush: Boolean) {
  admin {
    notifications {
      sendPushNotification(type: $type, message: $message, title: $title, userId: $userId, destinationPath: $destinationPath, forcePush: $forcePush) {
        success
        errors {
          ... on UserDoesNotHaveRegisteredDeviceError {
            message
            path
          }
          ... on UserNotFoundError {
            message
            path
          }
        }
      }
    }
  }
}

