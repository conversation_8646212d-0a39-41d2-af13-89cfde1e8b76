mutation updateBoostMessage($message: String!, $boostId: ObjectId!) {
  admin {
    boost {
      updateBoostMessage(message: $message, boostId: $boostId) {
        boost {
          ...Boost
        }
        errors {
          ... on BoostNotFoundError {
            message
            path
          }
          ... on BoostMessageAlreadySetError {
            message
            path
          }
        }
      }
    }
  }
}