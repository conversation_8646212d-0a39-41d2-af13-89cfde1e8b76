mutation uploadLeagueLogo($uploadLeagueLogoId: ObjectId!, $input: UploadLeagueLogoInput) {
  admin {
    league {
      uploadLeagueLogo(id: $uploadLeagueLogoId, input: $input) {
        preSignedUrl
        league {
          id
          name
          slug
          logo
          countries
        }
        errors {
          ... on FailedToCreatePresignedUrl {
            message
            path
          }
        }
      }
    }
  }
}
