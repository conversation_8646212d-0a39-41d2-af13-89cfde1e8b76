mutation updateTeam($input: UpdateTeamInput!, $updateTeamId: ObjectId!) {
  updateTeam(input: $input, id: $updateTeamId) {
    errors {
      ... on UpdateTeamNotFoundError {
        message
        path
      }
    }
    team {
      id
      name
      slug
      logo
      country
      eliteProspectLink
      checked
      abbreviation
      organization {
        id
        name
        slug
        country
        logo
      }
      order
      externalId
      shortName
      shortNameSlug
    }
  }
}