query topViewedUsers($period: Period!, $filter: TopViewedUsersFilter, $pagination: Pagination) {
  topViewedUsers(period: $period, filter: $filter, pagination: $pagination) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        id
        name
        country
        currentTeam {
          id
          name
        }
      }
      views
    }
    myPosition
  }
}