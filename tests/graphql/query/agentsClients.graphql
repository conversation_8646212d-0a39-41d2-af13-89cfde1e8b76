query agentsClients ($slug: String!, $after: String, $first: Int) {
  agentsClients (slug: $slug, after: $after, first: $first) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        name
        currentTeam {
          name
        }
      }
    }
    aggregatedInfo {
      name
      items {
        key
        value
      }
    }
  }
}