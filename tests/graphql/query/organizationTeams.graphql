query organizationTeams($pagination: Pagination, $slug: String, $organizationId: ObjectId) {
  organizationTeams(pagination: $pagination, slug: $slug, organizationId: $organizationId) {
    edges {
      node {
        id
        name
        organization {
          id
          name
          country
        }
      }
      playersCount
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}