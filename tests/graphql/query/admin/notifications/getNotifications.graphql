query getNotifications($pagination: Pagination, $filter: AdminNotificationFilter) {
  admin {
    notifications {
      getNotifications(pagination: $pagination, filter: $filter) {
        edges {
          node {
            ...Notifications
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}