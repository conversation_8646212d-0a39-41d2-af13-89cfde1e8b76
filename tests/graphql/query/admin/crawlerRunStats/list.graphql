query admin_crawlerRunStats_list(
  $first: Int
  $after: String
  $before: String
  $filter: CrawlerRunStatsFilter
) {
  admin {
    crawlerRunStats {
      list(
        first: $first
        after: $after
        before: $before
        filter: $filter
      ) {
        edges {
          node {
            id
            startedAt
            finishedAt
            durationMs
            totalVisitedUrls
            totalDiscoveredUrls
            newlyDiscoveredUrls
            urlPatternStats {
              urlPattern
              matchCount
            }
            crawlerDomainConfig {
              id
              domain
            }
          }
        }
        pageInfo {
          hasNextPage
          hasPreviousPage
          startCursor
          endCursor
          totalCount
        }
      }
    }
  }
}
