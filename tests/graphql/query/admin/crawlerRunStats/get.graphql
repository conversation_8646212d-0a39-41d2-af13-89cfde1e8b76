query admin_crawlerRunStats_get($id: String!) {
  admin {
    crawlerRunStats {
      get(id: $id) {
        id
        startedAt
        finishedAt
        durationMs
        totalVisitedUrls
        totalDiscoveredUrls
        newlyDiscoveredUrls
        urlPatternStats {
          urlPattern
          matchCount
        }
        crawlerDomainConfig {
          id
          domain
        }
      }
    }
  }
}
