query getCrawlerDomainConfigHistoryItem($id: String!) {
  admin {
    crawlerDomainConfigHistory {
      get(id: $id) {
        id
        op
        modelName
        collectionName
        collectionId
        patch {
          op
          path
          value
        }
        user {
          id
          email
          admin
          role
          iat
          exp
        }
        version
        createdAt
        updatedAt
      }
    }
  }
}
