query getCrawlerLogs($first: Int, $after: String, $before: String, $filter: CrawlerDomainConfigHistoryFilter) {
  admin {
    crawlerLogs {
      list(first: $first, after: $after, before: $before, filter: $filter) {
        edges {
          node {
            id
            op
            modelName
            collectionName
            collectionId
            patch {
              op
              path
              value
            }
            user {
              id
              email
              admin
              role
              iat
              exp
            }
            version
            createdAt
            updatedAt
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
