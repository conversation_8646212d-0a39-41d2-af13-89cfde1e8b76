query discoveredUrlList($filter: DiscoveredUrlFilter, $pagination: Pagination) {
  admin {
    discoveredUrl {
      list(filter: $filter, pagination: $pagination) {
        edges {
          node {
            ...DiscoveredUrlList
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
