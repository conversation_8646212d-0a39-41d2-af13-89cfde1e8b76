query userInfo($filter: UserInfoFilter!, $pagination: Pagination) {
  admin {
    userInfo {
      userInfos(filter: $filter, pagination: $pagination) {
        edges {
          node {
            id
            name
            externalId
            crawlerDomainConfig {
              id
              domain              
            }
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}