query adminGameList ($pagination: Pagination, $filter: AdminGameFilter, $sort: SortInput) {
  admin {
    game {
      list (filter: $filter, pagination: $pagination, sort: $sort) {
        edges {
          node {
            ...GameListItem
          }
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}