query getCrawlerLogsItem($id: String!) {
  admin {
    crawlerLogs {
      get(id: $id) {
        id
        op
        modelName
        collectionName
        collectionId
        patch {
          op
          path
          value
        }
        user {
          id
          email
          admin
          role
          iat
          exp
        }
        version
        createdAt
        updatedAt
      }
    }
  }
}
