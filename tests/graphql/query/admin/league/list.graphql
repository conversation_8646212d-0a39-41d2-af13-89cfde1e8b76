query adminLeagueList($filter: AdminLeaguesFilter, $pagination: Pagination) {
  admin {
    league {
      list(filter: $filter, pagination: $pagination) {
        edges {
          node {
            ...League
          }
          used
        }
        pageInfo {
          hasNextPage
          endCursor
          hasPreviousPage
          startCursor
          totalCount
        }
      }
    }
  }
}
