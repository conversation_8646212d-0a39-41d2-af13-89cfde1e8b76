query games($filter: GamesFilter, $first: Int, $after: String, $before: String, $sort: SortInput) {
  games(filter: $filter, first: $first, after: $after, before: $before, sort: $sort) {
    edges {
      node {
        ...GameResponse
      }
      currentUserPlayer {
        goals
        assists
        saves
        externalInfo {
          playerName
          playerExternalId
        }
        player {
          id
        }
      }
      currentUserTeam
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}
