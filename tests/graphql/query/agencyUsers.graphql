query agencyUsers($pagination: Pagination, $agencyId: ObjectId!, $filter: AgencyUsersFilter) {
  agencyUsers(pagination: $pagination, agencyId: $agencyId, filter: $filter) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      node {
        id
        name
        currentTeam {
          id
          name
        }
        agentV2 {
          id
          name          
        }
      }
    }
    aggregatedInfo {
      name
      items {
        key
        value
      } 
    }
  }
}