query playersStatsList($filter: PlayersStatsFilter!, $first: Int, $after: String, $sort: SortInput) {
  playersStatsList(filter: $filter, first: $first, after: $after, sort: $sort), {
    edges {
      node {
        id
        user {
          id
          name
          slug
          firstname
          lastname
          role
          country
          bio {
            position
          }
        }
        team {
          id
          name
        }
        league {
          id
          name
        }
        goals
        assists
        points
        pim

        gamesPlayed
        svp
        gaa
        shutouts

        toi
        saves
        ga
        season
        externalInfo {
          externalLeagueCountry
          externalLeagueName
          externalTeamCountry
          externalTeamName
          externalPlayerName
          stage {
            value
            label
          }
        }
        position
      }
    }
    pageInfo {
      totalCount
    }
    leagueStages {
      league {
        id
        name
      }
      stages {
        value
        label
      }
    }
  }
}