query myFollowers($filter: MyFollowersFilter!, $first: Int, $after: String, $before: String) {
  myFollowers(filter: $filter, first: $first, after: $after, before: $before) {
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
    edges {
      iFollow
      node {
        name
        firstname
        lastname
        currentTeam {
          id
          name
        }
      }
    }
  }
}