query latestClips(
  $filter: LatestClipsFilter
  $before: String
  $after: String
  $first: Int
  $last: Int
) {
  latestClips(filter: $filter, before: $before, after: $after, first: $first, last: $last) {
    edges {
      node {
        id
        path
        createdAt
        user {
          id
          name
          dateOfBirth
          bio {
            position
          }

          currentTeam {
            id
            name
          }
        }
      }
    }
    pageInfo {
      startCursor
      endCursor
      hasNextPage
      hasPreviousPage
    }
  }
}
