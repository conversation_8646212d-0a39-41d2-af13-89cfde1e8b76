query wallVideosReel(
  $filter: WallVideosFilter
  $first: Int
  $last: Int
  $after: String
  $before: String
) {
  wallVideosReel: wallVideos(
    filter: $filter
    includeCounts: true
    first: $first
    last: $last
    after: $after
    before: $before
  ) {
    edges {
      node {
        ...WallVideoReel
      }
    }
    pageInfo {
      endCursor
      hasNextPage
      hasPreviousPage
      startCursor
      totalCount
    }
    categoryCounts {
      category
      count
    }
  }
}
