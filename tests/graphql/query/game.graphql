query game($gameId: ObjectId!) {
  game(id: $gameId) {
    id
    date
    externalId
    externalInfo {
      homeTeamName
      awayTeamName
      leagueName
    }
    league {
      id
      name
    }
    awayTeam {
      id
      name
      shortNameSlug
      organization {
        slug
      }

    }
    homeTeam {
      id
      name
      shortNameSlug
      organization {
        slug
      }
    }
    stats {
      homeTeam {
        score
        shotsOnGoal
        blockedShots
        faceoffs
        powerPlayGoals
        shortHandedGoals
        penalties
        majorPenalties
        penaltyMinutes
        goalieSaves
      }
      awayTeam {
        score
        shotsOnGoal
        blockedShots
        faceoffs
        powerPlayGoals
        shortHandedGoals
        penalties
        majorPenalties
        penaltyMinutes
        goalieSaves
      }
      periodScores
    }
    eventsByPeriod {
      time
      type
      isHomeTeam
      externalInfo {
        teamName
        playerName
        playerExternalId
      }
      player {
        name
      }
      assists {
        playerName
        playerExternalId
      }
    }
    homeTeamRoster {
      externalInfo {
        playerName
        playerExternalId
      }
      player {
        name
        id
      }
      goals
      assists
      points
      pim
      plusMinus
      saves
      goalsAgainst
      svp
      jerseyNumber
      position
    }
    awayTeamRoster {
      externalInfo {
        playerName
        playerExternalId
      }
      player {
        name
      }
      goals
      assists
      points
      pim
      plusMinus
      saves
      goalsAgainst
      svp
      jerseyNumber
      position
    }
    syncedAt
  }
}
