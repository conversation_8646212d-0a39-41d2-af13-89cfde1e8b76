query leagueStandings($teamId: ObjectId, $leagueId: ObjectId, $season: String) {
  leagueStandings(teamId: $teamId, leagueId: $leagueId, season: $season) {
    league {
      name
    }
    groups {
      group
      standings {
        standing
        team {
          name
          shortName
        }
        season
        points
        overtimeWins
        overtimeLosses
        losses
        games
      }
    }
  }
}
