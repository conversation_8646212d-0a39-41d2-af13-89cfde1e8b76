query team(
  $pagination: Pagination
  $orgSlug: String
  $slug: String
  $includeShadowPlayers: Boolean
  $season: String
  $teamId: ObjectId
) {
  team(
    pagination: $pagination
    orgSlug: $orgSlug
    slug: $slug
    includeShadowPlayers: $includeShadowPlayers
    season: $season
    id: $teamId
  ) {
    errors {
      ... on TeamNotFoundError {
        message
        path
      }
    }
    team {
      id
      name
      hasGames
      organization {
        id
        name
      }
    }
    users {
      edges {
        node {
          id
          name
          currentTeam {
            id
            name
          }
        }
      }
      pageInfo {
        hasNextPage
        endCursor
        hasPreviousPage
        startCursor
        totalCount
      }
    }
  }
}
