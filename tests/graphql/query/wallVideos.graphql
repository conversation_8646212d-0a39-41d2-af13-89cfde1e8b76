query wallVideos ($filter: WallVideosFilter, $first: Int, $last: Int, $after: String, $before: String) {
  wallVideos (filter: $filter, includeCounts: true, first: $first, last: $last, after: $after, before: $before) {
    edges {
      node {
        id
        path
        width
        height
      }
    }
    categoryCounts {
      category
      count
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      endCursor
      startCursor
    }
  }
}