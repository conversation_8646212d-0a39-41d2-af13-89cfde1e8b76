query users($filter: AdminUsersFilter!, $pagination: Pagination) {
  users(filter: $filter, pagination: $pagination) {
    edges {
      node {
        id
        firstname
        lastname
        email
        country
        currentTeam {
          id
          name
        }
        recommended
        inspired
        moreSelected
        dateOfBirth
        avatar
      }
    }
    pageInfo {
      hasNextPage
      endCursor
      hasPreviousPage
      startCursor
      totalCount
    }
  }
}