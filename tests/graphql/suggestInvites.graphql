query suggestInvites($teamId: ObjectId!) {
  suggestInvites(teamId: $teamId) {
    edges {
      node {
        id
        name
        firstname
        lastname
        country
        role
        avatar
        avatars {
          tiny
          small
          medium
          large
        }
        dateOfBirth
        possibleYearsOfBirth
        bio {
          position
          handedness
          playerType
        }
      }
    }
  }
}
