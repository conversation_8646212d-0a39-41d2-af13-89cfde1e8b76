import assert from 'assert'
import sinon from 'sinon'

import { UserModel } from '../../src/modules/user/models'
import { BoostModel, BoostState } from '../../src/modules/user/models/boost'
import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'

import { amplitudeTrackStub, stripeClientStub } from '.'

describe('Boost query', () => {
  it('should return error if user not found by slug', async () => {
    const user = await generateRandomUser()

    try {
      await client(user.tokens.accessToken).boostsQuery({
        filter: {
          slug: 'invalid-slug',
        },
      })
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'Failed to fetch boosts. Please try again later.'
      )
    }
  })

  it('should return error if user not logged in', async () => {
    try {
      await client().boostsQuery({})
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'Failed to fetch boosts. Please try again later.'
      )
    }
  })

  it('should return response', async () => {
    const user = await generateRandomUser({ firstname: null, lastname: null })
    const responseUser = await client(user.tokens.accessToken).updateUser({
      input: {
        firstname: 'Jarmil',
        lastname: 'Novak',
      },
    })

    for (let i = 0; i < 60; i++) {
      await BoostModel.create({
        amount: 100,
        currency: 'EUR',
        user: responseUser?.updateUser.user?.id,
        paymentIntentId: 'paymentIntentId(' + i + ')',
        state: BoostState.CONFIRMED,
      })
    }

    const response = await client(user.tokens.accessToken).boostsQuery({
      filter: {
        slug: responseUser.updateUser.user?.slug,
      },
      pagination: {
        first: 50,
      },
    })

    assert.strictEqual(response.boosts.edges.length, 50)
    assert.strictEqual(response.boosts.pageInfo.hasNextPage, true)
    assert.strictEqual(response.boosts.pageInfo.hasPreviousPage, false)
    assert.strictEqual(response.boosts.pageInfo.totalCount, 60)

    sinon.assert.calledOnce(amplitudeTrackStub)
    sinon.assert.calledWith(
      amplitudeTrackStub,
      'User Updated',
      {},
      { user_id: user.user._id.toString() }
    )
  })
})

describe('Boost mutation', () => {
  it('should return error if user not found', async () => {
    const user = await generateRandomUser()

    try {
      await client(user.tokens.accessToken).createBoost({
        input: {
          amount: 100,
          currency: 'EUR',
          userId: '11ac1111ef11111111d1cd11',
        },
      })
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(err.response.errors[0].message, 'User not found.')
    }
  })

  it('should return error if user dont have stripe account', async () => {
    const user = await generateRandomUser()

    const response = await client(user.tokens.accessToken).createBoost({
      input: {
        amount: 100,
        currency: 'EUR',
        userId: user.user._id,
      },
    })

    assert.strictEqual(response.createBoost.errors?.length, 1)
    assert.strictEqual(response.createBoost.errors[0].message, 'Stripe is not connected')
  })

  it('create boost - should return response', async () => {
    const user = await generateRandomUser()

    await UserModel.updateOne(
      {
        _id: user.user._id,
      },
      {
        stripeAccountId: 'valid-stripe-account-id',
      }
    )

    const response = await client(user.tokens.accessToken).createBoost({
      input: {
        amount: 100,
        currency: 'EUR',
        userId: user.user._id,
      },
    })

    sinon.assert.calledOnce(stripeClientStub.customers.create)
    sinon.assert.calledWith(
      stripeClientStub.customers.create,
      {},
      {
        stripeAccount: 'valid-stripe-account-id',
      }
    )

    sinon.assert.calledOnce(stripeClientStub.paymentMethodDomains.create)
    sinon.assert.calledWith(
      stripeClientStub.paymentMethodDomains.create,
      {
        domain_name: 'graet.dev',
      },
      { stripeAccount: 'valid-stripe-account-id' }
    )

    sinon.assert.calledOnce(stripeClientStub.ephemeralKeys.create)
    sinon.assert.calledWith(stripeClientStub.ephemeralKeys.create, { customer: 'customerId' })

    sinon.assert.calledOnce(stripeClientStub.paymentIntents.create)
    sinon.assert.calledWith(stripeClientStub.paymentIntents.create, {
      amount: 100,
      currency: 'EUR',
      customer: 'customerId',
      application_fee_amount: 6,
    })

    assert.strictEqual(response.createBoost.errors?.length, 0)
    assert.strictEqual(response.createBoost.boost?.amount, 100)
    assert.strictEqual(response.createBoost.boost?.currency, 'EUR')
    assert.strictEqual(response.createBoost.boost?.state, 'CREATED')
    assert.strictEqual(response.createBoost.paymentSheet?.customerId, 'customerId')
    assert.strictEqual(response.createBoost.paymentSheet?.ephemeralKey, 'empheralKeySecret')
    assert.strictEqual(response.createBoost.paymentSheet?.paymentIntent, 'paymentClientSecret')
    assert.strictEqual(response.createBoost.paymentSheet?.accountId, 'valid-stripe-account-id')
  })

  it('should return error if stripe client call fails', async () => {
    stripeClientStub.customers.create.throws('Error')

    const user = await generateRandomUser()
    await UserModel.updateOne(
      {
        _id: user.user._id,
      },
      {
        stripeAccountId: 'invalid-stripe-account-id',
      }
    )

    try {
      await client(user.tokens.accessToken).createBoost({
        input: {
          amount: 100,
          currency: 'EUR',
          userId: user.user._id,
        },
      })
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'Failed to create payment sheet. Please try again later.'
      )
    }
  })
})
describe('Create boost Messages', () => {
  it('should return error if boost not found', async () => {
    const response = await client().createBoostMessage({
      input: {
        message: 'Hello',
        name: 'Jarmil',
        paymentIntentId: 'invalid - paymentIntentId',
      },
    })
    assert.strictEqual(response.createBoostMessage.errors?.length, 1)
    assert.strictEqual(response.createBoostMessage.errors[0].message, 'Boost not found')
  })

  it('should return error if boost message is already set ', async () => {
    const user = await generateRandomUser()

    await BoostModel.create({
      amount: 200,
      currency: 'EUR',
      user: user?.user._id,
      paymentIntentId: 'paymentIntentId',
      message: 'Test message',
      state: BoostState.CONFIRMED,
    })

    const response = await client().createBoostMessage({
      input: {
        message: 'Test message',
        name: 'Jarmila',
        paymentIntentId: 'paymentIntentId',
      },
    })

    assert.strictEqual(response.createBoostMessage.errors?.length, 1)
    assert.strictEqual(response.createBoostMessage.errors[0].message, 'Boost message already set')
  })

  it('should return response', async () => {
    const user = await generateRandomUser()

    await BoostModel.create({
      amount: 200,
      currency: 'EUR',
      user: user.user?._id,
      paymentIntentId: 'valid-paymentIntentId',
      state: BoostState.CREATED,
    })

    const response = await client().createBoostMessage({
      input: {
        message: 'Enjoy boost',
        name: 'Lukas',
        paymentIntentId: 'valid-paymentIntentId',
      },
    })

    assert.strictEqual(response.createBoostMessage.errors?.length, 0)
    assert.strictEqual(response.createBoostMessage.boost?.message, 'Enjoy boost')
    assert.strictEqual(response.createBoostMessage.boost?.name, 'Lukas')
    assert.strictEqual(response.createBoostMessage.boost?.state, 'WAITING_FOR_CONFIRMATION')
    assert.strictEqual(response.createBoostMessage.boost?.amount, 200)
    assert.strictEqual(response.createBoostMessage.boost?.currency, 'EUR')
  })

  it('should return error if db fails', async () => {
    await BoostModel.deleteMany({})

    const user = await generateRandomUser()
    const findByIdAndUpdate = BoostModel.findByIdAndUpdate
    const findByIDStub = (BoostModel.findByIdAndUpdate = sinon.stub().throws('error'))

    await BoostModel.create({
      amount: 200,
      currency: 'EUR',
      user: user?.user._id,
      paymentIntentId: 'eror-paymentIntentId',
      state: BoostState.CREATED,
    })

    const boost = await BoostModel.findOne()
    const id = boost?.id

    try {
      await client().createBoostMessage({
        input: {
          message: 'I want error',
          name: 'Lukas',
          paymentIntentId: 'eror-paymentIntentId',
        },
      })

      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'Failed to update boost. Please try again later.'
      )
    }

    sinon.assert.calledOnce(findByIDStub)
    sinon.assert.calledWith(findByIDStub, id?.toString(), {
      message: 'I want error',
      name: 'Lukas',
      state: 'WAITING_FOR_CONFIRMATION',
    })

    BoostModel.findByIdAndUpdate = findByIdAndUpdate
  })
})
