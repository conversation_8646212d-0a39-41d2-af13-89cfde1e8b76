import assert from 'assert'

import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'

import { amplitudeTrackStub } from '.'

describe('Analytics Resolver', () => {
  it('should send track event amplitude event', async () => {
    const user = await generateRandomUser()

    const response = await client(user.tokens.accessToken).createPassiveEvent({
      input: {
        pathname: 'likeGained',
        params: JSON.stringify({ param: 1 }),
        userId: user.user._id.toString(),
      },
    })

    // amplitudeTrackStub was called with the correct arguments
    assert.strictEqual(amplitudeTrackStub.calledOnce, true)

    // The arguments passed to amplitudeTrackStub
    const [eventName, eventProperties, userId] = amplitudeTrackStub.firstCall.args

    assert.strictEqual(eventName, 'likeGained')
    assert.deepStrictEqual(eventProperties, { param: 1 })
    assert.deepStrictEqual(userId, { user_id: user.user._id.toString() })

    assert.strictEqual(response.createPassiveEvent, true)
  })
})
