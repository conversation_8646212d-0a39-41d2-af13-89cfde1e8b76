import assert from 'assert'
import mongoose from 'mongoose'
import sinon from 'sinon'

import { UserModel } from '../../src/modules/user/models'
import { client } from '../utils/graphql'
import {
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
  getUserUpdateInput,
} from '../utils/users'

import { amplitudeTrackStub } from '.'

describe('User resolver', () => {
  describe('User Query by Slug, id', () => {
    it('will return null, if slug contains invalid character "."', async () => {
      const response1 = await client().userQuery({
        slug: 'p.ter-doe',
      })

      assert.strictEqual(response1.user, null)
    })

    it('should return null if user is not found', async () => {
      const randomUser = await generateRandomUser()

      const response = await client(randomUser.tokens.accessToken).userQuery({
        slug: 'non-existing-user',
      })

      assert.strictEqual(response.user, null)
    })

    it('should return user by slug', async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })

      const getUserInput = await getUserUpdateInput()

      await client(randomUser.tokens.accessToken).updateUser({
        input: getUserInput,
      })
      const userFromDB = await UserModel.findById(randomUser.user._id)
      const slug = userFromDB?.slug

      const response1 = await client(randomUser.tokens.accessToken).userQuery({
        slug: slug!,
      })

      const user = response1.user
      assert.strictEqual(user?.id, userFromDB?._id.toString())
      assert.strictEqual(user?.slug, userFromDB?.slug)
      assert.strictEqual(user?.firstname, getUserInput.firstname)
      assert.strictEqual(user?.lastname, getUserInput.lastname)
      assert.strictEqual(user?.name, getUserInput.firstname + ' ' + getUserInput.lastname)
      assert.strictEqual(user?.dateOfBirth, getUserInput.dateOfBirth.toISOString())
      assert.strictEqual(user?.country, getUserInput.country)
      assert.strictEqual(user?.contact?.firstname, getUserInput.contact.firstname)
      assert.strictEqual(user?.contact?.lastname, getUserInput.contact.lastname)
      assert.strictEqual(user?.contact?.type, getUserInput.contact.type)
      assert.strictEqual(user?.boostMessage, getUserInput.boostMessage)
      assert.strictEqual(user?.boost?.currency, getUserInput.boost.currency)
      assert.strictEqual(user?.boost?.goalAmount, getUserInput.boost.goalAmount)
      assert.strictEqual(user?.currentTeam?.id, getUserInput.currentTeam)

      const bio = response1.user?.bio
      assert.strictEqual(bio?.position, getUserInput.bio.position)
      assert.strictEqual(bio?.handedness, getUserInput.bio.handedness)
      assert.strictEqual(bio?.playerType, getUserInput.bio.playerType)
      assert.strictEqual(bio?.schoolType, getUserInput.bio.schoolType)
      assert.strictEqual(bio?.height?.feet, getUserInput.bio.height.feet)
      assert.strictEqual(bio?.height?.inches, getUserInput.bio.height.inches)
      assert.strictEqual(bio?.height?.centimeters, getUserInput.bio.height.centimeters)
      assert.strictEqual(bio?.weight?.pounds, getUserInput.bio.weight.pounds)
      assert.strictEqual(bio?.weight?.kilograms, getUserInput.bio.weight.kilograms)
      assert.strictEqual(bio?.birthplace, getUserInput.bio.birthplace)
      assert.strictEqual(bio?.nameOfSchool, getUserInput.bio.nameOfSchool)
      assert.strictEqual(bio?.graduationYear, getUserInput.bio.graduationYear)
      assert.strictEqual(bio?.cumulativeGPA, getUserInput.bio.cumulativeGPA)
      assert.strictEqual(bio?.coreGPA, getUserInput.bio.coreGPA)
      assert.strictEqual(bio?.unweightedGPA, getUserInput.bio.unweightedGPA)
      assert.strictEqual(bio?.weightedGPA, getUserInput.bio.weightedGPA)
      assert.strictEqual(bio?.satScore, getUserInput.bio.satScore)
      assert.strictEqual(bio?.actScore, getUserInput.bio.actScore)
      assert.strictEqual(bio?.toeflScore, getUserInput.bio.toeflScore)
      assert.strictEqual(bio?.ncaaEligibility, getUserInput.bio.ncaaEligibility)
      assert.strictEqual(bio?.naiaEligibility, getUserInput.bio.naiaEligibility)

      const externalLinks = response1.user?.externalLinks
      assert.strictEqual(externalLinks?.length, getUserInput.externalLinks.length)
      assert.strictEqual(externalLinks?.[0].name, getUserInput.externalLinks[0].name)
      assert.strictEqual(externalLinks?.[0].url, getUserInput.externalLinks[0].url)
      assert.strictEqual(externalLinks?.[0].type, getUserInput.externalLinks[0].type)

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should return user by id', async () => {
      const randomUser = await generateRandomUser({ firstname: 'Karl', lastname: 'Von Banhoff' })

      const reponse = await client(randomUser.tokens.accessToken).userByIdQuery({
        id: randomUser.user._id.toString(),
      })

      const { userById } = reponse

      assert.strictEqual(randomUser?.user.slug, userById?.slug)
      assert.strictEqual(randomUser?.user.name, userById?.name)
    })

    it('should return null if not found user by id', async () => {
      const randomUser = await generateRandomUser({ firstname: 'Karl', lastname: 'Von Banhoff' })

      const reponse = await client(randomUser.tokens.accessToken).userByIdQuery({
        id: new mongoose.Types.ObjectId(),
      })

      const { userById } = reponse

      assert.strictEqual(null, userById)
    })

    it('should return users by ids', async () => {
      const organization = await generateRandomOrganization('Hockey hard')
      const team = await generateRandomTeam({ name: 'Dudesons', organization: organization.id })

      const randomUser = await generateRandomUser({
        firstname: 'Karl',
        lastname: 'Von Banhoff',
        team: team.id,
      })
      const randomUser2 = await generateRandomUser({ firstname: 'Sir Lig', lastname: 'Ma Balls' })
      const randomUser3 = await generateRandomUser({ firstname: 'Biggus', lastname: 'Calamum' })

      const reponse = await client(randomUser.tokens.accessToken).usersByIdsQuery({
        userIds: [
          randomUser.user._id.toString(),
          randomUser2.user._id.toString(),
          randomUser3.user._id.toString(),
        ],
      })

      const { usersByIds } = reponse
      assert.strictEqual(3, usersByIds?.length)

      assert.strictEqual(randomUser?.user.slug, usersByIds?.[0].slug)
      assert.strictEqual(randomUser?.user.name, usersByIds?.[0].name)
      assert.strictEqual(team?.name, usersByIds?.[0].currentTeam?.name)
      assert.strictEqual(organization?.name, usersByIds?.[0].currentTeam?.organization?.name)
      assert.strictEqual(randomUser3?.user.slug, usersByIds?.[2].slug)
      assert.strictEqual(randomUser3?.user.name, usersByIds?.[2].name)
    })

    it('should return null if not found users by ids', async () => {
      const randomUser = await generateRandomUser({ firstname: 'Karl', lastname: 'Von Banhoff' })

      const reponse = await client(randomUser.tokens.accessToken).usersByIdsQuery({
        userIds: [new mongoose.Types.ObjectId().toString()],
      })

      const { usersByIds } = reponse

      assert.strictEqual(null, usersByIds)
    })
  })
})
