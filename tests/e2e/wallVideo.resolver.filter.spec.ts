import assert from 'node:assert'

import { UserModel, WallVideoModel } from '../../src/modules/user/models'
import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'
import { generateWallVideo } from '../utils/wallVideos'

describe('Wall videos filtered with videoId', () => {
  beforeEach(async () => {
    await UserModel.deleteMany({})
    await WallVideoModel.deleteMany({})
  })
  after(async () => {
    await UserModel.deleteMany({})
    await WallVideoModel.deleteMany({})
  })
  it('Filters based on videoId, expects videos before and after based on sorting', async () => {
    // mock data generation
    const user = await generateRandomUser()

    let numVideos = 20
    const videos = []
    while (numVideos--) {
      videos.push(await generateWallVideo({ user: user.user, path: `video${numVideos}` }))
    }

    const videoId = videos[9]._id.toString()

    // query DB
    const response = await client(user.tokens.accessToken).wallVideos({
      filter: {
        videoId,
      },
      first: 10,
    })

    // assert results
    const foundVideoById = response.wallVideos.edges.find((video) => video.node.id === videoId)

    assert.strictEqual(response.wallVideos.edges.length, 20)

    assert.strictEqual(foundVideoById?.node.id, videoId)
  })

  it('Filters based on videoId, expects videos before and after based on sorting, modifying first, not logged in user', async () => {
    // mock data generation
    const user = await generateRandomUser()

    let numVideos = 20
    const videos = []
    while (numVideos--) {
      videos.push(await generateWallVideo({ user: user.user, path: `video${numVideos}` }))
    }

    // pick video from the middle
    const videoId = videos[9]._id.toString()

    // query DB
    const response = await client().wallVideos({
      filter: {
        videoId,
      },
      first: 8,
    })

    // assert results
    const foundVideoById = response.wallVideos.edges.find((video) => video.node.id === videoId)

    assert.strictEqual(response.wallVideos.edges.length, 2 * 8 + 1)

    assert.strictEqual(foundVideoById?.node.id, videoId)

    // load remaining videos forwards
    const response2 = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      first: 8,
      after: response.wallVideos.pageInfo.endCursor,
    })

    // load remaining videos backwards
    const response3 = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      last: 8,
      before: response.wallVideos.pageInfo.startCursor,
    })

    assert.strictEqual(response2.wallVideos.edges.length, 1)
    assert.strictEqual(response2.wallVideos.edges[0].node.path, 'video19')

    assert.strictEqual(response3.wallVideos.edges.length, 2)
    assert.strictEqual(response3.wallVideos.edges[0].node.path, 'video0')
    assert.strictEqual(response3.wallVideos.edges[1].node.path, 'video1')
  })

  it('Filters based on videoId, expects videos before and after based on sorting, modifying first, videoId from the end, not logged in user', async () => {
    // mock data generation
    const user = await generateRandomUser()

    let numVideos = 20
    const videos = []
    while (numVideos--) {
      videos.push(await generateWallVideo({ user: user.user, path: `video${numVideos}` }))
    }

    // pick the oldest video, hence being last in results
    const videoId = videos[0]._id.toString()

    // query DB
    const response = await client().wallVideos({
      filter: {
        videoId,
      },
      first: 8,
    })

    // assert results

    const foundVideoById = response.wallVideos.edges.find((video) => video.node.id === videoId)
    const foundIndexVideoById = response.wallVideos.edges.findIndex(
      (video) => video.node.id === videoId
    )

    assert.strictEqual(foundIndexVideoById, response.wallVideos.edges.length - 1)
    assert.strictEqual(response.wallVideos.edges.length, 8 + 1)
    assert.strictEqual(response.wallVideos.pageInfo.hasNextPage, false)

    assert.strictEqual(foundVideoById?.node.id, videoId)
  })

  it('Filters based on videoId, expects videos before and after based on sorting, modifying first, videoId from the start, not logged in user', async () => {
    // mock data generation
    const user = await generateRandomUser()

    let numVideos = 20
    const videos = []
    while (numVideos--) {
      videos.push(await generateWallVideo({ user: user.user, path: `video${numVideos}` }))
    }

    // pick the newest video, hence being first in results
    const videoId = videos[videos.length - 1]._id.toString()

    // query DB
    const response = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      first: 8,
    })

    // assert results

    const foundVideoById = response.wallVideos.edges.find((video) => video.node.id === videoId)
    const foundIndexVideoById = response.wallVideos.edges.findIndex(
      (video) => video.node.id === videoId
    )

    assert.strictEqual(foundIndexVideoById, 0)
    assert.strictEqual(response.wallVideos.edges.length, 8 + 1)
    assert.strictEqual(response.wallVideos.pageInfo.hasPreviousPage, false)

    assert.strictEqual(foundVideoById?.node.id, videoId)
  })

  it('Filters based on videoId, then pagination calls, starting forward', async () => {
    // mock data generation
    const user = await generateRandomUser()

    let numVideos = 60
    const videos = []
    while (numVideos--) {
      videos.push(await generateWallVideo({ user: user.user, path: `video${numVideos}` }))
    }

    // pick video somewhere in the first page
    const videoId = videos[60 - 6]._id.toString()

    // query DB
    const response = await client().wallVideos({
      filter: {
        videoId,
      },
      first: 10,
    })

    // assert results

    const foundVideoById = response.wallVideos.edges.find((video) => video.node.id === videoId)
    const foundIndexVideoById = response.wallVideos.edges.findIndex(
      (video) => video.node.id === videoId
    )

    assert.deepStrictEqual(
      response.wallVideos.edges.map(({ node }) => node.path),
      [
        'video0',
        'video1',
        'video2',
        'video3',
        'video4',
        'video5',
        'video6',
        'video7',
        'video8',
        'video9',
        'video10',
        'video11',
        'video12',
        'video13',
        'video14',
        'video15',
      ]
    )

    assert.strictEqual(foundIndexVideoById, 5)
    assert.strictEqual(response.wallVideos.edges.length, 16)
    assert.strictEqual(response.wallVideos.pageInfo.hasNextPage, true)

    assert.strictEqual(foundVideoById?.node.id, videoId)

    // load remaining videos forwards
    const response2 = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      first: 8,
      after: response.wallVideos.pageInfo.endCursor,
    })

    assert.deepStrictEqual(
      response2.wallVideos.edges.map(({ node }) => node.path),
      ['video16', 'video17', 'video18', 'video19', 'video20', 'video21', 'video22', 'video23']
    )

    assert.strictEqual(response2.wallVideos.edges.length, 8)
    assert.strictEqual(response2.wallVideos.pageInfo.hasNextPage, true)
    assert.strictEqual(response2.wallVideos.pageInfo.hasPreviousPage, true)

    // load some previous videos
    const response3 = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      last: 8,
      before: response2.wallVideos.pageInfo.startCursor,
    })

    assert.deepStrictEqual(
      response3.wallVideos.edges.map(({ node }) => node.path),
      ['video8', 'video9', 'video10', 'video11', 'video12', 'video13', 'video14', 'video15']
    )

    assert.strictEqual(response3.wallVideos.edges.length, 8)
    assert.strictEqual(response3.wallVideos.pageInfo.hasNextPage, true)
    assert.strictEqual(response3.wallVideos.pageInfo.hasPreviousPage, true)

    // load some previous videos more
    const response4 = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      first: 8,
      before: response3.wallVideos.pageInfo.startCursor,
    })

    assert.deepStrictEqual(
      response4.wallVideos.edges.map(({ node }) => node.path),
      ['video0', 'video1', 'video2', 'video3', 'video4', 'video5', 'video6', 'video7']
    )

    assert.strictEqual(response4.wallVideos.edges.length, 8)
    assert.strictEqual(response4.wallVideos.pageInfo.hasNextPage, true)
    assert.strictEqual(response4.wallVideos.pageInfo.hasPreviousPage, false)
  })

  // paginated backwards

  it('Filters based on videoId, then pagination calls, starting backwards', async () => {
    // mock data generation
    const user = await generateRandomUser()

    let numVideos = 60
    const videos = []
    while (numVideos--) {
      videos.push(await generateWallVideo({ user: user.user, path: `video${numVideos}` }))
    }

    // pick video somewhere in the first half of all data
    const videoId = videos[60 - 20]._id.toString()

    // query DB
    const response = await client().wallVideos({
      filter: {
        videoId,
      },
      first: 10,
    })

    // assert results

    const foundVideoById = response.wallVideos.edges.find((video) => video.node.id === videoId)
    const foundIndexVideoById = response.wallVideos.edges.findIndex(
      (video) => video.node.id === videoId
    )

    assert.deepStrictEqual(
      response.wallVideos.edges.map(({ node }) => node.path),
      [
        'video9',
        'video10',
        'video11',
        'video12',
        'video13',
        'video14',
        'video15',
        'video16',
        'video17',
        'video18',
        'video19',
        'video20',
        'video21',
        'video22',
        'video23',
        'video24',
        'video25',
        'video26',
        'video27',
        'video28',
        'video29',
      ]
    )

    assert.strictEqual(foundIndexVideoById, 10)
    assert.strictEqual(response.wallVideos.edges.length, 21)
    assert.strictEqual(response.wallVideos.pageInfo.hasPreviousPage, true)
    assert.strictEqual(response.wallVideos.pageInfo.hasNextPage, true)

    assert.strictEqual(foundVideoById?.node.id, videoId)

    // load remaining videos backwards
    const response2 = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      last: 8,
      before: response.wallVideos.pageInfo.startCursor,
    })

    assert.deepStrictEqual(
      response2.wallVideos.edges.map(({ node }) => node.path),
      ['video1', 'video2', 'video3', 'video4', 'video5', 'video6', 'video7', 'video8']
    )

    assert.strictEqual(response2.wallVideos.edges.length, 8)
    assert.strictEqual(response2.wallVideos.pageInfo.hasNextPage, true)
    assert.strictEqual(response2.wallVideos.pageInfo.hasPreviousPage, true)

    // load some previous videos even more
    const response3 = await client().wallVideos({
      filter: {
        userId: user.user._id.toString(),
        videoId,
      },
      last: 8,
      before: response2.wallVideos.pageInfo.startCursor,
    })

    assert.deepStrictEqual(
      response3.wallVideos.edges.map(({ node }) => node.path),
      ['video0']
    )

    assert.strictEqual(response3.wallVideos.edges.length, 1)
    assert.strictEqual(response3.wallVideos.pageInfo.hasNextPage, true)
    assert.strictEqual(response3.wallVideos.pageInfo.hasPreviousPage, false)
  })
})
