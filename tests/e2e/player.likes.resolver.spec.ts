import { Types } from 'mongoose'
import assert from 'node:assert'
import sinon from 'sinon'

import { capitalizeWords } from '../../src/helpers/format'
import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { GameModel } from '../../src/modules/stats/models/game/game'
import { LeagueModel } from '../../src/modules/stats/models/league'
import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { Team, TeamModel } from '../../src/modules/stats/models/team'
import { UserModel } from '../../src/modules/user/models'
import { PlayerPerformanceLikeModel } from '../../src/modules/user/models/playerPerformanceLike'
import { buildGameActivityPayload, FeedActivityVerb } from '../../src/services/helpers/streamFeed'
import { PushNotificationType } from '../../src/services/pushNotification'
import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'

import { generateRandomGameStats } from './../utils/stats'
import { pushNotificationServiceStub, streamFeedAddActivityStub } from '.'

import { DocumentType } from '@typegoose/typegoose'

describe('Player Performance likes', () => {
  beforeEach(async () => {
    await GameModel.deleteMany({})
    await UserModel.deleteMany({})
    await OrganizationModel.deleteMany({})
    await TeamModel.deleteMany({})
    await LeagueModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
    await PlayerPerformanceLikeModel.deleteMany({})
  })
  it('should create game stats', async () => {
    const game1 = await generateRandomGameStats({ nameSuffix: 'A' })
    const game2 = await generateRandomGameStats({ nameSuffix: 'B' })

    assert(game1._id !== game2._id)
    assert(game1.homeTeam !== game2.homeTeam)
    assert.ok(game1.awayTeam)
    assert.ok(game2.awayTeam)
  })

  it('should add player performance like', async () => {
    const game1 = await generateRandomGameStats({ nameSuffix: 'A' })

    const homePlayer1Id = game1.homeTeamRoster[0].player

    const user = await generateRandomUser()

    const addLikeResponse = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: homePlayer1Id,
    })

    assert.strictEqual(addLikeResponse.likePlayerPerformance?.player?.id, homePlayer1Id?.toString())

    const hometeam = await TeamModel.findById(game1.homeTeam)
    const awayteam = await TeamModel.findById(game1.awayTeam)

    const { home, away } = await buildGameActivityPayload(
      game1,
      hometeam as DocumentType<Team>,
      awayteam as DocumentType<Team>
    )

    sinon.assert.calledWith(
      streamFeedAddActivityStub,
      sinon.match({
        actor: user.user._id.toString(),
        foreign_id: user.user._id.toString(),
        object: 'user',
        verb: FeedActivityVerb.Fire,
        actor_name: user?.user.name,
        actor_slug: user?.user.slug,
        home,
        away,
      })
    )
  })

  it('should add and remove player performance like', async () => {
    const game1 = await generateRandomGameStats({ nameSuffix: 'A' })

    const homePlayer1Id = game1.homeTeamRoster[0].player

    const user = await generateRandomUser()

    const addLikeResponse = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: homePlayer1Id,
    })

    assert.strictEqual(addLikeResponse.likePlayerPerformance?.player?.id, homePlayer1Id?.toString())

    const hometeam = await TeamModel.findById(game1.homeTeam)
    const awayteam = await TeamModel.findById(game1.awayTeam)

    const { home, away } = await buildGameActivityPayload(
      game1,
      hometeam as DocumentType<Team>,
      awayteam as DocumentType<Team>
    )

    sinon.assert.calledWith(
      streamFeedAddActivityStub,
      sinon.match({
        actor: user.user._id.toString(),
        foreign_id: user.user._id.toString(),
        object: 'user',
        verb: FeedActivityVerb.Fire,
        actor_name: user?.user.name,
        actor_slug: user?.user.slug,
        home,
        away,
      })
    )

    sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

    const homePlayer = await UserModel.findById(homePlayer1Id)

    const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args

    assert.strictEqual(pushArgs[0].name, homePlayer?.name)

    const title = 'Your game is fire! 💥'

    const capitalizedSubRole = user.user?.subrole ? `${capitalizeWords(user.user?.subrole)}` : ''

    const message = `${capitalizedSubRole ? `${capitalizedSubRole} ` : ''}${user.user?.name} just dropped 🔥.`

    assert.strictEqual(pushArgs[1].title, title)
    assert.strictEqual(pushArgs[1].message, message)
    assert.strictEqual(pushArgs[1].type, PushNotificationType.Fire)
    assert.strictEqual(
      pushArgs[1].key,
      `newFire-${user.user._id.toString()}-${game1._id.toString()}-${homePlayer?._id.toString()}`
    )

    const removeLikeResponse = await client(user.tokens.accessToken).unlikePlayerPerformance({
      gameId: game1._id,
      playerId: homePlayer1Id,
    })

    assert.strictEqual(
      removeLikeResponse.unlikePlayerPerformance?.player?.id,
      homePlayer1Id?.toString()
    )
  })

  it('should add likes to multiple players', async () => {
    const game1 = await generateRandomGameStats({ nameSuffix: 'A', numPlayersInEachTeam: 6 })

    const homePlayerIds = game1.homeTeamRoster.map((player) => player.player?._id?.toString())
    const awayPlayerIds = game1.awayTeamRoster.map((player) => player.player?._id?.toString())

    const user = await generateRandomUser()

    const addLikeResponse1 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: homePlayerIds[0],
    })

    assert.strictEqual(addLikeResponse1.likePlayerPerformance?.player?.id, homePlayerIds[0])

    const addLikeResponse2 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: awayPlayerIds[0],
    })

    assert.strictEqual(addLikeResponse2.likePlayerPerformance?.player?.id, awayPlayerIds[0])

    const addLikeResponse3 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: awayPlayerIds[1],
    })

    assert.strictEqual(addLikeResponse3.likePlayerPerformance?.player?.id, awayPlayerIds[1])

    const addLikeResponse4 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: awayPlayerIds[2],
    })

    sinon.assert.callCount(streamFeedAddActivityStub, 4)
    sinon.assert.callCount(pushNotificationServiceStub.sendPushNotification, 4)

    const hometeam = await TeamModel.findById(game1.homeTeam)
    const awayteam = await TeamModel.findById(game1.awayTeam)

    const { home, away } = await buildGameActivityPayload(
      game1,
      hometeam as DocumentType<Team>,
      awayteam as DocumentType<Team>
    )

    sinon.assert.calledWith(
      streamFeedAddActivityStub,
      sinon.match({
        actor: user.user._id.toString(),
        foreign_id: user.user._id.toString(),
        object: 'user',
        verb: FeedActivityVerb.Fire,
        actor_name: user?.user.name,
        actor_slug: user?.user.slug,
        home,
        away,
      })
    )

    const homePlayer = await UserModel.findById(homePlayerIds[0])

    const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args

    assert.strictEqual(pushArgs[0].name, homePlayer?.name)

    const title = 'Your game is fire! 💥'

    const capitalizedSubRole = user.user?.subrole ? `${capitalizeWords(user.user?.subrole)}` : ''

    const message = `${capitalizedSubRole ? `${capitalizedSubRole} ` : ''}${user.user?.name} just dropped 🔥.`

    assert.strictEqual(pushArgs[1].title, title)
    assert.strictEqual(pushArgs[1].message, message)
    assert.strictEqual(pushArgs[1].type, PushNotificationType.Fire)
    assert.strictEqual(
      pushArgs[1].key,
      `newFire-${user.user._id.toString()}-${game1._id.toString()}-${homePlayer?._id.toString()}`
    )

    assert.strictEqual(addLikeResponse4.likePlayerPerformance?.player?.id, awayPlayerIds[2])

    // get which players ids user liked for particular game
    const playerLikesResponse = await client(user.tokens.accessToken).userLikesPlayersPerformance({
      input: { gameId: game1._id },
    })

    assert.deepStrictEqual(
      playerLikesResponse?.userLikesPlayersPerformance?.playersByGame?.players,
      [
        { id: homePlayerIds[0] },
        { id: awayPlayerIds[0] },
        { id: awayPlayerIds[1] },
        { id: awayPlayerIds[2] },
      ]
    )

    // get which players ids user liked for particular game, filter only some players ids
    const playerLikesResponseFiltered = await client(
      user.tokens.accessToken
    ).userLikesPlayersPerformance({
      input: { gameId: game1._id, playerIds: awayPlayerIds },
    })

    assert.deepStrictEqual(
      playerLikesResponseFiltered?.userLikesPlayersPerformance?.playersByGame?.players
        ?.map(({ id }) => id)
        .sort(),
      awayPlayerIds.slice(0, 3).sort()
    )
  })

  it('should add likes to multiple players in multiple games', async () => {
    const game1 = await generateRandomGameStats({ nameSuffix: 'A', numPlayersInEachTeam: 6 })
    const game2 = await generateRandomGameStats({ nameSuffix: 'B', numPlayersInEachTeam: 6 })

    const homePlayerIds = game1.homeTeamRoster.map((player) => player.player?._id?.toString())
    const awayPlayerIds = game1.awayTeamRoster.map((player) => player.player?._id?.toString())

    const homePlayerIds2 = game2.homeTeamRoster.map((player) => player.player?._id?.toString())
    const awayPlayerIds2 = game2.awayTeamRoster.map((player) => player.player?._id?.toString())

    const user = await generateRandomUser()

    const addLikeResponse1 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: homePlayerIds[0],
    })

    assert.strictEqual(addLikeResponse1.likePlayerPerformance?.player?.id, homePlayerIds[0])

    const addLikeResponse2 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game1._id,
      playerId: awayPlayerIds[0],
    })

    assert.strictEqual(addLikeResponse2.likePlayerPerformance?.player?.id, awayPlayerIds[0])

    const addLikeResponse3 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game2._id,
      playerId: homePlayerIds2[0],
    })

    assert.strictEqual(addLikeResponse3.likePlayerPerformance?.player?.id, homePlayerIds2[0])

    const addLikeResponse4 = await client(user.tokens.accessToken).likePlayerPerformance({
      gameId: game2._id,
      playerId: awayPlayerIds2[0],
    })

    assert.strictEqual(addLikeResponse4.likePlayerPerformance?.player?.id, awayPlayerIds2[0])

    sinon.assert.callCount(streamFeedAddActivityStub, 4)
    sinon.assert.callCount(pushNotificationServiceStub.sendPushNotification, 4)

    const homePlayer = await UserModel.findById(homePlayerIds[0])
    const homePlayer2 = await UserModel.findById(homePlayerIds2[0])
    const awayPlayer = await UserModel.findById(awayPlayerIds[0])
    const awayPlayer2 = await UserModel.findById(awayPlayerIds2[0])

    // get which players ids user liked for particular game
    const playersGamesLikesResponse = await client(
      user.tokens.accessToken
    ).userLikesPlayersGamesPerformance({
      input: {
        gameIds: [game1._id, game2._id],
        playerIds: [
          homePlayer?._id.toString(),
          homePlayer2?._id.toString(),
          awayPlayer?._id.toString(),
          awayPlayer2?._id.toString(),
        ],
      },
    })

    const playersByGame1 =
      playersGamesLikesResponse?.userLikesPlayersGamesPerformance?.playersByGame?.find(
        (game) => game.id === game1._id.toString()
      )

    const playersByGame2 =
      playersGamesLikesResponse?.userLikesPlayersGamesPerformance?.playersByGame?.find(
        (game) => game.id === game2._id.toString()
      )

    assert.deepStrictEqual(playersByGame1, {
      id: game1._id.toString(),
      players: [{ id: homePlayer?._id.toString() }, { id: awayPlayer?._id.toString() }],
    })
    assert.deepStrictEqual(playersByGame2, {
      id: game2._id.toString(),
      players: [{ id: homePlayer2?._id.toString() }, { id: awayPlayer2?._id.toString() }],
    })

    // get which players ids user liked for particular game
    const playersGamesLikesResponse2 = await client(
      user.tokens.accessToken
    ).userLikesPlayersGamesPerformance({
      input: {
        gameIds: [game1._id, game2._id],
        playerIds: [homePlayer?._id.toString(), homePlayer2?._id.toString()],
      },
    })

    const playersByGame12 =
      playersGamesLikesResponse2?.userLikesPlayersGamesPerformance?.playersByGame?.find(
        (game) => game.id === game1._id.toString()
      )

    const playersByGame22 =
      playersGamesLikesResponse2?.userLikesPlayersGamesPerformance?.playersByGame?.find(
        (game) => game.id === game2._id.toString()
      )

    assert.deepStrictEqual(playersByGame12, {
      id: game1._id.toString(),
      players: [{ id: homePlayer?._id.toString() }],
    })

    assert.deepStrictEqual(playersByGame22, {
      id: game2._id.toString(),
      players: [{ id: homePlayer2?._id.toString() }],
    })
  })

  it('should fail to add likes to multiple players in multiple games', async () => {
    const user = await generateRandomUser()
    try {
      // get which players ids user liked for particular game
      const playersGamesLikesResponse = await client(
        user.tokens.accessToken
      ).userLikesPlayersGamesPerformance({
        input: { gameIds: [new Types.ObjectId().toString(), new Types.ObjectId().toString()] },
      })

      const error = playersGamesLikesResponse?.userLikesPlayersGamesPerformance?.errors?.[0]

      assert.strictEqual(error?.message, 'Game(s) do(es) not exist.')
      assert.strictEqual(error?.path, 'userLikesPlayersGamesPerformance')

      assert.fail('Game(s) do(es) not exist.')
    } catch (error) {
      assert.strictEqual(error.message, 'Game(s) do(es) not exist.')
    }
  })

  it('should fail to add player performance like to non existing player', async () => {
    const user = await generateRandomUser()

    try {
      const addLikeResponse = await client(user.tokens.accessToken).likePlayerPerformance({
        gameId: new Types.ObjectId().toString(),
        playerId: new Types.ObjectId().toString(),
      })

      const error = addLikeResponse?.likePlayerPerformance?.errors?.[0]

      assert.strictEqual(error.message, 'Player does not exist.')
      assert.strictEqual(error.path, 'likePlayerPerformance')

      assert.fail('Player does not exist.')
    } catch (error) {
      assert.strictEqual(error.message, 'Player does not exist.')
    }
  })

  it('should fail to add player performance like to non existing game', async () => {
    const user = await generateRandomUser()
    const player = await generateRandomUser()

    try {
      const addLikeResponse = await client(user.tokens.accessToken).likePlayerPerformance({
        gameId: new Types.ObjectId().toString(),
        playerId: player?.user._id?.toString(),
      })

      const error = addLikeResponse?.likePlayerPerformance?.errors?.[0]

      assert.strictEqual(error.message, 'Game does not exist.')
      assert.strictEqual(error.path, 'likePlayerPerformance')

      assert.fail('Game does not exist.')
    } catch (error) {
      assert.strictEqual(error.message, 'Game does not exist.')
    }
  })

  it('should fail to remove player performance like from non existing player', async () => {
    const user = await generateRandomUser()

    try {
      const addLikeResponse = await client(user.tokens.accessToken).unlikePlayerPerformance({
        gameId: new Types.ObjectId().toString(),
        playerId: new Types.ObjectId().toString(),
      })

      const error = addLikeResponse?.unlikePlayerPerformance?.errors?.[0]

      assert.strictEqual(error.message, 'Player does not exist.')
      assert.strictEqual(error.path, 'unlikePlayerPerformance')

      assert.fail('Player does not exist.')
    } catch (error) {
      assert.strictEqual(error.message, 'Player does not exist.')
    }
  })

  it('should fail to remove player performance like from non existing game', async () => {
    const user = await generateRandomUser()
    const player = await generateRandomUser()

    try {
      const removeLikeResponse = await client(user.tokens.accessToken).unlikePlayerPerformance({
        gameId: new Types.ObjectId().toString(),
        playerId: player?.user._id?.toString(),
      })

      const error = removeLikeResponse?.unlikePlayerPerformance?.errors?.[0]

      assert.strictEqual(error.message, 'Game does not exist.')
      assert.strictEqual(error.path, 'unlikePlayerPerformance')

      assert.fail('Game does not exist.')
    } catch (error) {
      assert.strictEqual(error.message, 'Game does not exist.')
    }
  })

  it('should fail to find player performance likes per gameId from non existing game', async () => {
    const user = await generateRandomUser()

    try {
      const userLikesResponse = await client(user.tokens.accessToken).userLikesPlayersPerformance({
        input: {
          gameId: new Types.ObjectId().toString(),
        },
      })

      const error = userLikesResponse?.userLikesPlayersPerformance?.errors?.[0]

      assert.strictEqual(error?.message, 'Game does not exist.')
      assert.strictEqual(error?.path, 'userLikesPlayersPerformance')

      assert.fail('Game does not exist.')
    } catch (error) {
      assert.strictEqual(error.message, 'Game does not exist.')
    }
  })
})
