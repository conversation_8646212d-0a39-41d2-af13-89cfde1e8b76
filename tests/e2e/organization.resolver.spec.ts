import assert from 'assert'
import sinon from 'sinon'

import * as s3RequestPresigner from '../../src/helpers/getSignedUrl'
import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { StatusType } from '../graphql/components'
import { client } from '../utils/graphql'
import {
  generateRandomAdminUser,
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
} from '../utils/users'

import { s3ClientStub } from '.'

import { DeleteObjectCommand } from '@aws-sdk/client-s3'

describe('Organization resolver', () => {
  describe('organization query', () => {
    it('should return response by slug', async () => {
      await generateRandomOrganization('Test organization1', 'DE')

      const response = await client().organization({ slug: 'test-organization1' })

      assert.strictEqual(response.organization?.name, 'Test organization1')
      assert.strictEqual(response.organization?.country, 'DE')
      assert.strictEqual(response.organization?.slug, 'test-organization1')
    })

    it('should return response by id', async () => {
      await OrganizationModel.deleteMany({})

      const testOrganization = await generateRandomOrganization('Test organization2', 'SK')
      const response = await client().organization({ organizationId: testOrganization.id })

      assert.strictEqual(response.organization?.id, testOrganization.id)
      assert.strictEqual(response.organization?.name, 'Test organization2')
      assert.strictEqual(response.organization?.country, 'SK')
      assert.strictEqual(response.organization?.slug, 'test-organization2')
    })

    it('should return return error if failed to find organization', async () => {
      await OrganizationModel.deleteMany({})

      const findOne = OrganizationModel.findOne
      OrganizationModel.findOne = sinon.stub().throws('error')

      try {
        await client().organization({
          slug: null,
          organizationId: null,
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to find organization.')
      }

      OrganizationModel.findOne = findOne
    })
  })

  describe('organizations query', async () => {
    it('should return response if country and search query is null', async () => {
      await OrganizationModel.deleteMany({})
      const organizations = []

      for (let i = 0; i < 11; i++) {
        const organizationCz = await generateRandomOrganization('Test CZ Organization' + i)
        organizations.push(organizationCz)
      }

      for (let i = 0; i < 11; i++) {
        const organizationSk = await generateRandomOrganization('Test SK Organization' + i, 'SK')
        organizations.push(organizationSk)
      }

      const firstPage = await client().organizations({
        filter: {
          searchQuery: null,
          country: null,
        },
        pagination: {
          first: 10,
        },
      })
      assert.strictEqual(firstPage.organizations.edges.length, 10)
      assert.strictEqual(firstPage.organizations.pageInfo.totalCount, 22)
      assert.strictEqual(firstPage.organizations.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPage.organizations.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPage.organizations.edges[0].node.name, 'Test SK Organization10')
      assert.strictEqual(firstPage.organizations.edges[9].node.name, 'Test SK Organization1')

      const secondPage = await client().organizations({
        filter: {
          searchQuery: null,
          country: null,
        },
        pagination: {
          first: 10,
          after: firstPage.organizations.pageInfo.endCursor,
        },
      })
      assert.strictEqual(secondPage.organizations.pageInfo.hasNextPage, true)
      assert.strictEqual(secondPage.organizations.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPage.organizations.edges.length, 10)
      assert.strictEqual(secondPage.organizations.edges[0].node.name, 'Test SK Organization0')
      assert.strictEqual(secondPage.organizations.edges[9].node.name, 'Test CZ Organization2')

      const thirdPage = await client().organizations({
        filter: {
          searchQuery: null,
          country: null,
        },
        pagination: {
          first: 10,
          after: secondPage.organizations.pageInfo.endCursor,
        },
      })
      assert.strictEqual(thirdPage.organizations.pageInfo.hasNextPage, false)
      assert.strictEqual(thirdPage.organizations.pageInfo.hasPreviousPage, true)
      assert.strictEqual(thirdPage.organizations.edges.length, 2)
      assert.strictEqual(thirdPage.organizations.edges[0].node.name, 'Test CZ Organization1')
      assert.strictEqual(thirdPage.organizations.edges[1].node.name, 'Test CZ Organization0')
    })

    it('should return response with country SK ', async () => {
      await OrganizationModel.deleteMany({})
      const organizations = []

      for (let i = 0; i < 11; i++) {
        const organizationCz = await generateRandomOrganization('Test CZ Organization' + i)
        organizations.push(organizationCz)
      }

      for (let i = 0; i < 11; i++) {
        const organizationSk = await generateRandomOrganization('Test SK Organization' + i, 'SK')
        organizations.push(organizationSk)
      }

      const firstPage = await client().organizations({
        filter: {
          country: 'SK',
        },
        pagination: {
          first: 10,
        },
      })
      assert.strictEqual(firstPage.organizations.edges.length, 10)
      assert.strictEqual(firstPage.organizations.pageInfo.totalCount, 11)
      assert.strictEqual(firstPage.organizations.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPage.organizations.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPage.organizations.edges[0].node.name, 'Test SK Organization10')
      assert.strictEqual(firstPage.organizations.edges[9].node.name, 'Test SK Organization1')

      const secondPage = await client().organizations({
        filter: {
          country: 'SK',
        },
        pagination: {
          first: 10,
          after: firstPage.organizations.pageInfo.endCursor,
        },
      })
      assert.strictEqual(secondPage.organizations.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPage.organizations.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPage.organizations.edges.length, 1)
      assert.strictEqual(secondPage.organizations.edges[0].node.name, 'Test SK Organization0')
    })

    it('should return response with checked', async () => {
      await OrganizationModel.deleteMany()
      const organizations = []

      for (let i = 0; i < 2; i++) {
        const organizationCheckedUndefined = await generateRandomOrganization(
          'Test CZ Checked Undefined Organization' + i
        )
        organizations.push(organizationCheckedUndefined)
      }

      for (let i = 0; i < 5; i++) {
        const organizationNotCheckedCz = await generateRandomOrganization(
          'Test CZ Organization' + i,
          'CZ',
          false
        )
        organizations.push(organizationNotCheckedCz)
      }

      for (let i = 0; i < 11; i++) {
        const organizationCheckedSk = await generateRandomOrganization(
          'Test SK Organization' + i,
          'SK',
          true
        )
        organizations.push(organizationCheckedSk)
      }

      const firstCheckedPage = await client().organizations({
        filter: {
          checked: StatusType.True,
        },
        pagination: {
          first: 10,
        },
      })

      assert.strictEqual(firstCheckedPage.organizations.edges.length, 10)
      assert.strictEqual(firstCheckedPage.organizations.pageInfo.totalCount, 11)
      assert.strictEqual(firstCheckedPage.organizations.pageInfo.hasNextPage, true)
      assert.strictEqual(firstCheckedPage.organizations.pageInfo.hasPreviousPage, false)
      assert.strictEqual(
        firstCheckedPage.organizations.edges[0].node.name,
        'Test SK Organization10'
      )
      assert.strictEqual(firstCheckedPage.organizations.edges[9].node.name, 'Test SK Organization1')

      const secondCheckedPage = await client().organizations({
        filter: {
          checked: StatusType.True,
        },
        pagination: {
          first: 10,
          after: firstCheckedPage.organizations.pageInfo.endCursor,
        },
      })
      assert.strictEqual(secondCheckedPage.organizations.pageInfo.hasNextPage, false)
      assert.strictEqual(secondCheckedPage.organizations.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondCheckedPage.organizations.edges.length, 1)
      assert.strictEqual(
        secondCheckedPage.organizations.edges[0].node.name,
        'Test SK Organization0'
      )

      const notChecked = await client().organizations({
        filter: {
          checked: StatusType.False,
        },
      })

      assert.strictEqual(notChecked.organizations.edges.length, 5)
      assert.strictEqual(notChecked.organizations.pageInfo.hasNextPage, false)
      assert.strictEqual(notChecked.organizations.pageInfo.hasPreviousPage, false)
      assert.strictEqual(notChecked.organizations.edges[0].node.name, 'Test CZ Organization4')
      assert.strictEqual(notChecked.organizations.edges[4].node.name, 'Test CZ Organization0')

      const responseNotSet = await client().organizations({
        filter: {
          checked: StatusType.NotSet,
        },
      })

      assert.strictEqual(responseNotSet.organizations.edges.length, 2)
      assert.strictEqual(responseNotSet.organizations.pageInfo.hasNextPage, false)
      assert.strictEqual(responseNotSet.organizations.pageInfo.hasPreviousPage, false)
      assert.strictEqual(
        responseNotSet.organizations.edges[0].node.name,
        'Test CZ Checked Undefined Organization1'
      )
      assert.strictEqual(
        responseNotSet.organizations.edges[1].node.name,
        'Test CZ Checked Undefined Organization0'
      )

      const responseAll = await client().organizations({ pagination: { first: 20 } })

      assert.strictEqual(responseAll.organizations.edges.length, 18)
      assert.strictEqual(responseAll.organizations.pageInfo.hasNextPage, false)
      assert.strictEqual(responseAll.organizations.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseAll.organizations.edges[0].node.name, 'Test SK Organization10')
      assert.strictEqual(
        responseAll.organizations.edges[17].node.name,
        'Test CZ Checked Undefined Organization0'
      )
    })

    it('should return organization without logo', async () => {
      await OrganizationModel.deleteMany({})

      await generateRandomOrganization('Test organization without logo', 'SK')

      await generateRandomOrganization(
        'Test organization with logo',
        'SK',
        true,
        'test-organization-with-logo',
        'images/organizations/1/logo/1.png'
      )

      const response = await client().organizations({
        filter: {
          withoutLogo: true,
        },
      })

      assert.strictEqual(response.organizations.edges.length, 1)
      assert.strictEqual(
        response.organizations.edges[0].node.name,
        'Test organization without logo'
      )
    })
  })

  describe('create organization mutation', () => {
    it('should return organization', async () => {
      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).createOrganization({
        input: {
          name: 'Test create organization',
          country: 'SK',
          alternativeNames: [
            { name: 'Test create organization alternative name' },
            { name: 'Test create organization alternative name' },
          ],
          website: 'https://test-create-organization.com',
          instagramUrl: 'https://instagram.com/test-create-organization',
          facebookUrl: 'https://facebook.com/test-create-organization',
          twitterUrl: 'https://twitter.com/test-create-organization',
          tiktokUrl: 'https://tiktok.com/test-create-organization',
          arenaName: 'Test create organization arena',
          arenaAddress: 'Test create organization arena address',
          abbreviation: 'TCO',
        },
      })

      assert.strictEqual(response.createOrganization.organization?.name, 'Test create organization')
      assert.strictEqual(response.createOrganization.organization?.country, 'SK')
      assert.strictEqual(response.createOrganization.organization?.slug, 'test-create-organization')
      // it should filter unique alternative names
      assert.strictEqual(response.createOrganization.organization?.alternativeNames!.length, 1)
      assert.strictEqual(
        response.createOrganization.organization?.alternativeNames![0].name,
        'Test create organization alternative name'
      )
      assert.strictEqual(
        response.createOrganization.organization?.alternativeNames![0].slug,
        'test-create-organization-alternative-name'
      )
      assert.strictEqual(
        response.createOrganization.organization?.website,
        'https://test-create-organization.com'
      )
      assert.strictEqual(
        response.createOrganization.organization?.instagramUrl,
        'https://instagram.com/test-create-organization'
      )
      assert.strictEqual(
        response.createOrganization.organization?.facebookUrl,
        'https://facebook.com/test-create-organization'
      )
      assert.strictEqual(
        response.createOrganization.organization?.twitterUrl,
        'https://twitter.com/test-create-organization'
      )
      assert.strictEqual(
        response.createOrganization.organization?.tiktokUrl,
        'https://tiktok.com/test-create-organization'
      )
      assert.strictEqual(
        response.createOrganization.organization?.arenaName,
        'Test create organization arena'
      )
      assert.strictEqual(
        response.createOrganization.organization?.arenaAddress,
        'Test create organization arena address'
      )
      assert.strictEqual(response.createOrganization.organization?.abbreviation, 'TCO')
    })

    it('should return error if try to create duplicate organization', async () => {
      await generateRandomOrganization('Test create organization duplicate', 'SK')

      const admin = await generateRandomAdminUser()

      try {
        await client(admin.tokens.accessToken).createOrganization({
          input: { name: 'Test create organization duplicate', country: 'SK' },
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Organization already exists.')
      }
    })

    it('should return error if failed to create organization', async () => {
      const admin = await generateRandomAdminUser()

      const create = OrganizationModel.create
      OrganizationModel.create = sinon.stub().throws('error')

      try {
        await client(admin.tokens.accessToken).createOrganization({
          input: { name: 'Test create organization failed', country: 'SK' },
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to create organization.')
      }

      OrganizationModel.create = create
    })
  })

  describe('delete organization mutation', () => {
    it('should return true if organization is deleted', async () => {
      const admin = await generateRandomAdminUser()

      const organization = await generateRandomOrganization('Test delete organization', 'SK')

      const response = await client(admin.tokens.accessToken).deleteOrganization({
        deleteOrganizationId: organization.id,
      })

      assert.strictEqual(response.deleteOrganization, true)
    })

    it('should return error if organization cannot be deleted because is in use', async () => {
      const admin = await generateRandomAdminUser()

      const organization = await generateRandomOrganization(
        'Test delete organization cannot be deleted',
        'SK'
      )

      await generateRandomLeague(
        'Test league delete organization cannot be deleted',
        'SK',
        'Test-league-delete-organization-cannot-be-deleted'
      )

      await generateRandomTeam({
        name: 'Test team delete organization cannot be deleted',
        organization: organization.id,
      })

      try {
        const response = await client(admin.tokens.accessToken).deleteOrganization({
          deleteOrganizationId: organization.id,
        })
        assert.strictEqual(response.deleteOrganization, false)
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Organization is in use and cannot be deleted.'
        )
      }
    })

    it('should throw error if organization delete failed', async () => {
      const admin = await generateRandomAdminUser()

      const organization = await generateRandomOrganization('Test delete organization failed', 'SK')

      const findByIdAndDelete = sinon.stub(OrganizationModel, 'findByIdAndDelete').throws('error')

      try {
        await client(admin.tokens.accessToken).deleteOrganization({
          deleteOrganizationId: organization.id,
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to delete organization.')
      }

      findByIdAndDelete.restore()
    })
  })

  describe('update organization mutation', () => {
    it('should return organization', async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization(
        'Test update organization original',
        'SK'
      )

      const response = await client(admin.tokens.accessToken).updateOrganization({
        input: {
          country: 'CZ',
          name: 'Test update organization updated',
          alternativeNames: [{ name: 'Test create organization alternative name' }],
          website: 'https://test-create-organization.com',
          instagramUrl: 'https://instagram.com/test-create-organization',
          facebookUrl: 'https://facebook.com/test-create-organization',
          twitterUrl: 'https://twitter.com/test-create-organization',
          tiktokUrl: 'https://tiktok.com/test-create-organization',
          arenaName: 'Test create organization arena',
          arenaAddress: 'Test create organization arena address',
          abbreviation: 'TCOU',
        },
        updateOrganizationId: organization.id,
      })

      assert.strictEqual(response.updateOrganization.errors, null)
      assert.strictEqual(response.updateOrganization.organization?.country, 'CZ')
      assert.strictEqual(
        response.updateOrganization.organization?.name,
        'Test update organization updated'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.alternativeNames![0].name,
        'Test create organization alternative name'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.alternativeNames![0].slug,
        'test-create-organization-alternative-name'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.website,
        'https://test-create-organization.com'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.instagramUrl,
        'https://instagram.com/test-create-organization'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.facebookUrl,
        'https://facebook.com/test-create-organization'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.twitterUrl,
        'https://twitter.com/test-create-organization'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.tiktokUrl,
        'https://tiktok.com/test-create-organization'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.arenaName,
        'Test create organization arena'
      )
      assert.strictEqual(
        response.updateOrganization.organization?.arenaAddress,
        'Test create organization arena address'
      )
      assert.strictEqual(response.updateOrganization.organization?.abbreviation, 'TCOU')
    })

    it('should update organization without changing slug when name is not provided ', async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization(
        'Test update organization original',
        'SK'
      )

      const response = await client(admin.tokens.accessToken).updateOrganization({
        input: {
          country: 'CZ',
        },
        updateOrganizationId: organization.id,
      })

      assert.strictEqual(response.updateOrganization.errors, null)
      assert.strictEqual(response.updateOrganization.organization?.country, 'CZ')
      assert.strictEqual(
        response.updateOrganization.organization?.name,
        'Test update organization original'
      )
      assert.strictEqual(organization.slug, 'test-update-organization-original')
      assert.strictEqual(
        response.updateOrganization.organization?.slug,
        'test-update-organization-original'
      )
    })

    it('should return error if update fail', async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization('Test update organization fail', 'SK')

      const findOneAndUpdate = sinon.stub(OrganizationModel, 'findOneAndUpdate').throws('error')

      try {
        await client(admin.tokens.accessToken).updateOrganization({
          input: {
            country: 'CZ',
            name: 'Test update organization failed',
          },
          updateOrganizationId: organization.id,
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to update organization.')
      }

      findOneAndUpdate.restore()
    })

    it('should return error if a MongoServerError with a code other than 11000', async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization(
        'Test update organization mongo error',
        'SK'
      )

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mongoError = new Error('Other MongoDB error') as any
      mongoError.code = 12000

      sinon.stub(OrganizationModel, 'findByIdAndUpdate').throws(mongoError)

      try {
        await client(admin.tokens.accessToken).updateOrganization({
          input: {
            country: 'CZ',
            name: 'Test update organization mongo error',
          },
          updateOrganizationId: organization.id,
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to update organization.')
      }
    })
  })

  describe("upload organization's logo mutation", () => {
    it('should return response if upload logo is successful ', async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization('Test upload logo organization', 'SK')

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
      })

      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(response.uploadOrganizationLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(
        response.uploadOrganizationLogo.organization?.name,
        'Test upload logo organization'
      )
      assert.strictEqual(response.uploadOrganizationLogo.organization?.logo, null)

      const responseConfirmation = await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
        input: {
          uploadSuccessful: true,
        },
      })

      assert.strictEqual(responseConfirmation.uploadOrganizationLogo.errors, null)

      const logoKeyPattern = new RegExp(`images/organizations/${organization.id}/logo/\\d+\\.png$`)

      const check = await OrganizationModel.findById(organization.id)

      assert.match(check?.logo || '', logoKeyPattern)
      assert.strictEqual(check?.logoUnconfirmed, null)
    })

    it("should return error if couldn't create presigned url", async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization(
        'Test upload logo organization presigned fail',
        'SK'
      )

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .throws(new Error('Failed to create presigned url'))

      try {
        await client(admin.tokens.accessToken).uploadOrganizationLogo({
          uploadOrganizationLogoId: organization.id,
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to upload organization logo.')
      }

      s3RequestPresignerstub.restore()
    })

    it('should return response if upload unsuccessful', async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization(
        'Test upload logo organization unsuccessful',
        'SK'
      )

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
      })
      assert.strictEqual(response.uploadOrganizationLogo.errors, null)
      assert.strictEqual(response.uploadOrganizationLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(
        response.uploadOrganizationLogo.organization?.name,
        'Test upload logo organization unsuccessful'
      )
      assert.strictEqual(response.uploadOrganizationLogo.organization.country, 'SK')
      assert.strictEqual(response.uploadOrganizationLogo.organization?.logo, null)

      const logoKeyPattern = new RegExp(`images/organizations/${organization.id}/logo/\\d+\\.png$`)

      const firstCheck = await OrganizationModel.findById(organization.id)
      assert.strictEqual(firstCheck?.logo, undefined)
      assert.match(firstCheck?.logoUnconfirmed || '', logoKeyPattern)

      const responseConfirmation = await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
        input: {
          uploadSuccessful: false,
        },
      })

      assert.strictEqual(responseConfirmation.uploadOrganizationLogo.errors, null)

      const secondCheck = await OrganizationModel.findById(organization.id)
      assert.strictEqual(secondCheck?.logo, undefined)
      assert.strictEqual(secondCheck?.logoUnconfirmed, null)
    })

    it('should remove old logo from S3 if upload is successful', async () => {
      const admin = await generateRandomAdminUser()
      const organization = await generateRandomOrganization(
        'Test upload logo organization delete',
        'SK'
      )

      const s3RequestPresignerStub = sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
      })

      assert.strictEqual(s3RequestPresignerStub.calledOnce, true)
      assert.strictEqual(response.uploadOrganizationLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(
        response.uploadOrganizationLogo.organization?.name,
        'Test upload logo organization delete'
      )
      assert.strictEqual(response.uploadOrganizationLogo.organization?.logo, null)

      const responseConfirmation = await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
        input: {
          uploadSuccessful: true,
        },
      })

      s3ClientStub.send.resolves({
        key: 'some-key',
      })

      assert.strictEqual(responseConfirmation.uploadOrganizationLogo.errors, null)

      const logoKeyPattern = new RegExp(`images/organizations/${organization.id}/logo/\\d+\\.png$`)
      const check = await OrganizationModel.findById(organization.id)
      assert.match(check!.logo, logoKeyPattern)
      assert.strictEqual(check?.logoUnconfirmed, null)

      await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
      })

      const responseNewLogo = await client(admin.tokens.accessToken).uploadOrganizationLogo({
        uploadOrganizationLogoId: organization.id,
        input: { uploadSuccessful: true },
      })

      assert.strictEqual(responseNewLogo.uploadOrganizationLogo.errors, null)

      sinon.assert.called(s3ClientStub.send)
      sinon.assert.calledWith(
        s3ClientStub.send,
        sinon.match
          .instanceOf(DeleteObjectCommand)
          .and(sinon.match.has('input', sinon.match.has('Key', sinon.match(logoKeyPattern))))
      )

      s3RequestPresignerStub.restore()
    })
  })
})
