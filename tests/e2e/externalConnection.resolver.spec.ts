import assert from 'assert'

import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { UserInfoModel } from '../../src/modules/stats/models/userInfo'
import { client } from '../utils/graphql'
import { generateDiscoverUrl } from '../utils/stats'
import { generateRandomUser } from '../utils/users'

describe('ExternalConnection resolver', () => {
  before(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await UserInfoModel.deleteMany({})
  })

  it('should return name for external connection', async () => {
    const discoveredUrl = await generateDiscoverUrl()
    const externalId = 'test-external-id'
    const userName = 'Test User Name'

    await UserInfoModel.create({
      name: userName,
      externalId,
      crawlerDomainConfig: discoveredUrl.crawlerDomainConfig,
    })

    await generateRandomUser({
      firstname: 'Test',
      lastname: 'User',
      externalConnections: [
        {
          externalId,
          crawlerDomainConfig: discoveredUrl.crawlerDomainConfig,
        },
      ],
    })

    const response = await client().userQuery({
      slug: 'test-user',
    })

    assert.ok(response.user?.externalConnections)
    assert.strictEqual(response.user.externalConnections.length, 1)
    assert.strictEqual(response.user.externalConnections[0].externalId, externalId)
    assert.strictEqual(response.user.externalConnections[0].name, userName)
  })
})
