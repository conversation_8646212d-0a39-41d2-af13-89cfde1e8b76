import appleSigninAuth from 'apple-signin-auth'
import assert from 'assert'
import axios from 'axios'
import config from 'config'
import dayjs from 'dayjs'
import { readFileSync } from 'fs'
import jwt from 'jsonwebtoken'
import moment from 'moment'
import { Types } from 'mongoose'
import sinon from 'sinon'

import * as s3RequestPresigner from '../../src/helpers/getSignedUrl'
import { CURRENT_SEASON } from '../../src/helpers/league'
import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../src/modules/stats/models/game/game'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserInfoModel } from '../../src/modules/stats/models/userInfo'
import { UserStatsModel } from '../../src/modules/stats/models/userStats'
import { getBirthYearLimitFor18 } from '../../src/modules/user/helpers/birthYearLimit'
import { UserModel } from '../../src/modules/user/models'
import { AgencyModel } from '../../src/modules/user/models/agency'
import { Role as dbrole, SubRole } from '../../src/modules/user/models/user'
import { UserSubscriptionModel } from '../../src/modules/user/models/userSubscription'
import { UserViewModel } from '../../src/modules/user/models/userViews'
import {
  UserViewAggregatedModel,
  ViewAggregationType,
} from '../../src/modules/user/models/userViewsAggregated'
import { PushNotificationType } from '../../src/services/pushNotification'
import { client } from '../utils/graphql'
import { generateDiscoverUrl, generateRandomGameStats } from '../utils/stats'
import {
  createUserViews,
  generateRandomAdminUser,
  generateRandomAgency,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
  getUserUpdateInput,
} from '../utils/users'

import {
  BioGender,
  BioHandedness,
  BioPlayerType,
  BioPosition,
  BioSchoolType,
  ContactType,
  ExternalLinkType,
  Period,
  Platform,
  Role,
  TopViewedFilterType,
} from './../graphql/components'
import {
  amplitudeIdentifyStub,
  amplitudeTrackStub,
  googleServiceStub,
  pushNotificationServiceStub,
  s3ClientStub,
  sesClientStub,
  slackClientStub,
  snsClientStub,
  stripeClientStub,
} from '.'

import { faker } from '@faker-js/faker'

describe('User resolver', () => {
  describe('onboarding', () => {
    it('creates and verify new user', async () => {
      const email = faker.internet.email()
      const response = await client().registerOrLoginByEmail({
        input: {
          email,
          role: Role.Player,
        },
        newUrl: true,
      })

      assert.strictEqual(response.registerOrLoginByEmail.errors.length, 0)
      assert.strictEqual(response.registerOrLoginByEmail.user, null)

      // now get OTP from db and verify email
      const user = await UserModel.findOne({ email })

      assert.strictEqual(user?.userStats?.views, 0)
      assert.strictEqual(user?.userStats?.boosts, 0)
      assert.strictEqual(user?.userStats?.followers, 0)

      const verifyResponse = await client().verifyByEmail({
        code: user?.verificationCode?.code as string,
        email,
      })

      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [email]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'VerifyEmailStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )

      const verificationCode = user?.verificationCode?.code
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has(
            'TemplateData',
            `{"code":"${verificationCode}","verifyUrl":"https://www.graet.dev/verify-email?email=${user?.email ?? ''}&code=${verificationCode}"}`
          )
        )
      )

      const verifyByEmail = verifyResponse.verifyByEmail
      assert.strictEqual(verifyByEmail.errors.length, 0)
      assert.notEqual(verifyByEmail.tokens?.accessToken, null)

      assert.notEqual(verifyByEmail.tokens?.refreshToken, null)
      assert.strictEqual(verifyByEmail.user?.email, email)

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(amplitudeTrackStub, 'User Updated', {}, { user_id: user?.id })
    })

    it('creates and verifies a new user with the default role', async () => {
      const email = faker.internet.email()

      const response = await client().registerOrLoginByEmail({
        input: {
          email,
        },
        newUrl: true,
      })
      assert.strictEqual(response.registerOrLoginByEmail.errors.length, 0)
      assert.strictEqual(response.registerOrLoginByEmail.user, null)

      const user = await UserModel.findOne({ email })
      assert.strictEqual(user?.role, undefined)

      const verifyResponse = await client().verifyByEmail({
        code: user?.verificationCode?.code as string,
        email,
      })

      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [email]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'VerifyEmailStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )

      const verificationCode = user?.verificationCode?.code
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has(
            'TemplateData',
            `{"code":"${verificationCode}","verifyUrl":"https://www.graet.dev/verify-email?email=${user?.email ?? ''}&code=${verificationCode}"}`
          )
        )
      )

      const verifyByEmail = verifyResponse.verifyByEmail
      assert.strictEqual(verifyByEmail.errors.length, 0)
      assert.notEqual(verifyByEmail.tokens?.accessToken, null)
      assert.notEqual(verifyByEmail.tokens?.refreshToken, null)

      assert.strictEqual(verifyByEmail.user?.email, email)

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(amplitudeTrackStub, 'User Updated', {}, { user_id: user?.id })
    })

    it('logins already existing user and verify by OTP', async () => {
      const randomUser = await generateRandomUser()

      const response = await client().registerOrLoginByEmail({
        input: {
          email: randomUser.user.email,
          role: Role.Player,
        },
        newUrl: true,
      })

      assert.strictEqual(response.registerOrLoginByEmail.errors.length, 0)
      assert.strictEqual(response.registerOrLoginByEmail.user, null)
      sinon.assert.calledOnce(sesClientStub.send)
    })

    it('throws error if user email does not exist in db', async () => {
      const email = faker.internet.email()
      const response = await client().verifyByEmail({
        code: '123456',
        email,
      })

      const verifyByEmail = response.verifyByEmail
      assert.strictEqual(verifyByEmail.errors.length, 1)
      assert.strictEqual(
        verifyByEmail.errors[0].message,
        `User with email address ${email} not found`
      )
    })

    it('throws error, if verification code is incorrect ', async () => {
      const randomUser = await generateRandomUser()
      const response = await client().verifyByEmail({
        code: '123456',
        email: randomUser.user.email,
      })

      const verifyByEmail = response.verifyByEmail
      assert.strictEqual(verifyByEmail.errors.length, 1)
      assert.strictEqual(verifyByEmail.errors[0].message, 'Invalid verification code')
    })

    it('throws error, if verification code is expired ', async () => {
      const randomUser = await generateRandomUser()
      const response = await client().verifyByEmail({
        code: randomUser.user.verificationCode?.code as string,
        email: randomUser.user.email,
      })
      const verifyByEmail = response.verifyByEmail

      assert.strictEqual(verifyByEmail.errors.length, 0)

      await UserModel.updateOne(
        { _id: randomUser.user._id },
        { 'verificationCode.expirationDate': new Date(Date.now() - 1000 * 60 * 60) }
      )

      const responseExpired = await client().verifyByEmail({
        code: randomUser.user.verificationCode?.code as string,
        email: randomUser.user.email,
      })

      const verifyByEmailExpired = responseExpired.verifyByEmail
      assert.strictEqual(verifyByEmailExpired.errors.length, 1)
      assert.strictEqual(
        verifyByEmailExpired.errors[0].message,
        'Verification code expired, new code sent'
      )

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('throws error if failed to register via email', async () => {
      const email = faker.internet.email()
      const findOne = UserModel.findOne
      UserModel.findOne = sinon.stub().throws('error')
      try {
        await client().registerOrLoginByEmail({
          input: {
            email: email,
            role: Role.Player,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to register user by email. Please try again later.'
        )
      }
      UserModel.findOne = findOne
    })

    it('should send slack message if user sets agencyUnverified', async () => {
      const randomUser = await generateRandomUser()

      const agency = await generateRandomAgency('agencyUnverified test agency slack')
      const agencyId = agency._id.toString()

      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: agencyId,
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).currentUserWithAgencyUnverified()
      assert.strictEqual(response.currentUser.bio?.agencyUnverified?.id, agencyId)

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `User ${randomUser.user.name} set new agency which needs verification: ${agencyId}, <http://admin.${config.domain}/dashboard/users/form/?id=${randomUser.user._id.toString()}|admin panel>`
          )
        )
      )
    })
  })

  describe('visitUser', () => {
    it('increases number of user view', async () => {
      const randomUser = await generateRandomUser()

      const response = await client().visitUser({
        visitUserId: randomUser.user._id,
      })
      assert.strictEqual(response.visitUser.userStats?.views, 1)
      assert.strictEqual(response.visitUser.id, randomUser.user._id.toString())

      const response2 = await client(undefined, {
        'user-agent': 'safari',
      }).visitUser({
        visitUserId: randomUser.user._id,
      })
      assert.strictEqual(response2.visitUser.userStats?.views, 2)

      const updatedUser = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(updatedUser?.userStats?.views, 2)
      assert.strictEqual(updatedUser?.id, randomUser.user._id.toString())

      const viewer = await UserViewModel.findOne({ user: randomUser.user._id })
      assert.notStrictEqual(viewer, null)
    })

    it('does not increase number of user view if user is the same (increase after 4 hours)', async () => {
      const randomUser = await generateRandomUser()

      const response = await client(randomUser.tokens.accessToken).visitUser({
        visitUserId: randomUser.user._id,
      })
      assert.strictEqual(response.visitUser.userStats?.views, 1)

      const response1 = await client(randomUser.tokens.accessToken).visitUser({
        visitUserId: randomUser.user._id,
      })
      assert.strictEqual(response1.visitUser.userStats?.views, 1)

      // after 4 hours it should increase again (4 hours + 1 second)
      const clock = sinon.useFakeTimers(new Date(Date.now() + 1000 + 1000 * 60 * 60 * 4).getTime())
      const response2 = await client(randomUser.tokens.accessToken).visitUser({
        visitUserId: randomUser.user._id,
      })
      assert.strictEqual(response2.visitUser.userStats?.views, 2)

      clock.restore()
    })

    it('throws error when user does not exist', async () => {
      try {
        await client().visitUser({
          visitUserId: new Types.ObjectId(),
        })
        assert.strictEqual('this should never happen', '')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to visit user')
      }
    })

    it('should aggregate views on weekly and monthly basis', async () => {
      const randomUser = await generateRandomUser()

      const dayInMiliseconds = 24 * 60 * 60 * 1000
      const fourHoursInMiliseconds = 4 * 60 * 60 * 1000
      const clock = sinon.useFakeTimers() // always start with 1970-01-01

      const visit = async () => {
        return await client().visitUser({
          visitUserId: randomUser.user._id,
        })
      }

      // 1st week one visit
      const response = await visit()
      assert.strictEqual(response.visitUser.userStats?.views, 1)

      clock.tick(8 * dayInMiliseconds)

      const response2 = await visit()
      assert.strictEqual(response2.visitUser.userStats?.views, 2)

      clock.tick(1 * fourHoursInMiliseconds)

      // 2nd week 2 visits + 1st month 3 visits
      const response3 = await visit()
      assert.strictEqual(response3.visitUser.userStats?.views, 3)

      // 2nd month 1 visits
      clock.tick(32 * dayInMiliseconds)

      const response4 = await visit()
      assert.strictEqual(response4.visitUser.userStats?.views, 4)

      const aggregatedViews = await UserViewAggregatedModel.find({
        user: randomUser.user._id,
      })

      assert.strictEqual(aggregatedViews.length, 5)

      // reset clock so we can assert dates easily
      clock.reset()

      // 1st week
      assert.strictEqual(
        aggregatedViews[0].date.toISOString(),
        moment().utc().startOf('isoWeek').toDate().toISOString()
      )
      assert.strictEqual(aggregatedViews[0].type, ViewAggregationType.WEEKLY)
      assert.strictEqual(aggregatedViews[0].totalViews, 1)

      // 1st month
      assert.strictEqual(
        aggregatedViews[1].date.toISOString(),
        moment().utc().startOf('month').toDate().toISOString()
      )
      assert.strictEqual(aggregatedViews[1].type, ViewAggregationType.MONTHLY)
      assert.strictEqual(aggregatedViews[1].totalViews, 3)

      // 2nd week of 1st month
      assert.strictEqual(
        aggregatedViews[2].date.toISOString(),
        moment().utc().startOf('isoWeek').add(1, 'week').toDate().toISOString()
      )
      assert.strictEqual(aggregatedViews[2].type, ViewAggregationType.WEEKLY)
      assert.strictEqual(aggregatedViews[2].totalViews, 2)

      // 2nd month
      assert.strictEqual(
        aggregatedViews[3].date.toISOString(),
        moment().utc().startOf('month').add(1, 'month').toDate().toISOString()
      )
      assert.strictEqual(aggregatedViews[3].type, ViewAggregationType.MONTHLY)
      assert.strictEqual(aggregatedViews[3].totalViews, 1)

      // 2nd week of 2nd month
      assert.strictEqual(
        aggregatedViews[4].date.toISOString(),
        moment().utc().add(1, 'month').add(8, 'day').startOf('isoWeek').toDate().toISOString()
      )
      assert.strictEqual(aggregatedViews[4].type, ViewAggregationType.WEEKLY)
      assert.strictEqual(aggregatedViews[4].totalViews, 1)

      clock.restore()
    })

    it('should send notification to user if visit is from an agent with agency', async () => {
      const agency = await generateRandomAgency('test visit agency')
      const agent = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
        agency: agency.id,
      })
      const randomUser = await generateRandomUser()

      await client(agent.tokens.accessToken).visitUser({
        visitUserId: randomUser.user._id,
      })

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      assert.strictEqual(pushArgs[0].name, randomUser.user.name)
      assert.strictEqual(pushArgs[1].key, undefined)
      assert.strictEqual(
        pushArgs[1].message,
        `Agent ${agent.user.name} from ${agency.name} just viewed your profile.`
      )
      assert.strictEqual(pushArgs[1].type, PushNotificationType.VIEW_AGENT)
      assert.strictEqual(pushArgs[1].title, 'New view from agent! 🕵️')
      assert.strictEqual(pushArgs[1].params.destinationPath, `/agent/${agent.user.slug}`)
    })

    it('should send notification to user if visit is from a scout', async () => {
      const agent = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.SCOUT,
      })

      const randomUser = await generateRandomUser()

      await client(agent.tokens.accessToken).visitUser({
        visitUserId: randomUser.user._id,
      })

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      assert.strictEqual(pushArgs[0].name, randomUser.user.name)
      assert.strictEqual(pushArgs[1].key, undefined)
      assert.strictEqual(pushArgs[1].message, `Scout ${agent.user.name} just viewed your profile.`)
      assert.strictEqual(pushArgs[1].type, PushNotificationType.VIEW_SCOUT)
      assert.strictEqual(pushArgs[1].title, 'New view from scout! 🕵️')
      assert.strictEqual(pushArgs[1].params.destinationPath, `/`)
    })

    it('should send notification to user if visit is from a coach', async () => {
      const agent = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.COACH,
      })

      const randomUser = await generateRandomUser()

      await client(agent.tokens.accessToken).visitUser({
        visitUserId: randomUser.user._id,
      })

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      assert.strictEqual(pushArgs[0].name, randomUser.user.name)
      assert.strictEqual(pushArgs[1].key, undefined)
      assert.strictEqual(pushArgs[1].message, `Coach ${agent.user.name} just viewed your profile.`)
      assert.strictEqual(pushArgs[1].type, PushNotificationType.VIEW_COACH)
      assert.strictEqual(pushArgs[1].title, 'New view from coach! 🕵️')
      assert.strictEqual(pushArgs[1].params.destinationPath, `/`)
    })

    it('should send notification if user gets 50 views', async () => {
      const randomUser = await generateRandomUser()

      await UserModel.updateOne(
        { _id: randomUser.user._id },
        {
          'userStats.views': 49,
        }
      )

      await client().visitUser({
        visitUserId: randomUser.user._id,
      })

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      assert.strictEqual(pushArgs[0].name, randomUser.user.name)
      assert.strictEqual(
        pushArgs[1].message,
        'Congrats on reaching 50 views. Check out your progress.'
      )
      assert.strictEqual(pushArgs[1].type, PushNotificationType.VIEW_INSIGHTS)
      assert.strictEqual(pushArgs[1].title, 'View insights unlocked! 🎉')
      assert.strictEqual(pushArgs[1].params.destinationPath, '/activity')
    })
  })

  describe('user hasGames', async () => {
    beforeEach(async () => {
      await GameModel.deleteMany({})
      await CrawlerDomainConfigModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
    })

    it('should return true if user has games', async () => {
      const discoveredUrl = await generateDiscoverUrl()

      const user = await generateRandomUser({
        externalConnections: [
          { externalId: '123', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
        ],
      })

      await generateRandomGameStats({
        nameSuffix: 'UserHasGamesTest',
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '5',
          discoveredUrl,
          season: '2024-2025', // Current season
          externalInfo: {
            homeTeamName: 'Home Team',
            awayTeamName: 'Away Team',
            leagueName: 'Test League',
          },
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: 'Test Player',
                playerExternalId: '123',
              },
              player: null,
            },
          ],
        },
      })

      const response = await client().userQuery({
        slug: user.user.slug || '',
      })

      assert.strictEqual(response.user?.hasGames, true)
    })

    it('should return false if user has no games', async () => {
      const discoveredUrl = await generateDiscoverUrl()

      const user = await generateRandomUser({
        externalConnections: [
          { externalId: '123', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
        ],
      })

      await generateRandomGameStats({
        nameSuffix: 'UserHasNoGamesTest',
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '5',
          discoveredUrl,
          externalInfo: {
            homeTeamName: 'Home Team',
            awayTeamName: 'Away Team',
            leagueName: 'Test League',
          },
        },
      })

      const response = await client().userQuery({
        slug: user.user.slug || '',
      })

      assert.strictEqual(response.user?.hasGames, false)
    })

    it('should return false if user only has games from previous seasons', async () => {
      const discoveredUrl = await generateDiscoverUrl()

      const user = await generateRandomUser({
        externalConnections: [
          { externalId: '123', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
        ],
      })

      // Create a game in the previous season
      await generateRandomGameStats({
        nameSuffix: 'UserHasPreviousSeasonGamesTest',
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '5',
          discoveredUrl,
          season: '2023-2024', // Previous season
          externalInfo: {
            homeTeamName: 'Home Team',
            awayTeamName: 'Away Team',
            leagueName: 'Test League',
          },
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: 'Test Player',
                playerExternalId: '123',
              },
              player: null,
            },
          ],
        },
      })

      const response = await client().userQuery({
        slug: user.user.slug || '',
      })

      // Should return false because the game is from a previous season
      assert.strictEqual(response.user?.hasGames, false)
    })

    it('should only consider current season games for hasGames field', async () => {
      const discoveredUrl = await generateDiscoverUrl()

      const user = await generateRandomUser({
        externalConnections: [
          { externalId: '123', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
        ],
      })

      // Create a game in the current season
      const currentSeasonGame = await generateRandomGameStats({
        nameSuffix: `CurrentSeason`,
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '5',
          discoveredUrl,
          season: CURRENT_SEASON, // Current season
          externalInfo: {
            homeTeamName: 'Home Team Current',
            awayTeamName: 'Away Team Current',
            leagueName: 'Test League Current',
          },
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: 'Test Player',
                playerExternalId: '123',
              },
              player: null,
            },
          ],
        },
      })

      // Also create a game in the previous season
      await generateRandomGameStats({
        nameSuffix: `PreviousSeason`,
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '6',
          discoveredUrl,
          season: '2023-2024', // Previous season
          externalInfo: {
            homeTeamName: 'Home Team Previous',
            awayTeamName: 'Away Team Previous',
            leagueName: 'Test League Previous',
          },
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: 'Test Player',
                playerExternalId: '123',
              },
              player: null,
            },
          ],
        },
      })

      // First check - should return true because there's a current season game
      let response = await client().userQuery({
        slug: user.user.slug || '',
      })
      assert.strictEqual(response.user?.hasGames, true)

      // Now delete the current season game
      await GameModel.deleteOne({ _id: currentSeasonGame._id })

      // Second check - should return false even though there's still a previous season game
      response = await client().userQuery({
        slug: user.user.slug || '',
      })
      assert.strictEqual(response.user?.hasGames, false)
    })
  })

  describe('followUser', () => {
    it('increases number of followers by registered users', async () => {
      const follower = await generateRandomUser()
      const followee = await generateRandomUser()
      const response = await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      assert.strictEqual(response.followUser.userStats?.followers, 1)
      assert.strictEqual(response.followUser?.id, followee.user._id.toString())

      const updateUser = await UserModel.findById(followee.user._id)
      assert.strictEqual(updateUser?.userStats?.followers, 1)

      const subscription = await UserSubscriptionModel.findOne({
        user: followee.user._id,
        follower: follower.user._id,
      })
      assert.notStrictEqual(subscription, null)
    })

    it('increases number of followings for the follower', async () => {
      const follower = await generateRandomUser()
      const followee = await generateRandomUser()

      // Check initial followings count
      const initialFollowerResponse = await client(
        follower.tokens.accessToken
      ).currentUserFollowings()
      assert.strictEqual(initialFollowerResponse.currentUser.userStats?.followings, 0)

      // Follow user
      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      // Check updated followings count
      const updatedFollowerResponse = await client(
        follower.tokens.accessToken
      ).currentUserFollowings()
      assert.strictEqual(updatedFollowerResponse.currentUser.userStats?.followings, 1)

      // Verify in database
      const updatedFollower = await UserModel.findById(follower.user._id)
      assert.strictEqual(updatedFollower?.userStats?.followings, 1)
    })

    it('should be possible to get information whether I follow user or not', async () => {
      const follower = await generateRandomUser()
      const followee = await generateRandomUser()
      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      const response = await client(follower.tokens.accessToken).userQuery({
        slug: followee.user.slug!,
      })

      assert.strictEqual(response.user?.following, true)
    })

    it('should send push notification to user when followed by a agent', async () => {
      const agency = await generateRandomAgency('test follower agency')
      const follower = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
        agency: agency.id,
      })

      const followee = await generateRandomUser()

      await client(followee.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      assert.strictEqual(pushArgs[0].name, followee.user.name)
      assert.strictEqual(pushArgs[1].key, `newFollower-${follower.user._id}-${followee.user._id}`)
      assert.strictEqual(pushArgs[1].message, `Agent ${follower.user.name} is now following you.`)
      assert.strictEqual(pushArgs[1].type, PushNotificationType.FOLLOW_AGENT)
      assert.strictEqual(pushArgs[1].title, 'New follow from agent! 🤩🕵️')
      assert.strictEqual(pushArgs[1].params.destinationPath, `/agent/${follower.user.slug}`)
    })

    it('should send push notification to user when followed by a coach', async () => {
      const follower = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.COACH,
      })

      const followee = await generateRandomUser()

      await client(followee.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      assert.strictEqual(pushArgs[0].name, followee.user.name)
      assert.strictEqual(pushArgs[1].key, `newFollower-${follower.user._id}-${followee.user._id}`)
      assert.strictEqual(pushArgs[1].message, `Coach ${follower.user.name} is now following you.`)
      assert.strictEqual(pushArgs[1].type, PushNotificationType.FOLLOW_COACH)
      assert.strictEqual(pushArgs[1].title, 'New follow from coach! 🤩🕵️')
      assert.strictEqual(pushArgs[1].params.destinationPath, `/`)
    })

    it('should send push notification to user when followed by an scout', async () => {
      const follower = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.SCOUT,
      })

      const followee = await generateRandomUser()

      await client(followee.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushArgs = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      assert.strictEqual(pushArgs[0].name, followee.user.name)
      assert.strictEqual(pushArgs[1].key, `newFollower-${follower.user._id}-${followee.user._id}`)
      assert.strictEqual(pushArgs[1].message, `Scout ${follower.user.name} is now following you.`)
      assert.strictEqual(pushArgs[1].type, PushNotificationType.FOLLOW_SCOUT)
      assert.strictEqual(pushArgs[1].title, 'New follow from scout! 🤩🕵️')
      assert.strictEqual(pushArgs[1].params.destinationPath, `/`)
    })

    it('throws error when user does not exist', async () => {
      try {
        const user = await generateRandomUser()
        await client(user.tokens.accessToken).followUser({
          followUserId: new Types.ObjectId().toString(),
        })
        assert.strictEqual('this should never happen', '')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User does not exist.')
      }
    })

    it('throws error when no user logged in and tries to call follow mutation', async () => {
      const randomUser = await generateRandomUser()
      try {
        await client().followUser({
          followUserId: randomUser.user._id.toString(),
        })
        assert.strictEqual('this should never happen', '')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Authentication token is not valid.')
      }
    })

    it('should send push notification to user when followed', async () => {
      const follower = await generateRandomUser()
      const followee = await generateRandomUser()

      await client(followee.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      // if this ever fails, notification was not saved fast enough into db
      await new Promise((resolve) => setTimeout(resolve, 500))

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)
      assert.strictEqual(
        pushNotificationServiceStub.sendPushNotification.getCall(0).args[1].message,
        `${follower.user.name} is now following you. Check out their profile!`
      )

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(amplitudeTrackStub, 'newFollower', {})
    })

    it('should return error if a MongoServerError with a code other than 11000', async () => {
      const follower = await generateRandomUser()
      const followee = await generateRandomUser()

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mongoError = new Error('Duplicate key error') as any
      mongoError.code = 12000

      const findOneAndUpdate = sinon.stub(UserModel, 'findOneAndUpdate').throws(mongoError)

      const response = await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      assert.strictEqual(response.followUser.id, followee.user._id.toString())

      sinon.assert.calledOnce(findOneAndUpdate)

      findOneAndUpdate.restore()
    })
  })

  describe('unfollowUser', () => {
    it('decreases number of followers by registered users and control also there is no row in UserSubscription collection for this user', async () => {
      const follower = await generateRandomUser()
      const followee = await generateRandomUser()
      const response = await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      assert.strictEqual(response.followUser.userStats?.followers, 1)

      const updatedResponse = await client(follower.tokens.accessToken).unfollowUser({
        unfollowUserId: followee.user._id.toString(),
      })

      await new Promise((resolve) => setTimeout(resolve, 500))

      assert.strictEqual(updatedResponse.unfollowUser.user?.userStats?.followers, 0)
      assert.strictEqual(updatedResponse.unfollowUser?.user?.id, followee.user._id.toString())

      const updateUser = await UserModel.findById(followee.user._id)
      assert.strictEqual(updateUser?.userStats?.followers, 0)

      const subscription = await UserSubscriptionModel.findOne({
        user: followee.user._id,
        follower: follower.user._id,
      })
      assert.strictEqual(subscription, null)
    })

    it('decreases number of followings for the follower', async () => {
      const follower = await generateRandomUser()
      const followee = await generateRandomUser()

      // Follow user first
      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      // Check followings count after following
      const followerAfterFollowing = await client(
        follower.tokens.accessToken
      ).currentUserFollowings()
      assert.strictEqual(followerAfterFollowing.currentUser.userStats?.followings, 1)

      // Unfollow user
      await client(follower.tokens.accessToken).unfollowUser({
        unfollowUserId: followee.user._id.toString(),
      })

      // Check updated followings count
      const updatedFollowerResponse = await client(
        follower.tokens.accessToken
      ).currentUserFollowings()
      assert.strictEqual(updatedFollowerResponse.currentUser.userStats?.followings, 0)

      // Verify in database
      const updatedFollower = await UserModel.findById(follower.user._id)
      assert.strictEqual(updatedFollower?.userStats?.followings, 0)
    })

    it('throws error when user to unfollow does not exist', async () => {
      const user = await generateRandomUser()
      try {
        await client(user.tokens.accessToken).unfollowUser({
          unfollowUserId: new Types.ObjectId().toString(),
        })

        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User does not exist.')
      }
    })

    it('throws an error when you try to unfollow a user you havent followed', async () => {
      const user1 = await generateRandomUser()
      const user2 = await generateRandomUser()
      const response = await client(user1.tokens.accessToken).unfollowUser({
        unfollowUserId: user2.user._id.toString(),
      })
      assert.strictEqual(response.unfollowUser.errors[0].message, 'Follow not found')
    })
  })

  describe('myFollowers query', () => {
    it('will create 20 users, page after 10 users and check it', async () => {
      await generateRandomTeam({ name: 'Team 1' })

      const followee = await generateRandomUser()
      const followers = []
      for (let i = 0; i < 20; i++) {
        const follower = await generateRandomUser()
        await client(follower.tokens.accessToken).followUser({
          followUserId: followee.user._id.toString(),
        })
        followers.push(follower)
      }

      // follow back first user
      await client(followee.tokens.accessToken).followUser({
        followUserId: followers[0].user._id.toString(),
      })

      const response = await client(followee.tokens.accessToken).myFollowers({
        first: 10,
        filter: { role: Role.Player },
      })
      assert.strictEqual(response.myFollowers.pageInfo.hasNextPage, true)
      assert.strictEqual(response.myFollowers.edges.length, 10)
      assert.strictEqual(response.myFollowers.edges[0].node.name, followers[19].user.name)
      assert.strictEqual(response.myFollowers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(response.myFollowers.edges[0].iFollow, false)
      assert.strictEqual(response.myFollowers.edges[9].node.name, followers[10].user.name)
      assert.strictEqual(response.myFollowers.edges[9].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(response.myFollowers.edges[9].iFollow, false)

      const response1 = await client(followee.tokens.accessToken).myFollowers({
        after: response.myFollowers.pageInfo.endCursor,
        filter: { role: Role.Player },
      })

      assert.strictEqual(response1.myFollowers.pageInfo.hasNextPage, false)
      assert.strictEqual(response1.myFollowers.pageInfo.hasPreviousPage, true)
      assert.strictEqual(response1.myFollowers.edges.length, 10)
      assert.strictEqual(response1.myFollowers.edges[0].node.name, followers[9].user.name)
      assert.strictEqual(response1.myFollowers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(response1.myFollowers.edges[0].iFollow, false)
      assert.strictEqual(response1.myFollowers.edges[9].node.name, followers[0].user.name)
      assert.strictEqual(response1.myFollowers.edges[9].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(response1.myFollowers.edges[9].iFollow, true)
    })
  })

  describe('deleteAccount mutation', () => {
    it('will create user and then delete, check db that this user still exists, but is marked for deletion.', async () => {
      const user = await generateRandomUser()
      const response = await client(user.tokens.accessToken).deleteAccount()

      assert.strictEqual(response?.deleteAccount.valueOf(), true)

      const foundUser = await UserModel.findById(user.user._id)
      assert.strictEqual(foundUser?.markedForDeletion, true)

      sinon.assert.calledOnce(amplitudeIdentifyStub)
      sinon.assert.calledWith(
        amplitudeIdentifyStub,
        sinon.match.has(
          '_properties',
          sinon.match.has(
            '$set',
            sinon.match({
              deleted: true,
            })
          )
        ),
        sinon.match({ user_id: user.user._id.toString() })
      )
    })

    it('throws error when user doesnt exist', async () => {
      try {
        await client().deleteAccount()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Authentication token is not valid.')
      }
    })

    it('throws error when deleted user does not exist.', async () => {
      const user = await generateRandomUser()
      await UserModel.deleteOne({ _id: user.user._id })
      try {
        await client(user.tokens.accessToken).deleteAccount()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to delete user')
      }
    })

    it('will send slack message when user deletes account', async () => {
      const user = await generateRandomUser()

      await client(user.tokens.accessToken).deleteAccount()

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('text', sinon.match(`User ${user.user.name} has deleted their account.`))
      )

      sinon.assert.calledOnce(amplitudeIdentifyStub)
      sinon.assert.calledWith(
        amplitudeIdentifyStub,
        sinon.match.has(
          '_properties',
          sinon.match.has(
            '$set',
            sinon.match({
              deleted: true,
            })
          )
        ),
        sinon.match({ user_id: user.user._id.toString() })
      )
    })

    it('should call deleteEndpoint for each registered device with arn', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        { _id: user.user._id },
        {
          registeredDevices: [
            { arn: 'arn:aws:sns:device1' },
            { arn: 'arn:aws:sns:device2' },
            { arn: null },
          ],
        }
      )

      await client(user.tokens.accessToken).deleteAccount()

      sinon.assert.calledTwice(snsClientStub.deleteEndpoint)
      sinon.assert.calledWith(snsClientStub.deleteEndpoint, {
        EndpointArn: 'arn:aws:sns:device1',
      })
      sinon.assert.calledWith(snsClientStub.deleteEndpoint, {
        EndpointArn: 'arn:aws:sns:device2',
      })

      sinon.assert.notCalled(
        snsClientStub.deleteEndpoint.withArgs({
          EndpointArn: null,
        })
      )
    })
  })

  describe('myFollowingAgencies query', () => {
    it('will create 25 agencies, then follow with them, page after 10 agencies, and check pagination', async () => {
      const follower = await generateRandomUser()
      const agencies = []
      for (let i = 0; i < 25; i++) {
        const followee = await generateRandomAgency(`Agency ${i + 1}`)
        await client(follower.tokens.accessToken).followAgency({
          id: followee._id.toString(),
        })
        agencies.push(followee)
      }
      const isFollowingAgency = await client(follower.tokens.accessToken).isFollowingAgency({
        agencyId: agencies[6].id?.toString(),
      })
      const response = await client(follower.tokens.accessToken).myFollowingAgencies({
        pagination: {
          first: 10,
        },
      })

      assert.equal(isFollowingAgency.isFollowingAgency, true)
      assert.strictEqual(response.myFollowingAgencies.pageInfo.hasNextPage, true)
      assert.strictEqual(response.myFollowingAgencies.edges.length, 10)
      assert.strictEqual(response.myFollowingAgencies.edges[0].node.name, 'Agency 25')
      assert.strictEqual(response.myFollowingAgencies.edges[9].node.name, 'Agency 16')

      const response1 = await client(follower.tokens.accessToken).myFollowingAgencies({
        pagination: {
          first: 10,
          after: response.myFollowingAgencies.pageInfo.endCursor,
        },
      })

      assert.strictEqual(response1.myFollowingAgencies.pageInfo.hasNextPage, true)
      assert.strictEqual(response1.myFollowingAgencies.pageInfo.hasPreviousPage, true)
      assert.strictEqual(response1.myFollowingAgencies.edges.length, 10)
      assert.strictEqual(response1.myFollowingAgencies.edges[0].node.name, 'Agency 15')
      assert.strictEqual(response1.myFollowingAgencies.edges[9].node.name, 'Agency 6')

      const response2 = await client(follower.tokens.accessToken).myFollowingAgencies({
        pagination: {
          first: 10,
          after: response1.myFollowingAgencies.pageInfo.endCursor,
        },
      })
      assert.strictEqual(response2.myFollowingAgencies.pageInfo.hasNextPage, false)
      assert.strictEqual(response2.myFollowingAgencies.pageInfo.hasPreviousPage, true)
      assert.strictEqual(response2.myFollowingAgencies.edges.length, 5)
    })
  })

  describe('myFollowing query', () => {
    it('will create 20 users, then follow with them, page after 10 users, and check pagination', async () => {
      const follower = await generateRandomUser()
      const followees = []
      for (let i = 0; i < 20; i++) {
        const followee = await generateRandomUser()
        await client(follower.tokens.accessToken).followUser({
          followUserId: followee.user._id.toString(),
        })
        followees.push(followee)
      }

      const response = await client(follower.tokens.accessToken).myFollowing()
      assert.strictEqual(response.myFollowing.pageInfo.hasNextPage, true)
      assert.strictEqual(response.myFollowing.edges.length, 10)
      assert.strictEqual(response.myFollowing.edges[0].node.name, followees[19].user.name)
      assert.strictEqual(response.myFollowing.edges[9].node.name, followees[10].user.name)
      assert.strictEqual(response.myFollowing.edges[0].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(response.myFollowing.edges[9].node.currentTeam?.name, 'Team 1')

      const response1 = await client(follower.tokens.accessToken).myFollowing({
        pagination: {
          first: 10,
          after: response.myFollowing.pageInfo.endCursor,
        },
      })

      assert.strictEqual(response1.myFollowing.pageInfo.hasNextPage, false)
      assert.strictEqual(response1.myFollowing.pageInfo.hasPreviousPage, true)
      assert.strictEqual(response1.myFollowing.edges.length, 10)
      assert.strictEqual(response1.myFollowing.edges[0].node.name, followees[9].user.name)
      assert.strictEqual(response1.myFollowing.edges[9].node.name, followees[0].user.name)
      assert.strictEqual(response1.myFollowing.edges[0].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(response1.myFollowing.edges[9].node.currentTeam?.name, 'Team 1')
    })
  })

  describe('isFollowing query', () => {
    it('will create user, follow with another user and check if is following', async () => {
      const follower = await generateRandomUser()

      const followee = await generateRandomUser()
      await client(follower.tokens.accessToken).followUser({
        followUserId: followee.user._id.toString(),
      })

      const response = await client(follower.tokens.accessToken).isFollowing({
        userId: followee.user._id.toString(),
      })

      assert.strictEqual(response.isFollowing, true)
    })
  })

  describe('currentUser Query', () => {
    it('should return current user information or throw error if user does not exist', async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })

      const updatedUser = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          firstname: 'Jan',
          lastname: 'Hrabe',
        },
      })

      const response = await client(randomUser.tokens.accessToken).currentUser()
      assert.strictEqual(response.currentUser.firstname, 'Jan')
      assert.strictEqual(response.currentUser.lastname, 'Hrabe')
      assert.strictEqual(response.currentUser.currentTeam?.name, 'Team 1')

      await UserModel.deleteOne({ _id: updatedUser.updateUser.user?.id })

      try {
        await client(randomUser.tokens.accessToken).currentUser()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User does not exist.')
      }

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should return current user with team and organization', async () => {
      const randomOrganization = await generateRandomOrganization('Test organization current user')
      const randomTeam = await generateRandomTeam({
        organization: randomOrganization.id,
        name: 'Team user 1',
      })

      const randomUser = await generateRandomUser({ team: randomTeam.id })

      const response = await client(randomUser.tokens.accessToken).currentUser()

      assert.strictEqual(response.currentUser.currentTeam?.name, randomTeam.name)
      assert.strictEqual(
        response.currentUser.currentTeam?.organization?.name,
        randomOrganization.name
      )
    })
  })

  describe('example user', () => {
    it('should return example users with highlights', async () => {
      const user = await generateRandomUser()

      for (let i = 0; i < 3; i++) {
        await generateRandomUser({ highlights: [{ id: 'test', path: 'path' }] })
      }

      const response = await client(user.tokens.accessToken).exampleUsers()

      assert.strictEqual(response.exampleUsers.length, 3)
      assert.strictEqual(
        response.exampleUsers.find((u) => u.highlights?.length === 0),
        undefined
      )
    })
  })

  describe('agencyUnverified and agency updates', () => {
    it('only agency deletion should be supported', async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })

      const agency = await generateRandomAgency('agency100')
      const agencyId = agency._id.toString()

      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agency: agencyId,
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).currentUserWithAgencyUnverified()
      assert.strictEqual(response.currentUser.bio?.agency?.id, undefined)
    })

    it("updateUserWithAgencyUnverified should set agencyUnverified and currentUserWithAgencyUnverified should return current user's agencyUnverified", async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })

      const agency = await generateRandomAgency('agencyUnverified test agency')
      const agencyId = agency._id.toString()

      const updateUserWithAgencyUnverifiedResponse = await client(
        randomUser.tokens.accessToken
      ).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: agencyId,
          },
        },
      })
      assert.strictEqual(
        updateUserWithAgencyUnverifiedResponse.updateUser.user?.bio?.agencyUnverified?.id,
        agencyId
      )

      const response = await client(randomUser.tokens.accessToken).currentUserWithAgencyUnverified()
      assert.strictEqual(response.currentUser.bio?.agencyUnverified?.id, agencyId)
    })

    it('updateUserWithAgencyUnverified delete agencyUnverified when requested', async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })

      const agency = await generateRandomAgency('agencyUnverified test agency delete')
      const agencyId = agency._id.toString()

      // let's set agencyUnverified first
      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: agencyId,
          },
        },
      })

      // and delete it then
      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: null,
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).currentUserWithAgencyUnverified()
      assert.strictEqual(response.currentUser.bio?.agencyUnverified?.id, undefined)
    })

    it('updateUserWithAgencyUnverified delete agency when requested', async () => {
      const agency = await generateRandomAgency('agency123')

      const randomUser = await generateRandomUser({
        firstname: null,
        lastname: null,
        role: dbrole.PLAYER,
        agency: agency.id,
      })

      // and delete  agency
      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agency: null,
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).currentUserWithAgencyUnverified()
      assert.strictEqual(response.currentUser.bio?.agency?.id, undefined)
    })

    it('updateUserWithAgencyUnverified delete agencyUnverified and agency when requested', async () => {
      const agency = await generateRandomAgency('agencyUnverified test agency delete 2')

      const randomUser = await generateRandomUser({
        role: dbrole.PLAYER,
        agency: agency.id,
      })

      const agency2 = await generateRandomAgency('agencyUnverified test agencyUnverified delete')
      const agency2Id = agency2._id.toString()

      // let's set agencyUnverified first
      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: agency2Id,
          },
        },
      })

      // and delete agencyUnverified and agency
      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: null,
            agency: null,
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).currentUserWithAgencyUnverified()
      assert.strictEqual(response.currentUser.bio?.agencyUnverified?.id, undefined)
      assert.strictEqual(response.currentUser.bio?.agency?.id, undefined)
    })

    it('updateUserWithAgencyUnverified should not update agencyUnverified if same agencyId provided', async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })

      const agency = await generateRandomAgency('agencyUnverified test agency 2')
      const agencyId = agency._id.toString()

      // set agencyUnverified to agencyId
      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: agencyId,
          },
        },
      })

      // try to set agencyUnverified to same agencyId
      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: agencyId,
          },
        },
      })

      const user = await UserModel.findById(randomUser.user._id)

      assert.strictEqual(user?.bio?.agencyUnverified?.toString(), agencyId)
    })

    it('updateUserWithAgencyUnverified should NOT set agencyUnverified if it has the same value as user.bio.agency', async () => {
      const agency = await generateRandomAgency('agencyUnverified test agency 3')
      const agencyId = agency._id.toString()

      const randomUser = await generateRandomUser({
        role: dbrole.PLAYER,
        agency: agency.id,
      })

      await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
        input: {
          bio: {
            agencyUnverified: agencyId,
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).currentUserWithAgencyUnverified()
      assert.strictEqual(response.currentUser.bio?.agency?.id, agencyId)
      assert.strictEqual(response.currentUser.bio?.agencyUnverified?.id, undefined)
      const currentUserResponse = await client(randomUser.tokens.accessToken).currentUser()
      assert.strictEqual(currentUserResponse.currentUser.bio?.agency?.id, agencyId)
    })
  })

  describe('SocialSignInResponse', () => {
    describe('googleSignIn', () => {
      it('should throw GraphQLError when verify id token function fails', async function () {
        googleServiceStub.verifyIdToken.throws('Error')

        try {
          await client().googleSignIn({
            input: {
              idToken: 'invalid-token',
              role: Role.SportsFan,
            },
          })
          assert.fail('Expected error was not thrown')
        } catch (err) {
          assert.strictEqual(
            err.response.errors[0].message,
            'Failed to sign in with Google. Please try again later.'
          )
        }
      })

      it('should sign in user with google token', async function () {
        googleServiceStub.verifyIdToken.resolves({
          getPayload: () => ({
            sub: '1234567890',
            email: '<EMAIL>',
          }),
        })

        const response = await client().googleSignIn({
          input: {
            idToken: 'valid-token',
            role: Role.Player,
          },
        })

        const user = await UserModel.findOne({ email: '<EMAIL>' })

        assert.strictEqual(response.googleSignIn.errors.length, 0)
        assert.strictEqual(response.googleSignIn.user?.email, '<EMAIL>')

        sinon.assert.calledOnce(amplitudeTrackStub)
        sinon.assert.calledWith(amplitudeTrackStub, 'User Updated', {}, { user_id: user?.id })
      })

      it('should sign in user with default role with google token', async function () {
        googleServiceStub.verifyIdToken.resolves({
          getPayload: () => ({
            sub: '1234567890',
            email: '<EMAIL>',
          }),
        })

        const response = await client().googleSignIn({
          input: {
            idToken: 'valid-token',
          },
        })

        const user = await UserModel.findOne({ email: '<EMAIL>' })

        assert.strictEqual(response.googleSignIn.errors.length, 0)
        assert.strictEqual(response.googleSignIn.user?.email, '<EMAIL>')

        sinon.assert.calledOnce(amplitudeTrackStub)
        sinon.assert.calledWith(amplitudeTrackStub, 'User Updated', {}, { user_id: user?.id })
      })
    })

    describe('appleSignIn', () => {
      it('should throw GraphQLError when verify id token function fails ', async function () {
        const decodeStub = sinon.stub(jwt, 'decode').returns({
          nonce: 'nonce',
        })

        const appleSignInStub = sinon.stub(appleSigninAuth, 'verifyIdToken').throws('Error')

        try {
          await client().appleSignIn({
            input: {
              idToken: 'invalid-token',
              role: Role.Player,
            },
          })
        } catch (err) {
          assert.strictEqual(
            err.response.errors[0].message,
            'Failed to sign in with Apple. Please try again later.'
          )
        }

        decodeStub.restore()
        appleSignInStub.restore()
      })

      it('should throw GraphQLError when token return null values  ', async function () {
        const decodeStub = sinon.stub(jwt, 'decode').returns({
          nonce: 'nonce',
        })

        const appleSignInStub = sinon.stub(appleSigninAuth, 'verifyIdToken').returns({
          email: null,
          sub: null,

          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } as any)

        try {
          await client().appleSignIn({
            input: {
              idToken: 'invalid-token',
              role: Role.Player,
            },
          })
        } catch (err) {
          assert.strictEqual(
            err.response.errors[0].message,
            'Failed to sign in with Apple. Please try again later.'
          )
        }

        decodeStub.restore()
        appleSignInStub.restore()
      })

      it('should throw GraphQLError when user is not found, email is not provided and appleUserId is present', async function () {
        const decodeStub = sinon.stub(jwt, 'decode').returns({
          nonce: 'nonce',
        })

        const appleSignInStub = sinon.stub(appleSigninAuth, 'verifyIdToken').returns({
          email: null,
          sub: '23lj4ask2l34',

          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } as any)

        try {
          await client().appleSignIn({
            input: {
              idToken: 'invalid-token',
              role: Role.Player,
            },
          })
        } catch (err) {
          assert.strictEqual(
            err.response.errors[0].message,
            'Failed to sign in with Apple. Please try again later.'
          )
        }

        decodeStub.restore()
        appleSignInStub.restore()
      })

      it('should sign in user with apple token', async function () {
        const decodeStub = sinon.stub(jwt, 'decode').returns({
          nonce: 'nonce',
        })

        const appleSignInStub = sinon.stub(appleSigninAuth, 'verifyIdToken').returns({
          email: '<EMAIL>',
          sub: '23lj4k2l34',
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } as any)

        const response = await client().appleSignIn({
          input: {
            idToken: 'valid-token',
            role: Role.Player,
          },
        })

        assert.strictEqual(response.appleSignIn.errors.length, 0)
        assert.strictEqual(response.appleSignIn.user?.email, '<EMAIL>')

        decodeStub.restore()
        appleSignInStub.restore()

        const user = await UserModel.findOne({ email: '<EMAIL>' })

        sinon.assert.calledOnce(amplitudeTrackStub)
        sinon.assert.calledWith(amplitudeTrackStub, 'User Updated', {}, { user_id: user?.id })
      })
    })

    it('should sign in user with default role with apple token', async () => {
      const decodeStub = sinon.stub(jwt, 'decode').returns({
        nonce: 'nonce',
      })

      const appleSignInStub = sinon.stub(appleSigninAuth, 'verifyIdToken').returns({
        email: '<EMAIL>',
        sub: '23lj4k2l33',
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as any)

      const response = await client().appleSignIn({
        input: {
          idToken: 'valid-token',
        },
      })

      assert.strictEqual(response.appleSignIn.errors.length, 0)
      assert.strictEqual(response.appleSignIn.user?.email, '<EMAIL>')

      decodeStub.restore()
      appleSignInStub.restore()

      const user = await UserModel.findOne({ email: '<EMAIL>' })

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(amplitudeTrackStub, 'User Updated', {}, { user_id: user?.id })
    })
  })

  describe('UploadAvatarResponse)', () => {
    it('should upload avatar and handle successful upload', async () => {
      const randomUser = await generateRandomUser()

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(randomUser.tokens.accessToken).uploadAvatar()
      assert.strictEqual(response.uploadAvatar.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const response1 = await client(randomUser.tokens.accessToken).uploadAvatar({
        input: {
          uploadSuccessful: true,
        },
      })
      assert.strictEqual(response1.uploadAvatar.errors, null)

      const check = await UserModel.findById(response1.uploadAvatar.user?.id)
      assert.strictEqual(check?.avatars.tiny.includes('-tiny'), true)
      assert.strictEqual(check?.avatars.small.includes('-small'), true)
      assert.strictEqual(check?.avatars.medium.includes('-medium'), true)
      assert.strictEqual(check?.avatars.large.includes('-large'), true)

      s3RequestPresignerstub.restore()
    })

    it('should handle failed presigned URL creation', async () => {
      const randomUser = await generateRandomUser()
      const s3RequestPresignerstub = await sinon.stub(s3RequestPresigner, 'getSignedUrl').throws()
      try {
        await client(randomUser.tokens.accessToken).uploadAvatar()
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to upload avatar')
      }
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)

      s3RequestPresignerstub.restore()
    })

    it('should remove avatar, if there was previously uploaded avatar already', async () => {
      const randomUser = await generateRandomUser()

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(randomUser.tokens.accessToken).uploadAvatar()
      assert.strictEqual(response.uploadAvatar.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const response1 = await client(randomUser.tokens.accessToken).uploadAvatar({
        input: {
          uploadSuccessful: true,
        },
      })
      assert.strictEqual(response1.uploadAvatar.errors, null)
      assert.strictEqual(response1?.uploadAvatar.user?.avatars?.tiny.includes('-tiny'), true)

      const response2 = await client(randomUser.tokens.accessToken).uploadAvatar({
        input: {
          uploadSuccessful: true,
        },
      })

      assert.strictEqual(response2.uploadAvatar.errors, null)
      assert.strictEqual(s3ClientStub.send.callCount, 18) // 6x remove, 6x upload of thumbnails (createImageThumbnails) twice

      s3RequestPresignerstub.restore()
    })

    it('will handle unsuccessful upload correctly', async () => {
      const randomUser = await generateRandomUser()

      await client(randomUser.tokens.accessToken).uploadAvatar()

      const response = await client(randomUser.tokens.accessToken).uploadAvatar({
        input: {
          uploadSuccessful: false,
        },
      })
      assert.strictEqual(response.uploadAvatar.user?.id, randomUser.user._id.toString())

      const response1 = await UserModel.findById(randomUser.user._id)

      assert.strictEqual(response1?.avatars, undefined)
      assert.strictEqual(response1?.avatarUnconfirmed, null)
    })
  })

  describe('register devices', () => {
    it('should be able to register device and currentUser returns it', async () => {
      const user = await generateRandomUser()
      const response = await client(user.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(response.registerDevice.errors.length, 0)
      assert.strictEqual(response.registerDevice.user?.registeredDevices?.[0].token, 'test-token')
      assert.strictEqual(
        response.registerDevice.user?.registeredDevices?.[0].platform,
        Platform.Ios
      )
      assert.strictEqual(
        response.registerDevice.user?.registeredDevices?.[0].arn,
        'arn:aws:sns:us-east-1:123456789012:endpoint/APNS/MyApplication/'
      )

      // and current user should have this device
      const updatedUser = await client(user.tokens.accessToken).currentUser()
      assert.strictEqual(updatedUser.currentUser.registeredDevices?.[0].token, 'test-token')
      assert.strictEqual(updatedUser.currentUser.registeredDevices?.[0].platform, Platform.Ios)
      assert.strictEqual(
        updatedUser.currentUser?.registeredDevices?.[0].arn,
        'arn:aws:sns:us-east-1:123456789012:endpoint/APNS/MyApplication/'
      )
    })

    it('should be able to unregister device of currentUser returns it', async () => {
      const user = await generateRandomUser()

      const registeredResponse = await client(user.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(registeredResponse.registerDevice.errors.length, 0)
      assert.strictEqual(
        registeredResponse.registerDevice.user?.registeredDevices?.[0].token,
        'test-token'
      )
      assert.strictEqual(
        registeredResponse.registerDevice.user?.registeredDevices?.[0].platform,
        Platform.Ios
      )
      assert.strictEqual(
        registeredResponse.registerDevice.user?.registeredDevices?.[0].arn,
        'arn:aws:sns:us-east-1:123456789012:endpoint/APNS/MyApplication/'
      )

      const response = await client(user.tokens.accessToken).unregisterDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(response.unregisterDevice.errors.length, 0)
      assert.strictEqual(response.unregisterDevice.user?.registeredDevices?.length, 0)

      // and current user should have this device
      const updatedUser = await client(user.tokens.accessToken).currentUser()
      assert.strictEqual(updatedUser.currentUser.registeredDevices?.length, 0)
    })

    it("should be able to unregister device of currentUser's many devices returns it", async () => {
      const user = await generateRandomUser()

      const registeredResponse1 = await client(user.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(registeredResponse1.registerDevice.errors.length, 0)
      assert.strictEqual(
        registeredResponse1.registerDevice.user?.registeredDevices?.[0].token,
        'test-token'
      )
      assert.strictEqual(
        registeredResponse1.registerDevice.user?.registeredDevices?.[0].platform,
        Platform.Ios
      )
      assert.strictEqual(
        registeredResponse1.registerDevice.user?.registeredDevices?.[0].arn,
        'arn:aws:sns:us-east-1:123456789012:endpoint/APNS/MyApplication/'
      )

      const registeredResponse2 = await client(user.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token-2',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(registeredResponse2.registerDevice.errors.length, 0)
      assert.strictEqual(
        registeredResponse2.registerDevice.user?.registeredDevices?.[1].token,
        'test-token-2'
      )
      assert.strictEqual(
        registeredResponse2.registerDevice.user?.registeredDevices?.[1].platform,
        Platform.Ios
      )
      assert.strictEqual(
        registeredResponse2.registerDevice.user?.registeredDevices?.[1].arn,
        'arn:aws:sns:us-east-1:123456789012:endpoint/APNS/MyApplication/'
      )

      const response = await client(user.tokens.accessToken).unregisterDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(response.unregisterDevice.errors.length, 0)
      assert.strictEqual(response.unregisterDevice.user?.registeredDevices?.length, 1)
      assert.deepStrictEqual(response.unregisterDevice.user?.registeredDevices[0], {
        arn: 'arn:aws:sns:us-east-1:123456789012:endpoint/APNS/MyApplication/',
        platform: 'IOS',
        token: 'test-token-2',
      })

      // and current user should have this device
      const updatedUser = await client(user.tokens.accessToken).currentUser()
      assert.strictEqual(updatedUser.currentUser.registeredDevices?.length, 1)
    })

    it("throws error when user doesn't exist, registerDevice", async () => {
      const user = await generateRandomUser()

      await UserModel.deleteOne({ _id: user.user._id })

      try {
        await client(user.tokens.accessToken).registerDevice({
          input: {
            token: 'test-token',
            platform: Platform.Ios,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to register device. Please try again later.'
        )
      }
    })

    it('return error if register device failed, registerDevice', async () => {
      const user = await generateRandomUser()

      const snsError = new Error('Failed to register device')
      snsClientStub.createPlatformEndpoint.throws(snsError)

      const response = await client(user.tokens.accessToken).registerDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(
        response.registerDevice.errors[0].message,
        'This device token is already registered.'
      )

      sinon.assert.calledOnce(snsClientStub.createPlatformEndpoint)
    })

    it("throws error when user doesn't exist, unregisterDevice", async () => {
      const user = await generateRandomUser()

      await UserModel.deleteOne({ _id: user.user._id })

      try {
        await client(user.tokens.accessToken).unregisterDevice({
          input: {
            token: 'test-token',
            platform: Platform.Ios,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to unregister device. Please try again later.'
        )
      }
    })

    it('return error if unregister device failed, unregisterDevice', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).unregisterDevice({
        input: {
          token: 'test-token',
          platform: Platform.Ios,
        },
      })

      assert.strictEqual(response.unregisterDevice.errors[0].message, 'This device was not found.')

      sinon.assert.notCalled(snsClientStub.createPlatformEndpoint)
    })
  })

  describe('User Query', () => {
    it('will return null, if slug contains invalid character "."', async () => {
      const response1 = await client().userQuery({
        slug: 'p.ter-doe',
      })

      assert.strictEqual(response1.user, null)
    })

    it('should return null if user is not found', async () => {
      const randomUser = await generateRandomUser()

      const response = await client(randomUser.tokens.accessToken).userQuery({
        slug: 'non-existing-user',
      })

      assert.strictEqual(response.user, null)
    })

    it('should return user by slug', async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })

      const getUserInput = await getUserUpdateInput()

      await client(randomUser.tokens.accessToken).updateUser({
        input: getUserInput,
      })
      const userFromDB = await UserModel.findById(randomUser.user._id)
      const slug = userFromDB?.slug

      const response1 = await client(randomUser.tokens.accessToken).userQuery({
        slug: slug!,
      })

      const user = response1.user
      assert.strictEqual(user?.id, userFromDB?._id.toString())
      assert.strictEqual(user?.slug, userFromDB?.slug)
      assert.strictEqual(user?.firstname, getUserInput.firstname)
      assert.strictEqual(user?.lastname, getUserInput.lastname)
      assert.strictEqual(user?.name, getUserInput.firstname + ' ' + getUserInput.lastname)
      assert.strictEqual(user?.dateOfBirth, getUserInput.dateOfBirth.toISOString())
      assert.strictEqual(user?.country, getUserInput.country)
      assert.strictEqual(user?.contact?.firstname, getUserInput.contact.firstname)
      assert.strictEqual(user?.contact?.lastname, getUserInput.contact.lastname)
      assert.strictEqual(user?.contact?.type, getUserInput.contact.type)
      assert.strictEqual(user?.boostMessage, getUserInput.boostMessage)
      assert.strictEqual(user?.boost?.currency, getUserInput.boost.currency)
      assert.strictEqual(user?.boost?.goalAmount, getUserInput.boost.goalAmount)
      assert.strictEqual(user?.currentTeam?.id, getUserInput.currentTeam)

      const bio = response1.user?.bio
      assert.strictEqual(bio?.position, getUserInput.bio.position)
      assert.strictEqual(bio?.handedness, getUserInput.bio.handedness)
      assert.strictEqual(bio?.playerType, getUserInput.bio.playerType)
      assert.strictEqual(bio?.schoolType, getUserInput.bio.schoolType)
      assert.strictEqual(bio?.height?.feet, getUserInput.bio.height.feet)
      assert.strictEqual(bio?.height?.inches, getUserInput.bio.height.inches)
      assert.strictEqual(bio?.height?.centimeters, getUserInput.bio.height.centimeters)
      assert.strictEqual(bio?.weight?.pounds, getUserInput.bio.weight.pounds)
      assert.strictEqual(bio?.weight?.kilograms, getUserInput.bio.weight.kilograms)
      assert.strictEqual(bio?.birthplace, getUserInput.bio.birthplace)
      assert.strictEqual(bio?.nameOfSchool, getUserInput.bio.nameOfSchool)
      assert.strictEqual(bio?.graduationYear, getUserInput.bio.graduationYear)
      assert.strictEqual(bio?.cumulativeGPA, getUserInput.bio.cumulativeGPA)
      assert.strictEqual(bio?.coreGPA, getUserInput.bio.coreGPA)
      assert.strictEqual(bio?.unweightedGPA, getUserInput.bio.unweightedGPA)
      assert.strictEqual(bio?.weightedGPA, getUserInput.bio.weightedGPA)
      assert.strictEqual(bio?.satScore, getUserInput.bio.satScore)
      assert.strictEqual(bio?.actScore, getUserInput.bio.actScore)
      assert.strictEqual(bio?.toeflScore, getUserInput.bio.toeflScore)
      assert.strictEqual(bio?.ncaaEligibility, getUserInput.bio.ncaaEligibility)
      assert.strictEqual(bio?.naiaEligibility, getUserInput.bio.naiaEligibility)

      const externalLinks = response1.user?.externalLinks
      assert.strictEqual(externalLinks?.length, getUserInput.externalLinks.length)
      assert.strictEqual(externalLinks?.[0].name, getUserInput.externalLinks[0].name)
      assert.strictEqual(externalLinks?.[0].url, getUserInput.externalLinks[0].url)
      assert.strictEqual(externalLinks?.[0].type, getUserInput.externalLinks[0].type)

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should return user marked for deletion and not with public profile for admin', async () => {
      const adminUser = await generateRandomAdminUser()

      await UserModel.create({
        firstname: 'Jan',
        lastname: 'Rychta',
        slug: 'jan-rychta',
        markedForDeletion: true,
        public: false,
      })

      const response = await client(adminUser.tokens.accessToken).userQuery({
        slug: 'jan-rychta',
      })

      assert.strictEqual(response.user?.firstname, 'Jan')
      assert.strictEqual(response.user?.lastname, 'Rychta')
    })
  })

  it('should create non unique slug', async () => {
    const user = await generateRandomUser({ firstname: null, lastname: null })

    await client(user.tokens.accessToken).updateUser({
      input: {
        firstname: 'Jan',
        lastname: 'Rychta',
      },
    })

    const userFromDB = await UserModel.findOne({ _id: user.user._id })

    assert.strictEqual(userFromDB?.slug, 'jan-rychta')
    assert.strictEqual(userFromDB?.nonUniqueSlug, 'jan-rychta')

    // register another user with the same name
    await generateRandomUser({ firstname: null, lastname: null })

    await client(user.tokens.accessToken).updateUser({
      input: {
        firstname: 'Jan',
        lastname: 'Rychta',
      },
    })

    const userFromDB2 = await UserModel.findOne({ _id: user.user._id })

    assert.strictEqual(userFromDB2?.slug?.startsWith('jan-rychta'), true)
    assert.strictEqual(userFromDB2?.nonUniqueSlug, 'jan-rychta')
  })

  describe('pushNotificationsResponse', () => {
    it('should throw an error if user is not found', async () => {
      const randomUser = await generateRandomUser()

      await UserModel.deleteOne({ _id: randomUser.user._id })

      try {
        await client(randomUser.tokens.accessToken).pushNotifications({
          input: {
            enabled: true,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to update push notifications. Please try again later.'
        )
      }
    })

    it('should update push notifications', async () => {
      const randomUser = await generateRandomUser()

      const response = await client(randomUser.tokens.accessToken).pushNotifications({
        input: {
          enabled: true,
        },
      })

      assert.strictEqual(response.pushNotifications.errors.length, 0)
      assert.strictEqual(response.pushNotifications.user?.pushEnabled, true)

      let updatedUser = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(updatedUser?.pushEnabled, true)

      const response1 = await client(randomUser.tokens.accessToken).pushNotifications({
        input: {
          enabled: false,
        },
      })

      assert.strictEqual(response1.pushNotifications.errors.length, 0)
      assert.strictEqual(response1.pushNotifications.user?.pushEnabled, false)

      updatedUser = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(updatedUser?.pushEnabled, false)
    })
  })

  describe('fetchUserDataEP', () => {
    it('should send a notifications on auto sync failure', async () => {
      const randomUser = await generateRandomUser({
        dateOfBirth: new Date('2000-01-01'),
        country: 'CZ',
      })

      const axiosStub = sinon.stub(axios, 'post').throws()

      await client(randomUser.tokens.accessToken).fetchUserDataEP()

      sinon.assert.calledOnce(axiosStub)
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `Auto sync (Elite Prospects) failed: User ${randomUser.user.name} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${randomUser.user._id}|admin panel>`
          )
        )
      )

      axiosStub.restore()
    })

    it('should handle user not found error', async () => {
      const randomUser = await generateRandomUser()

      await UserModel.deleteOne({ _id: randomUser.user._id })

      try {
        await client(randomUser.tokens.accessToken).fetchUserDataEP()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User does not exist.')
      }
    })

    it('should fetch user data and update user profile correctly', async () => {
      const randomUser = await generateRandomUser({
        firstname: 'Player',
        lastname: 'Lakatos',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date('2000-01-01'),
      })

      const axiosStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            players: {
              edges: [
                {
                  shoots: 'R',
                  height: {
                    imperial: '6\'0"',
                    metrics: '183',
                  },
                  weight: {
                    imperial: '190',
                    metrics: '86',
                  },
                  links: {
                    eliteprospectsUrl: 'https://www.eliteprospects.com/player/123456',
                  },
                },
              ],
            },
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).fetchUserDataEP()

      sinon.assert.calledTwice(axiosStub)
      sinon.assert.calledWith(
        axiosStub,
        'https://gql.eliteprospects.com',
        sinon.match({
          variables: {
            firstName: randomUser.user._firstname,
            lastName: randomUser.user._lastname,
            yearOfBirth: dayjs(randomUser.user.dateOfBirth).format('YYYY'),
            nationality: randomUser.user.country,
          },
        })
      )

      assert.strictEqual(response.fetchUserDataEP.errors.length, 0)
      assert.strictEqual(
        response.fetchUserDataEP.user?.externalLinks?.[0].type,
        ExternalLinkType.EliteProspects
      )
      assert.strictEqual(
        response.fetchUserDataEP.user?.externalLinks?.[0].url,
        'https://www.eliteprospects.com/player/123456'
      )

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `Auto sync: User ${response.fetchUserDataEP.user?.name} - Profile: ${response.fetchUserDataEP.user?.externalLinks?.[0].url} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${response.fetchUserDataEP.user.id}|admin panel>`
          )
        )
      )

      axiosStub.restore()
    })

    it('should handle user data fetch with null external links ', async () => {
      const randomUser = await generateRandomUser({
        country: 'CZ',
        dateOfBirth: new Date('2000-01-01'),
      })

      await UserModel.updateOne(
        {
          _id: randomUser.user._id,
        },
        {
          $set: {
            externalLinks: null,
          },
        }
      )

      const axiosStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            players: {
              edges: [
                {
                  shoots: 'R',
                  height: {
                    imperial: '6\'0"',
                    metrics: '183',
                  },
                  weight: {
                    imperial: '190',
                    metrics: '86',
                  },
                  links: {
                    eliteprospectsUrl: 'https://www.eliteprospects.com/player/123456',
                  },
                },
              ],
            },
          },
        },
      })

      const response = await client(randomUser.tokens.accessToken).fetchUserDataEP()

      sinon.assert.calledWith(axiosStub, 'https://gql.eliteprospects.com')
      sinon.assert.calledTwice(axiosStub)

      assert.strictEqual(response.fetchUserDataEP.errors.length, 0)
      assert.strictEqual(response.fetchUserDataEP.user?.externalLinks?.length, 1)

      axiosStub.restore()
    })

    it('should return error if fetch function was called twice', async () => {
      const randomUser = await generateRandomUser()

      const axiosStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            players: {
              edges: [
                {
                  shoots: 'R',
                  height: {
                    imperial: '6\'0"',
                    metrics: '183',
                  },
                  weight: {
                    imperial: '190',
                    metrics: '86',
                  },
                  links: {
                    eliteprospectsUrl: 'https://www.eliteprospects.com/player/123456',
                  },
                },
              ],
            },
          },
        },
      })

      await client(randomUser.tokens.accessToken).fetchUserDataEP()

      try {
        await client(randomUser.tokens.accessToken).fetchUserDataEP()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'User stats have been already synced and messages have been sent.'
        )
      }
      sinon.assert.callCount(slackClientStub.chat.postMessage, 1)

      axiosStub.restore()
    })

    it('should not send message twice if fetch fails', async () => {
      const randomUser = await generateRandomUser()

      const axiosStub = sinon.stub(axios, 'post').throws()

      await client(randomUser.tokens.accessToken).fetchUserDataEP()

      try {
        await client(randomUser.tokens.accessToken).fetchUserDataEP()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'User stats have been already synced and messages have been sent.'
        )
      }
      sinon.assert.callCount(slackClientStub.chat.postMessage, 1)

      axiosStub.restore()
    })

    it('should claim shadow profile if user matches slug, country and year of birth', async () => {
      await DiscoveredUrlModel.deleteMany({})
      await CrawlerDomainConfigModel.deleteMany({})

      const registeredUser = await generateRandomUser({
        firstname: 'Jan',
        lastname: 'Rychta',
        country: 'CZ',
        dateOfBirth: new Date('2000-01-01'),
      })
      const agency = await generateRandomAgency('Fetch shadow profile agency')

      // just registered user
      await UserModel.updateOne(
        { _id: registeredUser.user._id.toString() },
        {
          slug: 'jan-rychta-3jkfe',
        }
      )

      // shadow profile
      const shadowUser = await UserModel.create({
        firstname: 'Jan',
        lastname: 'Rychta',
        email: '<EMAIL>',
        slug: 'jan-rychta',
        shadowSlug: 'jan-rychta',
        nonUniqueSlug: 'jan-rychta',
        country: 'CZ',
        dateOfBirth: new Date('2000-01-01'),
        role: Role.ShadowPlayer,
        avatar: 'avatar',
        bio: {
          agency: agency,
        },
        avatars: {
          tiny: 'tiny',
          small: 'small',
          medium: 'medium',
          large: 'large',
        },
        externalLinks: [
          {
            name: 'Elite Prospects',
            type: ExternalLinkType.EliteProspects,
            url: 'https://www.eliteprospects.com/player/123456',
          },
        ],
        userStats: {
          views: 3,
          followers: 2,
          boosts: 1,
        },
      })

      const discoveredUrl = await generateDiscoverUrl()
      const userExternalId = '123456'
      await UserInfoModel.create({
        name: 'Jan Rychta',
        slug: 'jan-rychta',
        country: 'CZ',
        possibleYearsOfBirth: [2000, 2001],
        externalId: userExternalId,
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig,
      })

      await UserSubscriptionModel.create({
        user: shadowUser._id,
      })

      await UserViewModel.create({
        user: shadowUser._id,
      })

      await UserStatsModel.create({
        user: shadowUser._id,
        goals: 2,
        season: '2023-2024',
      })

      await UserStatsModel.create({
        user: shadowUser._id,
        goals: 3,
        season: '2024-2025',
      })
      await generateRandomGameStats({
        nameSuffix: 'UserHasGames1',
        gameStatsPayload: {
          discoveredUrl,
          date: new Date(),
          externalId: '1',
          externalInfo: {
            homeTeamName: 'Home Team',
            awayTeamName: 'Away Team',
            leagueName: 'Test League',
          },
          awayTeamRoster: [
            {
              externalInfo: {
                playerName: 'Jan Rychta',
                playerExternalId: userExternalId,
              },
              player: null,
            },
          ],
          homeTeamRoster: [],
        },
      })

      await generateRandomGameStats({
        nameSuffix: 'UserHasGames2',
        gameStatsPayload: {
          discoveredUrl,
          date: new Date(),
          externalId: '2',
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: 'Jan Rychta',
                playerExternalId: userExternalId,
              },
              player: null,
            },
          ],
          awayTeamRoster: [],
        },
      })

      const response = await client(registeredUser.tokens.accessToken).fetchUserDataEP()

      assert.strictEqual(response.fetchUserDataEP.errors.length, 0)
      assert.strictEqual(
        response.fetchUserDataEP.user?.externalLinks?.[0]?.type,
        ExternalLinkType.EliteProspects
      )
      assert.strictEqual(
        response.fetchUserDataEP.user?.externalLinks?.[0]?.url,
        'https://www.eliteprospects.com/player/123456'
      )

      // check that shadow profile is marked for deletion
      const shadowProfile = await UserModel.findOne({ _id: shadowUser._id })
      assert.strictEqual(shadowProfile?.markedForDeletion, true)
      assert.strictEqual(shadowProfile?.slug, null)
      assert.strictEqual(shadowProfile?.shadowSlug, null)

      // check all info has been migrated to new account from shadow
      const updatedUser = await UserModel.findOne({ _id: registeredUser.user._id })
      assert.strictEqual(updatedUser?.userStats?.views, 3)
      assert.strictEqual(updatedUser?.userStats?.followers, 2)
      assert.strictEqual(updatedUser?.userStats?.boosts, 1)
      assert.strictEqual(updatedUser?.slug, 'jan-rychta')
      assert.strictEqual(updatedUser?.avatar, 'avatar')
      assert.strictEqual(updatedUser?.avatars?.tiny, 'tiny')
      assert.strictEqual(updatedUser?.avatars?.small, 'small')
      assert.strictEqual(updatedUser?.avatars?.medium, 'medium')
      assert.strictEqual(updatedUser?.avatars?.large, 'large')

      const userSubscriptions = await UserSubscriptionModel.find({ user: updatedUser?._id })
      const shadowSubscriptions = await UserSubscriptionModel.find({ user: shadowProfile?._id })

      assert.strictEqual(userSubscriptions.length, 1)
      assert.strictEqual(shadowSubscriptions.length, 0)

      const userViews = await UserViewModel.find({ user: updatedUser?._id })
      const shadowViews = await UserViewModel.find({ user: shadowProfile?._id })

      assert.strictEqual(userViews.length, 1)
      assert.strictEqual(shadowViews.length, 0)

      const userStats = await UserStatsModel.find({ user: updatedUser?._id })
      const shadowStats = await UserStatsModel.find({ user: shadowProfile?._id })

      assert.strictEqual(userStats.length, 2)
      assert.strictEqual(userStats[0].goals, 2)
      assert.strictEqual(userStats[1].goals, 3)

      assert.strictEqual(shadowStats.length, 0)

      // game stats
      const games = await client(registeredUser.tokens.accessToken).games({
        filter: {
          userId: updatedUser?._id,
        },
      })

      const shadowGames = await client(registeredUser.tokens.accessToken).games({
        filter: {
          userId: shadowProfile?._id,
        },
      })

      assert.strictEqual(games.games.edges.length, 2)
      assert.strictEqual(shadowGames.games.edges.length, 0)

      const counts = await AgencyModel.findById(agency._id)
      assert.strictEqual(counts?.shadowClientsCount, 0)
      assert.strictEqual(counts?.clientsCount, 1)

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `Auto sync (shadow account claimed): User ${response.fetchUserDataEP.user?.name} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${response.fetchUserDataEP.user.id}|admin panel>`
          )
        )
      )
    })

    it('should claim shadow profile if user matches name and year of birth but country does not match', async () => {
      await DiscoveredUrlModel.deleteMany({})
      await CrawlerDomainConfigModel.deleteMany({})

      const registeredUser = await generateRandomUser({
        firstname: 'Martin',
        lastname: 'Novak',
        country: 'SK', // Different country than shadow user
        dateOfBirth: new Date('1998-05-15'),
      })

      // just registered user
      await UserModel.updateOne(
        { _id: registeredUser.user._id.toString() },
        {
          slug: 'martin-novak-abc123',
        }
      )

      // shadow profile with same name and year of birth but different country
      const shadowUser = await UserModel.create({
        firstname: 'Martin',
        lastname: 'Novak',
        email: '<EMAIL>',
        slug: 'martin-novak',
        shadowSlug: 'martin-novak',
        nonUniqueSlug: 'martin-novak',
        country: 'CZ', // Different country than registered user
        dateOfBirth: new Date('1998-05-15'),
        role: Role.ShadowPlayer,
        avatar: 'shadow-avatar',
        userStats: {
          views: 5,
          followers: 3,
          boosts: 2,
        },
      })

      const response = await client(registeredUser.tokens.accessToken).fetchUserDataEP()

      assert.strictEqual(response.fetchUserDataEP.errors.length, 0)

      // check that shadow profile is marked for deletion
      const shadowProfile = await UserModel.findOne({ _id: shadowUser._id })
      assert.strictEqual(shadowProfile?.markedForDeletion, true)
      assert.strictEqual(shadowProfile?.slug, null)
      assert.strictEqual(shadowProfile?.shadowSlug, null)

      // check all info has been migrated to new account from shadow
      const updatedUser = await UserModel.findOne({ _id: registeredUser.user._id })
      assert.strictEqual(updatedUser?.slug, 'martin-novak')

      // Verify country remains the original one from registered user
      assert.strictEqual(updatedUser?.country, 'SK')

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `Auto sync (shadow account claimed): User ${response.fetchUserDataEP.user?.name} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${response.fetchUserDataEP.user?.id}|admin panel>`
          )
        )
      )
    })

    it('should send slack message if shadow profile fail to claim for user from CZ', async () => {
      const randomUser = await generateRandomUser({
        firstname: 'Petr',
        lastname: 'Kovaricek',
        country: 'CZ',
        dateOfBirth: new Date('2002-01-01'),
      })

      const response = await client(randomUser.tokens.accessToken).fetchUserDataEP()

      assert.strictEqual(response.fetchUserDataEP.errors.length, 0)

      sinon.assert.calledTwice(slackClientStub.chat.postMessage)

      const firstCallArgs = slackClientStub.chat.postMessage.firstCall.args[0]
      assert.strictEqual(firstCallArgs.channel, 'db-stage')
      assert.strictEqual(
        firstCallArgs.text,
        `Shadow profile not found for user ${response.fetchUserDataEP.user?.name} - Admin: <http://admin.${config.domain}/dashboard/users/form/?id=${response.fetchUserDataEP.user?.id}|admin panel>`
      )
    })

    it('should skip sync when user role is not PLAYER or SHADOW_PLAYER', async () => {
      const randomUser = await generateRandomUser({
        firstname: 'Tomas',
        lastname: 'Parent',
        role: dbrole.PARENT,
      })

      const axiosStub = sinon.stub(axios, 'post')

      const response = await client(randomUser.tokens.accessToken).fetchUserDataEP()

      assert.strictEqual(response.fetchUserDataEP.errors.length, 0)

      sinon.assert.callCount(slackClientStub.chat.postMessage, 0)
      sinon.assert.notCalled(axiosStub)

      axiosStub.restore()
    })
  })

  describe('user weekly stats', () => {
    let nowStub: sinon.SinonStub
    afterEach(() => {
      nowStub.restore()
      sinon.restore()
    })

    it('should return weekly stats for the user across 2 years', async () => {
      const randomUser = await generateRandomUser()

      // Mock specific moment now
      const mockDate = new Date('2025-01-31T00:00:00.000Z')
      nowStub = sinon.stub(moment, 'now').returns(mockDate.valueOf())

      const currentWeek = moment().utcOffset('+0000').endOf('isoWeek')
      const threeMonthsAgo = currentWeek.clone().subtract(13, 'weeks').startOf('isoWeek')

      const today = moment().utcOffset('+0000').day()
      const isSunday = today === 0

      const startOfCurrentWeek = isSunday
        ? moment().utcOffset('+0000').subtract(1, 'day').startOf('isoWeek').toDate()
        : moment().utcOffset('+0000').startOf('isoWeek').toDate()

      for (let i = 0; i < 4; i++) {
        await UserViewModel.create({
          user: randomUser.user._id,
          totalViews: 1,
          createdAt: startOfCurrentWeek,
        })
      }

      await UserViewModel.create({
        user: randomUser.user._id,
        totalViews: 1,
        createdAt: threeMonthsAgo.clone().add(2, 'day'), // add 2 days to be in the first week
      })

      const response = await client(randomUser.tokens.accessToken).userViewStats()

      assert.ok(
        response.userViewsStats.weeklyStats.length >= 13 &&
          response.userViewsStats.weeklyStats.length <= 14
      )

      assert.strictEqual(response.userViewsStats.weeklyStats[0].views, 1)
      assert.strictEqual(
        response.userViewsStats.weeklyStats[response.userViewsStats.weeklyStats.length - 1].views,
        4
      )
    })

    it('should return weekly stats for the user', async () => {
      const randomUser = await generateRandomUser()

      const currentWeek = moment().utcOffset('+0000').endOf('isoWeek')
      const threeMonthsAgo = currentWeek.clone().subtract(13, 'weeks').startOf('isoWeek')

      const today = moment().utcOffset('+0000').day()
      const isSunday = today === 0

      const startOfCurrentWeek = isSunday
        ? moment().utcOffset('+0000').subtract(1, 'day').startOf('isoWeek').toDate()
        : moment().utcOffset('+0000').startOf('isoWeek').toDate()

      await UserViewModel.create({
        user: randomUser.user._id,
        totalViews: 1,
        createdAt: startOfCurrentWeek,
      })

      await UserViewModel.create({
        user: randomUser.user._id,
        totalViews: 1,
        createdAt: startOfCurrentWeek,
      })

      await UserViewModel.create({
        user: randomUser.user._id,
        totalViews: 1,
        createdAt: threeMonthsAgo.clone().add(2, 'day'), // add 2 days to be in the first week
      })

      const response = await client(randomUser.tokens.accessToken).userViewStats()

      assert.ok(
        response.userViewsStats.weeklyStats.length >= 13 &&
          response.userViewsStats.weeklyStats.length <= 14
      )

      assert.strictEqual(response.userViewsStats.weeklyStats[0].views, 1)
      assert.strictEqual(
        moment(response.userViewsStats.weeklyStats[0].from).utcOffset('+0000').format('YYYY-MM-DD'),
        threeMonthsAgo.format('YYYY-MM-DD')
      )
      assert.strictEqual(
        moment(response.userViewsStats.weeklyStats[0].to).utcOffset('+0000').format('YYYY-MM-DD'),
        threeMonthsAgo.endOf('isoWeek').format('YYYY-MM-DD')
      )

      const lastIndex = response.userViewsStats.weeklyStats.length - 1
      assert.strictEqual(response.userViewsStats.weeklyStats[lastIndex].views, 2)
      assert.strictEqual(
        moment(response.userViewsStats.weeklyStats[lastIndex].from)
          .utcOffset('+0000')
          .format('YYYY-MM-DD'),
        moment().startOf('isoWeek').format('YYYY-MM-DD')
      )
      assert.strictEqual(
        moment(response.userViewsStats.weeklyStats[lastIndex].to)
          .utcOffset('+0000')
          .format('YYYY-MM-DD'),
        moment().endOf('isoWeek').format('YYYY-MM-DD')
      )

      // rest of the weeks should have 0 views
      for (let i = 1; i < 12; i++) {
        assert.strictEqual(response.userViewsStats.weeklyStats[i].views, 0)
      }
    })

    it('should throw an error if user data not load', async () => {
      const userViewStatsStub = sinon.stub(UserViewModel, 'aggregate').throws('Error')

      try {
        await client().userViewStats({})
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to get user views stats')
      }
      userViewStatsStub.restore()
    })
  })

  describe('randomUsers query', () => {
    beforeEach(async () => {
      await UserModel.deleteMany({})
    })

    it('should return a list of random users with default limit', async () => {
      for (let i = 0; i < 70; i++) {
        await generateRandomUser({ role: dbrole.PLAYER, avatar: '/path/to/picture.png' })
      }

      const response = await client().randomUsers()

      assert.strictEqual(response.randomUsers.edges.length, 60) // default limit
      assert.strictEqual(response.randomUsers.totalCount, 70) // total count
    })

    it('should only return users with PLAYER role', async () => {
      await generateRandomUser({ role: dbrole.SHADOW_PLAYER })
      await generateRandomUser({ role: dbrole.SPORTS_FAN })
      await generateRandomUser({ role: dbrole.PLAYER, avatar: '/path/to/picture.png' })

      const response = await client().randomUsers()

      assert.strictEqual(response.randomUsers.edges.length, 1)
      assert.strictEqual(response.randomUsers.totalCount, 1)
    })

    it('should populate currentTeam', async () => {
      const team = await TeamModel.findOne({ name: 'Team 1' })
      await generateRandomUser({
        role: dbrole.PLAYER,
        team: team?.id,
        avatar: '/path/to/picture.png',
      })

      const response = await client().randomUsers()
      const user = response.randomUsers.edges[0].node

      assert.ok(user.currentTeam, 'currentTeam should be populated')
      assert.strictEqual(user.currentTeam.name, 'Team 1')
    })
  })

  describe('recommendedUsers query', () => {
    it('should return recommended 3 users ', async () => {
      const NUMBER_OF_RECOMMENDED_USERS = 3
      const COUNTRY = 'SE'
      const YOB = 2000
      const team = await TeamModel.findOne({ name: 'Team 1' })
      const users = []
      const excludedIds = []

      // generate 2 users matching recommended conditions
      for (let i = 0; i < 2; i++) {
        const user = await generateRandomUser({
          firstname: `UserAll ${i}`,
          role: dbrole.PLAYER,
          team: team?.id,
          country: COUNTRY,
          dateOfBirth: new Date(YOB, 1, 1),
          inspired: true,
        })

        users.push(user)

        await UserModel.updateOne(
          {
            _id: users[i].user._id,
          },
          {
            $set: {
              bio: {
                position: BioPosition.Center,
                handedness: BioHandedness.Right,
              },
              userStats: {
                views: 10,
                followers: 10,
                boosts: 0,
              },
              numberOfVideos: 2,
              avatar: '/path/to/picture.jpeg',
              searchEnabled: true,
              public: true,
              externalLinks: [
                {
                  id: new Types.ObjectId(),
                  name: 'Seznam',
                  url: 'https://www.seznam.cz/',
                  type: ExternalLinkType.EliteProspects,
                },
              ],
            },
          }
        )
        if (i === 0) excludedIds.push(user.user._id.toString())
      }

      //generate user matching all condition except country and yob
      const user = await generateRandomUser({
        firstname: 'userWithoutCountry',
        role: dbrole.PLAYER,
        team: team?.id,
        country: 'CZ',
        inspired: true,
      })

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          $set: {
            bio: {
              position: BioPosition.Center,
              handedness: BioHandedness.Right,
            },
            userStats: {
              views: 10,
              followers: 10,
              boosts: 0,
            },
            numberOfVideos: 2,
            avatar: '/path/to/picture.jpeg',
            searchEnabled: true,
            public: true,
            externalLinks: [
              {
                id: new Types.ObjectId(),
                name: 'Seznam',
                url: 'https://www.seznam.cz/',
                type: ExternalLinkType.EliteProspects,
              },
            ],
          },
        }
      )
      users.push(user)

      //generate user without country, yearOfBirth, Sport Details and Eliteprospects link
      const userWithoutCountryYobAndDetails = await generateRandomUser({
        firstname: 'userWithoutCountryYobAndDetails',
        role: dbrole.PLAYER,
        team: team?.id,
        country: 'CZ',
        inspired: true,
      })

      await UserModel.updateOne(
        {
          _id: userWithoutCountryYobAndDetails.user._id,
        },
        {
          $set: {
            userStats: {
              views: 10,
              followers: 10,
              boosts: 0,
            },
            numberOfVideos: 2,
            avatar: '/path/to/picture.jpeg',
            searchEnabled: true,
            public: true,
          },
        }
      )

      users.push(userWithoutCountryYobAndDetails)

      // generate 10 more users NOT matching recommended conditions
      for (let i = 0; i < 10; i++) {
        const user = await generateRandomUser()
        users.push(user)
      }

      const response = await client().recommendedUsers({
        yearOfBirth: { year: YOB },
        country: { country: COUNTRY },
        activeUserId: excludedIds[0],
        inspired: true,
      })

      assert.strictEqual(response.recommendedUsers.length, NUMBER_OF_RECOMMENDED_USERS)

      const recommendedUserIds = response.recommendedUsers.map((user) => user.id)
      excludedIds.forEach((excludedId) => {
        assert.strictEqual(recommendedUserIds.includes(excludedId), false)
      })

      assert.strictEqual(
        response.recommendedUsers.map((user) => user.firstname).includes('UserAll 1'),
        true
      )

      assert.strictEqual(
        response.recommendedUsers.map((user) => user.firstname).includes('userWithoutCountry'),
        true
      )

      assert.strictEqual(
        response.recommendedUsers
          .map((user) => user.firstname)
          .includes('userWithoutCountryYobAndDetails'),
        true
      )

      // make another call to compare if result is random
      const response2 = await client().recommendedUsers({
        yearOfBirth: { year: YOB },
        country: { country: COUNTRY },
      })
      assert.strictEqual(response2.recommendedUsers.length, NUMBER_OF_RECOMMENDED_USERS)

      assert.notStrictEqual(response.recommendedUsers, response2.recommendedUsers)
    })

    it('should return skip user with _id matching activeUserId and return recommended 3 users', async () => {
      const NUMBER_OF_RECOMMENDED_USERS = 3
      const COUNTRY = 'SE'
      const YOB = 2000
      const team = await TeamModel.findOne({ name: 'Team 1' })
      const users = []

      // generate 4 users matching recommended conditions
      for (let i = 0; i < 4; i++) {
        const user = await generateRandomUser({
          role: dbrole.PLAYER,
          team: team?.id,
          country: COUNTRY,
          dateOfBirth: new Date(YOB, 1, 1),
        })

        users.push(user)

        await UserModel.updateOne(
          {
            _id: users[i].user._id,
          },
          {
            $set: {
              bio: {
                position: BioPosition.Center,
                handedness: BioHandedness.Right,
              },
              userStats: {
                views: 10,
                followers: 10,
                boosts: 0,
              },
              numberOfVideos: 2,
              avatar: '/path/to/picture.jpeg',
              searchEnabled: true,
              public: true,
              externalLinks: [
                {
                  id: new Types.ObjectId(),
                  name: 'Seznam',
                  url: 'https://www.seznam.cz/',
                  type: ExternalLinkType.EliteProspects,
                },
              ],
            },
          }
        )
      }
      const firstUserId = users[0].user._id.toString()

      const response = await client().recommendedUsers({
        yearOfBirth: { year: YOB },
        country: { country: COUNTRY },
        activeUserId: firstUserId,
      })
      assert.strictEqual(response.recommendedUsers.length, NUMBER_OF_RECOMMENDED_USERS)

      assert.strictEqual(response.recommendedUsers.filter((u) => u.id === firstUserId).length, 0)
    })

    it('should return more than 3 users with year and country empty', async () => {
      const NUMBER_OF_RECOMMENDED_USERS = 10
      const team = await TeamModel.findOne({ name: 'Team 1' })
      const users = []

      // generate 12 users
      for (let i = 0; i < 12; i++) {
        const user = await generateRandomUser({
          role: dbrole.PLAYER,
          team: team?.id,
          country: 'US',
          dateOfBirth: new Date(2000, 1, 1),
        })

        users.push(user)

        await UserModel.updateOne(
          {
            _id: users[i].user._id,
          },
          {
            $set: {
              bio: {
                position: BioPosition.Center,
                handedness: BioHandedness.Right,
              },
              userStats: {
                views: 10,
                followers: 10,
                boosts: 0,
              },
              numberOfVideos: 2,
              avatar: '/path/to/picture.jpeg',
              searchEnabled: true,
              public: true,
              externalLinks: [
                {
                  id: new Types.ObjectId(),
                  name: 'Seznam',
                  url: 'https://www.seznam.cz/',
                  type: ExternalLinkType.EliteProspects,
                },
              ],
            },
          }
        )
      }
      const response = await client().recommendedUsers({
        limit: 10,
      })
      assert.strictEqual(response.recommendedUsers.length, NUMBER_OF_RECOMMENDED_USERS)
    })

    it('should return 8 users with inspired', async () => {
      const NUMBER_OF_RECOMMENDED_USERS = 8
      const team = await TeamModel.findOne({ name: 'Team 1' })
      const users = []

      await UserModel.deleteMany({})

      for (let i = 0; i < 8; i++) {
        const user = await generateRandomUser({
          role: dbrole.PLAYER,
          team: team?.id,
          country: 'US',
          dateOfBirth: new Date(2000, 1, 1),
        })

        users.push(user)

        await UserModel.updateOne(
          {
            _id: users[i].user._id,
          },
          {
            $set: {
              bio: {
                position: BioPosition.Center,
                handedness: BioHandedness.Right,
              },
              userStats: {
                views: 10,
                followers: 10,
                boosts: 0,
              },
              inspired: true,
              numberOfVideos: 2,
              avatar: '/path/to/picture.jpeg',
              searchEnabled: true,
              public: true,
              externalLinks: [
                {
                  id: new Types.ObjectId(),
                  name: 'Seznam',
                  url: 'https://www.seznam.cz/',
                  type: ExternalLinkType.EliteProspects,
                },
              ],
            },
          }
        )
      }

      // generate 4 users with inspired false
      for (let i = 0; i < 4; i++) {
        const user = await generateRandomUser({
          role: dbrole.PLAYER,
          team: team?.id,
          country: 'US',
          dateOfBirth: new Date(2000, 1, 1),
        })

        users.push(user)

        await UserModel.updateOne(
          {
            _id: users[i].user._id,
          },
          {
            $set: {
              numberOfVideos: 2,
              avatar: '/path/to/picture.jpeg',
            },
          }
        )
      }

      const response = await client().recommendedUsers({
        limit: 10,
        inspired: true,
      })
      assert.strictEqual(response.recommendedUsers.length, NUMBER_OF_RECOMMENDED_USERS)
    })

    it('should return skip user with _id matching activeUserId and context and return recommended 3 users', async () => {
      const NUMBER_OF_RECOMMENDED_USERS = 3
      const COUNTRY = 'SE'
      const YOB = 2000
      const team = await TeamModel.findOne({ name: 'Team 1' })
      const users = []

      // generate 5 users matching recommended conditions
      // two of them should be excluded from results
      // as first one will be used to pass as loggedInUser using context
      // and second one will be used to pass as activeUserId
      for (let i = 0; i < 5; i++) {
        const user = await generateRandomUser({
          role: dbrole.PLAYER,
          team: team?.id,
          country: COUNTRY,
          dateOfBirth: new Date(YOB, 1, 1),
        })

        users.push(user)

        await UserModel.updateOne(
          {
            _id: users[i].user._id,
          },
          {
            $set: {
              bio: {
                position: BioPosition.Center,
                handedness: BioHandedness.Right,
              },
              userStats: {
                views: 10,
                followers: 10,
                boosts: 0,
              },
              numberOfVideos: 2,
              avatar: '/path/to/picture.jpeg',
              searchEnabled: true,
              public: true,
              externalLinks: [
                {
                  id: new Types.ObjectId(),
                  name: 'Seznam',
                  url: 'https://www.seznam.cz/',
                  type: ExternalLinkType.EliteProspects,
                },
              ],
            },
          }
        )
      }
      const loggedInUser = users[0]
      const loggedInUserId = users[0].user._id.toString()
      const activeUserId = users[0].user._id.toString()

      const response = await client(loggedInUser.tokens.accessToken).recommendedUsers({
        yearOfBirth: { year: YOB },
        country: { country: COUNTRY },
        activeUserId: activeUserId,
      })
      assert.strictEqual(response.recommendedUsers.length, NUMBER_OF_RECOMMENDED_USERS)

      assert.strictEqual(
        response.recommendedUsers.filter((u) => u.id === loggedInUserId || u.id === activeUserId)
          .length,
        0
      )
    })
  })

  describe('moreSelectedUsers query', () => {
    it('should return more selected users ', async () => {
      const users = []

      for (let i = 0; i < 31; i++) {
        const user = await generateRandomUser()

        users.push(user)

        await UserModel.updateOne(
          {
            _id: users[i].user._id,
          },
          { $set: { moreSelected: true } }
        )
      }

      const response = await client().moreSelectedUsers()
      assert.strictEqual(response.moreSelectedUsers.length, 30)
      assert.strictEqual(response.moreSelectedUsers[0].name, users[0].user.name)
      assert.strictEqual(
        response.moreSelectedUsers[0].currentTeam?.id,
        users[0].user.currentTeam?._id.toString()
      )
      assert.strictEqual(response.moreSelectedUsers[0].currentTeam?.name, 'Team 1')
    })
  })

  describe('inspiredUsers query', () => {
    it('should return inspired users ', async () => {
      for (let i = 0; i < 4; i++) {
        const user = await generateRandomUser()

        await UserModel.updateOne(
          {
            _id: user.user._id,
          },
          { $set: { inspired: true } }
        )
      }

      const response = await client().inspiredUsers()
      assert.strictEqual(response.inspiredUsers.length, 3)

      for (let i = 0; i < response.inspiredUsers.length; i++) {
        assert.strictEqual(response.inspiredUsers[i].name!.length > 0, true)
        assert.strictEqual(response.inspiredUsers[i].currentTeam?.name, 'Team 1')
      }
    })

    it('should return inspired users based on first param', async () => {
      for (let i = 0; i < 20; i++) {
        const user = await generateRandomUser()

        await UserModel.updateOne(
          {
            _id: user.user._id,
          },
          { $set: { inspired: true } }
        )
      }

      const response = await client().inspiredUsers({ first: 10 })

      for (let i = 0; i < response.inspiredUsers.length; i++) {
        assert.strictEqual(response.inspiredUsers[i].name!.length > 0, true)
        assert.strictEqual(response.inspiredUsers[i].currentTeam?.name, 'Team 1')
      }
    })
  })

  describe('topViewedUsers query', () => {
    it('should return top viewed users - all time period', async () => {
      await UserModel.deleteMany({})
      await UserViewModel.deleteMany({})

      const randomUser = await generateRandomUser()

      await client().visitUser({
        visitUserId: randomUser.user._id,
      })

      const response = await client(randomUser.tokens.accessToken).topViewedUsers({
        period: Period.AllTime,
        pagination: {
          first: 1,
        },
      })

      assert.strictEqual(response.topViewedUsers.edges[0].node.id, randomUser.user._id.toString())
      assert.strictEqual(response.topViewedUsers.edges[0].node.name, randomUser.user.name)
      assert.strictEqual(response.topViewedUsers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(
        response.topViewedUsers.edges[0].node.currentTeam?.id,
        randomUser.user.currentTeam?._id.toString()
      )
      assert.strictEqual(response.topViewedUsers.edges.length, 1)
    })

    it('should return and sort top viewed users monthly, filtered by country and year of birth, paginated', async () => {
      await UserViewModel.deleteMany({})
      await UserViewAggregatedModel.deleteMany({})

      const limitYear = getBirthYearLimitFor18()

      const userWithFiveViewThisMonthCZ = await generateRandomUser({
        firstname: 'Lenka',
        lastname: 'Fiveviews',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(limitYear, 1, 1),
      })
      const userWithMoreViewThisMonthCZ = await generateRandomUser({
        firstname: 'Jitka',
        lastname: 'Moreviews',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(limitYear, 1, 1),
      })
      const userWithOneViewThisMonthCZ = await generateRandomUser({
        firstname: 'Aneta',
        lastname: 'Oneview',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(2004, 1, 1),
      })
      const userWithTenViewsLastMonthCZ = await generateRandomUser({
        firstname: 'LastMonth',
        lastname: 'Tenviews',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(2002, 1, 1),
      })
      const userWithMoreViewThisMonthUS = await generateRandomUser({
        firstname: 'UsLastMonth',
        lastname: 'Views',
        role: dbrole.PLAYER,
        country: 'US',
        dateOfBirth: new Date(2005, 1, 1),
      })

      const userAnotherYear = await generateRandomUser({
        firstname: 'Another',
        lastname: 'BirthYear',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(limitYear, 1, 1),
      })

      const today = new Date()
      const thisMonth = moment(today).utc().toDate()
      const lastMonth = moment(today).utc().subtract(1, 'month').toDate()

      await createUserViews(userWithOneViewThisMonthCZ.user, 1, thisMonth)
      await createUserViews(userWithFiveViewThisMonthCZ.user, 5, thisMonth)
      await createUserViews(userWithMoreViewThisMonthCZ.user, 10, thisMonth)
      await createUserViews(userWithMoreViewThisMonthUS.user, 10, thisMonth)
      await createUserViews(userWithTenViewsLastMonthCZ.user, 10, lastMonth)
      await createUserViews(userAnotherYear.user, 1, thisMonth)

      const responseFirstPageMonthly = await client(
        userWithOneViewThisMonthCZ.tokens.accessToken
      ).topViewedUsers({
        period: Period.Monthly,
        pagination: { first: 2 },
        filter: { country: 'CZ', yearOfBirth: limitYear },
      })

      assert.strictEqual(responseFirstPageMonthly.topViewedUsers.myPosition, 4)
      assert.strictEqual(responseFirstPageMonthly.topViewedUsers.edges.length, 2)
      assert.strictEqual(
        responseFirstPageMonthly.topViewedUsers.edges[0].node.name,
        'Jitka Moreviews'
      )
      assert.strictEqual(
        responseFirstPageMonthly.topViewedUsers.edges[0].node.currentTeam?.name,
        'Team 1'
      )
      assert.strictEqual(responseFirstPageMonthly.topViewedUsers.edges[0].views, 10)
      assert.strictEqual(
        responseFirstPageMonthly.topViewedUsers.edges[1].node.name,
        'Lenka Fiveviews'
      )
      assert.strictEqual(
        responseFirstPageMonthly.topViewedUsers.edges[1].node.currentTeam?.name,
        'Team 1'
      )
      assert.strictEqual(responseFirstPageMonthly.topViewedUsers.edges[1].views, 5)
      assert.strictEqual(responseFirstPageMonthly.topViewedUsers.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPageMonthly.topViewedUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPageMonthly.topViewedUsers.pageInfo.totalCount, 3)

      const responseSecondPageMonthly = await client().topViewedUsers({
        period: Period.Monthly,
        pagination: { first: 2, after: responseFirstPageMonthly.topViewedUsers.pageInfo.endCursor },
        filter: { country: 'CZ', yearOfBirth: limitYear },
      })

      assert.strictEqual(responseSecondPageMonthly.topViewedUsers.edges.length, 1)
      assert.strictEqual(
        responseSecondPageMonthly.topViewedUsers.edges[0].node.name,
        'Another BirthYear'
      )
      assert.strictEqual(
        responseSecondPageMonthly.topViewedUsers.edges[0].node.currentTeam?.name,
        'Team 1'
      )
      assert.strictEqual(responseSecondPageMonthly.topViewedUsers.edges[0].views, 1)
      assert.strictEqual(responseSecondPageMonthly.topViewedUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPageMonthly.topViewedUsers.pageInfo.hasPreviousPage, true)

      const responseUS = await client().topViewedUsers({
        period: Period.Monthly,
        pagination: { first: 1 },
        filter: { country: 'US' },
      })
      assert.strictEqual(responseUS.topViewedUsers.edges.length, 1)
      assert.strictEqual(responseUS.topViewedUsers.edges[0].node.name, 'UsLastMonth Views')
      assert.strictEqual(responseUS.topViewedUsers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(responseUS.topViewedUsers.edges[0].node.country, 'US')
      assert.strictEqual(responseUS.topViewedUsers.edges[0].views, 10)
      assert.strictEqual(responseUS.topViewedUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(responseUS.topViewedUsers.pageInfo.hasPreviousPage, false)

      const responseAnotherYearOfBirth = await client().topViewedUsers({
        period: Period.Monthly,
        pagination: { first: 3 },
        filter: { country: 'CZ', yearOfBirth: limitYear },
      })
      assert.strictEqual(responseAnotherYearOfBirth.topViewedUsers.edges.length, 3)
      assert.strictEqual(
        responseAnotherYearOfBirth.topViewedUsers.edges[0].node.name,
        'Jitka Moreviews'
      )
      assert.strictEqual(
        responseAnotherYearOfBirth.topViewedUsers.edges[0].node.currentTeam?.name,
        'Team 1'
      )
      assert.strictEqual(responseAnotherYearOfBirth.topViewedUsers.edges[0].views, 10)
      assert.strictEqual(
        responseAnotherYearOfBirth.topViewedUsers.edges[0].node.name,
        'Jitka Moreviews'
      )
      assert.strictEqual(responseAnotherYearOfBirth.topViewedUsers.edges[2].views, 1)
      assert.strictEqual(
        responseAnotherYearOfBirth.topViewedUsers.edges[2].node.name,
        'Another BirthYear'
      )
      assert.strictEqual(responseAnotherYearOfBirth.topViewedUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(responseAnotherYearOfBirth.topViewedUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseAnotherYearOfBirth.topViewedUsers.pageInfo.totalCount, 3)
    })

    it('should return and sort top viewed users all time, filtered by country and year of birth, paginated', async () => {
      await UserViewModel.deleteMany({})
      await UserModel.deleteMany({})

      const limitYear = getBirthYearLimitFor18()

      const userWithFiveViewCZ = await generateRandomUser({
        firstname: 'Tomas',
        lastname: 'Lessviews',
        role: dbrole.PLAYER,

        country: 'CZ',
        dateOfBirth: new Date(2000, 1, 1),
      })
      const userWithMoreViewCZ = await generateRandomUser({
        firstname: 'Marek',
        lastname: 'Moreviews',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(limitYear, 1, 1),
      })
      const userWithOneViewCZ = await generateRandomUser({
        firstname: 'Lisa',
        lastname: 'Oneview',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(limitYear, 1, 1),
      })

      const userWithMoreViewUS = await generateRandomUser({
        firstname: 'Usman',
        lastname: 'Views',
        role: dbrole.PLAYER,
        country: 'US',
        dateOfBirth: new Date(2001, 1, 1),
      })

      const userAnotherYear = await generateRandomUser({
        firstname: 'Another',
        lastname: 'BirthYear',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(limitYear, 1, 1),
      })

      const today = new Date()
      const oneDayAgo = new Date(today)
      oneDayAgo.setDate(today.getDate() - 1)
      const thisMonth = new Date(today)
      thisMonth.setMonth(today.getMonth())
      const lastMonth = new Date(today)
      lastMonth.setMonth(today.getMonth() - 1)

      await createUserViews(userWithOneViewCZ.user, 5, today)
      await createUserViews(userWithFiveViewCZ.user, 15, oneDayAgo)
      await createUserViews(userWithMoreViewCZ.user, 20, thisMonth)
      await createUserViews(userWithMoreViewUS.user, 20, lastMonth)
      await createUserViews(userAnotherYear.user, 1, today)

      const responseFirstPageAllTime = await client().topViewedUsers({
        period: Period.AllTime,
        pagination: { first: 2 },
        filter: { country: 'CZ', yearOfBirth: limitYear },
      })

      assert.strictEqual(responseFirstPageAllTime.topViewedUsers.edges.length, 2)
      assert.strictEqual(
        responseFirstPageAllTime.topViewedUsers.edges[0].node.name,
        'Marek Moreviews'
      )
      assert.strictEqual(
        responseFirstPageAllTime.topViewedUsers.edges[0].node.currentTeam?.name,
        'Team 1'
      )
      assert.strictEqual(responseFirstPageAllTime.topViewedUsers.edges[0].views, 20)
      assert.strictEqual(responseFirstPageAllTime.topViewedUsers.edges[1].node.name, 'Lisa Oneview')
      assert.strictEqual(
        responseFirstPageAllTime.topViewedUsers.edges[1].node.currentTeam?.name,
        'Team 1'
      )
      assert.strictEqual(responseFirstPageAllTime.topViewedUsers.edges[1].views, 5)
      assert.strictEqual(responseFirstPageAllTime.topViewedUsers.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPageAllTime.topViewedUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPageAllTime.topViewedUsers.pageInfo.totalCount, 3)

      const responseSecondPageAllTime = await client(
        userWithFiveViewCZ.tokens.accessToken
      ).topViewedUsers({
        period: Period.AllTime,
        pagination: { first: 2, after: responseFirstPageAllTime.topViewedUsers.pageInfo.endCursor },
        filter: { country: 'CZ', yearOfBirth: limitYear },
      })
      assert.strictEqual(responseSecondPageAllTime.topViewedUsers.edges.length, 1)
      assert.strictEqual(
        responseSecondPageAllTime.topViewedUsers.edges[0].node.name,
        'Another BirthYear'
      )
      assert.strictEqual(
        responseSecondPageAllTime.topViewedUsers.edges[0].node.currentTeam?.name,
        'Team 1'
      )
      assert.strictEqual(responseSecondPageAllTime.topViewedUsers.edges[0].views, 1)
      assert.strictEqual(responseSecondPageAllTime.topViewedUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPageAllTime.topViewedUsers.pageInfo.hasPreviousPage, true)

      const responseUS = await client(userWithMoreViewUS.tokens.accessToken).topViewedUsers({
        period: Period.AllTime,
        pagination: { first: 1 },
        filter: { country: 'US' },
      })
      assert.strictEqual(responseUS.topViewedUsers.edges.length, 1)
      assert.strictEqual(responseUS.topViewedUsers.edges[0].node.name, 'Usman Views')
      assert.strictEqual(responseUS.topViewedUsers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.strictEqual(responseUS.topViewedUsers.edges[0].node.country, 'US')
      assert.strictEqual(responseUS.topViewedUsers.edges[0].views, 20)
      assert.strictEqual(responseUS.topViewedUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(responseUS.topViewedUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseUS.topViewedUsers.pageInfo.totalCount, 1)
    })

    it('should use default pagination if no pagination arguments are provided', async () => {
      await UserViewModel.deleteMany({})
      await UserModel.deleteMany({})

      const userWithFiveViewCZ = await generateRandomUser({
        firstname: 'Tomas',
        lastname: 'Lessviews',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(2000, 1, 1),
      })
      const userWithMoreViewCZ = await generateRandomUser({
        firstname: 'Marek',
        lastname: 'Moreviews',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(2000, 1, 1),
      })
      const userWithOneViewCZ = await generateRandomUser({
        firstname: 'Lisa',
        lastname: 'Oneview',
        role: dbrole.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date(2000, 1, 1),
      })

      const today = new Date()
      const oneDayAgo = new Date(today)
      oneDayAgo.setDate(today.getDate() - 1)

      await createUserViews(userWithOneViewCZ.user, 5, today)
      await createUserViews(userWithFiveViewCZ.user, 15, oneDayAgo)
      await createUserViews(userWithMoreViewCZ.user, 20, today)

      const response = await client(userWithFiveViewCZ.tokens.accessToken).topViewedUsers({
        period: Period.AllTime,
        filter: { country: 'CZ' },
      })
      assert.strictEqual(response.topViewedUsers.edges.length, 3)
      assert.strictEqual(response.topViewedUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(response.topViewedUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.topViewedUsers.pageInfo.totalCount, 3)
      assert.strictEqual(response.topViewedUsers.edges[0].node.name, 'Marek Moreviews')
      assert.strictEqual(response.topViewedUsers.edges[0].views, 20)
      assert.strictEqual(response.topViewedUsers.edges[1].node.name, 'Tomas Lessviews')
      assert.strictEqual(response.topViewedUsers.edges[1].views, 15)
      assert.strictEqual(response.topViewedUsers.edges[2].node.name, 'Lisa Oneview')
      assert.strictEqual(response.topViewedUsers.edges[2].views, 5)
    })

    it('should show only new players in the last 14 days', async () => {
      await UserViewModel.deleteMany({})
      await UserModel.deleteMany({})

      const today = new Date()
      const oldUser = await generateRandomUser()
      await createUserViews(oldUser.user, 5, today)

      await UserModel.updateOne(
        {
          _id: oldUser.user._id,
        },
        {
          $set: {
            createdAt: moment().subtract(16, 'days').toDate(),
          },
        },
        {
          timestamps: false,
        }
      )

      const newUser1 = await generateRandomUser()
      const newUser2 = await generateRandomUser()
      await createUserViews(newUser1.user, 5, today)
      await createUserViews(newUser2.user, 10, today)

      const response = await client().topViewedUsers({
        period: Period.AllTime,
        filter: {
          type: TopViewedFilterType.New,
        },
      })

      assert.strictEqual(response.topViewedUsers.edges.length, 2)
      assert.strictEqual(response.topViewedUsers.edges[0].node.name, newUser2.user.name)
      assert.strictEqual(response.topViewedUsers.edges[0].views, 10)
      assert.strictEqual(response.topViewedUsers.edges[1].node.name, newUser1.user.name)
      assert.strictEqual(response.topViewedUsers.edges[1].views, 5)
    })

    it('should filter users with yearOfBirth less than limitYear', async () => {
      await UserViewModel.deleteMany({})
      await UserModel.deleteMany({})

      const user1 = await generateRandomUser({
        dateOfBirth: new Date('2000-01-31T00:00:00.000+00:00'),
      })
      const user2 = await generateRandomUser({
        dateOfBirth: new Date('2010-01-31T00:00:00.000+00:00'),
      })

      await createUserViews(user1.user, 5, new Date())
      await createUserViews(user2.user, 10, new Date())

      const response = await client(user1.tokens.accessToken).topViewedUsers({
        period: Period.AllTime,
        filter: { yearOfBirth: 2000 },
      })

      assert.strictEqual(response.topViewedUsers.edges.length, 1)
      assert.strictEqual(response.topViewedUsers.edges[0].node.id, user2.user._id.toString())
    })
  })

  describe('searchUsers query', () => {
    it('will generate 15 users with same name+index, return searched users by last name response and check pagination', async () => {
      const users = []

      for (let i = 0; i < 15; i++) {
        const user = await generateRandomUser({ firstname: 'Tomas', lastname: 'Remetak' + i })
        users.push(user)
      }

      // hack to allow MongoDB to index data
      await new Promise((resolve) => setTimeout(resolve, 1500))

      const firstPage = await client().searchUsers({
        filter: {
          searchQuery: 'Remetak',
        },
        pagination: {
          first: 10,
        },
      })

      assert.strictEqual(firstPage.searchUsers.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPage.searchUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPage.searchUsers.edges.length, 10)
      assert.strictEqual(firstPage.searchUsers.edges[0].node.name, 'Tomas Remetak14')
      assert.strictEqual(firstPage.searchUsers.edges[0].node.currentTeam?.name, 'Team 1')

      const secondPage = await client().searchUsers({
        filter: {
          searchQuery: 'Remetak',
        },
        pagination: {
          first: 10,
          after: firstPage.searchUsers.pageInfo.endCursor,
        },
      })

      assert.strictEqual(secondPage.searchUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPage.searchUsers.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPage.searchUsers.edges.length, 5)
      assert.strictEqual(secondPage.searchUsers.edges[0].node.name, 'Tomas Remetak4')
      assert.strictEqual(firstPage.searchUsers.edges[0].node.currentTeam.name, 'Team 1')
    })
    it('should filter users by country', async () => {
      await UserModel.deleteMany({})

      // Create users with different countries
      const userCZ = await generateRandomUser({
        firstname: 'Jan',
        lastname: 'Novak',
        country: 'CZ',
      })
      await generateRandomUser({
        firstname: 'John',
        lastname: 'Smith',
        country: 'US',
      })

      // Allow MongoDB to index data
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const response = await client().searchUsers({
        filter: {
          searchQuery: null,
          country: 'CZ',
        },
      })

      assert.strictEqual(response.searchUsers.edges.length, 1)
      assert.strictEqual(response.searchUsers.edges[0].node.name, userCZ.user.name)
      assert.strictEqual(response.searchUsers.edges[0].node.country, userCZ.user.country)
    })

    it('should filter users by role', async () => {
      await UserModel.deleteMany({})

      // Create users with different roles
      await generateRandomUser({
        firstname: 'Player',
        lastname: 'User',
        role: dbrole.PLAYER,
      })
      const parentUser = await generateRandomUser({
        firstname: 'Parent',
        lastname: 'User',
        role: dbrole.PARENT,
      })

      // Allow MongoDB to index data
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const response = await client().searchUsers({
        filter: {
          searchQuery: null,
          role: dbrole.PARENT,
        },
      })

      assert.strictEqual(response.searchUsers.edges.length, 1)
      assert.strictEqual(response.searchUsers.edges[0].node.name, parentUser.user.name)
      assert.strictEqual(response.searchUsers.edges[0].node.role, dbrole.PARENT)
    })

    it('should filter users by both country and role', async () => {
      await UserModel.deleteMany({})

      // Create users with different combinations
      const czPlayer = await generateRandomUser({
        firstname: 'CZ',
        lastname: 'Player',
        country: 'CZ',
        role: dbrole.PLAYER,
      })
      await generateRandomUser({
        firstname: 'US',
        lastname: 'Player',
        country: 'US',
        role: dbrole.PLAYER,
      })
      await generateRandomUser({
        firstname: 'CZ',
        lastname: 'Parent',
        country: 'CZ',
        role: dbrole.PARENT,
      })
      await generateRandomUser({
        firstname: 'US',
        lastname: 'Parent',
        country: 'US',
        role: dbrole.PARENT,
      })

      // Allow MongoDB to index data
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const response = await client().searchUsers({
        filter: {
          searchQuery: null,
          country: 'CZ',
          role: dbrole.PLAYER,
        },
      })

      assert.strictEqual(response.searchUsers.edges.length, 1)
      assert.strictEqual(response.searchUsers.edges[0].node.name, czPlayer.user.name)
      assert.strictEqual(response.searchUsers.edges[0].node.country, czPlayer.user.country)
      assert.strictEqual(response.searchUsers.edges[0].node.role, dbrole.PLAYER)
    })

    it('should filter users by multiple roles using roles array', async () => {
      await UserModel.deleteMany({})

      // Create users with different roles
      await generateRandomUser({ firstname: 'Regular', lastname: 'Player', role: dbrole.PLAYER })
      await generateRandomUser({
        firstname: 'Shadow',
        lastname: 'Player',
        role: dbrole.SHADOW_PLAYER,
      })
      await generateRandomUser({ firstname: 'Sports', lastname: 'Fan', role: dbrole.SPORTS_FAN })
      await generateRandomUser({
        firstname: 'Sports',
        lastname: 'Pro',
        role: dbrole.SPORTS_PROFESSIONAL,
      })

      // Allow MongoDB to index data
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const response = await client().searchUsers({
        filter: {
          searchQuery: null,
          roles: [Role.Player, Role.ShadowPlayer],
        },
      })

      assert.strictEqual(response.searchUsers.edges.length, 2)

      // Check that only users with PLAYER or SHADOW_PLAYER roles are returned
      const roleSet = new Set(response.searchUsers.edges.map((edge) => edge.node.role))
      assert.strictEqual(roleSet.size, 2)
      assert.ok(roleSet.has(Role.Player))
      assert.ok(roleSet.has(Role.ShadowPlayer))

      // Verify the specific users
      const names = response.searchUsers.edges.map((edge) => edge.node.name)
      assert.ok(names.includes('Regular Player'))
      assert.ok(names.includes('Shadow Player'))
    })
  })

  describe('update user currency admin', () => {
    it('should return an error if user doesnt exist', async () => {
      const randomUser = await generateRandomUser()
      const randomAdmin = await generateRandomAdminUser()

      const findOneStub = sinon.stub(UserModel, 'findOne').resolves(null)

      const response = await client(randomAdmin.tokens.accessToken).updateUserCurrency({
        currency: 'CZK',
        updateUserCurrencyId: randomUser.user._id,
      })

      assert.strictEqual(response.updateUserCurrency.errors[0].message, 'User not found')

      sinon.assert.calledOnce(findOneStub)
      findOneStub.restore()
    })

    it('should return an error if user doesnt have stripe account', async () => {
      const randomUser = await generateRandomUser()
      const randomAdmin = await generateRandomAdminUser()

      const response = await client(randomAdmin.tokens.accessToken).updateUserCurrency({
        currency: 'CZK',
        updateUserCurrencyId: randomUser.user._id,
      })

      assert.strictEqual(response.updateUserCurrency.errors[0].message, 'User not found')
    })

    it('should update user currency', async () => {
      const randomUser = await generateRandomUser()
      const randomAdmin = await generateRandomAdminUser()

      await UserModel.updateOne(
        {
          _id: randomUser.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )
      stripeClientStub.accounts.update.resolves({
        id: 'acct_2',
        individual: {
          first_name: 'Active',
          last_name: 'Payments',
        },
        country: 'CZ',
        default_currency: 'EUR',
        payouts_enabled: true,
        payments_enabled: true,
        capabilities: {
          card_payments: 'active',
        },
      })

      const response = await client(randomAdmin.tokens.accessToken).updateUserCurrency({
        currency: 'CZK',
        updateUserCurrencyId: randomUser.user._id,
      })

      assert.strictEqual(stripeClientStub.accounts.update.calledOnce, true)
      sinon.assert.calledWith(stripeClientStub.accounts.update, 'valid-stripe-account-id', {
        individual: {
          ssn_last_4: undefined,
          email: undefined,
          first_name: undefined,
          last_name: undefined,
          relationship: {
            title: undefined,
          },
          address: {
            city: undefined,
            line1: undefined,
            postal_code: undefined,
            state: undefined,
          },

          phone: undefined,
        },
        email: undefined,
        default_currency: 'CZK',
        settings: {
          payouts: {
            schedule: {
              interval: 'weekly',
              weekly_anchor: 'monday',
            },
          },
          payments: {
            statement_descriptor: `GRAET ${randomUser.user.name}`.substring(0, 22),
          },
        },
      })
      assert.strictEqual(response.updateUserCurrency.errors.length, 0)
      assert.strictEqual(response.updateUserCurrency.currency, 'EUR')
    })

    it('should throw error if account is not updated succesfully', async () => {
      const randomUser = await generateRandomUser()
      const randomAdmin = await generateRandomAdminUser()

      stripeClientStub.accounts.update.rejects(new Error('Failed to update user currency'))

      await UserModel.updateOne(
        {
          _id: randomUser.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      try {
        await client(randomAdmin.tokens.accessToken).updateUserCurrency({
          currency: 'CZK',
          updateUserCurrencyId: randomUser.user._id,
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to update user currency')
      }
    })
  })

  describe('update user admin', () => {
    it('should return an error if user not found', async () => {
      const randomAdmin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      await UserModel.deleteOne({ _id: randomUser.user._id })

      const response = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: {
          firstname: 'Tom',
          lastname: 'Update',
        },
        updateUserAdminId: randomUser.user._id,
      })

      assert.strictEqual(response.updateUserAdmin.errors[0].message, 'User not found')
    })

    it('should return updated user', async () => {
      const team = await TeamModel.findOne({ name: 'Team 1' })

      const randomAdmin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('agency124')
      const agencyId = agency._id.toString()

      const randomUser = await generateRandomUser({
        role: dbrole.PLAYER,
        agency: agency._id,
      })

      const agent = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
      })

      const getUserInput = {
        firstname: 'Evzen',
        lastname: 'Hnizdo',
        dateOfBirth: new Date('2010-03-25'),
        country: 'CZ',
        contact: {
          firstname: 'Jana',
          lastname: 'Hnizdova',
          email: '<EMAIL>',
          phoneNumber: {
            countryPrefix: '421',
            number: '123456789',
          },
          type: ContactType.Mother,
        },
        bio: {
          position: BioPosition.Center,
          handedness: BioHandedness.Right,
          playerType: BioPlayerType.Hybrid,
          schoolType: BioSchoolType.GraduateSchool,
          gender: BioGender.Male,
          agency: agencyId,
          height: {
            feet: 9,
            inches: 2,
            centimeters: 185,
          },
          weight: {
            pounds: 100,
            kilograms: 60,
          },
          birthplace: 'Náchod',
          nameOfSchool: 'SPŠ Stavební Náchod',
          graduationYear: 2015,
          cumulativeGPA: 1,
          coreGPA: 2,
          unweightedGPA: 3,
          weightedGPA: 4,
          satScore: 1100,
          actScore: 25,
          toeflScore: 20,
          ncaaEligibility: true,
          naiaEligibility: false,
        },
        phoneNumber: {
          countryPrefix: '421',
          number: '987654321',
        },
        externalLinks: [
          {
            name: 'Seznam',
            url: 'https://www.seznam.cz/',
            type: ExternalLinkType.Custom,
          },
        ],
        boostMessage: 'test boostmessage',
        boost: {
          currency: 'EUR',
          goalAmount: 15000,
        },
        currentTeam: team?.id,
        inspired: true,
        recommended: true,
        role: Role.Player,
        searchEnabled: true,
        slug: 'evzen-hnizdo',
        public: true,
        moreSelected: true,
        email: randomUser.user.email,
        agent: agent.user._id,
      }

      const response = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: getUserInput,

        updateUserAdminId: randomUser.user._id,
      })

      const user = response.updateUserAdmin.user
      assert.strictEqual(user?.firstname, getUserInput.firstname)
      assert.strictEqual(user?.lastname, getUserInput.lastname)
      assert.strictEqual(user?.dateOfBirth, getUserInput.dateOfBirth.toISOString())
      assert.strictEqual(user?.country, getUserInput.country)
      assert.strictEqual(user?.contact?.firstname, getUserInput.contact.firstname)
      assert.strictEqual(user?.contact?.lastname, getUserInput.contact.lastname)
      assert.strictEqual(user?.contact?.type, getUserInput.contact.type)
      assert.strictEqual(user?.boostMessage, getUserInput.boostMessage)
      assert.strictEqual(user?.boost?.currency, getUserInput.boost.currency)
      assert.strictEqual(user?.boost?.goalAmount, getUserInput.boost.goalAmount)
      assert.strictEqual(user?.currentTeam?.id, getUserInput.currentTeam)
      assert.strictEqual(user?.inspired, getUserInput.inspired)
      assert.strictEqual(user?.recommended, getUserInput.recommended)
      assert.strictEqual(user?.role, getUserInput.role)
      assert.strictEqual(user?.searchEnabled, getUserInput.searchEnabled)
      assert.strictEqual(user?.slug, getUserInput.slug)
      assert.strictEqual(user?.public, getUserInput.public)
      assert.strictEqual(user?.moreSelected, getUserInput.moreSelected)
      assert.strictEqual(user?.email, getUserInput.email)
      assert.strictEqual(user?.agentV2?.email, agent.user.email)

      const bio = response.updateUserAdmin.user?.bio
      assert.strictEqual(bio?.position, getUserInput.bio.position)
      assert.strictEqual(bio?.handedness, getUserInput.bio.handedness)
      assert.strictEqual(bio?.playerType, getUserInput.bio.playerType)
      assert.strictEqual(bio?.schoolType, getUserInput.bio.schoolType)
      assert.strictEqual(bio?.height?.feet, getUserInput.bio.height.feet)
      assert.strictEqual(bio?.height?.inches, getUserInput.bio.height.inches)
      assert.strictEqual(bio?.height?.centimeters, getUserInput.bio.height.centimeters)
      assert.strictEqual(bio?.weight?.pounds, getUserInput.bio.weight.pounds)
      assert.strictEqual(bio?.weight?.kilograms, getUserInput.bio.weight.kilograms)
      assert.strictEqual(bio?.birthplace, getUserInput.bio.birthplace)
      assert.strictEqual(bio?.nameOfSchool, getUserInput.bio.nameOfSchool)
      assert.strictEqual(bio?.graduationYear, getUserInput.bio.graduationYear)
      assert.strictEqual(bio?.cumulativeGPA, getUserInput.bio.cumulativeGPA)
      assert.strictEqual(bio?.coreGPA, getUserInput.bio.coreGPA)
      assert.strictEqual(bio?.unweightedGPA, getUserInput.bio.unweightedGPA)
      assert.strictEqual(bio?.weightedGPA, getUserInput.bio.weightedGPA)
      assert.strictEqual(bio?.satScore, getUserInput.bio.satScore)
      assert.strictEqual(bio?.actScore, getUserInput.bio.actScore)
      assert.strictEqual(bio?.toeflScore, getUserInput.bio.toeflScore)
      assert.strictEqual(bio?.ncaaEligibility, getUserInput.bio.ncaaEligibility)
      assert.strictEqual(bio?.naiaEligibility, getUserInput.bio.naiaEligibility)
      assert.strictEqual(bio?.agency?.id, agencyId)
      assert.strictEqual(bio?.agency?.name, agency.name)

      const externalLinks = response.updateUserAdmin.user?.externalLinks
      assert.strictEqual(externalLinks?.length, getUserInput.externalLinks.length)
      assert.strictEqual(externalLinks?.[0].name, getUserInput.externalLinks[0].name)
      assert.strictEqual(externalLinks?.[0].url, getUserInput.externalLinks[0].url)
      assert.strictEqual(externalLinks?.[0].type, getUserInput.externalLinks[0].type)

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should be possible to null more that one value at a time', async () => {
      const team = await TeamModel.findOne({ name: 'Team 1' })

      const randomAdmin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('agency128')
      const agencyId = agency._id.toString()

      const randomUser = await generateRandomUser({
        role: dbrole.PLAYER,
        team: team?.id,
        agency: agency._id,
      })

      const agent = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
      })

      const getUserInput = {
        firstname: 'Evzen',
        lastname: 'Hnizdo',
        dateOfBirth: new Date('2010-03-25'),
        country: 'CZ',
        contact: {
          firstname: 'Jana',
          lastname: 'Hnizdova',
          email: '<EMAIL>',
          phoneNumber: {
            countryPrefix: '421',
            number: '123456789',
          },
          type: ContactType.Mother,
        },
        bio: {
          position: BioPosition.Center,
          handedness: BioHandedness.Right,
          playerType: BioPlayerType.Hybrid,
          schoolType: BioSchoolType.GraduateSchool,
          gender: BioGender.Male,
          agency: agencyId,
          birthplace: 'Náchod',
          nameOfSchool: 'SPŠ Stavební Náchod',
          graduationYear: 2015,
          cumulativeGPA: 1,
          coreGPA: 2,
          unweightedGPA: 3,
          weightedGPA: 4,
          satScore: 1100,
          actScore: 25,
          toeflScore: 20,
          ncaaEligibility: true,
          naiaEligibility: false,
        },
        boostMessage: 'test boostmessage',
        currentTeam: team?.id,
        agent: agent.user._id,
      }

      await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: getUserInput,
        updateUserAdminId: randomUser.user._id,
      })

      const response = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: {
          contact: null,
          bio: { agency: null },
        },
        updateUserAdminId: randomUser.user._id,
      })

      const user = response.updateUserAdmin.user
      assert.strictEqual(user?.firstname, getUserInput.firstname)
      assert.strictEqual(user?.contact, null)
      const bio = response.updateUserAdmin.user?.bio
      assert.strictEqual(bio?.agency, null)

      sinon.assert.calledTwice(amplitudeTrackStub)
    })

    it('should recalculate agency clients if agency is updated via admin', async () => {
      const agency1 = await generateRandomAgency('agency1')
      const agency2 = await generateRandomAgency('agency2')

      const randomAdmin = await generateRandomAdminUser()

      const user1 = await generateRandomUser()

      await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: {
          bio: {
            agency: agency1._id.toString(),
          },
        },
        updateUserAdminId: user1.user._id,
      })

      const agency1Updated = await AgencyModel.findById(agency1._id)
      assert.strictEqual(agency1Updated?.clientsCount, 1)

      await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: {
          bio: {
            agency: agency2._id.toString(),
          },
        },
        updateUserAdminId: user1.user._id,
      })

      const agency1Updated2 = await AgencyModel.findById(agency1._id)
      assert.strictEqual(agency1Updated2?.clientsCount, 0)

      const agency2Updated = await AgencyModel.findById(agency2._id)
      assert.strictEqual(agency2Updated?.clientsCount, 1)
    })

    it('should return an error if updated user not found', async () => {
      const randomAdmin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()
      const getUserInput = await getUserUpdateInput()

      const query = {
        populate: sinon.stub().resolves(null),
      }

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const findOneAndUpdateStub = sinon.stub(UserModel, 'findOneAndUpdate').returns(query as any)

      const response = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: getUserInput,
        updateUserAdminId: randomUser.user._id,
      })

      assert.strictEqual(response.updateUserAdmin.errors[0].message, 'Updated user not found')

      sinon.assert.calledOnce(findOneAndUpdateStub)
      sinon.assert.calledOnce(query.populate)

      findOneAndUpdateStub.restore()
    })

    it('should unset fields that are null in the input', async () => {
      const randomAdmin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const getUserInput = {
        firstname: 'John',
        lastname: null,
        country: 'CZ',
      }

      const response = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: getUserInput,
        updateUserAdminId: randomUser.user._id,
      })
      assert.strictEqual(response.updateUserAdmin.user?.firstname, 'John')
      assert.strictEqual(response.updateUserAdmin.user?.lastname, '')

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should catch error if database fail', async () => {
      const randomAdmin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const findOneAndUpdateStub = sinon
        .stub(UserModel, 'findOneAndUpdate')
        .throws(new Error('Database error'))

      const response = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: { firstname: 'Lamanche' },
        updateUserAdminId: randomUser.user._id,
      })

      assert.strictEqual(response.updateUserAdmin.errors[0].message, 'User not found')

      findOneAndUpdateStub.restore()
    })

    it('should update user subrole and change agent counts in agency', async () => {
      const agency = await generateRandomAgency('agency test count 123')
      const agent = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.COACH,
        agency: agency._id,
      })

      const randomAdmin = await generateRandomAdminUser()

      const startCount = await AgencyModel.findById(agency._id)
      assert.strictEqual(startCount?.agentsCount, 0)

      const responseAgent = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: {
          subrole: SubRole.AGENT,
          bio: { agency: agency.id },
        },
        updateUserAdminId: agent.user._id,
      })

      assert.strictEqual(responseAgent.updateUserAdmin.user?.subrole, SubRole.AGENT)

      const plusCount = await AgencyModel.findById(agency._id)
      assert.strictEqual(plusCount?.agentsCount, 1)

      const responseCoach = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: {
          subrole: SubRole.COACH,
          bio: { agency: agency.id },
        },
        updateUserAdminId: agent.user._id,
      })

      assert.strictEqual(responseCoach.updateUserAdmin.user?.subrole, SubRole.COACH)

      const minusCount = await AgencyModel.findById(agency._id)
      assert.strictEqual(minusCount?.agentsCount, 0)
    })

    it('should remove agent from user', async () => {
      const randomAdmin = await generateRandomAdminUser()
      const agent = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
      })

      const user = await generateRandomUser({
        role: dbrole.PLAYER,
        agent: agent.user._id,
      })

      const response = await client(randomAdmin.tokens.accessToken).updateUserAdmin({
        input: {
          agent: null,
        },
        updateUserAdminId: user.user._id,
      })

      assert.strictEqual(response.updateUserAdmin.user?.agentV2, undefined)
    })
  })

  describe('entity counts', () => {
    beforeEach(async () => {
      await UserModel.deleteMany({})
      await AgencyModel.deleteMany({})
    })

    it('should return entity counts', async () => {
      await generateRandomUser({ role: dbrole.PLAYER })
      await generateRandomUser({ role: dbrole.SPORTS_PROFESSIONAL })
      await generateRandomAgency('Test agency counts', 'https://agency.com', true)

      const counts = await client().entityCounts()

      assert.strictEqual(counts.entityCounts.players, 1)
      assert.strictEqual(counts.entityCounts.professionals, 1)
      assert.strictEqual(counts.entityCounts.agencies, 1)
    })
  })

  describe('lastActiveAt', () => {
    it('should update lastActiveAt when currentUser is called', async () => {
      const user = await generateRandomUser()

      // First call to currentUser should set lastActiveAt
      const response = await client(user.tokens.accessToken).currentUser()
      assert.ok(response.currentUser)

      // Check that lastActiveAt was set in the database
      const updatedUser = await UserModel.findById(user.user._id)
      assert.ok(updatedUser?.lastActiveAt)

      // Store the initial lastActiveAt value
      const initialLastActiveAt = updatedUser?.lastActiveAt

      // Make another call immediately - lastActiveAt should not change (10 min throttle)
      await client(user.tokens.accessToken).currentUser()

      // Check that lastActiveAt was not updated
      const notUpdatedUser = await UserModel.findById(user.user._id)
      assert.strictEqual(notUpdatedUser?.lastActiveAt?.getTime(), initialLastActiveAt?.getTime())

      // Simulate time passing (11 minutes)
      const clock = sinon.useFakeTimers(new Date(Date.now() + 11 * 60 * 1000).getTime())

      // Make another call after time has passed - lastActiveAt should update
      await client(user.tokens.accessToken).currentUser()

      // Check that lastActiveAt was updated
      const updatedAgainUser = await UserModel.findById(user.user._id)
      assert.notStrictEqual(
        updatedAgainUser?.lastActiveAt?.getTime(),
        initialLastActiveAt?.getTime()
      )

      clock.restore()
    })
  })

  describe('users query admin', () => {
    it('should return users filtered by recommended with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const users = []
      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User' + i })
        users.push(user)
      }

      for (let i = 0; i < 2; i++) {
        await client(randomAdmin.tokens.accessToken).updateUserAdmin({
          input: { recommended: true },
          updateUserAdminId: users[i].user._id,
        })
      }

      const responseFirstPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { recommended: true },
        pagination: { first: 1 },
      })

      assert.strictEqual(responseFirstPage.users.edges.length, 1)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseFirstPage.users.edges[0].node.recommended, true)
      assert.strictEqual(responseFirstPage.users.edges[0].node.firstname, 'User1')

      const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { recommended: true },
        pagination: { first: 1, after: responseFirstPage.users.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.users.edges.length, 1)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
      assert.strictEqual(responseSecondPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseSecondPage.users.edges[0].node.recommended, true)
      assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User0')

      sinon.assert.calledTwice(amplitudeTrackStub) // 1 for each updateUserAdmin from for cycle
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[0].user._id.toString() })
      )
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[1].user._id.toString() })
      )
    })

    it('should return users filtered by inspired with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const users = []
      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User' + i })
        users.push(user)
      }

      for (let i = 0; i < 2; i++) {
        await client(randomAdmin.tokens.accessToken).updateUserAdmin({
          input: { inspired: true },
          updateUserAdminId: users[i].user._id,
        })
      }

      const responseFirstPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { inspired: true },
        pagination: { first: 1 },
      })

      assert.strictEqual(responseFirstPage.users.edges.length, 1)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseFirstPage.users.edges[0].node.inspired, true)
      assert.strictEqual(responseFirstPage.users.edges[0].node.firstname, 'User1')

      const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { inspired: true },
        pagination: { first: 1, after: responseFirstPage.users.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.users.edges.length, 1)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
      assert.strictEqual(responseSecondPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseSecondPage.users.edges[0].node.inspired, true)
      assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User0')

      sinon.assert.calledTwice(amplitudeTrackStub) // 1 for each updateUserAdmin from for cycle
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[0].user._id.toString() })
      )
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[1].user._id.toString() })
      )
    })

    it('should return users filtered by moreSelected with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const users = []
      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User' + i })
        users.push(user)
      }

      for (let i = 0; i < 2; i++) {
        await client(randomAdmin.tokens.accessToken).updateUserAdmin({
          input: { moreSelected: true },
          updateUserAdminId: users[i].user._id,
        })
      }

      const responseFirstPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { moreSelected: true },
        pagination: { first: 1 },
      })

      assert.strictEqual(responseFirstPage.users.edges.length, 1)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseFirstPage.users.edges[0].node.moreSelected, true)
      assert.strictEqual(responseFirstPage.users.edges[0].node.firstname, 'User1')

      const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { moreSelected: true },
        pagination: { first: 1, after: responseFirstPage.users.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.users.edges.length, 1)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
      assert.strictEqual(responseSecondPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseSecondPage.users.edges[0].node.moreSelected, true)
      assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User0')

      sinon.assert.calledTwice(amplitudeTrackStub) // 1 for each updateUserAdmin from for cycle
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[0].user._id.toString() })
      )
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[1].user._id.toString() })
      )
    })

    it('should return users filtered by withoutTeam with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const users = []
      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User' + i })
        users.push(user)
      }

      for (let i = 0; i < 2; i++) {
        await client(randomAdmin.tokens.accessToken).updateUserAdmin({
          input: { currentTeam: null },
          updateUserAdminId: users[i].user._id,
        })
      }

      const responseFirstPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { withoutTeam: true },
        pagination: { first: 1 },
      })

      assert.strictEqual(responseFirstPage.users.edges.length, 1)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseFirstPage.users.edges[0].node.currentTeam, null)
      assert.strictEqual(responseFirstPage.users.edges[0].node.firstname, 'User1')

      const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { withoutTeam: true },
        pagination: { first: 1, after: responseFirstPage.users.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.users.edges.length, 1)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
      assert.strictEqual(responseSecondPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseSecondPage.users.edges[0].node.currentTeam, null)
      assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User0')

      sinon.assert.calledTwice(amplitudeTrackStub) // 1 for each updateUserAdmin from for cycle
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[0].user._id.toString() })
      )
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[1].user._id.toString() })
      )
    })

    it('should return users filtered by withoutPicture with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const users = []
      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User' + i })
        users.push(user)
      }

      await UserModel.findByIdAndUpdate(users[0].user._id, { avatar: '/path/to/picture.jpeg' })

      const responseFirstPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { withoutPicture: true },
        pagination: { first: 1 },
      })

      assert.strictEqual(responseFirstPage.users.edges.length, 1)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseFirstPage.users.edges[0].node.firstname, 'User2')
    })

    it('should return users filtered by country with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const users = []
      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User' + i })
        users.push(user)
      }

      for (let i = 0; i < 2; i++) {
        await client(randomAdmin.tokens.accessToken).updateUserAdmin({
          input: { country: 'US' },
          updateUserAdminId: users[i].user._id,
        })
      }

      const responseFirstPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { country: 'US' },
        pagination: { first: 1 },
      })

      assert.strictEqual(responseFirstPage.users.edges.length, 1)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseFirstPage.users.edges[0].node.country, 'US')
      assert.strictEqual(responseFirstPage.users.edges[0].node.firstname, 'User1')

      const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { country: 'US' },
        pagination: { first: 1, after: responseFirstPage.users.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.users.edges.length, 1)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
      assert.strictEqual(responseSecondPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(responseSecondPage.users.edges[0].node.country, 'US')
      assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User0')

      sinon.assert.calledTwice(amplitudeTrackStub) // 1 for each updateUserAdmin from for cycle
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[0].user._id.toString() })
      )
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[1].user._id.toString() })
      )
    })

    it('should return users filtered by year of birth with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const users = []
      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User' + i })
        users.push(user)
      }

      for (let i = 0; i < 2; i++) {
        await client(randomAdmin.tokens.accessToken).updateUserAdmin({
          input: { dateOfBirth: new Date(2000, 1, 1) },
          updateUserAdminId: users[i].user._id,
        })
      }

      const responseFirstPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { yearOfBirth: 2000 },
        pagination: { first: 1 },
      })

      assert.strictEqual(responseFirstPage.users.edges.length, 1)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseFirstPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(
        new Date(responseFirstPage.users.edges[0].node.dateOfBirth).getFullYear(),
        2000
      )
      assert.strictEqual(responseFirstPage.users.edges[0].node.firstname, 'User1')

      const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { yearOfBirth: 2000 },
        pagination: { first: 1, after: responseFirstPage.users.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.users.edges.length, 1)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
      assert.strictEqual(responseSecondPage.users.pageInfo.totalCount, 2)
      assert.strictEqual(
        new Date(responseSecondPage.users.edges[0].node.dateOfBirth).getFullYear(),
        2000
      )
      assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User0')

      sinon.assert.calledTwice(amplitudeTrackStub) // 1 for each updateUserAdmin from for cycle
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[0].user._id.toString() })
      )
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: users[1].user._id.toString() })
      )
    })

    it('should return users filtered by agency with pagination', async () => {
      await UserModel.deleteMany({})

      const randomAdmin = await generateRandomAdminUser()

      const agency = await generateRandomAgency('Test agency user filter')

      const users = []
      for (let i = 0; i < 2; i++) {
        const user = await generateRandomUser({
          firstname: 'User with agency' + i,
          agency: agency._id,
        })
        users.push(user)
      }

      for (let i = 0; i < 3; i++) {
        const user = await generateRandomUser({ firstname: 'User without agency' + i })
        users.push(user)
      }

      const response = await client(randomAdmin.tokens.accessToken).users({
        filter: { agency: agency._id.toString() },
        pagination: { first: 1 },
      })

      assert.strictEqual(response.users.edges.length, 1)
      assert.strictEqual(response.users.pageInfo.hasNextPage, true)
      assert.strictEqual(response.users.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.users.pageInfo.totalCount, 2)
      assert.strictEqual(response.users.edges[0].node.firstname, 'User with agency1')

      const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
        filter: { agency: agency._id.toString() },
        pagination: { first: 1, after: response.users.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.users.edges.length, 1)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
      assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
      assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User with agency0')
    })
  })

  it('should return users filtered by possibleYearsOfBirth with pagination', async () => {
    await UserModel.deleteMany({})

    const randomAdmin = await generateRandomAdminUser()

    const users = []
    for (let i = 0; i < 3; i++) {
      const user = await generateRandomUser({
        firstname: 'User 2000-2001 ' + i,
        possibleYearsOfBirth: [2000, 2001],
      })
      users.push(user)
    }
    for (let i = 0; i < 3; i++) {
      const user = await generateRandomUser({
        firstname: 'User 2010-2011 ' + i,
        possibleYearsOfBirth: [2010, 2011],
      })
      users.push(user)
    }

    for (let i = 0; i < 2; i++) {
      const user = await generateRandomUser({
        firstname: 'User 2001-2002 ' + i,
        possibleYearsOfBirth: [2001, 2002],
      })
      users.push(user)
    }

    const response = await client(randomAdmin.tokens.accessToken).users({
      filter: { possibleYearsOfBirth: 2001 },
      pagination: { first: 3 },
    })

    assert.strictEqual(response.users.edges.length, 3)
    assert.strictEqual(response.users.pageInfo.hasNextPage, true)
    assert.strictEqual(response.users.pageInfo.hasPreviousPage, false)
    assert.strictEqual(response.users.edges[0].node.firstname, 'User 2001-2002 1')
    assert.strictEqual(response.users.edges[1].node.firstname, 'User 2001-2002 0')
    assert.strictEqual(response.users.edges[2].node.firstname, 'User 2000-2001 2')
    assert.strictEqual(response.users.pageInfo.totalCount, 5)

    const responseSecondPage = await client(randomAdmin.tokens.accessToken).users({
      filter: { possibleYearsOfBirth: 2001 },
      pagination: { first: 3, after: response.users.pageInfo.endCursor },
    })

    assert.strictEqual(responseSecondPage.users.edges.length, 2)
    assert.strictEqual(responseSecondPage.users.pageInfo.hasNextPage, false)
    assert.strictEqual(responseSecondPage.users.edges[0].node.firstname, 'User 2000-2001 1')
    assert.strictEqual(responseSecondPage.users.edges[1].node.firstname, 'User 2000-2001 0')

    assert.strictEqual(responseSecondPage.users.pageInfo.hasPreviousPage, true)
  })
})
