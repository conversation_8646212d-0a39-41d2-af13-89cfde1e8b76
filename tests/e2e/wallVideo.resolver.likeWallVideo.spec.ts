import assert from 'assert'
import sinon from 'sinon'

import { FeedActivityVerb } from '../../src/services/helpers/streamFeed'
import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'
import { generateWallVideo } from '../utils/wallVideos'

import { streamFeedAddActivityStub } from '.'

describe('Wall videos/likeWallVideo', () => {
  it('should call streamFeed addActivity with liked video info', async () => {
    const user = await generateRandomUser()
    const videoUser = await generateRandomUser()

    const video = await generateWallVideo({ user: videoUser.user, path: 'video1' })

    const response = await client(user.tokens.accessToken).likeWallVideo({
      id: video._id.toString(),
      isHighlight: false,
    })

    const theUser = user.user

    assert.deepStrictEqual(response, { likeWallVideo: true })
    sinon.assert.calledOnce(streamFeedAddActivityStub)

    sinon.assert.calledWith(
      streamFeedAddActivityStub,
      sinon.match({
        actor: theUser._id.toString(),
        foreign_id: video._id.toString(),
        object: video._id.toString(),
        verb: FeedActivityVerb.LIKE_VIDEO,
        actor_name: theUser?.name,
        actor_slug: theUser?.slug,
      })
    )
  })
})
