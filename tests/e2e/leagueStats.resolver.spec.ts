import assert from 'assert'

import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { LeagueModel } from '../../src/modules/stats/models/league'
import { LeagueStatsModel } from '../../src/modules/stats/models/leagueStats/leagueStats'
import { TeamModel } from '../../src/modules/stats/models/team'
import { client } from '../utils/graphql'
import { generateDiscoverUrl, generateLeagueStat } from '../utils/stats'
import { generateRandomLeague, generateRandomTeam } from '../utils/users'

import { leagueStatsMock } from './services/leagueStatsSyncer.helper'

describe('League Stats resolver', () => {
  beforeEach(async () => {
    await LeagueStatsModel.deleteMany({})
    await TeamModel.deleteMany({})
    await LeagueModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
  })

  describe('leagueStandings team query', () => {
    it('should return league standings for a team', async () => {
      // Create test data
      const league = await generateRandomLeague('Test League 3')
      const team1 = await generateRandomTeam({ name: 'Team 4' })
      const team2 = await generateRandomTeam({ name: 'Team 5' })
      const team3 = await generateRandomTeam({ name: 'Team 6' })

      const discoveredUrl = await generateDiscoverUrl()

      // Create league stats records in the same group
      const groupName = 'Group C'
      const season = '2023-2024'

      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team1.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team1._id,
          season,
          group: groupName,
          games: 10,
          wins: 7,
          losses: 2,
          overtimeWins: 1,
          overtimeLosses: 0,
          points: 16,
          goalsFor: 25,
          goalsAgainst: 15,
          standing: 1,
        },
      })

      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team2.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team2._id,
          season,
          group: groupName,
          games: 10,
          wins: 5,
          losses: 4,
          overtimeWins: 0,
          overtimeLosses: 1,
          points: 11,
          goalsFor: 18,
          goalsAgainst: 20,
          standing: 2,
        },
      })

      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team3.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team3._id,
          season,
          group: groupName,
          games: 10,
          wins: 3,
          losses: 6,
          overtimeWins: 0,
          overtimeLosses: 1,
          points: 7,
          goalsFor: 15,
          goalsAgainst: 22,
          standing: 3,
        },
      })

      const { leagueStandings } = await client().leagueStandings({
        teamId: team1._id.toString(),
        season,
      })

      assert(leagueStandings.length === 1)

      const leagueStanding = leagueStandings[0]

      assert(leagueStanding?.league?.name === 'Test League 3')
      assert(leagueStanding?.groups?.length === 1)

      const group = leagueStanding?.groups?.[0]

      assert(group?.group === groupName)
      assert(group?.standings?.length === 3)

      const standings = group?.standings
      if (!standings) {
        throw new Error('Standings not found')
      }

      assert(standings[0]?.team?.name === 'Team 4')
      assert(standings[0]?.standing === 1)
      assert(standings[0]?.points === 16)

      assert(standings[1]?.team?.name === 'Team 5')
      assert(standings[1]?.standing === 2)
      assert(standings[1]?.points === 11)

      assert(standings[2]?.team?.name === 'Team 6')
      assert(standings[2]?.standing === 3)
      assert(standings[2]?.points === 7)
    })

    it('should filter by season', async () => {
      const league = await generateRandomLeague('Test League 4')
      const team = await generateRandomTeam({ name: 'Team 7' })

      const groupName = 'Group D'
      const season1 = '2022-2023'
      const season2 = '2023-2024'

      const discoveredUrl = await generateDiscoverUrl()

      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team._id,
          season: season1,
          group: groupName,
          games: 10,
          wins: 6,
          losses: 3,
          overtimeWins: 1,
          overtimeLosses: 0,
          points: 14,
          goalsFor: 22,
          goalsAgainst: 18,
          standing: 2,
        },
      })

      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team._id,
          season: season2,
          group: groupName,
          games: 5,
          wins: 4,
          losses: 1,
          overtimeWins: 0,
          overtimeLosses: 0,
          points: 8,
          goalsFor: 12,
          goalsAgainst: 7,
          standing: 1,
        },
      })

      const response1 = await client().leagueStandings({
        teamId: team._id.toString(),
        season: season1,
      })

      // Assertions for season1
      assert(response1.leagueStandings.length === 1)
      const standing1 = response1.leagueStandings[0]?.groups?.[0]?.standings?.[0]
      assert(standing1?.season === season1)
      assert(standing1?.standing === 2)

      const response2 = await client().leagueStandings({
        teamId: team._id.toString(),
        season: season2,
      })

      // Assertions for season2
      assert(response2.leagueStandings.length === 1)
      const standing2 = response2.leagueStandings[0]?.groups?.[0]?.standings?.[0]
      assert(standing2?.season === season2)
      assert(standing2?.standing === 1)
    })
  })

  describe('leagueStandings league query', () => {
    it('should return league standings for a league', async () => {
      // Create test data
      const league = await generateRandomLeague('Test League 5')
      const league2 = await generateRandomLeague('Test League 6')
      const team1 = await generateRandomTeam({ name: 'Team 8' })
      const team2 = await generateRandomTeam({ name: 'Team 9' })
      const team3 = await generateRandomTeam({ name: 'Team 10' })
      const team4 = await generateRandomTeam({ name: 'Team 11' })

      const discoveredUrl = await generateDiscoverUrl()

      // Create league stats records in multiple groups
      const season = '2023-2024'
      const groupA = 'Group A'
      const groupB = 'Group B'

      // Group A teams
      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team1.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team1._id,
          season,
          group: groupA,
          games: 10,
          wins: 7,
          losses: 2,
          overtimeWins: 1,
          overtimeLosses: 0,
          points: 16,
          goalsFor: 25,
          goalsAgainst: 15,
          standing: 1,
        },
      })

      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team2.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team2._id,
          season,
          group: groupA,
          games: 10,
          wins: 4,
          losses: 5,
          overtimeWins: 1,
          overtimeLosses: 0,
          points: 10,
          goalsFor: 18,
          goalsAgainst: 20,
          standing: 2,
        },
      })

      // Group B teams
      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team3.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team3._id,
          season,
          group: groupB,
          games: 10,
          wins: 8,
          losses: 1,
          overtimeWins: 1,
          overtimeLosses: 0,
          points: 18,
          goalsFor: 30,
          goalsAgainst: 12,
          standing: 1,
        },
      })

      // team from different league
      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team4.name,
            leagueName: league2.name,
          },
          league: league2._id,
          team: team4._id,
          season,
          group: groupB,
          games: 10,
          wins: 8,
          losses: 1,
          overtimeWins: 1,
          overtimeLosses: 0,
          points: 18,
          goalsFor: 30,
          goalsAgainst: 12,
          standing: 1,
        },
      })

      const { leagueStandings } = await client().leagueStandings({
        leagueId: league._id.toString(),
        season,
      })

      assert(leagueStandings.length === 1)
      const leagueStanding = leagueStandings[0]

      assert(leagueStanding?.league?.name === 'Test League 5')
      assert(leagueStanding?.groups?.length === 2)

      // Check Group A
      const groupAData = leagueStanding?.groups?.find((g) => g.group === groupA)
      assert(groupAData?.standings?.length === 2)

      const standingsA = groupAData?.standings
      if (!standingsA) {
        throw new Error('Group A standings not found')
      }

      assert(standingsA[0]?.team?.name === 'Team 8')
      assert(standingsA[0]?.standing === 1)
      assert(standingsA[0]?.points === 16)

      assert(standingsA[1]?.team?.name === 'Team 9')
      assert(standingsA[1]?.standing === 2)
      assert(standingsA[1]?.points === 10)

      // Check Group B
      const groupBData = leagueStanding?.groups?.find((g) => g.group === groupB)
      assert(groupBData?.standings?.length === 1)

      const standingsB = groupBData?.standings
      if (!standingsB) {
        throw new Error('Group B standings not found')
      }

      assert(standingsB[0]?.team?.name === 'Team 10')
      assert(standingsB[0]?.standing === 1)
      assert(standingsB[0]?.points === 18)
    })

    it('should filter by season for league standings', async () => {
      const league = await generateRandomLeague('Test League 6')
      const team = await generateRandomTeam({ name: 'Team 11' })

      const groupName = 'Group E'
      const season1 = '2022-2023'
      const season2 = '2023-2024'

      const discoveredUrl = await generateDiscoverUrl()

      // Create league stats for season 1
      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team._id,
          season: season1,
          group: groupName,
          games: 10,
          wins: 6,
          losses: 3,
          overtimeWins: 1,
          overtimeLosses: 0,
          points: 14,
          goalsFor: 22,
          goalsAgainst: 18,
          standing: 1,
        },
      })

      // Create league stats for season 2
      await generateLeagueStat({
        discoveredUrl,
        leagueStats: {
          ...leagueStatsMock.leagueStats[0],
          externalInfo: {
            teamName: team.name,
            leagueName: league.name,
          },
          league: league._id,
          team: team._id,
          season: season2,
          group: groupName,
          games: 5,
          wins: 4,
          losses: 1,
          overtimeWins: 0,
          overtimeLosses: 0,
          points: 8,
          goalsFor: 12,
          goalsAgainst: 7,
          standing: 1,
        },
      })

      // Query for season 1
      const response1 = await client().leagueStandings({
        leagueId: league._id.toString(),
        season: season1,
      })

      // Assertions for season1
      assert(response1.leagueStandings.length === 1)
      const standing1 = response1.leagueStandings[0]?.groups?.[0]?.standings?.[0]
      assert(standing1?.season === season1)
      assert(standing1?.points === 14)

      // Query for season 2
      const response2 = await client().leagueStandings({
        leagueId: league._id.toString(),
        season: season2,
      })

      // Assertions for season2
      assert(response2.leagueStandings.length === 1)
      const standing2 = response2.leagueStandings[0]?.groups?.[0]?.standings?.[0]
      assert(standing2?.season === season2)
      assert(standing2?.points === 8)
    })
  })
})
