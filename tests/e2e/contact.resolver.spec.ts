import assert from 'assert'
import sinon from 'sinon'

import { UserModel } from '../../src/modules/user/models'
import { AgencyModel } from '../../src/modules/user/models/agency'
import { Role } from '../../src/modules/user/models/user'
import { ContactType } from '../graphql/components'
import { client } from '../utils/graphql'
import { generateRandomAgency, generateRandomUser } from '../utils/users'

import { amplitudeTrackStub, sesClientStub, slackClientStub } from '.'

import { faker } from '@faker-js/faker'

describe('Contact resolver', () => {
  describe('sendContactMessage mutation', () => {
    it('should return error if user not found', async () => {
      const user = await generateRandomUser()
      const sender = await generateRandomUser()

      await UserModel.deleteOne({ _id: user.user._id })

      const response = await client().sendContactMessage({
        input: {
          email: sender.user.email,
          message: 'testing message',
          name: `${sender.user.firstname}`,
          userId: user.user._id,
        },
      })

      assert.strictEqual(response.sendContactMessage.success, false)
      assert.strictEqual(response.sendContactMessage.errors?.[0].message, 'Contact not found')
      assert.strictEqual(response.sendContactMessage.errors?.[0].path, 'sendContactMessage')
    })

    it('should send message and slack message if userid not filled contact mail', async () => {
      const user = await generateRandomUser()
      const sender = await generateRandomUser()
      const inputMessage = 'testing message for user'

      const response = await client(sender.tokens.accessToken).sendContactMessage({
        input: {
          email: sender.user.email,
          message: inputMessage,
          name: `${sender.user.name}`,
          userId: user.user._id,
        },
      })
      assert.strictEqual(response.sendContactMessage.success, true)
      assert.strictEqual(response.sendContactMessage.errors?.[0], undefined)

      sinon.assert.calledOnce(slackClientStub.chat.postMessage)
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `Message to ${user.user.name} from ${sender.user.name} (${sender.user.email}):
        "${inputMessage}"
        `
          )
        )
      )
      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [user.user.email]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'ContactMessageStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
      slackClientStub.chat.postMessage.resetHistory()
    })

    it('should throw GraphQL error if email service fails', async () => {
      sesClientStub.send = sinon.stub().throws('error')

      try {
        await client().sendContactMessage()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to send contact message. Please try again later.'
        )
      }
    })

    it('should send message to user contact if user have contact filled in and sender is not player', async () => {
      const user = await generateRandomUser()
      const randomEmail = faker.internet.email()
      const sender = await generateRandomUser({ role: Role.SPORTS_PROFESSIONAL })
      const inputMessage = 'testing message for user contact, if sender is not player'

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          contact: {
            firstname: 'Contact',
            lastname: 'Mother',
            email: randomEmail,
            phoneNumber: {
              countryPrefix: '424',
              number: '123456789',
            },
            type: ContactType.Mother,
          },
        },
      })

      const sendMessage = await client(sender.tokens.accessToken).sendContactMessage({
        input: {
          email: sender.user.email,
          message: inputMessage,
          name: `${sender.user.name}`,
          userId: user.user._id,
        },
      })

      assert.strictEqual(sendMessage.sendContactMessage.success, true)
      assert.strictEqual(sendMessage.sendContactMessage.errors?.[0], undefined)

      sinon.assert.calledOnce(slackClientStub.chat.postMessage)
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `Message to ${user.user.name} from ${sender.user.name} (${sender.user.email}):
        "${inputMessage}"
        `
          )
        )
      )
      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has(
            'Destination',
            sinon.match.has('ToAddresses', [response.updateUser.user?.contact?.email])
          )
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'ContactMessageStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
      slackClientStub.chat.postMessage.resetHistory()

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: user.user._id.toString() }
      )
    })

    it('should send message to user when contact email is filled and sender role is player', async () => {
      const user = await generateRandomUser()
      const randomEmail = faker.internet.email()
      const sender = await generateRandomUser()
      const inputMessage = 'testing message for player to player if have filled contact'

      await client(user.tokens.accessToken).updateUser({
        input: {
          contact: {
            firstname: 'Contact',
            lastname: 'Father',
            email: randomEmail,
            phoneNumber: {
              countryPrefix: '424',
              number: '123456789',
            },
            type: ContactType.Father,
          },
        },
      })

      const response = await client(sender.tokens.accessToken).sendContactMessage({
        input: {
          email: sender.user.email,
          message: inputMessage,
          name: `${sender.user.name}`,
          userId: user.user._id,
        },
      })

      assert.strictEqual(response.sendContactMessage.success, true)
      assert.strictEqual(response.sendContactMessage.errors?.[0], undefined)

      sinon.assert.calledOnce(slackClientStub.chat.postMessage)
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `Message to ${user.user.name} from ${sender.user.name} (${sender.user.email}):
        "${inputMessage}"
        `
          )
        )
      )
      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [user.user.email]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'ContactMessageStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
      slackClientStub.chat.postMessage.resetHistory()
      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: user.user._id.toString() }
      )
    })

    it("should send message to agency if agency id is provided and agency's email is filled", async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency email to agency')
      const inputMessage = 'test input message'

      await AgencyModel.findByIdAndUpdate(
        {
          _id: agency._id,
        },
        { email: '<EMAIL>' }
      )

      const response = await client(user.tokens.accessToken).sendContactMessage({
        input: {
          agencyId: agency.id,
          email: user.user.email,
          message: inputMessage,
          name: user.user.name,
        },
      })

      sinon.assert.calledOnce(slackClientStub.chat.postMessage)
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(`Message to ${agency.name} from ${user.user.name} (${user.user.email}):
        "${inputMessage}"
        `)
        )
      )

      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', ['<EMAIL>']))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'ContactMessageStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
      assert.strictEqual(response.sendContactMessage.success, true)
      assert.strictEqual(response.sendContactMessage.errors?.length, 0)

      slackClientStub.chat.postMessage.resetHistory()
    })

    it('should return error if agency dont have email', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency dont have email')
      const inputMessage = 'test input message'

      const response = await client(user.tokens.accessToken).sendContactMessage({
        input: {
          agencyId: agency.id,
          email: user.user.email,
          message: inputMessage,
          name: user.user.name,
        },
      })

      assert.strictEqual(response.sendContactMessage.success, false)
      assert.strictEqual(response.sendContactMessage.errors?.[0].message, 'Contact not found')
      assert.strictEqual(response.sendContactMessage.errors?.[0].path, 'sendContactMessage')
    })

    it('should return error if agency not found', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency not found')
      const inputMessage = 'test input message'

      await AgencyModel.findByIdAndDelete(agency._id)

      const response = await client(user.tokens.accessToken).sendContactMessage({
        input: {
          agencyId: agency.id,
          email: user.user.email,
          message: inputMessage,
          name: user.user.name,
        },
      })
      assert.strictEqual(response.sendContactMessage.success, false)
      assert.strictEqual(response.sendContactMessage.errors?.[0].message, 'Contact not found')
      assert.strictEqual(response.sendContactMessage.errors?.[0].path, 'sendContactMessage')
    })

    it('should return error if agencyId is not provided', async () => {
      const user = await generateRandomUser()
      const inputMessage = 'test input message'

      const response = await client(user.tokens.accessToken).sendContactMessage({
        input: {
          email: user.user.email,
          message: inputMessage,
          name: user.user.name,
        },
      })

      assert.strictEqual(response.sendContactMessage.success, false)
      assert.strictEqual(response.sendContactMessage.errors?.[0].message, 'Contact not found')
      assert.strictEqual(response.sendContactMessage.errors?.[0].path, 'sendContactMessage')
    })
  })
})
