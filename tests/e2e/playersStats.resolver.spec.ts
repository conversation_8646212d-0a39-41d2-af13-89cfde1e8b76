import assert from 'node:assert'

import { CURRENT_SEASON } from '../../src/helpers/league'
import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrl, DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { League, LeagueModel } from '../../src/modules/stats/models/league'
import { Organization, OrganizationModel } from '../../src/modules/stats/models/organization'
import { Team, TeamModel } from '../../src/modules/stats/models/team'
import {
  StatsSource,
  UserStatsModel,
  UserStatsPosition,
} from '../../src/modules/stats/models/userStats'
import { UserModel } from '../../src/modules/user/models'
import { SortOrder } from '../graphql/components'
import { client } from '../utils/graphql'
import { generateDiscoverUrl } from '../utils/stats'
import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
  GenerateRandomUserResponse,
} from '../utils/users'

import { DocumentType } from '@typegoose/typegoose'

const generateRandomUsers = async (
  num = 10,
  discoveredUrl: DocumentType<DiscoveredUrl>,
  userExternalIds: string[]
): Promise<GenerateRandomUserResponse[]> => {
  return Promise.all(
    Array(num)
      .fill(null)
      .map((_, index) =>
        generateRandomUser({
          externalConnections: [
            {
              externalId: userExternalIds[index],
              crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
            },
          ],
        })
      )
  )
}

const generateRandomOrganizations = async (num = 10): Promise<DocumentType<Organization>[]> => {
  return Promise.all(
    Array(num)
      .fill(null)
      .map((_, index) => generateRandomOrganization(`Test PlayersStats Org${index}`))
  )
}
const generateRandomLeagues = async (num = 10): Promise<DocumentType<League>[]> => {
  return Promise.all(
    Array(num)
      .fill(null)
      .map((_, index) => generateRandomLeague(`Test Aggregation League${index}`))
  )
}
const generateRandomTeams = async (
  organizations: DocumentType<Organization>[]
): Promise<DocumentType<Team>[]> => {
  return Promise.all(
    Array(organizations.length)
      .fill(null)
      .map((_, index) =>
        generateRandomTeam({
          name: `Test Aggregation Team${index}`,
          organization: organizations[index].id,
        })
      )
  )
}

describe("PlayersStats are UserStatsModel queries for client's Stats screens in Team Detail and League Detail", () => {
  beforeEach(async () => {
    await UserStatsModel.deleteMany({})
    await UserModel.deleteMany({})
    await TeamModel.deleteMany({})
    await OrganizationModel.deleteMany({})
    await LeagueModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
  })

  it('should correctly query based on teamId or leagueId, season and stage', async () => {
    // Create test data

    const randomAppUsers = await generateRandomUser()

    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(
      10,
      discoveredUrl,
      Array(10)
        .fill(null)
        .map((_, index) => `${100000 + index}`)
    )

    const leagues = await generateRandomLeagues(4)

    const organizations = await generateRandomOrganizations(2)
    const teams = await generateRandomTeams(organizations)

    let iteration = 0

    let usedTeams: DocumentType<Team>[] = []
    let usedLeagues: DocumentType<League>[] = []

    for (const randomUserResponse of randomUsers) {
      const league = leagues[Math.floor(Math.random() * leagues.length)]
      const team = teams[Math.floor(Math.random() * teams.length)]

      usedTeams.push(team)
      usedLeagues.push(league)

      usedTeams = [...new Set(usedTeams)]
      usedLeagues = [...new Set(usedLeagues)]

      await UserStatsModel.create({
        // this is to document that user is not used anymore, it is not obvious from the use of userStats alone
        //user: randomUserResponse.user._id,
        userExternalId: randomUserResponse.user.externalConnections[0].externalId,
        team: team._id,
        league: league._id,
        season: CURRENT_SEASON,
        gamesPlayed: 3 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        source: StatsSource.SCRAPER,
        externalInfo: {
          stage: { value: 'Regular Season', label: 'Regular Season' },
        },
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
      })

      await UserStatsModel.create({
        // this is to document that user is not used anymore, it is not obvious from the use of userStats alone
        //user: randomUserResponse.user._id,
        userExternalId: randomUserResponse.user.externalConnections[0].externalId,
        team: team._id,
        league: league._id,
        season: CURRENT_SEASON,
        gamesPlayed: 2 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        source: StatsSource.SCRAPER,
        externalInfo: {
          stage: { value: 'Playoffs', label: 'Playoffs' },
        },
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
      })

      iteration += 1
    }

    const response0 = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        leagueId: usedLeagues[0].id.toString(),
        season: CURRENT_SEASON,
        stage: 'Playoffs',
      },
    })

    assert.strictEqual(response0.playersStatsList.edges[0].node.league?.name, usedLeagues[0].name)
    assert.strictEqual(
      response0.playersStatsList.edges[0].node.externalInfo?.stage?.value,
      'Playoffs'
    )

    const response1 = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        leagueId: usedLeagues[1].id.toString(),
        season: CURRENT_SEASON,
        stage: 'Regular Season',
      },
    })

    assert.strictEqual(response1.playersStatsList.edges[0].node.league?.name, usedLeagues[1].name)
    assert.strictEqual(
      response1.playersStatsList.edges[0].node.externalInfo?.stage?.value,
      'Regular Season'
    )

    const response2 = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        leagueId: usedLeagues[0].id.toString(),
        teamId: usedTeams[0].id.toString(),
        season: CURRENT_SEASON,
        stage: 'Playoffs',
      },
    })

    assert.strictEqual(response2.playersStatsList.edges[0].node.team?.name, usedTeams[0].name)
    assert.strictEqual(
      response2.playersStatsList.edges[0].node.externalInfo?.stage?.value,
      'Playoffs'
    )
  })

  it('should correctly query player/users stats from the same season, team, and league', async () => {
    // Create test data

    const randomAppUsers = await generateRandomUser()

    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(
      10,
      discoveredUrl,
      Array(10)
        .fill(null)
        .map((_, index) => `${100000 + index}`)
    )

    const leagues = await generateRandomLeagues(1)

    const organizations = await generateRandomOrganizations(1)
    const teams = await generateRandomTeams(organizations)

    let iteration = 0

    for (const randomUserResponse of randomUsers) {
      await UserStatsModel.create({
        // this is to document that user is not used anymore, it is not obvious from the use of userStats alone
        //user: randomUserResponse.user._id,
        userExternalId: randomUserResponse.user.externalConnections[0].externalId,
        team: teams[0]._id,
        league: leagues[0]._id,
        season: CURRENT_SEASON,
        gamesPlayed: 3 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        ga: iteration,
        gaa: iteration,
        saves: iteration,
        source: StatsSource.SCRAPER,

        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
      })

      iteration += 1
    }

    const response = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        leagueId: leagues[0].id.toString(),
        season: CURRENT_SEASON,
        stage: 'all',
      },
    })

    assert.strictEqual(response.playersStatsList.edges.length, randomUsers.length)
    assert.strictEqual(response.playersStatsList.edges[0].node.league?.name, leagues[0].name)
    assert.strictEqual(response.playersStatsList.edges[0].node.team?.name, teams[0].name)

    const response4 = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        teamId: teams[0].id.toString(),
        leagueId: leagues[0].id.toString(),
        season: CURRENT_SEASON,
      },
    })

    assert.strictEqual(response4.playersStatsList.edges.length, randomUsers.length)
    assert.strictEqual(response4.playersStatsList.edges[0].node.league?.name, leagues[0].name)
    assert.strictEqual(response4.playersStatsList.edges[0].node.team?.name, teams[0].name)

    const response5 = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        teamId: teams[0].id.toString(),
        leagueId: leagues[0].id.toString(),
        season: CURRENT_SEASON,
        stage: 'all',
      },
    })

    assert.strictEqual(response5.playersStatsList.edges.length, randomUsers.length)
    assert.strictEqual(response5.playersStatsList.edges[0].node.league?.name, leagues[0].name)
    assert.strictEqual(response5.playersStatsList.edges[0].node.team?.name, teams[0].name)
    assert.strictEqual(
      response5.playersStatsList.edges[0].node.user?.id,
      randomUsers[0].user._id.toString()
    )

    // sorting

    const response6 = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        teamId: teams[0].id.toString(),
        leagueId: leagues[0].id.toString(),
        season: CURRENT_SEASON,
        // stage: 'all', same as stage: null adn stage: ''
        stage: '',
      },
      sort: {
        fields: [
          {
            field: 'goals',
            order: SortOrder.Desc,
          },
        ],
      },
    })

    assert.strictEqual(response6.playersStatsList.edges.length, randomUsers.length)
    assert.strictEqual(response6.playersStatsList.edges[0].node.league?.name, leagues[0].name)
    assert.strictEqual(response6.playersStatsList.edges[0].node.team?.name, teams[0].name)
    assert.strictEqual(
      response6.playersStatsList.edges[0].node.user?.id,
      randomUsers[randomUsers.length - 1].user._id.toString()
    )

    assert.strictEqual(response6.playersStatsList.edges[0].node.gamesPlayed, 12)
  })

  it('should correctly query based on leagueId, position', async () => {
    // Create test data

    const randomAppUsers = await generateRandomUser()

    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(
      10,
      discoveredUrl,
      Array(10)
        .fill(null)
        .map((_, index) => `${100000 + index}`)
    )

    const leagues = await generateRandomLeagues(1)
    const organizations = await generateRandomOrganizations(1)
    const teams = await generateRandomTeams(organizations)

    let iteration = 0

    for (const randomUserResponse of randomUsers) {
      await UserStatsModel.create({
        userExternalId: randomUserResponse.user.externalConnections[0].externalId,
        team: teams[0]._id,
        league: leagues[0]._id,
        season: CURRENT_SEASON,
        gamesPlayed: 3 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        source: StatsSource.SCRAPER,
        externalInfo: {
          stage: { value: 'Regular Season', label: 'Regular Season' },
          externalPlayerName: randomUserResponse.user.name,
        },
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
        position: UserStatsPosition.Skater,
      })

      await UserStatsModel.create({
        userExternalId: randomUserResponse.user.externalConnections[0].externalId,
        team: teams[0]._id,
        league: leagues[0]._id,
        season: CURRENT_SEASON,
        gamesPlayed: 2 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        source: StatsSource.SCRAPER,
        externalInfo: {
          stage: { value: 'Regular Season', label: 'Regular Season' },
          externalPlayerName: randomUserResponse.user.name,
        },
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
        position: UserStatsPosition.Goalie,
      })

      iteration += 1
    }

    const responseAll = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        leagueId: leagues[0].id.toString(),
        season: CURRENT_SEASON,
        stage: 'Regular Season',
      },
      first: 20,
    })

    assert.strictEqual(responseAll.playersStatsList.edges.length, randomUsers.length * 2)

    const responseSkaters = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        leagueId: leagues[0].id.toString(),
        season: CURRENT_SEASON,
        stage: 'Regular Season',
        position: UserStatsPosition.Skater,
      },
      first: 20,
    })

    assert.strictEqual(responseSkaters.playersStatsList.edges.length, randomUsers.length)

    assert.strictEqual(
      responseSkaters.playersStatsList.edges[0].node.externalInfo?.externalPlayerName,
      randomUsers[0].user.name
    )

    assert.strictEqual(
      responseSkaters.playersStatsList.edges[0].node.position,
      UserStatsPosition.Skater
    )

    const responseGoalies = await client(randomAppUsers.tokens.accessToken).playersStatsList({
      filter: {
        leagueId: leagues[0].id.toString(),
        season: CURRENT_SEASON,
        stage: 'Regular Season',
        position: UserStatsPosition.Goalie,
      },
      first: 20,
    })

    assert.strictEqual(responseGoalies.playersStatsList.edges.length, randomUsers.length)

    assert.strictEqual(
      responseGoalies.playersStatsList.edges[0].node.externalInfo?.externalPlayerName,
      randomUsers[0].user.name
    )

    assert.strictEqual(
      responseGoalies.playersStatsList.edges[0].node.position,
      UserStatsPosition.Goalie
    )
  })

  it('should fail on input validation', async () => {
    // Create test data

    const randomAppUsers = await generateRandomUser()

    const discoveredUrl = await generateDiscoverUrl()

    const randomUsers = await generateRandomUsers(
      10,
      discoveredUrl,
      Array(10)
        .fill(null)
        .map((_, index) => `${100000 + index}`)
    )

    const leagues = await generateRandomLeagues(1)

    const organizations = await generateRandomOrganizations(1)
    const teams = await generateRandomTeams(organizations)

    let iteration = 0

    for (const randomUserResponse of randomUsers) {
      await UserStatsModel.create({
        // this is to document that user is not used anymore, it is not obvious from the use of userStats alone
        //user: randomUserResponse.user._id,
        userExternalId: randomUserResponse.user.externalConnections[0].externalId,
        team: teams[0]._id,
        league: leagues[0]._id,
        season: CURRENT_SEASON,
        gamesPlayed: 3 + iteration,
        goals: iteration,
        assists: iteration,
        pim: iteration,
        plusMinus: iteration,
        ga: iteration,
        gaa: iteration,
        saves: iteration,
        source: StatsSource.SCRAPER,

        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
      })

      iteration += 1
    }

    try {
      await client(randomAppUsers.tokens.accessToken).playersStatsList({
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        filter: {
          season: CURRENT_SEASON,
          stage: 'all',
        },
      })
    } catch (error) {
      assert.strictEqual(
        error.response.errors[0].message,
        'Variable "$filter" got invalid value { season: "2024-2025", stage: "all" }; Field "leagueId" of required type "ObjectId!" was not provided.'
      )
    }
  })
})
