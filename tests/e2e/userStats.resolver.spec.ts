import assert from 'assert'
import axios from 'axios'
import sinon from 'sinon'

import { slugifyString } from '../../src/helpers/string'
import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { League, LeagueModel } from '../../src/modules/stats/models/league'
import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { Team, TeamModel } from '../../src/modules/stats/models/team'
import { StatsSource, UserStatsModel } from '../../src/modules/stats/models/userStats'
import { UserModel } from '../../src/modules/user/models'
import { User } from '../../src/modules/user/models/user'
import { client } from '../utils/graphql'
import { generateDiscoverUrl } from '../utils/stats'
import {
  generateRandomAdminUser,
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
  userStatsQuery,
} from '../utils/users'

import { s3ClientStub } from '.'

// Helper function for testing approximate equality of floating point values
function assertApproximately(actual: number, expected: number, delta: number, message?: string) {
  const pass = Math.abs(actual - expected) <= delta
  assert.ok(pass, message || `Expected ${actual} to be approximately ${expected} +/- ${delta}`)
}

describe('UserStats resolver', () => {
  describe('userStats query', () => {
    beforeEach(async () => {
      await UserStatsModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
      await CrawlerDomainConfigModel.deleteMany({})
    })

    it('should return user stats by id and slug', async () => {
      const randomFirstUser = await generateRandomUser()
      const organization = await generateRandomOrganization()
      const league = await generateRandomLeague()
      const team = await generateRandomTeam({ name: 'Test team', organization: organization.id })

      randomFirstUser.user = (await UserModel.findOneAndUpdate(
        {
          _id: randomFirstUser.user._id,
        },
        {
          slug: slugifyString(randomFirstUser.user.firstname + ' ' + randomFirstUser.user.lastname),
        },
        { new: true }
      )) as User

      await UserStatsModel.create({
        assists: 10,
        wins: 10,
        goals: 15,
        league: league._id,
        losses: 1,
        orderInSeason: 1,
        season: '2020',
        shutouts: 2,
        svp: 1,
        team: team.id,
        ties: 0,
        user: randomFirstUser.user._id,
        gamesPlayed: 11,
        gaa: 1,
      })

      await UserStatsModel.create({
        assists: 15,
        wins: 15,
        goals: 20,
        league: league._id,
        losses: 10,
        orderInSeason: 2,
        season: '2024',
        shutouts: 0,
        svp: 0,
        team: team.id,
        ties: 1,
        user: randomFirstUser.user._id,
        gamesPlayed: 26,
        gaa: 0,
      })

      const firstPageResponse = await client(randomFirstUser.tokens.accessToken).userStats({
        filter: {
          user: randomFirstUser.user._id.toString(),
        },
        pagination: {
          first: 1,
        },
      })

      const firstPageResponseSlug = await client().userStats({
        filter: {
          slug: randomFirstUser.user.slug,
        },
        pagination: {
          first: 1,
        },
      })

      assert.strictEqual(firstPageResponse.userStats.edges[0].node.league?.name, 'Test league')
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.team?.name, 'Test team')
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.goals, 15)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.assists, 10)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.wins, 10)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.losses, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.ties, 0)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.gamesPlayed, 11)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.gaa, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.svp, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.shutouts, 2)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.orderInSeason, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.season, '2020')
      assert.strictEqual(
        firstPageResponse.userStats.edges[0].node.user?.id,
        randomFirstUser.user._id.toString()
      )
      assert.strictEqual(firstPageResponse.userStats.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPageResponse.userStats.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPageResponse.userStats.edges.length, 1)
      assert.strictEqual(firstPageResponse.userStats.pageInfo.totalCount, 2)

      assert.strictEqual(firstPageResponseSlug.userStats.edges[0].node.league?.name, 'Test league')

      const secondPageResponse = await client(randomFirstUser.tokens.accessToken).userStats({
        filter: {
          user: randomFirstUser.user._id.toString(),
        },
        pagination: {
          first: 1,
          after: firstPageResponse.userStats.pageInfo.endCursor,
        },
      })

      const secondPageResponseSlug = await client().userStats({
        filter: {
          slug: randomFirstUser.user.slug,
        },
        pagination: {
          first: 1,
          after: firstPageResponseSlug.userStats.pageInfo.endCursor,
        },
      })

      assert.strictEqual(secondPageResponse.userStats.edges[0].node.league?.name, 'Test league')
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.team?.name, 'Test team')
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.goals, 20)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.assists, 15)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.wins, 15)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.losses, 10)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.ties, 1)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.gamesPlayed, 26)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.gaa, 0)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.svp, 0)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.shutouts, 0)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.orderInSeason, 2)
      assert.strictEqual(secondPageResponse.userStats.edges[0].node.season, '2024')
      assert.strictEqual(
        secondPageResponse.userStats.edges[0].node.user?.id,
        randomFirstUser.user._id.toString()
      )
      assert.strictEqual(secondPageResponse.userStats.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPageResponse.userStats.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPageResponse.userStats.edges.length, 1)

      assert.strictEqual(secondPageResponseSlug.userStats.edges[0].node.league?.name, 'Test league')
      assert.strictEqual(secondPageResponseSlug.userStats.edges.length, 1)
    })

    it('should return user stats without id filter and default pagination  ', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()
      const organization = await generateRandomOrganization('Test User Stats Query organization')
      const league = await generateRandomLeague('Test User Stats Query League')
      const team = await generateRandomTeam({
        name: 'Test User Stats Query team',
        organization: organization.id,
      })

      const userStatss = []
      for (let i = 0; i < 81; i++) {
        const userStats = await client(admin.tokens.accessToken).createUserStats({
          input: {
            assists: 1 + i,
            wins: 1 + i,
            goals: 1 + i,
            league: league._id,
            losses: 1 + i,
            orderInSeason: 1 + i,
            season: (1940 + i).toString(),
            shutouts: 1 + i,
            svp: 1 + i,
            team: team.id,
            ties: 1 + i,
            user: randomUser.user._id,
            gamesPlayed: 1 + i,
            gaa: 1 + i,
          },
        })
        userStatss.push(userStats)
      }

      const firstPageResponse = await client(randomUser.tokens.accessToken).userStats({})
      assert.strictEqual(firstPageResponse.userStats.edges.length, 80)
      assert.strictEqual(firstPageResponse.userStats.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPageResponse.userStats.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPageResponse.userStats.pageInfo.totalCount, 81)

      assert.strictEqual(firstPageResponse.userStats.edges[0].node.assists, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.wins, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.goals, 1)
      assert.strictEqual(
        firstPageResponse.userStats.edges[0].node.league?.name,
        'Test User Stats Query League'
      )
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.losses, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.orderInSeason, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.season, '1940')
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.shutouts, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.svp, 1)
      assert.strictEqual(
        firstPageResponse.userStats.edges[0].node.team?.name,
        'Test User Stats Query team'
      )
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.ties, 1)
      assert.strictEqual(
        firstPageResponse.userStats.edges[0].node.user?.id,
        randomUser.user._id.toString()
      )
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.gamesPlayed, 1)
      assert.strictEqual(firstPageResponse.userStats.edges[0].node.gaa, 1)

      const secondPageResponse = await client(randomUser.tokens.accessToken).userStats({
        pagination: {
          first: 1,
          after: firstPageResponse.userStats.pageInfo.endCursor,
        },
      })

      assert.strictEqual(secondPageResponse.userStats.edges.length, 1)
      assert.strictEqual(secondPageResponse.userStats.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPageResponse.userStats.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPageResponse.userStats.pageInfo.totalCount, 81)
    })

    it('should return user stats by external connection', async () => {
      const userExternalId = '123456'
      const discoveredUrl = await generateDiscoverUrl()

      const randomUser = await generateRandomUser({
        externalConnections: [
          {
            externalId: userExternalId,
            crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
          },
        ],
      })

      await UserStatsModel.create({
        gamesPlayed: 10,
        goals: 15,
        assists: 10,
        points: 25,
        pim: 10,
        plusMinus: 5,
        season: '2021',
        seasonType: 'REGULAR',
        userExternalId,
        crawlerDomainConfig: discoveredUrl.crawlerDomainConfig.id,
      })

      const response = await client(randomUser.tokens.accessToken).userStats()

      assert.strictEqual(response.userStats.edges[0].node.goals, 15)
    })
  })

  describe('createUserStats mutation', () => {
    it('should return response with created user stats', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()
      const organization = await generateRandomOrganization('Test Create User Stats organization')
      const league = await generateRandomLeague('Test Create User Stats League')
      const team = await generateRandomTeam({
        name: 'Test Create User Stats team',
        organization: organization.id,
      })

      const response = await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 11,
          wins: 11,
          goals: 9,
          league: league._id,
          losses: 2,
          orderInSeason: 1,
          season: '2011',
          shutouts: 7,
          svp: 1,
          team: team.id,
          ties: 1,
          user: randomUser.user._id,
          gamesPlayed: 10,
          gaa: 2,
        },
      })

      assert.strictEqual(
        response.createUserStats.userStats?.league?.name,
        'Test Create User Stats League'
      )
      assert.strictEqual(
        response.createUserStats.userStats?.team?.name,
        'Test Create User Stats team'
      )
      assert.strictEqual(response.createUserStats.userStats?.goals, 9)
      assert.strictEqual(response.createUserStats.userStats?.assists, 11)
      assert.strictEqual(response.createUserStats.userStats?.wins, 11)
      assert.strictEqual(response.createUserStats.userStats?.losses, 2)
      assert.strictEqual(response.createUserStats.userStats?.ties, 1)
      assert.strictEqual(response.createUserStats.userStats?.gamesPlayed, 10)
      assert.strictEqual(response.createUserStats.userStats?.gaa, 2)
      assert.strictEqual(response.createUserStats.userStats?.svp, 1)
      assert.strictEqual(response.createUserStats.userStats?.shutouts, 7)
      assert.strictEqual(response.createUserStats.userStats?.orderInSeason, 1)
      assert.strictEqual(response.createUserStats.userStats?.season, '2011')
      assert.strictEqual(
        response.createUserStats.userStats?.user?.id,
        randomUser.user._id.toString()
      )
    })

    it('should return error when user stats already exists', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const organization = await generateRandomOrganization(
        'Test Create User Stats Exist organization'
      )
      const league = await generateRandomLeague('Test Create User Stats Exist League')

      const team = await generateRandomTeam({
        name: 'Test Create User Stats Exist team',
        organization: organization.id,
      })

      await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 1,
          wins: 2,
          goals: 1,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 1,
          team: team.id,
          ties: 2,
          user: randomUser.user._id,
          gamesPlayed: 3,
          gaa: 1,
        },
      })

      const response = await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 1,
          wins: 2,
          goals: 1,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 1,
          team: team.id,
          ties: 2,
          user: randomUser.user._id,
          gamesPlayed: 3,
          gaa: 1,
        },
      })

      assert.strictEqual(response.createUserStats.errors?.length, 1)
      assert.strictEqual(response.createUserStats.errors?.[0].message, 'UserStats already exists')
    })
  })

  describe('deleteUserStats mutation', () => {
    it('should return true when user stats is deleted', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const organization = await generateRandomOrganization('Test Delete User Stats organization')
      const league = await generateRandomLeague('Test Delete User Stats League')

      const team = await generateRandomTeam({
        name: 'Test Delete User Stats team',
        organization: organization.id,
      })

      const userStats = await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 1,
          wins: 2,
          goals: 1,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 1,
          team: team.id,
          ties: 2,
          user: randomUser.user._id,
          gamesPlayed: 3,
          gaa: 1,
        },
      })

      const response = await client(admin.tokens.accessToken).deleteUserStats({
        deleteUserStatsId: userStats.createUserStats.userStats?.id,
      })

      const check = await UserStatsModel.findById({
        _id: userStats.createUserStats.userStats?.id,
      })

      assert.strictEqual(check, null)
      assert.strictEqual(response.deleteUserStats, true)
    })

    it('should return false when failed to delete user stats', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const organization = await generateRandomOrganization(
        'Test Delete User Stats Error organization'
      )
      const league = await generateRandomLeague('Test Delete User Stats Error League')

      const team = await generateRandomTeam({
        name: 'Test Delete User Stats Error team',
        organization: organization.id,
      })

      const findByIdAndDeleteStub = sinon.stub(UserStatsModel, 'findByIdAndDelete').throws()

      await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 1,
          wins: 2,
          goals: 1,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 1,
          team: team.id,
          ties: 2,
          user: randomUser.user._id,
          gamesPlayed: 3,
          gaa: 1,
        },
      })

      const response = await client(admin.tokens.accessToken).deleteUserStats({
        deleteUserStatsId: randomUser.user._id,
      })

      assert.strictEqual(response.deleteUserStats, false)

      findByIdAndDeleteStub.restore()
    })
  })

  describe('updateUserStats mutation', () => {
    it('should return response with updated user stats', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const organization = await generateRandomOrganization('Test Update User Stats organization')
      const league = await generateRandomLeague('Test Update User Stats League')

      const team = await generateRandomTeam({
        name: 'Test Update User Stats team',
        organization: organization.id,
      })

      const userStats = await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 1,
          wins: 2,
          goals: 1,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 1,
          team: team.id,
          ties: 2,
          user: randomUser.user._id,
          gamesPlayed: 3,
          gaa: 1,
        },
      })

      assert.strictEqual(userStats.createUserStats.userStats?.goals, 1)
      assert.strictEqual(userStats.createUserStats.userStats?.assists, 1)
      assert.strictEqual(userStats.createUserStats.userStats?.wins, 2)
      assert.strictEqual(userStats.createUserStats.userStats?.losses, 10)
      assert.strictEqual(userStats.createUserStats.userStats?.ties, 2)
      assert.strictEqual(userStats.createUserStats.userStats?.gamesPlayed, 3)
      assert.strictEqual(userStats.createUserStats.userStats?.gaa, 1)
      assert.strictEqual(userStats.createUserStats.userStats?.svp, 1)
      assert.strictEqual(userStats.createUserStats.userStats?.shutouts, 0)
      assert.strictEqual(userStats.createUserStats.userStats?.orderInSeason, 10)
      assert.strictEqual(userStats.createUserStats.userStats?.season, '2023')
      assert.strictEqual(
        userStats.createUserStats.userStats?.league?.name,
        'Test Update User Stats League'
      )
      assert.strictEqual(
        userStats.createUserStats.userStats?.team?.name,
        'Test Update User Stats team'
      )

      const newLeague = await generateRandomLeague('Test Update User Stats New League')
      const newOrganization = await generateRandomOrganization(
        'Test Update User Stats New Organization'
      )
      const newTeam = await generateRandomTeam({
        name: 'Test Update User Stats New team',
        organization: newOrganization.id,
      })

      const response = await client(admin.tokens.accessToken).updateUserStats({
        input: {
          assists: 10,
          gaa: 0,
          gamesPlayed: 10,
          goals: 10,
          league: newLeague._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 0,
          team: newTeam.id,
          ties: 10,
          wins: 10,
        },
        updateUserStatsId: userStats.createUserStats.userStats?.id,
      })

      assert.strictEqual(response.updateUserStats.userStats?.goals, 10)
      assert.strictEqual(response.updateUserStats.userStats?.assists, 10)
      assert.strictEqual(response.updateUserStats.userStats?.wins, 10)
      assert.strictEqual(response.updateUserStats.userStats?.losses, 10)
      assert.strictEqual(response.updateUserStats.userStats?.ties, 10)
      assert.strictEqual(response.updateUserStats.userStats?.gamesPlayed, 10)
      assert.strictEqual(response.updateUserStats.userStats?.gaa, 0)
      assert.strictEqual(response.updateUserStats.userStats?.svp, 0)
      assert.strictEqual(response.updateUserStats.userStats?.shutouts, 0)
      assert.strictEqual(response.updateUserStats.userStats?.orderInSeason, 10)
      assert.strictEqual(response.updateUserStats.userStats?.season, '2023')
      assert.strictEqual(response.updateUserStats.errors?.length, undefined)
      assert.strictEqual(
        response.updateUserStats.userStats?.league?.name,
        'Test Update User Stats New League'
      )
      assert.strictEqual(
        response.updateUserStats.userStats.team?.name,
        'Test Update User Stats New team'
      )
    })

    it('should return error when failed to update user stats', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const organization = await generateRandomOrganization(
        'Test Update User Stats Error organization'
      )
      const league = await generateRandomLeague('Test Update User Stats Error League')

      const team = await generateRandomTeam({
        name: 'Test Update User Stats Error team',
        organization: organization.id,
      })

      const userStats = await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 1,
          wins: 2,
          goals: 1,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 1,
          team: team.id,
          ties: 2,
          user: randomUser.user._id,
          gamesPlayed: 3,
          gaa: 1,
        },
      })

      const findByIdAndUpdateStub = sinon.stub(UserStatsModel, 'findByIdAndUpdate').throws()

      const response = await client(admin.tokens.accessToken).updateUserStats({
        input: {
          assists: 10,
          gaa: 0,
          gamesPlayed: 10,
          goals: 10,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 0,
          team: team.id,
          ties: 10,
          wins: 10,
        },
        updateUserStatsId: userStats.createUserStats.userStats?.id,
      })

      assert.strictEqual(response.updateUserStats.errors?.length, 1)
      assert.strictEqual(response.updateUserStats.errors?.[0].message, 'UserStats not found')

      findByIdAndUpdateStub.restore()
    })

    it('should update all user stats with same external team name and league name and missing team/league', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const league = await generateRandomLeague('Test league stats')

      const team = await generateRandomTeam({ name: 'Test team stats' })

      const userStats = await client(admin.tokens.accessToken).createUserStats({
        input: {
          gamesPlayed: 3,
          assists: 1,
          goals: 1,
          user: randomUser.user._id,
          season: '2023',
        },
      })

      await client(admin.tokens.accessToken).createUserStats({
        input: {
          gamesPlayed: 2,
          assists: 1,
          goals: 1,
          season: '2022',
          user: randomUser.user._id,
        },
      })

      await client(admin.tokens.accessToken).updateUserStats({
        input: {
          team: team.id,
          league: league.id,
        },
        updateUserStatsId: userStats.createUserStats.userStats?.id,
      })

      // now all other stats should have team and league
      const updatedUserStats = await UserStatsModel.find({ user: randomUser.user._id })

      assert.strictEqual(updatedUserStats.length, 2)
      assert.strictEqual(updatedUserStats[0].team?.toString(), team.id)
      assert.strictEqual(updatedUserStats[0].league?.toString(), league.id)

      assert.strictEqual(updatedUserStats[1].team?.toString(), team.id)
      assert.strictEqual(updatedUserStats[1].league?.toString(), league.id)
    })
  })

  describe('syncUserStats mutation', () => {
    it('should return false if sync failed', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      await UserModel.findByIdAndUpdate(randomUser.user._id, {
        externalLinks: {
          name: 'EliteProspects',
          url: 'https://www.eliteprospects.com/player/123321',
          type: 'ELITE_PROSPECTS',
        },
      })

      const axiosStub = sinon.stub(axios, 'post').rejects(new Error('Failed to get data'))

      const response = await client(admin.tokens.accessToken).syncUserStats({
        userId: randomUser.user._id.toString(),
      })

      assert.strictEqual(response.syncUserStats, false)

      axiosStub.restore()
    })

    it('should return true if sync is successful', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      await UserModel.findByIdAndUpdate(randomUser.user._id, {
        externalLinks: {
          name: 'EliteProspects',
          url: 'https://www.eliteprospects.com/player/123321',
          type: 'ELITE_PROSPECTS',
        },
      })

      const axiosPostStub = sinon.stub(axios, 'post').resolves(userStatsQuery('team-slug-123'))

      s3ClientStub.send.resolves({})

      const response = await client(admin.tokens.accessToken).syncUserStats({
        userId: randomUser.user._id.toString(),
      })

      assert.strictEqual(response.syncUserStats, true)

      sinon.assert.calledOnce(axiosPostStub)
      sinon.assert.calledWith(axiosPostStub, 'https://gql.eliteprospects.com', {
        query:
          '\n' +
          '  query playerStats {\n' +
          '    playerStats (player: "player", sort: "season") {\n' +
          '      edges {\n' +
          '        id\n' +
          '        leagueName\n' +
          '        leagueType\n' +
          '        postseasonType\n' +
          '        league {\n' +
          '            slug\n' +
          '            name\n' +
          '            country {\n' +
          '                iso_3166_1_alpha_2\n' +
          '            }\n' +
          '        }\n' +
          '        team {\n' +
          '            slug\n' +
          '            name\n' +
          '            country {\n' +
          '                iso_3166_1_alpha_2\n' +
          '            }\n' +
          '            logo {\n' +
          '                large\n' +
          '            }\n' +
          '        }\n' +
          '        season {\n' +
          '            slug\n' +
          '            startYear\n' +
          '            endYear\n' +
          '        }\n' +
          '        regularStats {\n' +
          '            GP\n' +
          '            G\n' +
          '            A\n' +
          '            PIM\n' +
          '            PM\n' +
          '\n' +
          '            W\n' +
          '            L\n' +
          '            T\n' +
          '            GAA\n' +
          '            SVP\n' +
          '            SO\n' +
          '        }\n' +
          '        postseasonStats {\n' +
          '          GP\n' +
          '          G\n' +
          '          A\n' +
          '          PIM\n' +
          '          PM\n' +
          '\n' +
          '          W\n' +
          '          L\n' +
          '          T\n' +
          '          GAA\n' +
          '          SVP\n' +
          '          SO\n' +
          '        }\n' +
          '      }\n' +
          '    }\n' +
          '  }\n' +
          '  ',
        variables: { playerId: 'player' },
      })

      // check userStats model in db
      const userStats = await UserStatsModel.findOne({
        user: randomUser.user._id,
      })

      assert.strictEqual(userStats?.gamesPlayed, 82)
      assert.strictEqual(userStats?.goals, 30)
      assert.strictEqual(userStats?.assists, 45)
      assert.strictEqual(userStats?.pim, 25)
      assert.strictEqual(userStats?.plusMinus, 20)

      assert.strictEqual(userStats?.postSeasonStats?.gamesPlayed, 20)
      assert.strictEqual(userStats?.postSeasonStats?.goals, 10)
      assert.strictEqual(userStats?.postSeasonStats?.assists, 15)
      assert.strictEqual(userStats?.postSeasonStats?.pim, 5)
      assert.strictEqual(userStats?.postSeasonStats?.plusMinus, 10)

      axiosPostStub.restore()
    })

    it('should sync stats which already exists and only update them', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      await UserModel.findByIdAndUpdate(randomUser.user._id, {
        externalLinks: {
          name: 'EliteProspects',
          url: 'https://www.eliteprospects.com/player/123321',
          type: 'ELITE_PROSPECTS',
        },
      })

      const league = await generateRandomLeague(
        'Swiss National League',
        'CH',
        'swiss-national-league'
      )
      await generateRandomTeam({ name: 'HC Davos', leagues: [league.id] })

      const mockData = userStatsQuery('team-slug-123')
      mockData.data.data.playerStats.edges[0].team.name = 'HC Davos'
      mockData.data.data.playerStats.edges[0].league.name = 'Swiss National League'
      mockData.data.data.playerStats.edges[0].league.country.iso_3166_1_alpha_2 = 'CH'

      let axiosPostStub = sinon.stub(axios, 'post').resolves(mockData)

      await client(admin.tokens.accessToken).syncUserStats({
        userId: randomUser.user._id.toString(),
      })

      const userStats = await UserStatsModel.findOne({
        user: randomUser.user._id,
      }).populate(['team', 'league'])

      assert.strictEqual(userStats?.gamesPlayed, 82)
      assert.strictEqual(userStats?.goals, 30)
      assert.strictEqual(userStats?.assists, 45)
      assert.strictEqual(userStats?.pim, 25)
      assert.strictEqual(userStats?.plusMinus, 20)

      assert.strictEqual(userStats?.postSeasonStats?.gamesPlayed, 20)
      assert.strictEqual(userStats?.postSeasonStats?.goals, 10)
      assert.strictEqual(userStats?.postSeasonStats?.assists, 15)
      assert.strictEqual(userStats?.postSeasonStats?.pim, 5)
      assert.strictEqual(userStats?.postSeasonStats?.plusMinus, 10)
      assert.strictEqual((userStats?.team as Team)?.name, 'HC Davos')
      assert.strictEqual((userStats?.league as League)?.name, 'Swiss National League')

      const oldStats = userStatsQuery('team-slug-123')
      oldStats.data.data.playerStats.edges[0].regularStats!.GP = 83
      oldStats.data.data.playerStats.edges[0].regularStats!.G = 32
      oldStats.data.data.playerStats.edges[0].regularStats!.A = 47

      axiosPostStub.restore()
      axiosPostStub = sinon.stub(axios, 'post').resolves(oldStats)

      await client(admin.tokens.accessToken).syncUserStats({
        userId: randomUser.user._id.toString(),
      })

      const userStatsAfterSync = await UserStatsModel.findOne({
        user: randomUser.user._id,
      })

      assert.strictEqual(userStatsAfterSync?.gamesPlayed, 83)
      assert.strictEqual(userStatsAfterSync?.goals, 32)
      assert.strictEqual(userStatsAfterSync?.assists, 47)
      assert.strictEqual(userStatsAfterSync?.pim, 25)
      assert.strictEqual(userStatsAfterSync?.plusMinus, 20)

      assert.strictEqual(userStatsAfterSync?.postSeasonStats?.gamesPlayed, 20)
      assert.strictEqual(userStatsAfterSync?.postSeasonStats?.goals, 10)

      axiosPostStub.restore()
    })

    it('should sync stats and merge same stats (takes newer)', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      await UserModel.findByIdAndUpdate(randomUser.user._id, {
        externalLinks: {
          name: 'EliteProspects',
          url: 'https://www.eliteprospects.com/player/123321',
          type: 'ELITE_PROSPECTS',
        },
      })

      const mockStat = (games: number | null = 82, goals: number | null = 30) => {
        return {
          id: '1',
          leagueName: 'NHL',
          leagueType: 'Professional',
          postseasonType: 'Regular Season',
          league: {
            slug: 'nhl',
            name: 'National Hockey League',
            country: {
              iso_3166_1_alpha_2: 'US',
            },
          },
          team: {
            slug: 'team-slug',
            name: 'Team Name',
            country: {
              iso_3166_1_alpha_2: 'CA',
            },
            logo: {
              large: 'https://www.example.com/logo.png',
            },
          },
          season: {
            slug: '2024',
            startYear: 2023,
            endYear: 2024,
          },
          regularStats: {
            GP: games,
            G: goals,
          },
        }
      }

      sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            playerStats: {
              edges: [
                mockStat(null, null),
                mockStat(80, 27),
                mockStat(82, 30),
                mockStat(79, 26),
                mockStat(null, null),
              ],
            },
          },
        },
      })

      const response = await client(admin.tokens.accessToken).syncUserStats({
        userId: randomUser.user._id.toString(),
      })

      assert.strictEqual(response.syncUserStats, true)

      const stats = await UserStatsModel.find({ user: randomUser.user._id })
      assert.strictEqual(stats.length, 1)
      assert.strictEqual(stats[0].gamesPlayed, 82)
      assert.strictEqual(stats[0].goals, 30)
      assert.strictEqual(stats[0].season, '2023-2024')
    })
  })

  describe('admin user stats query', () => {
    it('should return user stats', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()
      const organization = await generateRandomOrganization('Test Admin User Stats organization')
      const league = await generateRandomLeague('Test Admin User Stats League')
      const team = await generateRandomTeam({
        name: 'Test Admin User Stats team',
        organization: organization.id,
      })

      const userStats = await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 11,
          wins: 11,
          goals: 9,
          league: league._id,
          losses: 2,
          orderInSeason: 1,
          season: '2011',
          shutouts: 7,
          svp: 1,
          team: team.id,
          ties: 1,
          user: randomUser.user._id,
          gamesPlayed: 10,
          gaa: 2,
        },
      })

      const response = await client(admin.tokens.accessToken).adminUserStats({
        adminUserStatsId: userStats.createUserStats.userStats?.id,
      })

      assert.strictEqual(response.adminUserStats?.goals, 9)
      assert.strictEqual(response.adminUserStats?.assists, 11)
      assert.strictEqual(response.adminUserStats?.wins, 11)
      assert.strictEqual(response.adminUserStats?.losses, 2)
      assert.strictEqual(response.adminUserStats?.ties, 1)
      assert.strictEqual(response.adminUserStats?.gamesPlayed, 10)
      assert.strictEqual(response.adminUserStats?.gaa, 2)
      assert.strictEqual(response.adminUserStats?.svp, 1)
      assert.strictEqual(response.adminUserStats?.shutouts, 7)
      assert.strictEqual(response.adminUserStats?.orderInSeason, 1)
      assert.strictEqual(response.adminUserStats?.season, '2011')
      assert.strictEqual(response.adminUserStats?.user?.id, randomUser.user._id.toString())
      assert.strictEqual(response.adminUserStats?.league?.name, 'Test Admin User Stats League')
      assert.strictEqual(response.adminUserStats?.team?.name, 'Test Admin User Stats team')
    })

    it('should return null when failed find user stats', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()

      const organization = await generateRandomOrganization(
        'Test Admin User Stats Error organization'
      )
      const league = await generateRandomLeague('Test Admin User Stats Error League')

      const team = await generateRandomTeam({
        name: 'Test Admin User Stats Error team',
        organization: organization.id,
      })

      const userStats = await client(admin.tokens.accessToken).createUserStats({
        input: {
          assists: 1,
          wins: 2,
          goals: 1,
          league: league._id,
          losses: 10,
          orderInSeason: 10,
          season: '2023',
          shutouts: 0,
          svp: 1,
          team: team.id,
          ties: 2,
          user: randomUser.user._id,
          gamesPlayed: 3,
          gaa: 1,
        },
      })

      const findByIdStub = sinon.stub(UserStatsModel, 'findById').throws()

      const response = await client(admin.tokens.accessToken).adminUserStats({
        adminUserStatsId: userStats.createUserStats.userStats?.id,
      })

      assert.strictEqual(response.adminUserStats, null)

      findByIdStub.restore()
    })
  })

  describe('searchUserStats query', () => {
    it('should return user stats', async () => {
      const admin = await generateRandomAdminUser()
      const randomUser = await generateRandomUser()
      const organization = await generateRandomOrganization('Test Search User Stats organization')
      const league = await generateRandomLeague('Test Search User Stats League')
      const team = await generateRandomTeam({
        name: 'Test Search User Stats team',
        organization: organization.id,
      })

      const userStatss = []
      for (let i = 0; i < 11; i++) {
        const userStats = await client(admin.tokens.accessToken).createUserStats({
          input: {
            assists: 1 + i,
            wins: 1 + i,
            goals: 1 + i,
            league: league._id,
            losses: 1 + i,
            orderInSeason: 1 + i,
            season: (2010 + i).toString(),
            shutouts: 0 + i,
            svp: 1 + i,
            team: team.id,
            ties: 1 + i,
            user: randomUser.user._id,
            gamesPlayed: 1 + i,
            gaa: 1 + i,
          },
        })
        userStatss.push(userStats)
      }

      const firstPageResponse = await client(admin.tokens.accessToken).searchUserStats({
        filter: {
          user: randomUser.user._id.toString(),
        },
      })

      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.assists, 11)
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.wins, 11)
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.goals, 11)
      assert.strictEqual(
        firstPageResponse.searchUserStats.edges[0].node.league?.name,
        'Test Search User Stats League'
      )
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.losses, 11)
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.orderInSeason, 11)
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.season, '2020')
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.shutouts, 10)
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.svp, 11)
      assert.strictEqual(
        firstPageResponse.searchUserStats.edges[0].node.team?.name,
        'Test Search User Stats team'
      )
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.ties, 11)
      assert.strictEqual(
        firstPageResponse.searchUserStats.edges[0].node.user?.id,
        randomUser.user._id.toString()
      )
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.gamesPlayed, 11)
      assert.strictEqual(firstPageResponse.searchUserStats.edges[0].node.gaa, 11)

      const secondPageResponse = await client(admin.tokens.accessToken).searchUserStats({
        filter: {
          user: randomUser.user._id.toString(),
        },
        pagination: {
          first: 1,
          after: firstPageResponse.searchUserStats.pageInfo.endCursor,
        },
      })

      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.assists, 1)
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.wins, 1)
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.goals, 1)
      assert.strictEqual(
        secondPageResponse.searchUserStats.edges[0].node.league?.name,
        'Test Search User Stats League'
      )
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.losses, 1)
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.orderInSeason, 1)
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.season, '2010')
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.shutouts, 0)
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.svp, 1)
      assert.strictEqual(
        secondPageResponse.searchUserStats.edges[0].node.team?.name,
        'Test Search User Stats team'
      )
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.ties, 1)
      assert.strictEqual(
        secondPageResponse.searchUserStats.edges[0].node.user?.id,
        randomUser.user._id.toString()
      )
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.gamesPlayed, 1)
      assert.strictEqual(secondPageResponse.searchUserStats.edges[0].node.gaa, 1)

      assert.strictEqual(secondPageResponse.searchUserStats.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPageResponse.searchUserStats.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPageResponse.searchUserStats.edges.length, 1)
    })
  })

  describe('saveAggregatedStats functionality', () => {
    beforeEach(async () => {
      await UserStatsModel.deleteMany({})
      await UserModel.deleteMany({})
      await OrganizationModel.deleteMany({})
      await TeamModel.deleteMany({})
      await LeagueModel.deleteMany({})
    })

    it('should save aggregated stats when they differ from existing stats', async () => {
      const randomUser = await generateRandomUser()
      const organization = await generateRandomOrganization()
      const league = await generateRandomLeague()
      const team = await generateRandomTeam({
        name: 'Test Aggregated Stats Team',
        organization: organization.id,
      })

      // Create stats for current season (2024-2025)
      await UserStatsModel.create({
        user: randomUser.user._id,
        team: team.id,
        league: league._id,
        season: '2024-2025',
        gamesPlayed: 10,
        goals: 5,
        assists: 7,
        source: StatsSource.SCRAPER,
      })

      // Create stats for previous season
      await UserStatsModel.create({
        user: randomUser.user._id,
        team: team.id,
        league: league._id,
        season: '2023-2024',
        gamesPlayed: 20,
        goals: 8,
        assists: 12,
        source: StatsSource.SCRAPER,
      })

      // Verify user has no stats before query
      const userBeforeQuery = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(userBeforeQuery?.stats, undefined)

      // Query through GraphQL API to trigger saveAggregatedStats
      await client(randomUser.tokens.accessToken).userStats({
        filter: {
          user: randomUser.user._id.toString(),
        },
      })

      // Now verify user has stats after query and save
      const userAfterQuery = await UserModel.findById(randomUser.user._id)
      assert.ok(userAfterQuery?.stats, 'User should have stats after query and save')

      // Verify career stats
      assert.strictEqual(
        userAfterQuery?.stats?.career?.gamesPlayed,
        30,
        'Career games played should be sum of all seasons'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.goals,
        13,
        'Career goals should be sum of all seasons'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.assists,
        19,
        'Career assists should be sum of all seasons'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.points,
        32,
        'Career points should be sum of goals and assists'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.pointsPerGame,
        1.07,
        'Career points per game should be calculated correctly'
      )

      // Verify current season stats
      assert.strictEqual(
        userAfterQuery?.stats?.season?.gamesPlayed,
        10,
        'Season games played should match current season'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.goals,
        5,
        'Season goals should match current season'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.assists,
        7,
        'Season assists should match current season'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.points,
        12,
        'Season points should be sum of goals and assists'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.pointsPerGame,
        1.2,
        'Season points per game should be calculated correctly'
      )
    })

    it('should update aggregated stats when new stats are added', async () => {
      const randomUser = await generateRandomUser()
      const organization = await generateRandomOrganization()
      const league = await generateRandomLeague()
      const team = await generateRandomTeam({
        name: 'Test Updated Stats Team',
        organization: organization.id,
      })

      // Create initial stats
      await UserStatsModel.create({
        user: randomUser.user._id,
        team: team.id,
        league: league._id,
        season: '2024-2025',
        gamesPlayed: 10,
        goals: 5,
        assists: 7,
        source: StatsSource.SCRAPER,
      })

      // First query to set initial stats
      await client(randomUser.tokens.accessToken).userStats({
        filter: {
          user: randomUser.user._id.toString(),
        },
      })

      // Reload to get the persisted stats
      const userWithPersistedStats = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(userWithPersistedStats?.stats?.career?.gamesPlayed, 10)
      assert.strictEqual(userWithPersistedStats?.stats?.career?.goals, 5)

      // Add new stats
      await UserStatsModel.create({
        user: randomUser.user._id,
        team: team.id,
        league: league._id,
        season: '2023-2024',
        gamesPlayed: 20,
        goals: 8,
        assists: 12,
        source: StatsSource.SCRAPER,
      })

      // Query again to update stats
      await client(randomUser.tokens.accessToken).userStats({
        filter: {
          user: randomUser.user._id.toString(),
        },
      })

      // Verify stats were updated
      const userWithUpdatedStats = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(
        userWithUpdatedStats?.stats?.career?.gamesPlayed,
        30,
        'Career games played should be updated'
      )
      assert.strictEqual(
        userWithUpdatedStats?.stats?.career?.goals,
        13,
        'Career goals should be updated'
      )
      assert.strictEqual(
        userWithUpdatedStats?.stats?.career?.assists,
        19,
        'Career assists should be updated'
      )
    })

    it('should handle goalie stats correctly in aggregated stats', async () => {
      const randomUser = await generateRandomUser()
      const organization = await generateRandomOrganization()
      const league = await generateRandomLeague()
      const team = await generateRandomTeam({
        name: 'Test Goalie Stats Team',
        organization: organization.id,
      })

      // Create goalie stats for current season (2024-2025)
      await UserStatsModel.create({
        user: randomUser.user._id,
        team: team.id,
        league: league._id,
        season: '2024-2025',
        gamesPlayed: 15,
        wins: 10,
        losses: 3,
        ties: 2,
        shutouts: 3,
        gaa: 2.1,
        svp: 0.925,
        source: StatsSource.SCRAPER,
      })

      // Create goalie stats for previous season
      await UserStatsModel.create({
        user: randomUser.user._id,
        team: team.id,
        league: league._id,
        season: '2023-2024',
        gamesPlayed: 25,
        wins: 15,
        losses: 7,
        ties: 3,
        shutouts: 5,
        gaa: 1.9,
        svp: 0.935,
        source: StatsSource.SCRAPER,
      })

      // Query to trigger saveAggregatedStats
      await client(randomUser.tokens.accessToken).userStats({
        filter: {
          user: randomUser.user._id.toString(),
        },
      })

      // Verify user has stats after query
      const userAfterQuery = await UserModel.findById(randomUser.user._id)

      // Verify career goalie stats
      assert.strictEqual(
        userAfterQuery?.stats?.career?.gamesPlayed,
        40,
        'Career games played should be sum of all seasons'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.wins,
        25,
        'Career wins should be sum of all seasons'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.losses,
        10,
        'Career losses should be sum of all seasons'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.ties,
        5,
        'Career ties should be sum of all seasons'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.career?.shutouts,
        8,
        'Career shutouts should be sum of all seasons'
      )

      // GAA and SVP are weighted averages based on games played
      // GAA = (2.1 * 15 + 1.9 * 25) / 40 = 1.975
      // SVP = (0.925 * 15 + 0.935 * 25) / 40 = 0.931
      assertApproximately(
        userAfterQuery?.stats?.career?.gaa || 0,
        1.98,
        0.01,
        'Career GAA should be weighted average'
      )
      assertApproximately(
        userAfterQuery?.stats?.career?.svp || 0,
        0.931,
        0.001,
        'Career SVP should be weighted average'
      )

      // Verify current season goalie stats
      assert.strictEqual(
        userAfterQuery?.stats?.season?.gamesPlayed,
        15,
        'Season games played should match current season'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.wins,
        10,
        'Season wins should match current season'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.losses,
        3,
        'Season losses should match current season'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.ties,
        2,
        'Season ties should match current season'
      )
      assert.strictEqual(
        userAfterQuery?.stats?.season?.shutouts,
        3,
        'Season shutouts should match current season'
      )
      assertApproximately(
        userAfterQuery?.stats?.season?.gaa || 0,
        2.1,
        0.01,
        'Season GAA should match current season'
      )
      assertApproximately(
        userAfterQuery?.stats?.season?.svp || 0,
        0.925,
        0.001,
        'Season SVP should match current season'
      )
    })
  })
})
