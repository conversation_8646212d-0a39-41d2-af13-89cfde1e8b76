import { deepStrictEqual, strictEqual } from 'assert'
import { Types } from 'mongoose'

import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserModel } from '../../src/modules/user/models'
import { client } from '../utils/graphql'
import { generateRandomOrganization, generateRandomTeam, generateRandomUser } from '../utils/users'

describe('User resolver - Family Members', () => {
  beforeEach(async () => {
    await UserModel.deleteMany({})
    await TeamModel.deleteMany({})
    await OrganizationModel.deleteMany({})
  })

  describe('familyMembers field', () => {
    it('should return empty array when user has no family members', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).userQuery({
        slug: user.user.slug!,
      })

      deepStrictEqual(response.user?.familyMembers, [])
    })

    it('should return array of family members when they exist', async () => {
      const mainUser = await generateRandomUser()
      const familyMember1 = await generateRandomUser()
      const familyMember2 = await generateRandomUser()

      await UserModel.updateOne(
        { _id: mainUser.user._id },
        {
          $set: {
            familyMembers: [familyMember1.user._id, familyMember2.user._id],
          },
        }
      )

      const response = await client(mainUser.tokens.accessToken).userQuery({
        slug: mainUser.user.slug!,
      })

      strictEqual(response.user?.familyMembers?.[0].id, familyMember1.user._id.toString())
      strictEqual(response.user?.familyMembers?.[0].name, familyMember1.user.name)
      strictEqual(response.user?.familyMembers?.[0].slug, familyMember1.user.slug)

      strictEqual(response.user?.familyMembers?.[1].id, familyMember2.user._id.toString())
      strictEqual(response.user?.familyMembers?.[1].name, familyMember2.user.name)
      strictEqual(response.user?.familyMembers?.[1].slug, familyMember2.user.slug)
    })

    it('should handle non-existent family member IDs gracefully', async () => {
      const user = await generateRandomUser()
      const nonExistentId = new Types.ObjectId()

      await UserModel.updateOne(
        { _id: user.user._id },
        {
          $set: {
            familyMembers: [nonExistentId],
          },
        }
      )

      const response = await client(user.tokens.accessToken).userQuery({
        slug: user.user.slug!,
      })

      deepStrictEqual(response.user?.familyMembers, [])
    })

    it('should return null for family members field when user does not exist', async () => {
      const response = await client().userQuery({
        slug: 'non-existent-user',
      })

      deepStrictEqual(response.user, null)
    })
  })

  describe('addFamilyMember mutation', () => {
    it('should add pending family member successfully', async () => {
      const user = await generateRandomUser()
      const familyMember = await generateRandomUser()

      const response = await client(user.tokens.accessToken).addFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      strictEqual(response.addFamilyMember.errors, null)
      strictEqual(response.addFamilyMember.user?.id, familyMember.user._id.toString())

      strictEqual(
        response.addFamilyMember.user?.pendingFamilyMembers?.[0]?.id,
        user.user._id.toString()
      )

      const updatedFamilyMember = await UserModel.findById(familyMember.user._id)
      strictEqual(
        updatedFamilyMember?.pendingFamilyMembers?.[0]?.toString(),
        user.user._id.toString()
      )
    })

    it('should return error when trying to add non-existent user as family member', async () => {
      const user = await generateRandomUser()
      const nonExistentId = new Types.ObjectId()
      const response = await client(user.tokens.accessToken).addFamilyMember({
        userId: nonExistentId.toString(),
      })

      strictEqual(response.addFamilyMember.user, null)
      deepStrictEqual(response.addFamilyMember.errors?.[0], {
        message: 'User not found',
        path: 'addFamilyMember',
      })
    })

    it('should return error when trying to add self as family member', async () => {
      const user = await generateRandomUser()
      const response = await client(user.tokens.accessToken).addFamilyMember({
        userId: user.user._id.toString(),
      })

      strictEqual(response.addFamilyMember.user, null)

      deepStrictEqual(response.addFamilyMember.errors?.[0], {
        message: 'Cannot add yourself as a family member',
        path: 'addFamilyMember',
      })
    })
  })

  describe('confirmFamilyMember mutation', () => {
    it('should confirm pending family member successfully', async () => {
      const org = await generateRandomOrganization('Test confirm family member organization')

      const team = await generateRandomTeam({
        name: 'Test confirm family member team',
        organization: org._id.toString(),
      })

      const user = await generateRandomUser()
      const familyMember = await generateRandomUser({ team })

      // First add as pending
      const response0 = await client(user.tokens.accessToken).addFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      strictEqual(response0.addFamilyMember.errors, null)

      strictEqual(response0.addFamilyMember.user?.id, familyMember.user._id.toString())

      strictEqual(
        response0.addFamilyMember.user?.pendingFamilyMembers?.[0].id,
        user.user._id.toString()
      )

      const updatedFamilyMember = await UserModel.findById(familyMember.user._id)
      strictEqual(
        updatedFamilyMember?.pendingFamilyMembers?.[0]?.toString(),
        user.user._id.toString()
      )

      const response = await client(familyMember.tokens.accessToken).confirmFamilyMember({
        userId: user.user._id.toString(),
      })

      strictEqual(response.confirmFamilyMember.errors, null)

      strictEqual(
        response.confirmFamilyMember.user?.familyMembers?.[0].id,
        user.user._id.toString()
      )

      const updatedFamilyMember2 = await UserModel.findById(familyMember.user._id)
      strictEqual(updatedFamilyMember2?.familyMembers?.[0]?.toString(), user.user._id.toString())

      const responseCurrentUser = await client(user.tokens.accessToken).userQuery({
        slug: user.user.slug!,
      })

      deepStrictEqual(
        responseCurrentUser.user?.familyMembers?.[0]?.currentTeam?.id,
        team?._id?.toString()
      )
      deepStrictEqual(responseCurrentUser.user?.familyMembers?.[0]?.currentTeam?.name, team?.name)
    })

    it('should return error when confirming non-existent user', async () => {
      const user = await generateRandomUser()
      const nonExistentId = new Types.ObjectId()

      const response = await client(user.tokens.accessToken).confirmFamilyMember({
        userId: nonExistentId.toString(),
      })

      strictEqual(response.confirmFamilyMember.user, null)

      deepStrictEqual(response.confirmFamilyMember.errors?.[0], {
        message: 'User not found',
        path: 'confirmFamilyMember',
      })
    })

    it('should return error when user is not in pending family members', async () => {
      const user = await generateRandomUser()
      const familyMember = await generateRandomUser()
      const response = await client(user.tokens.accessToken).confirmFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      strictEqual(response.confirmFamilyMember.user, null)

      deepStrictEqual(response.confirmFamilyMember.errors?.[0], {
        message: 'User not in pending family members',
        path: 'confirmFamilyMember',
      })
    })

    it('should return error when user is already a family member', async () => {
      const user = await generateRandomUser()
      const familyMember = await generateRandomUser()

      // First add and confirm the family member
      await client(user.tokens.accessToken).addFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      const userWithPending = await UserModel.findOne({
        _id: familyMember.user._id,
        pendingFamilyMembers: user.user._id.toString(),
      })

      strictEqual(userWithPending?.pendingFamilyMembers?.[0]?.toString(), user.user._id.toString())

      await client(familyMember.tokens.accessToken).confirmFamilyMember({
        userId: user.user._id.toString(),
      })

      const userWithConfirmed = await UserModel.findOne({
        _id: familyMember.user._id,
        familyMembers: user.user._id.toString(),
      })

      strictEqual(userWithConfirmed?.pendingFamilyMembers?.length, 0)

      strictEqual(userWithConfirmed?.familyMembers?.[0]?.toString(), user.user._id.toString())

      const response = await client(familyMember.tokens.accessToken).confirmFamilyMember({
        userId: user.user._id.toString(),
      })

      strictEqual(response.confirmFamilyMember.user, null)
      deepStrictEqual(response.confirmFamilyMember.errors?.[0], {
        message: 'User already has this family member',
        path: 'confirmFamilyMember',
      })
    })
  })

  describe('removePendingFamilyMember mutation', () => {
    it('should remove pending family member successfully', async () => {
      const user = await generateRandomUser()
      const familyMember = await generateRandomUser()

      // First add as pending
      const response0 = await client(user.tokens.accessToken).addFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      strictEqual(response0.addFamilyMember.errors, null)
      strictEqual(
        response0.addFamilyMember.user?.pendingFamilyMembers?.[0].id,
        user.user._id.toString()
      )

      strictEqual(response0.addFamilyMember.user?.id, familyMember.user._id.toString())

      const updatedFamilyMember = await UserModel.findById(familyMember.user._id)
      strictEqual(
        updatedFamilyMember?.pendingFamilyMembers?.[0]._id.toString(),
        user.user._id.toString()
      )

      const response = await client(familyMember.tokens.accessToken).removePendingFamilyMember({
        userId: user.user._id.toString(),
      })

      strictEqual(response.removePendingFamilyMember.errors, null)
      deepStrictEqual(response.removePendingFamilyMember.user?.pendingFamilyMembers, [])

      const updatedFamilyMember2 = await UserModel.findById(familyMember.user._id)
      deepStrictEqual(updatedFamilyMember2?.pendingFamilyMembers, [])
    })

    it('should return error when removing non-existent user from pending', async () => {
      const user = await generateRandomUser()
      const nonExistentId = new Types.ObjectId()
      const response = await client(user.tokens.accessToken).removePendingFamilyMember({
        userId: nonExistentId.toString(),
      })

      strictEqual(response.removePendingFamilyMember.user, null)
      deepStrictEqual(response.removePendingFamilyMember.errors?.[0], {
        message: 'User not found',
        path: 'removePendingFamilyMember',
      })
    })

    it('should return error when user is not in pending family members', async () => {
      const user = await generateRandomUser()
      const familyMember = await generateRandomUser()
      const response = await client(user.tokens.accessToken).removePendingFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      strictEqual(response.removePendingFamilyMember.user, null)
      deepStrictEqual(response.removePendingFamilyMember.errors?.[0], {
        message: 'User not in pending family members',
        path: 'removePendingFamilyMember',
      })
    })
  })

  describe('removeFamilyMember mutation', () => {
    it('should remove family member successfully', async () => {
      const user = await generateRandomUser()
      const familyMember = await generateRandomUser()

      // First add and confirm the family member
      const responseAdd = await client(user.tokens.accessToken).addFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      strictEqual(responseAdd.addFamilyMember.errors, null)
      strictEqual(
        responseAdd.addFamilyMember.user?.pendingFamilyMembers?.[0].id,
        user.user._id.toString()
      )

      const updatedFamilyMember = await UserModel.findById(familyMember.user._id)
      strictEqual(
        updatedFamilyMember?.pendingFamilyMembers?.[0]._id.toString(),
        user.user._id.toString()
      )

      const responseConfirm = await client(familyMember.tokens.accessToken).confirmFamilyMember({
        userId: user.user._id.toString(),
      })
      strictEqual(responseConfirm.confirmFamilyMember.errors, null)
      strictEqual(
        responseConfirm.confirmFamilyMember.user?.familyMembers?.[0].id,
        user.user._id.toString()
      )

      const updatedFamilyMember2 = await UserModel.findById(user.user._id)
      strictEqual(
        updatedFamilyMember2?.familyMembers?.[0]._id.toString(),
        familyMember.user._id.toString()
      )

      const responseRemove = await client(familyMember.tokens.accessToken).removeFamilyMember({
        userId: user.user._id.toString(),
      })
      strictEqual(responseRemove.removeFamilyMember.errors, null)
      strictEqual(responseRemove.removeFamilyMember.user?.familyMembers?.length, 0)

      const updatedFamilyMember3 = await UserModel.findById(user.user._id)
      deepStrictEqual(updatedFamilyMember3?.familyMembers, [])
      deepStrictEqual(updatedFamilyMember3?.pendingFamilyMembers, [])
    })

    it('should return error when removing non-existent user', async () => {
      const user = await generateRandomUser()
      const nonExistentId = new Types.ObjectId()
      const reponse = await client(user.tokens.accessToken).removeFamilyMember({
        userId: nonExistentId.toString(),
      })

      strictEqual(reponse.removeFamilyMember.user, null)
      deepStrictEqual(reponse.removeFamilyMember.errors?.[0], {
        message: 'User not found',
        path: 'removeFamilyMember',
      })
    })

    it('should return error when user is not in family members', async () => {
      const user = await generateRandomUser()
      const familyMember = await generateRandomUser()

      const response = await client(user.tokens.accessToken).removeFamilyMember({
        userId: familyMember.user._id.toString(),
      })

      strictEqual(response.removeFamilyMember.user, null)
      deepStrictEqual(response.removeFamilyMember.errors?.[0], {
        message: 'User not in family members',
        path: 'removeFamilyMember',
      })
    })
  })
})
