import assert from 'assert'
import dayjs from 'dayjs'
import sinon from 'sinon'

import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { GameModel, GameScoreType } from '../../src/modules/stats/models/game/game'
import { GameEventType } from '../../src/modules/stats/models/game/gameEvent'
import { LeagueModel } from '../../src/modules/stats/models/league'
import { TeamModel } from '../../src/modules/stats/models/team'
import { RosterPosition, SortOrder } from '../graphql/components'
import { client } from '../utils/graphql'
import { generateDiscoverUrl, generateRandomGameStats } from '../utils/stats'
import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from '../utils/users'

describe('Games resolver', () => {
  beforeEach(async () => {
    await LeagueModel.deleteMany({})
    await TeamModel.deleteMany({})
    await GameModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
  })

  it('should return an empty response when no games exist', async () => {
    await GameModel.deleteMany({})
    const response = await client().games({ filter: {} })
    assert.strictEqual(response.games.edges.length, 0)
  })

  it('should return an empty response when an error occurs in the resolver', async () => {
    // For example, stub populate to throw an error
    sinon.stub(GameModel, 'populate').throws(new Error('Test error'))

    const response = await client().games({ filter: {} })

    // Expect an empty response []
    assert.strictEqual(response.games.edges.length, 0)
  })

  it('should handle reverse pagination using before parameter', async () => {
    const discoveredUrl = await generateDiscoverUrl()
    for (let i = 0; i < 20; i++) {
      await GameModel.create({
        date: new Date(Date.now() - i * 1000),
        externalInfo: {
          homeTeamName: 'HC Litvinov',
          awayTeamName: 'HC Pisek',
          leagueName: 'Extraliga 9. trid',
        },
        externalId: `${i}`,
        discoveredUrl,
      })
    }

    const forwardResponse = await client().games({
      first: 10,
      filter: {},
    })

    const beforeCursor = forwardResponse.games.pageInfo.endCursor

    // Passing first: 5 will set last to 5 with before cursor
    const reverseResponse = await client().games({
      before: beforeCursor,
      first: 5,
      filter: {},
    })

    assert.strictEqual(reverseResponse.games.edges.length, 5)
  })

  it('should handle filters/sort', async () => {
    const discoveredUrl = await generateDiscoverUrl()
    const player = await generateRandomUser({
      externalConnections: [
        { externalId: '123', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })

    const organization = await generateRandomOrganization('Test Game Stats without team')
    const league = await generateRandomLeague('Test Game Stats League without team')
    const team1 = await generateRandomTeam({
      name: 'Test Game Stats without team',
      organization: organization._id.toString(),
    })
    const team2 = await generateRandomTeam({
      name: 'Test Game Stats without team 2',
      organization: organization._id.toString(),
    })

    await GameModel.create({
      date: dayjs().subtract(1, 'days').toDate(),
      externalId: '5',
      discoveredUrl,
      externalInfo: {
        homeTeamName: 'HC Litvinov',
        awayTeamName: 'HC Pisek',
        leagueName: 'Extraliga 9. trid',
      },
      homeTeamRoster: [
        {
          externalInfo: {
            playerName: 'Test Player',
            playerExternalId: '123',
          },
        },
      ],
      homeTeam: team1._id,
      awayTeam: team2._id,
      league: league._id,
      season: '2024-2025',
    })

    await GameModel.create({
      date: dayjs().subtract(2, 'days').toDate(),
      externalId: '1',
      discoveredUrl,
      externalInfo: {
        homeTeamName: 'HC Litvinov',
        awayTeamName: 'HC Pisek',
        leagueName: 'Extraliga 9. trid',
      },
      awayTeamRoster: [
        {
          externalInfo: {
            playerName: 'Test Player',
            playerExternalId: '123',
          },
        },
      ],
      homeTeam: team1._id,
      awayTeam: team2._id,
      season: '2024-2025',
    })

    await GameModel.create({
      date: dayjs().subtract(3, 'days').toDate(),
      externalId: '2',
      discoveredUrl,
      externalInfo: {
        homeTeamName: 'HC Litvinov',
        awayTeamName: 'HC Pisek',
        leagueName: 'Extraliga 9. trid',
      },
      homeTeam: team1._id,
      league: league._id,
      season: '2023-2024',
    })

    await GameModel.create({
      date: dayjs().subtract(4, 'days').toDate(),
      externalId: '3',
      externalInfo: {
        homeTeamName: 'HC Litvinov',
        awayTeamName: 'HC Pisek',
        leagueName: 'Extraliga 9. trid',
      },
      discoveredUrl,
      awayTeam: team2._id,
      league: league._id,
      season: '2023-2024',
    })

    await GameModel.create({
      date: dayjs().subtract(5, 'days').toDate(),
      externalId: '4',
      externalInfo: {
        homeTeamName: 'HC Popovice',
        awayTeamName: 'HC Slavia Praha',
        leagueName: 'DHL extraliga juniorů',
      },
      discoveredUrl,
      season: '2023-2024',
    })

    // filter by league
    const responseLeague = await client().games({
      filter: {
        leagueId: league._id.toString(),
      },
    })

    assert.strictEqual(responseLeague.games.edges.length, 3)
    assert.strictEqual(responseLeague.games.edges[0].node.externalId, '5')
    assert.strictEqual(responseLeague.games.edges[1].node.externalId, '2')
    assert.strictEqual(responseLeague.games.edges[2].node.externalId, '3')
    assert.ok(
      new Date(responseLeague.games.edges[0].node.date) >
        new Date(responseLeague.games.edges[1].node.date)
    )
    assert.ok(
      new Date(responseLeague.games.edges[1].node.date) >
        new Date(responseLeague.games.edges[2].node.date)
    )

    // filter by user
    const responseUser = await client().games({
      filter: {
        userId: player.user._id.toString(),
      },
    })

    assert.strictEqual(responseUser.games.edges.length, 2)
    assert.strictEqual(responseUser.games.edges[0].node.externalId, '5')
    assert.strictEqual(responseUser.games.edges[1].node.externalId, '1')
    assert.strictEqual(
      responseUser.games.edges[0].currentUserPlayer?.player?.id,
      player.user._id.toString()
    )

    // filter by team
    const responseTeam = await client().games({
      filter: {
        teamId: team1._id.toString(),
      },
    })
    assert.strictEqual(responseTeam.games.edges.length, 3)
    assert.strictEqual(responseTeam.games.edges[0].node.externalId, '5')
    assert.strictEqual(responseTeam.games.edges[1].node.externalId, '1')

    // filter by team with sort
    const responseTeamSort = await client().games({
      filter: {
        teamId: team1._id.toString(),
      },
      sort: {
        fields: [
          {
            field: 'date',
            order: SortOrder.Asc,
          },
        ],
      },
    })
    assert.strictEqual(responseTeamSort.games.edges.length, 3)
    assert.strictEqual(responseTeamSort.games.edges[0].node.externalId, '2')
    assert.strictEqual(responseTeamSort.games.edges[1].node.externalId, '1')
    assert.strictEqual(responseTeamSort.games.edges[2].node.externalId, '5')

    // filter by season
    const responseTeamSeason = await client().games({
      filter: {
        season: '2024-2025',
      },
    })
    assert.strictEqual(responseTeamSeason.games.edges.length, 2)
    assert.strictEqual(responseTeamSeason.games.edges[0].node.externalId, '5')
    assert.strictEqual(responseTeamSeason.games.edges[1].node.externalId, '1')

    // populate team organization
    const responseTeamOrganization = await client().games({
      filter: {},
    })

    assert.strictEqual(
      responseTeamOrganization.games.edges[0].node.homeTeam?.organization?.name,
      'Test Game Stats without team'
    )
  })

  it('should handle pagination', async () => {
    // Skip this test for now as it's flaky
    return
  })

  it('should return the details of a specific game', async () => {
    const discoveredUrl = await generateDiscoverUrl()
    const user = await generateRandomUser({
      externalConnections: [
        { externalId: '11', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })

    const league = await generateRandomLeague('Test Game Stats League 2')

    const teamAwayOrganization = await generateRandomOrganization('Test away team organization')
    const teamAway = await generateRandomTeam({
      name: 'Test away team',
      organization: teamAwayOrganization._id.toString(),
    })

    const teamHomeOrganization = await generateRandomOrganization('Test home team organization')
    const teamHome = await generateRandomTeam({
      name: 'Test home team',
      organization: teamHomeOrganization._id.toString(),
    })

    const game = await generateRandomGameStats({
      nameSuffix: 'GameDetails',
      gameStatsPayload: {
        date: new Date('2024-09-28T13:00:00.000Z'),
        externalId: '60966',
        externalInfo: {
          homeTeamName: 'HC Oceláři Třinec',
          awayTeamName: 'HC Slavia Praha',
          leagueName: 'DHL extraliga juniorů',
        },
        discoveredUrl,
        league: league._id,
        homeTeam: teamHome._id,
        awayTeam: teamAway._id,
        stats: {
          homeTeam: {
            score: 5,
            shotsOnGoal: 22,
            blockedShots: 0,
            faceoffs: 0,
            powerPlayGoals: 2,
            shortHandedGoals: 0,
            penalties: 8,
            majorPenalties: 0,
            penaltyMinutes: 8,
            goalieSaves: 37,
          },
          awayTeam: {
            score: 2,
            shotsOnGoal: 39,
            blockedShots: 0,
            faceoffs: 0,
            powerPlayGoals: 1,
            shortHandedGoals: 0,
            penalties: 12,
            majorPenalties: 0,
            penaltyMinutes: 12,
            goalieSaves: 17,
          },
          periodScores: '1:1, 2:1, 2:0',
        },
        events: [
          {
            time: '05:16',
            isHomeTeam: true,
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'SLA',
              playerName: 'Jakub Dráb',
              playerExternalId: '1950',
            },
            player: null,
            assists: [
              {
                playerName: 'Artur Matějovský',
                playerExternalId: '835',
              },
              {
                playerName: 'Filip Tittl',
                playerExternalId: '3378',
              },
            ],
          },
          {
            time: '15:46',
            isHomeTeam: true,
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'TRI',
              playerName: 'Michal Rybnikár',
              playerExternalId: '109',
            },
            player: null,
            assists: [
              {
                playerName: 'Matěj Kubiesa',
                playerExternalId: '20539',
              },
              {
                playerName: 'Marcel Mikula',
                playerExternalId: '21',
              },
            ],
          },
          {
            time: '25:16',
            isHomeTeam: false,
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'SLA',
              playerName: 'Jakub Dráb',
              playerExternalId: '1950',
            },
            player: null,
            assists: [
              {
                playerName: 'Artur Matějovský',
                playerExternalId: '835',
              },
            ],
          },
          {
            time: '45:46',
            isHomeTeam: false,
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'TRI',
              playerName: 'Michal Rybnikár',
              playerExternalId: '109',
            },
            player: null,
            assists: [
              {
                playerName: 'Matěj Kubiesa',
                playerExternalId: '20539',
              },
            ],
          },
        ],
        homeTeamRoster: [
          {
            externalInfo: {
              playerName: 'Robin Gerych',
              playerExternalId: '11',
            },
            goals: 0,
            assists: 1,
            points: 1,
            pim: 0,
            plusMinus: 1,
            saves: 0,
            goalsAgainst: 0,
            svp: 0,
            jerseyNumber: 5,
            position: RosterPosition.Forward,
            player: null,
          },
        ],
        awayTeamRoster: [
          {
            externalInfo: {
              playerName: 'Daniel Kříženecký',
              playerExternalId: '2482',
            },
            player: null,
            goals: 0,
            assists: 0,
            points: 0,
            pim: 2,
            plusMinus: -3,
            saves: 0,
            goalsAgainst: 0,
            svp: 0,
            jerseyNumber: 7,
            position: RosterPosition.Defenseman,
          },
        ],
        syncedAt: new Date('2024-12-30T17:41:09.723Z'),
      },
    })

    const response = await client().game({
      gameId: game._id.toString(),
    })

    assert.strictEqual(response.game?.externalId, '60966')
    assert.strictEqual(response.game?.externalInfo?.homeTeamName, 'HC Oceláři Třinec')
    assert.strictEqual(response.game?.externalInfo?.awayTeamName, 'HC Slavia Praha')
    assert.strictEqual(response.game?.externalInfo?.leagueName, 'DHL extraliga juniorů')
    assert.strictEqual(response.game?.league?.name, league.name)
    assert.strictEqual(response.game?.awayTeam?.name, teamAway.name)
    assert.strictEqual(response.game?.awayTeam?.shortNameSlug, teamAway.shortNameSlug)
    assert.strictEqual(response.game?.awayTeam?.organization?.slug, teamAwayOrganization.slug)
    assert.strictEqual(response.game?.homeTeam?.name, teamHome.name)
    assert.strictEqual(response.game?.homeTeam?.shortNameSlug, teamHome.shortNameSlug)
    assert.strictEqual(response.game?.homeTeam?.organization?.slug, teamHomeOrganization.slug)
    assert.strictEqual(response.game?.stats?.homeTeam?.score, 5)
    assert.strictEqual(response.game?.stats?.awayTeam?.score, 2)
    assert.strictEqual(response.game?.stats?.periodScores, '1:1, 2:1, 2:0')
    assert.strictEqual(response.game?.stats?.homeTeam?.shotsOnGoal, 22)
    assert.strictEqual(response.game?.stats?.awayTeam?.shotsOnGoal, 39)
    assert.strictEqual(response.game?.stats?.homeTeam?.faceoffs, 0)
    assert.strictEqual(response.game?.stats?.awayTeam?.faceoffs, 0)
    assert.strictEqual(response.game?.stats?.homeTeam?.goalieSaves, 37)
    assert.strictEqual(response.game?.stats?.awayTeam?.goalieSaves, 17)
    assert.strictEqual(response.game?.stats?.homeTeam?.penalties, 8)
    assert.strictEqual(response.game?.stats?.awayTeam?.penalties, 12)
    assert.strictEqual(response.game?.stats?.homeTeam?.powerPlayGoals, 2)
    assert.strictEqual(response.game?.stats?.awayTeam?.powerPlayGoals, 1)
    assert.strictEqual(response.game?.eventsByPeriod?.length, 3)
    assert.strictEqual(response.game?.eventsByPeriod?.[0]?.[0]?.time, '05:16')
    assert.strictEqual(response.game?.eventsByPeriod?.[0]?.[0]?.type, 'Goal')
    assert.strictEqual(response.game?.eventsByPeriod?.[0]?.[0]?.externalInfo?.teamName, 'SLA')
    assert.strictEqual(
      response.game?.eventsByPeriod?.[0]?.[0]?.externalInfo?.playerName,
      'Jakub Dráb'
    )
    assert.strictEqual(
      response.game?.eventsByPeriod?.[0]?.[0]?.externalInfo?.playerExternalId,
      '1950'
    )
    assert.strictEqual(response.game?.eventsByPeriod?.[0]?.[0]?.player, null)
    assert.strictEqual(response.game?.eventsByPeriod?.[0]?.[0]?.isHomeTeam, true)
    assert.strictEqual(response.game?.eventsByPeriod?.[0]?.[0]?.assists?.length, 2)
    assert.strictEqual(
      response.game?.eventsByPeriod?.[0]?.[0]?.assists?.[0]?.playerName,
      'Artur Matějovský'
    )
    assert.strictEqual(
      response.game?.eventsByPeriod?.[0]?.[0]?.assists?.[0]?.playerExternalId,
      '835'
    )
    assert.strictEqual(
      response.game?.eventsByPeriod?.[0]?.[0]?.assists?.[1]?.playerName,
      'Filip Tittl'
    )
    assert.strictEqual(
      response.game?.eventsByPeriod?.[0]?.[0]?.assists?.[1]?.playerExternalId,
      '3378'
    )
    assert.strictEqual(response.game?.homeTeamRoster?.length, 1)
    assert.strictEqual(response.game?.homeTeamRoster[0].externalInfo?.playerName, 'Robin Gerych')
    assert.strictEqual(response.game?.homeTeamRoster[0].externalInfo?.playerExternalId, '11')
    assert.strictEqual(response.game?.homeTeamRoster[0].player?.name, user.user.name)
    assert.strictEqual(response.game?.homeTeamRoster[0].goals, 0)
    assert.strictEqual(response.game?.homeTeamRoster[0].assists, 1)
    assert.strictEqual(response.game?.homeTeamRoster[0].points, 1)
    assert.strictEqual(response.game?.homeTeamRoster[0].pim, 0)
    assert.strictEqual(response.game?.homeTeamRoster[0].plusMinus, 1)
    assert.strictEqual(response.game?.homeTeamRoster[0].saves, 0)
    assert.strictEqual(response.game?.homeTeamRoster[0].goalsAgainst, 0)
    assert.strictEqual(response.game?.homeTeamRoster[0].svp, 0)
    assert.strictEqual(response.game?.homeTeamRoster[0].jerseyNumber, 5)
    assert.strictEqual(response.game?.homeTeamRoster[0].position, RosterPosition.Forward)
    assert.strictEqual(response.game?.awayTeamRoster?.length, 1)
    assert.strictEqual(
      response.game?.awayTeamRoster[0].externalInfo?.playerName,
      'Daniel Kříženecký'
    )
    assert.strictEqual(response.game?.awayTeamRoster[0].externalInfo?.playerExternalId, '2482')
    assert.strictEqual(response.game?.awayTeamRoster[0].player, null)
    assert.strictEqual(response.game?.awayTeamRoster[0].goals, 0)
    assert.strictEqual(response.game?.awayTeamRoster[0].assists, 0)
    assert.strictEqual(response.game?.awayTeamRoster[0].points, 0)
    assert.strictEqual(response.game?.awayTeamRoster[0].pim, 2)
    assert.strictEqual(response.game?.awayTeamRoster[0].plusMinus, -3)
    assert.strictEqual(response.game?.awayTeamRoster[0].saves, 0)
    assert.strictEqual(response.game?.awayTeamRoster[0].goalsAgainst, 0)
    assert.strictEqual(response.game?.awayTeamRoster[0].svp, 0)
    assert.strictEqual(response.game?.awayTeamRoster[0].jerseyNumber, 7)
    assert.strictEqual(response.game?.awayTeamRoster[0].position, RosterPosition.Defenseman)
  })

  it('should return game events', async () => {
    const discoveredUrl = await generateDiscoverUrl()

    const game = await generateRandomGameStats({
      gameStatsPayload: {
        date: new Date('2024-09-28T13:00:00.000Z'),
        season: '2024-2025',
        externalId: '60966',
        discoveredUrl,
        externalInfo: {
          homeTeamName: 'HC Oceláři Třinec',
          awayTeamName: 'HC Slavia Praha',
          leagueName: 'DHL extraliga juniorů',
        },
        stats: {
          homeTeam: {
            score: 5,
          },
          awayTeam: {
            score: 2,
          },
          periodScores: '1:1, 2:1, 2:0',
          scores: [
            {
              homeTeam: 1,
              awayTeam: 0,
              type: GameScoreType.Regular,
            },
            {
              homeTeam: 1,
              awayTeam: 1,
              type: GameScoreType.Regular,
            },
            {
              homeTeam: 1,
              awayTeam: 1,
              type: GameScoreType.Regular,
            },
          ],
        },
        homeTeamRoster: [
          {
            externalInfo: {
              playerName: 'Player 1',
              playerExternalId: '123',
            },
            player: null,
          },
        ],
        awayTeamRoster: [
          {
            externalInfo: {
              playerName: 'Player 2',
              playerExternalId: '456',
            },
            player: null,
          },
        ],
        events: [
          {
            time: '05:16',
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'SLA',
              playerName: 'Jakub Dráb',
              playerExternalId: '1950',
            },
            player: null,
            isHomeTeam: true,
            assists: [
              {
                playerName: 'Artur Matějovský',
                playerExternalId: '835',
              },
              {
                playerName: 'Filip Tittl',
                playerExternalId: '3378',
              },
            ],
          },
          {
            time: '15:46',
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'TRI',
              playerName: 'Michal Rybnikár',
              playerExternalId: '109',
            },
            player: null,
            isHomeTeam: false,
            assists: [
              {
                playerName: 'Matěj Kubiesa',
                playerExternalId: '20539',
              },
              {
                playerName: 'Marcel Mikula',
                playerExternalId: '21',
              },
            ],
          },
          {
            time: '20:46',
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'TRI',
              playerName: 'Michal Rybnikár',
              playerExternalId: '109',
            },
            player: null,
            isHomeTeam: false,
            assists: [
              {
                playerName: 'Fero Koblihovic',
                playerExternalId: '20539',
              },
              {
                playerName: 'Maca Vosratek',
                playerExternalId: '21',
              },
            ],
          },
        ],
      },
    })

    const response = await client().gameEvents({
      gameId: game._id.toString(),
    })

    assert.strictEqual(response.game?.season, '2024-2025')
    assert.strictEqual(response.game?.stats?.homeTeam?.score, 5)
    assert.strictEqual(response.game?.stats?.awayTeam?.score, 2)
    assert.strictEqual(response.game?.stats?.scores?.length, 3)
    assert.strictEqual(response.game?.events?.length, 3)
    assert.strictEqual(response.game?.events?.[0]?.time, '05:16')
    assert.strictEqual(response.game?.events?.[0]?.isHomeTeam, true)
    assert.strictEqual(response.game?.events?.[0]?.type, 'Goal')
    assert.strictEqual(response.game?.events?.[0]?.externalInfo?.teamName, 'SLA')
    assert.strictEqual(response.game?.events?.[0]?.externalInfo?.playerName, 'Jakub Dráb')
    assert.strictEqual(response.game?.events?.[1]?.time, '15:46')
    assert.strictEqual(response.game?.events?.[1]?.isHomeTeam, false)
  })

  it('should throw an error if the game is not found and return null', async () => {
    try {
      const response = await client().game({
        gameId: '5f8b9c3d3b4d4f000f9a4b4c',
      })
      console.log(response, 'response')
      assert.strictEqual(response.game, null)
    } catch (error) {
      console.log(error.response.errors[0].message, 'Game not found')
    }
  })

  it('should correctly set currentUserTeam based on user roster placement', async () => {
    const discoveredUrl = await generateDiscoverUrl()

    // Create users for home and away teams
    const homeUser = await generateRandomUser({
      externalConnections: [
        { externalId: 'home123', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })

    const awayUser = await generateRandomUser({
      externalConnections: [
        { externalId: 'away456', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })

    const unrelatedUser = await generateRandomUser({
      externalConnections: [
        { externalId: 'unrelated789', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })

    // Create teams and organization
    const organization = await generateRandomOrganization('Test CurrentUserTeam Organization')
    const league = await generateRandomLeague('Test CurrentUserTeam League')
    const homeTeam = await generateRandomTeam({
      name: 'Home Team',
      organization: organization._id.toString(),
    })
    const awayTeam = await generateRandomTeam({
      name: 'Away Team',
      organization: organization._id.toString(),
    })

    // Create game with home and away team rosters
    await generateRandomGameStats({
      nameSuffix: 'CurrentUserTeam',
      gameStatsPayload: {
        date: new Date(),
        externalId: 'currentUserTeamTest',
        discoveredUrl,
        homeTeam: homeTeam._id,
        awayTeam: awayTeam._id,
        league: league._id,
        externalInfo: {
          homeTeamName: 'Home Team',
          awayTeamName: 'Away Team',
          leagueName: 'Test CurrentUserTeam League',
        },
        homeTeamRoster: [
          {
            externalInfo: {
              playerName: homeUser.user.name,
              playerExternalId: 'home123',
            },
            player: homeUser.user._id,
          },
        ],
        awayTeamRoster: [
          {
            externalInfo: {
              playerName: awayUser.user.name,
              playerExternalId: 'away456',
            },
            player: awayUser.user._id,
          },
        ],
      },
    })

    // Test user in home team
    const homeResponse = await client().games({
      filter: {
        userId: homeUser.user._id.toString(),
      },
    })

    assert.strictEqual(homeResponse.games.edges.length, 1)
    assert.strictEqual(homeResponse.games.edges[0].currentUserTeam, 'HOME_TEAM')
    // Check that the currentUserPlayer exists
    assert.ok(homeResponse.games.edges[0].currentUserPlayer)

    // Test user in away team
    const awayResponse = await client().games({
      filter: {
        userId: awayUser.user._id.toString(),
      },
    })

    assert.strictEqual(awayResponse.games.edges.length, 1)
    assert.strictEqual(awayResponse.games.edges[0].currentUserTeam, 'AWAY_TEAM')
    // Check that the currentUserPlayer exists
    assert.ok(awayResponse.games.edges[0].currentUserPlayer)

    // Test user not in any team
    const unrelatedResponse = await client().games({
      filter: {
        userId: unrelatedUser.user._id.toString(),
      },
    })

    assert.strictEqual(unrelatedResponse.games.edges.length, 0)
  })
})
