import assert from 'assert'
import config from 'config'
import { Types } from 'mongoose'
import sinon from 'sinon'

import * as s3RequestPresigner from '../../src/helpers/getSignedUrl'
import { slugifyString } from '../../src/helpers/string'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserModel, WallVideoModel } from '../../src/modules/user/models'
import { BioPosition, User } from '../../src/modules/user/models/user'
import { BioPosition as BioPositionGraphQL, WallVideoType } from '../graphql/components'
import { client } from '../utils/graphql'
import { generateRandomAgency, generateRandomTeam, generateRandomUser } from '../utils/users'
import { generateWallVideo } from '../utils/wallVideos'

import {
  amplitudeIdentifyStub,
  amplitudeTrackStub,
  slackClientStub,
  sqsClientStub,
  streamFeedMockActivityId,
} from '.'

describe('Wall videos', () => {
  it('can get only landscape wall videos', async () => {
    const user = await generateRandomUser()

    await generateWallVideo({ user: user.user, path: 'video1' })
    await generateWallVideo({ user: user.user, path: 'video2' })
    await generateWallVideo({ user: user.user, path: 'video3', width: 480, height: 640 })

    const response = await client(user.tokens.accessToken).wallVideos({
      filter: {
        onlyLandscape: true,
      },
    })

    assert.strictEqual(response.wallVideos.edges.length, 2)
    assert.strictEqual(response.wallVideos.edges[0].node.path, 'video2')
    assert.strictEqual(response.wallVideos.edges[1].node.path, 'video1')
    assert.strictEqual(response.wallVideos.categoryCounts?.length, 1)
    assert.strictEqual(response.wallVideos.categoryCounts[0].category, WallVideoType.Offensive)
    assert.strictEqual(response.wallVideos.categoryCounts[0].count, 3)
  })

  it('can get all wall videos (without filter)', async () => {
    const user = await generateRandomUser()

    await generateWallVideo({ user: user.user, path: 'video1' })
    await generateWallVideo({ user: user.user, path: 'video2' })
    await generateWallVideo({
      user: user.user,
      path: 'video3',
      width: 480,
      height: 640,
      type: WallVideoType.Defensive,
    })

    const response = await client(user.tokens.accessToken).wallVideos()

    const offensiveCategory = response.wallVideos.categoryCounts?.find(
      (category) => category.category === WallVideoType.Offensive
    )
    const defensiveCategory = response.wallVideos.categoryCounts?.find(
      (category) => category.category === WallVideoType.Defensive
    )

    assert.strictEqual(response.wallVideos.edges.length, 3)
    assert.strictEqual(response.wallVideos.edges[0].node.path, 'video3')
    assert.strictEqual(response.wallVideos.edges[1].node.path, 'video2')
    assert.strictEqual(response.wallVideos.edges[2].node.path, 'video1')
    assert.strictEqual(response.wallVideos.categoryCounts?.length, 2)
    assert.strictEqual(offensiveCategory?.count, 2)
    assert.strictEqual(defensiveCategory?.count, 1)
  })

  it('can get all wall videos by type', async () => {
    const user = await generateRandomUser()

    await generateWallVideo({ user: user.user, path: 'video1' })
    await generateWallVideo({ user: user.user, path: 'video2' })
    await generateWallVideo({
      user: user.user,
      path: 'video3',
      width: 480,
      height: 640,
      type: WallVideoType.Defensive,
    })

    const response = await client(user.tokens.accessToken).wallVideos({
      filter: {
        type: WallVideoType.Offensive,
      },
    })

    assert.strictEqual(response.wallVideos.edges.length, 2)
    assert.strictEqual(response.wallVideos.edges[0].node.path, 'video2')
    assert.strictEqual(response.wallVideos.edges[1].node.path, 'video1')
  })

  it('should return error if user is not logged in and user id is not provided', async () => {
    try {
      await client().wallVideos()
      assert.fail('Should have thrown an error')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'User not logged in or missing userId or slug in filter.'
      )
    }
  })

  it('should return paginatated results', async () => {
    const user = await generateRandomUser()

    await generateWallVideo({ user: user.user, path: 'video1' })
    await generateWallVideo({ user: user.user, path: 'video2' })
    await generateWallVideo({ user: user.user, path: 'video3' })

    const response = await client(user.tokens.accessToken).wallVideos({
      first: 2,
    })

    assert.strictEqual(response.wallVideos.edges.length, 2)
    assert.strictEqual(response.wallVideos.pageInfo.hasNextPage, true)
    assert.strictEqual(response.wallVideos.pageInfo.hasPreviousPage, false)

    const response2 = await client(user.tokens.accessToken).wallVideos({
      first: 2,
      after: response.wallVideos.pageInfo.endCursor,
    })

    assert.strictEqual(response2.wallVideos.edges.length, 1)
    assert.strictEqual(response2.wallVideos.pageInfo.hasNextPage, false)
    assert.strictEqual(response2.wallVideos.pageInfo.hasPreviousPage, true)
  })

  it('should get wall videos by slug', async () => {
    const user = await generateRandomUser()

    user.user = (await UserModel.findOneAndUpdate(
      { _id: user.user._id },
      { slug: slugifyString(user.user.name) },
      { new: true }
    )) as User

    await generateWallVideo({ user: user.user, path: 'video1' })
    await generateWallVideo({ user: user.user, path: 'video2' })
    await generateWallVideo({ user: user.user, path: 'video3' })

    const response = await client().wallVideos({
      filter: {
        slug: user.user.slug,
      },
    })

    assert.strictEqual(response.wallVideos.edges.length, 3)
  })

  it('should get example wall videos', async () => {
    await WallVideoModel.deleteMany({})
    const user = await generateRandomUser()

    await generateWallVideo({ user: user.user, path: 'video1' })

    for (let i = 0; i < 5; i++) {
      await generateWallVideo({
        user: user.user,
        path: `video${i}`,
        segmentation: { finished: true },
      })
    }

    const response = await client(user.tokens.accessToken).exampleWallVideos()

    assert.strictEqual(response.exampleWallVideos.length, 5)
    assert.strictEqual(
      response.exampleWallVideos.find((video) => !video.segmentation),
      undefined
    )
  })

  it('should get wallVideos with user, based on used query', async () => {
    await WallVideoModel.deleteMany({})
    const user = await generateRandomUser()

    for (let i = 0; i < 1; i++) {
      await generateWallVideo({
        user: user.user,
        path: `video${i}`,
        title: `The Video ${i}`,
      })
    }

    const responseBase = await client(user.tokens.accessToken).wallVideos()

    assert.strictEqual(
      (responseBase.wallVideos.edges[0].node as { user?: unknown })?.user,
      undefined
    )

    const response = await client(user.tokens.accessToken).wallVideosReel()

    assert.strictEqual(response.wallVideosReel.edges[0].node?.user?.slug, user.user.slug)
    assert.strictEqual(response.wallVideosReel.edges[0].node?.user?.name, user.user.name)
  })

  describe('Highligths', () => {
    it('can add highlights and reorder', async () => {
      const user = await generateRandomUser()

      const video1 = await generateWallVideo({ user: user.user, path: 'video1' })
      const video2 = await generateWallVideo({ user: user.user, path: 'video2' })

      const response = await client(user.tokens.accessToken).editHighlights({
        input: {
          highlights: [video1._id, video2._id],
        },
      })

      assert.strictEqual(response.editHighlights.errors, null)
      assert.strictEqual(response.editHighlights.user?.highlights?.length, 2)
      assert.strictEqual(response.editHighlights.user.highlights[0].path, 'video1')
      assert.strictEqual(response.editHighlights.user.highlights[1].path, 'video2')

      const video3 = await generateWallVideo({ user: user.user, path: 'video3' })

      const response2 = await client(user.tokens.accessToken).editHighlights({
        input: {
          highlights: [video2._id, video3._id, video1._id],
        },
      })

      assert.strictEqual(response2.editHighlights.errors, null)
      assert.strictEqual(response2.editHighlights.user?.highlights?.length, 3)
      assert.strictEqual(response2.editHighlights.user.highlights[0].path, 'video2')
      assert.strictEqual(response2.editHighlights.user.highlights[1].path, 'video3')
      assert.strictEqual(response2.editHighlights.user.highlights[2].path, 'video1')
    })

    it('fails when user does not exists anymore', async () => {
      const user = await generateRandomUser()

      const video1 = await generateWallVideo({ user: user.user, path: 'video1' })
      const video2 = await generateWallVideo({ user: user.user, path: 'video2' })

      await UserModel.deleteOne({ _id: user.user._id })

      const response = await client(user.tokens.accessToken).editHighlights({
        input: {
          highlights: [video1._id, video2._id],
        },
      })

      assert.strictEqual(response.editHighlights.errors?.length, 1)
      assert.strictEqual(response.editHighlights.errors[0].message, 'Failed to edit highlights')
    })

    it('should remove video from highlights if video was removed', async () => {
      const user = await generateRandomUser()

      const video1 = await generateWallVideo({ user: user.user, path: 'video1' })
      const video2 = await generateWallVideo({ user: user.user, path: 'video2' })

      const response = await client(user.tokens.accessToken).editHighlights({
        input: {
          highlights: [video1._id, video2._id],
        },
      })

      assert.strictEqual(response.editHighlights.errors, null)
      assert.strictEqual(response.editHighlights.user?.highlights?.length, 2)

      await client(user.tokens.accessToken).deleteWallVideo({ id: video1._id.toString() })

      const currentUser = await client(user.tokens.accessToken).currentUser()

      assert.strictEqual(currentUser.currentUser?.highlights?.length, 1)
      assert.strictEqual(currentUser.currentUser.highlights[0].path, 'video2')

      // since we have enabled soft delete, we can restore the video
      const dbVideo = await WallVideoModel.findOne({ _id: video1._id })
      const dbVideoSoft = await WallVideoModel.findOne({ _id: video1._id, deleted: true })

      assert.strictEqual(dbVideo, null)
      assert.strictEqual(dbVideoSoft?.deleted, true)
      assert.strictEqual(dbVideoSoft.deletedBy.toString(), user.user._id.toString())

      sinon.assert.calledTwice(amplitudeTrackStub)

      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'clipVideoDeleted',
        sinon.match({
          category: 'OFFENSIVE',
        }),
        sinon.match({
          user_id: user.user._id.toString(),
        })
      )

      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: user.user._id.toString() })
      )

      sinon.assert.callCount(amplitudeIdentifyStub, 2)

      amplitudeTrackStub.resetHistory()
      amplitudeIdentifyStub.resetHistory()
    })
  })

  it('should upload wall video', async () => {
    const user = await generateRandomUser()

    const updatedUser = await client(user.tokens.accessToken).updateUser({
      input: { firstname: 'UploadVideo', lastname: 'Tester' },
    })
    const wallURL = `${config.webUrl}${updatedUser.updateUser.user?.slug}/wall`.toLowerCase()
    const s3RequestPresignerstub = await sinon
      .stub(s3RequestPresigner, 'getSignedUrl')
      .resolves('presignedUrl')
    const response = await client(user.tokens.accessToken).uploadWallVideo({
      input: {
        data: {
          duration: 30,
          height: 300,
          size: 100,
          title: 'test',
          type: WallVideoType.Defensive,
          width: 500,
          repeat: {
            start: 1,
            end: 4,
          },
          segmentation: {
            frame: 0,
            selectionY: 200,
            selectionX: 350,
          },
        },
      },
    })
    const responseCofirmation = await client(user.tokens.accessToken).uploadWallVideo({
      input: {
        confirmation: {
          id: response.uploadWallVideo.wallVideo?.id,
          uploadSuccessful: true,
        },
      },
    })
    const dbWallVideo = await WallVideoModel.findById(response.uploadWallVideo.wallVideo?.id)
    assert.strictEqual(dbWallVideo?.segmentation.selectionX, 350)
    assert.strictEqual(dbWallVideo?.segmentation.selectionY, 200)
    assert.strictEqual(dbWallVideo?.segmentation.frame, 0)
    assert.strictEqual(dbWallVideo?.repeat.start, 1)
    assert.strictEqual(dbWallVideo?.repeat.end, 4)
    assert.strictEqual(response.uploadWallVideo.preSignedUrl, 'presignedUrl')
    assert.strictEqual(response.uploadWallVideo.errors, null)
    assert.strictEqual(response.uploadWallVideo.wallVideo?.type, WallVideoType.Defensive)
    assert.strictEqual(response.uploadWallVideo.wallVideo?.title, 'test')
    assert.strictEqual(response.uploadWallVideo.wallVideo?.duration, 30)
    assert.strictEqual(response.uploadWallVideo.wallVideo?.size, 100)
    assert.strictEqual(response.uploadWallVideo.wallVideo?.width, 500)
    assert.strictEqual(response.uploadWallVideo.wallVideo?.height, 300)

    assert.strictEqual(dbWallVideo?.streamFeedActivityId, streamFeedMockActivityId)

    assert.strictEqual(responseCofirmation.uploadWallVideo.errors, null)
    assert.strictEqual(responseCofirmation.uploadWallVideo.wallVideo?.type, WallVideoType.Defensive)

    assert.strictEqual(s3RequestPresignerstub.calledTwice, true)
    const firstCall = s3RequestPresignerstub.getCall(0)
    sinon.assert.match(
      firstCall.args[1],
      sinon.match({
        input: sinon.match({
          Bucket: 'graet-staging-assets',
          Key: `videos/${updatedUser.updateUser.user?.id}/wall-video/${response.uploadWallVideo.wallVideo?.id}.mp4`,
          ContentType: 'video/mp4',
        }),
      })
    )
    sinon.assert.calledOnce(slackClientStub.chat.postMessage)
    sinon.assert.calledWith(
      slackClientStub.chat.postMessage,
      sinon.match.has('channel', 'db-stage')
    )
    sinon.assert.calledWith(
      slackClientStub.chat.postMessage,
      sinon.match.has(
        'text',
        sinon.match(
          `User ${updatedUser.updateUser.user?.name} uploaded new wall video (${response.uploadWallVideo.wallVideo?.id}), ${wallURL}`
        )
      )
    )
    sinon.assert.calledOnce(sqsClientStub.send)
    sinon.assert.calledWith(
      sqsClientStub.send,
      sinon.match.has(
        'input',
        sinon.match.has(
          'QueueUrl',
          'https://sqs.eu-west-1.amazonaws.com/845702489680/VideoSegmentation-staging.fifo'
        )
      )
    )
    sinon.assert.calledWith(
      sqsClientStub.send,
      sinon.match.has(
        'input',
        sinon.match.has(
          'MessageBody',
          sinon.match(
            JSON.stringify({
              videoId: responseCofirmation.uploadWallVideo.wallVideo.id,
              userId: user.user._id,
            })
          )
        )
      )
    )
    sqsClientStub.send.resetHistory()
    slackClientStub.chat.postMessage.resetHistory()

    sinon.assert.calledThrice(amplitudeTrackStub)
    sinon.assert.calledWith(
      amplitudeTrackStub.getCall(0),
      'User Updated',
      sinon.match({}),
      sinon.match({ user_id: user.user._id.toString() })
    )

    sinon.assert.calledWith(
      amplitudeTrackStub.getCall(1),
      'clipVideoUploaded',
      sinon.match({
        category: 'DEFENSIVE',
      }),
      sinon.match({
        user_id: user.user._id.toString(),
      })
    )

    sinon.assert.calledWith(
      amplitudeTrackStub.getCall(2),
      'User Updated',
      sinon.match({}),
      sinon.match({ user_id: user.user._id.toString() })
    )

    sinon.assert.callCount(amplitudeIdentifyStub, 4)

    amplitudeTrackStub.resetHistory()
    amplitudeIdentifyStub.resetHistory()

    s3RequestPresignerstub.restore()
  })

  it('throw error if user not found', async () => {
    const user = await generateRandomUser()

    await UserModel.deleteOne({ _id: user.user._id })

    try {
      await client(user.tokens.accessToken).uploadWallVideo({
        input: {
          data: {
            duration: 30,
            height: 300,
            size: 100,
            title: 'test',
            type: WallVideoType.Defensive,
            width: 500,
          },
        },
      })
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(err.response.errors[0].message, 'User not found')
    }
  })

  it('should throw error if failed to create presigned url', async () => {
    const user = await generateRandomUser()

    const s3RequestPresignerstub = await sinon
      .stub(s3RequestPresigner, 'getSignedUrl')
      .throws("Failed to create preSignedUrl for users' avatar:")

    const response = await client(user.tokens.accessToken).uploadWallVideo({
      input: {
        data: {
          duration: 0,
          height: 0,
          size: 0,
          title: 'error',
          type: WallVideoType.Defensive,
          width: 0,
        },
      },
    })

    assert.strictEqual(
      response.uploadWallVideo.errors && response.uploadWallVideo.errors[0].message,
      'Failed to create preSignedUrl'
    )

    assert.strictEqual(s3RequestPresignerstub.calledOnce, true)

    s3RequestPresignerstub.restore()
  })

  it('should delete video if upload was unsuccesfull ', async () => {
    const user = await generateRandomUser()

    const s3RequestPresignerstub = await sinon
      .stub(s3RequestPresigner, 'getSignedUrl')
      .resolves('presignedUrl')

    const response = await client(user.tokens.accessToken).uploadWallVideo({
      input: {
        data: {
          duration: 30,
          height: 300,
          size: 100,
          title: 'test',
          type: WallVideoType.Defensive,
          width: 500,
        },
      },
    })

    const responseConfirmation = await client(user.tokens.accessToken).uploadWallVideo({
      input: {
        confirmation: {
          id: response.uploadWallVideo.wallVideo?.id,

          uploadSuccessful: false,
        },
      },
    })

    const wallVideo = await client(user.tokens.accessToken).wallVideos({
      filter: { userId: response.uploadWallVideo.wallVideo?.id },
    })

    assert.strictEqual(wallVideo.wallVideos.categoryCounts?.length, 0)
    assert.strictEqual(wallVideo.wallVideos.edges.length, 0)
    assert.strictEqual(responseConfirmation.uploadWallVideo.errors, null)

    s3RequestPresignerstub.restore()
  })

  describe('Wall video segmentation', () => {
    it('should upload wall video with segmentation', async () => {
      const user = await generateRandomUser()

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const wallVideo = await WallVideoModel.create({
        type: WallVideoType.Defensive,
        title: 'test',
        path: 'path',
        user: user.user._id,
      })

      await client(user.tokens.accessToken).uploadWallVideoSegmentation({
        input: {
          id: wallVideo.id,
        },
      })

      // and confirm upload
      await client(user.tokens.accessToken).uploadWallVideoSegmentation({
        input: {
          id: wallVideo.id,
          confirmation: {
            segmentationSuccessful: true,
          },
        },
      })

      const updatedWallVideo = await WallVideoModel.findById(wallVideo.id)

      sinon.assert.calledOnce(s3RequestPresignerstub)
      const firstCall = s3RequestPresignerstub.getCall(0)
      sinon.assert.match(
        firstCall.args[1],
        sinon.match({
          input: sinon.match({
            Bucket: 'graet-staging-assets',
            Key: `videos/${user.user._id}/wall-video/${wallVideo.id}-segmentation.json`,
            ContentType: 'application/json',
          }),
        })
      )

      assert.strictEqual(updatedWallVideo?.segmentation.finished, true)
      assert.strictEqual(
        updatedWallVideo?.segmentation.path,
        `videos/${user.user._id}/wall-video/${wallVideo.id}-segmentation.json`
      )
    })

    it('should return error if wall video not found', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).uploadWallVideoSegmentation({
        input: {
          id: '5f4b2d8e1f4c1d001f9e8b9b',
        },
      })

      assert.strictEqual(response.uploadWallVideoSegmentation.errors?.length, 1)
      assert.strictEqual(
        response.uploadWallVideoSegmentation.errors[0].message,
        'Wall video not found'
      )

      const response2 = await client(user.tokens.accessToken).uploadWallVideoSegmentation({
        input: {
          id: '5f4b2d8e1f4c1d001f9e8b9b',
          confirmation: {
            segmentationSuccessful: true,
          },
        },
      })

      assert.strictEqual(response2.uploadWallVideoSegmentation.errors?.length, 1)
      assert.strictEqual(
        response2.uploadWallVideoSegmentation.errors[0].message,
        'Wall video not found'
      )
    })

    it('should return error if find and update fails', async () => {
      const user = await generateRandomUser()

      const findByIdAndUpdateStub = sinon.stub(WallVideoModel, 'findOneAndUpdate').throws('Error')

      const response = await client(user.tokens.accessToken).uploadWallVideoSegmentation({
        input: {
          id: new Types.ObjectId(),
        },
      })

      assert.strictEqual(findByIdAndUpdateStub.calledOnce, true)
      assert.strictEqual(response.uploadWallVideoSegmentation.errors?.length, 1)
      assert.strictEqual(
        response.uploadWallVideoSegmentation.errors[0].message,
        'Failed to edit wall video segmentation'
      )
    })

    it('should only log error and return null values if confirmation is false', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).uploadWallVideoSegmentation({
        input: {
          id: new Types.ObjectId(),
          confirmation: {
            segmentationSuccessful: false,
          },
        },
      })

      assert.strictEqual(response.uploadWallVideoSegmentation.errors, null)
      assert.strictEqual(response.uploadWallVideoSegmentation.wallVideo, null)
    })
  })

  describe('Edit wall video category', () => {
    it('should edit wall video category', async () => {
      const user = await generateRandomUser()

      const video = await generateWallVideo({ user: user.user, path: 'video1' })
      assert.strictEqual(video.type, WallVideoType.Offensive)

      const response = await client(user.tokens.accessToken).editWallVideoCategory({
        input: {
          id: video._id,
          type: WallVideoType.Defensive,
        },
      })

      assert.strictEqual(response.editWallVideoCategory.errors, null)
      assert.strictEqual(response.editWallVideoCategory.wallVideo?.type, WallVideoType.Defensive)
    })

    it('should return error if wall video not found', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).editWallVideoCategory({
        input: {
          id: new Types.ObjectId(),
          type: WallVideoType.Defensive,
        },
      })

      assert.strictEqual(response.editWallVideoCategory.errors?.length, 1)
      assert.strictEqual(response.editWallVideoCategory.errors[0].message, 'Wall video not found')
    })

    it('should return error if find and update fails', async () => {
      const user = await generateRandomUser()

      const findByIdAndUpdateStub = sinon.stub(WallVideoModel, 'findOneAndUpdate').throws('Error')

      const video = await generateWallVideo({ user: user.user, path: 'video1' })

      const response = await client(user.tokens.accessToken).editWallVideoCategory({
        input: {
          id: video._id,
          type: WallVideoType.Defensive,
        },
      })

      assert.strictEqual(findByIdAndUpdateStub.calledOnce, true)
      assert.strictEqual(response.editWallVideoCategory.errors?.length, 1)
      assert.strictEqual(
        response.editWallVideoCategory.errors[0].message,
        'Failed to edit wall video category'
      )
    })
  })

  describe('Delete wall video', () => {
    it('should throw error if wall video not found', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).deleteWallVideo({
        id: new Types.ObjectId().toString(),
      })

      assert.strictEqual(response.deleteWallVideo.errors?.length, 1)
      assert.strictEqual(response.deleteWallVideo.errors[0].message, 'Wall video not found')
    })

    it('should throw error if user not found', async () => {
      const user = await generateRandomUser()
      const video = await generateWallVideo({ user: user.user, path: 'video1' })

      await UserModel.deleteOne({ _id: user.user._id })

      try {
        await client(user.tokens.accessToken).deleteWallVideo({
          id: video._id.toString(),
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User not found')
      }
    })

    it("should return error if delete video fails and don't delete video", async () => {
      const user = await generateRandomUser()

      const video = await generateWallVideo({ user: user.user, path: 'video1' })

      const deleteOneStub = sinon.stub(WallVideoModel, 'deleteOneByUser').throws('Error')

      const response = await client(user.tokens.accessToken).deleteWallVideo({
        id: video._id.toString(),
      })

      assert.strictEqual(deleteOneStub.calledOnce, true)
      assert.strictEqual(response.deleteWallVideo.errors?.length, 1)
      assert.strictEqual(response.deleteWallVideo.errors[0].message, 'Failed to delete wall video')
      assert.strictEqual(response.deleteWallVideo.success, false)

      deleteOneStub.restore()
    })
  })

  describe('Latest clips', () => {
    beforeEach(async () => {
      await WallVideoModel.deleteMany({})
      await TeamModel.deleteMany({})
    })

    it('should return latest clips for all players if there are not filter values', async () => {
      const team = await generateRandomTeam({ name: 'Testing latest clips team' })
      const user = await generateRandomUser({ team })
      const user2 = await generateRandomUser()

      await generateWallVideo({ user: user.user, path: 'video1' })
      await generateWallVideo({ user: user2.user, path: 'video2' })

      const response = await client(user.tokens.accessToken).latestClips()

      assert.strictEqual(response.latestClips.edges.length, 2)
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video2')
      assert.strictEqual(response.latestClips.edges[0].node.user?.name, user2.user.name)

      assert.strictEqual(response.latestClips.edges[1].node.path, 'video1')
      assert.strictEqual(response.latestClips.edges[1].node.user?.name, user.user.name)
      assert.strictEqual(response.latestClips.edges[1].node.user.currentTeam!.name, team.name)
    })

    it('should return latest clips for all players from specific team', async () => {
      const team = await generateRandomTeam({ name: 'Testing latest clips team' })
      const user = await generateRandomUser({ team })
      const user2 = await generateRandomUser()

      await generateWallVideo({ user: user.user, path: 'video1' })
      await generateWallVideo({ user: user2.user, path: 'video2' })

      const response = await client(user.tokens.accessToken).latestClips({
        filter: {
          teamId: team.id,
        },
      })

      assert.strictEqual(response.latestClips.edges.length, 1)
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video1')
    })

    it('should return latest clips for all players from specific agency', async () => {
      const agency = await generateRandomAgency('Testing latest clips agency')
      const user = await generateRandomUser({ agency: agency.id })
      const user2 = await generateRandomUser()

      await generateWallVideo({ user: user.user, path: 'video1' })
      await generateWallVideo({ user: user2.user, path: 'video2' })

      const response = await client(user.tokens.accessToken).latestClips({
        filter: {
          agencyId: agency.id,
        },
      })

      assert.strictEqual(response.latestClips.edges.length, 1)
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video1')
    })

    it('should return latest clips with pagination', async () => {
      const user = await generateRandomUser()

      for (let i = 0; i < 20; i++) {
        await generateWallVideo({ user: user.user, path: `video${i}` })
      }

      const response = await client(user.tokens.accessToken).latestClips({
        first: 2,
      })

      assert.strictEqual(response.latestClips.edges.length, 2)
      assert.strictEqual(response.latestClips.pageInfo.hasNextPage, true)
      assert.strictEqual(response.latestClips.pageInfo.hasPreviousPage, false)

      const response2 = await client(user.tokens.accessToken).latestClips({
        first: 2,
        after: response.latestClips.pageInfo.endCursor,
      })

      assert.strictEqual(response2.latestClips.edges.length, 2)
      assert.strictEqual(response2.latestClips.pageInfo.hasNextPage, true)
      assert.strictEqual(response2.latestClips.pageInfo.hasPreviousPage, true)

      // load rest
      const response3 = await client(user.tokens.accessToken).latestClips({
        first: 20,
        after: response2.latestClips.pageInfo.endCursor,
      })

      assert.strictEqual(response3.latestClips.edges.length, 16)
      assert.strictEqual(response3.latestClips.pageInfo.hasNextPage, false)
      assert.strictEqual(response3.latestClips.pageInfo.hasPreviousPage, true)
    })

    it('should return latest clips filtered by positions', async () => {
      const user1 = await generateRandomUser({ position: BioPosition.CENTER })
      const user2 = await generateRandomUser({ position: BioPosition.GOALTENDER })
      const user3 = await generateRandomUser({ position: BioPosition.RIGHT_WING })

      await generateWallVideo({
        user: user1.user,
        path: 'video1',
        createdAt: new Date('2025-01-03'),
      })
      await generateWallVideo({
        user: user2.user,
        path: 'video2',
        createdAt: new Date('2025-01-02'),
      })
      await generateWallVideo({
        user: user3.user,
        path: 'video3',
        createdAt: new Date('2025-01-01'),
      })

      const response = await client(user1.tokens.accessToken).latestClips({
        filter: { positions: [BioPositionGraphQL.Center, BioPositionGraphQL.RightWing] },
      })

      assert.strictEqual(response.latestClips.edges.length, 2)
      // newest first
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video1')
      assert.strictEqual(response.latestClips.edges[1].node.path, 'video3')

      // EMPTY
      const response2 = await client(user1.tokens.accessToken).latestClips({
        filter: { positions: [BioPositionGraphQL.LeftWing] },
      })

      assert.strictEqual(response2.latestClips.edges.length, 0)
    })

    it('should handle positions Defender', async () => {
      const user1 = await generateRandomUser({ position: BioPosition.RIGHT_DEFENSIVE })
      const user2 = await generateRandomUser({ position: BioPosition.LEFT_DEFENSIVE })
      const user3 = await generateRandomUser({ position: BioPosition.DEFENDER })

      await generateWallVideo({
        user: user1.user,
        path: 'video1',
        createdAt: new Date('2025-01-03'),
      })
      await generateWallVideo({
        user: user2.user,
        path: 'video2',
        createdAt: new Date('2025-01-02'),
      })
      await generateWallVideo({
        user: user3.user,
        path: 'video3',
        createdAt: new Date('2025-01-01'),
      })

      const response = await client(user1.tokens.accessToken).latestClips({
        filter: {
          positions: [BioPositionGraphQL.Defender],
        },
      })

      assert.strictEqual(response.latestClips.edges.length, 3)
      // newest first
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video1')
      assert.strictEqual(response.latestClips.edges[1].node.path, 'video2')
      assert.strictEqual(response.latestClips.edges[2].node.path, 'video3')

      // EMPTY
      const response2 = await client(user1.tokens.accessToken).latestClips({
        filter: {
          positions: [BioPositionGraphQL.Center],
        },
      })

      assert.strictEqual(response2.latestClips.edges.length, 0)
    })

    it('should return latest clips filtered by year of birth, 2005 earlier', async () => {
      const user1 = await generateRandomUser({ dateOfBirth: new Date('2008-06-15') })
      const user2 = await generateRandomUser({ dateOfBirth: new Date('1997-03-25') })
      const user3 = await generateRandomUser({ dateOfBirth: new Date('1995-03-25') })

      await generateWallVideo({
        user: user1.user,
        path: 'video1',
        createdAt: new Date('2025-01-03'),
      })
      await generateWallVideo({
        user: user2.user,
        path: 'video2',
        createdAt: new Date('2025-01-02'),
      })
      await generateWallVideo({
        user: user3.user,
        path: 'video3',
        createdAt: new Date('2025-01-01'),
      })

      const response = await client(user1.tokens.accessToken).latestClips({
        filter: { yearOfBirth: [2007, 2005] },
      })

      assert.strictEqual(response.latestClips.edges.length, 2)
      // newest first
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video2')
      assert.strictEqual(response.latestClips.edges[1].node.path, 'video3')

      // EMPTY
      const response2 = await client(user1.tokens.accessToken).latestClips({
        filter: { yearOfBirth: [2007, 2006] },
      })

      assert.strictEqual(response2.latestClips.edges.length, 0)
    })

    it('should return latest clips filtered by countries', async () => {
      const user1 = await generateRandomUser({ country: 'US' })
      const user2 = await generateRandomUser({ country: 'CA' })

      await generateWallVideo({ user: user1.user, path: 'video1' })
      await generateWallVideo({ user: user2.user, path: 'video2' })

      const response = await client(user1.tokens.accessToken).latestClips({
        filter: { countries: ['US'] },
      })

      assert.strictEqual(response.latestClips.edges.length, 1)
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video1')

      // EMPTY
      const response2 = await client(user1.tokens.accessToken).latestClips({
        filter: { countries: ['SK'] },
      })

      assert.strictEqual(response2.latestClips.edges.length, 0)
    })

    it('should handle AND filters correctly', async () => {
      const user1 = await generateRandomUser({
        position: BioPosition.CENTER,
        dateOfBirth: new Date('2000-06-15'),
        country: 'US',
      })
      const user2 = await generateRandomUser({
        position: BioPosition.GOALTENDER,
        dateOfBirth: new Date('1995-03-25'),
        country: 'CA',
      })
      const user3 = await generateRandomUser({
        position: BioPosition.LEFT_DEFENSIVE,
        dateOfBirth: new Date('2000-06-15'),
        country: 'US',
      })

      await generateWallVideo({ user: user1.user, path: 'video1' })
      await generateWallVideo({ user: user2.user, path: 'video2' })
      await generateWallVideo({ user: user3.user, path: 'video3' })

      const response = await client(user1.tokens.accessToken).latestClips({
        filter: {
          positions: [BioPositionGraphQL.Center],
          yearOfBirth: [2000],
          countries: ['US'],
        },
      })

      assert.strictEqual(response.latestClips.edges.length, 1)
      assert.strictEqual(response.latestClips.edges[0].node.path, 'video1')

      // EMPTY
      const response2 = await client(user1.tokens.accessToken).latestClips({
        filter: {
          positions: [BioPositionGraphQL.Center],
          yearOfBirth: [2001],
          countries: ['US'],
        },
      })

      assert.strictEqual(response2.latestClips.edges.length, 0)
    })
  })
})
