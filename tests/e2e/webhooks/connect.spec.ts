import assert from 'assert'
import { FastifyReply, FastifyRequest } from 'fastify'
import sinon from 'sinon'

import { UserModel } from '../../../src/modules/user/models'
import { PushNotificationType } from '../../../src/services/pushNotification'
import { WebhookConnect } from '../../../src/webhooks/connect'
import { generateRandomUser } from '../../utils/users'
import { pushNotificationServiceStub, stripeClientStub } from '..'

describe('Stripe Connect webhooks', () => {
  it('should handle account.updated event', async () => {
    const user = await generateRandomUser()

    const stripeAccountId = 'acct_1JH2XTIyNTgGDV2l'
    await UserModel.updateOne(
      { _id: user.user._id },
      {
        $set: {
          stripeAccountId: stripeAccountId,
        },
      }
    )

    const request = {
      body: {},
      headers: {
        'stripe-signature': 't=**********,v1=6f2c3e8a9b0d8d2e6f3c5b5f0a2b4c1b4a2',
      },
    } as unknown as FastifyRequest

    const response = {
      status: sinon.stub().resolves(() => ({
        send: sinon.stub(),
      })),
      send: sinon.stub(),
    } as unknown as FastifyReply

    stripeClientStub.webhooks.constructEvent.returns({
      type: 'account.updated',
      data: {
        object: {
          id: stripeAccountId,
          requirements: {
            currently_due: ['individual.verification.document'],
            pending_verification: ['individual.verification.document'],
          },
        },
      },
    })

    const webhookConnect = new WebhookConnect()

    await webhookConnect.handle(request, response)

    const constructEvent = stripeClientStub.webhooks.constructEvent.getCall(0).args
    sinon.assert.calledOnce(stripeClientStub.webhooks.constructEvent)
    assert.strictEqual(constructEvent[1], 't=**********,v1=6f2c3e8a9b0d8d2e6f3c5b5f0a2b4c1b4a2')

    const pushNotification = pushNotificationServiceStub.sendPushNotification.getCall(0).args

    sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)
    assert.strictEqual(pushNotification[0].name, user.user.name)
    assert.strictEqual(pushNotification[1].message, 'Your account needs verification')
    assert.strictEqual(pushNotification[1].title, 'Boost has been paused')
    assert.strictEqual(pushNotification[1].type, PushNotificationType.STRIPE_ACCOUNT_VERIFICATION)
    assert.strictEqual(pushNotification[1].params.destinationPath, `/boosts`)
    assert.strictEqual(pushNotification[1].forcePush, true)

    const updatedUser = await UserModel.findById(user.user._id)
    assert.strictEqual(updatedUser?.stripeAccountId, stripeAccountId)
    assert.strictEqual(updatedUser?.verificationPending, true)
    assert.strictEqual(updatedUser?.verificationNeeded, true)
    assert.strictEqual(updatedUser?.accountUpdateErrors?.[0], undefined)
  })
})
