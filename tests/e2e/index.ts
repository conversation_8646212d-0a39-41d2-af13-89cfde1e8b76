import nock from 'nock'
import puppeteer, { <PERSON> } from 'puppeteer-core'
import sinon, { SinonStubbedInstance } from 'sinon'
import { StreamChat } from 'stream-chat'
import Container from 'typedi'

import { getMongoDBConnection } from '../../src/helpers/mongoose.connection'

// set node env variable, we need to set it before boot import
process.env['SYNC_INDEXES'] = 'true'

import axios, { AxiosInstance } from 'axios'

import { apolloServer, boot } from '../../src'
import { cacheStorage } from '../../src/container'
import { Scraper } from '../../src/modules/stats/services/scraper/scraper'
import UserStatsSyncerService from '../../src/modules/stats/services/userStatsSyncer.service'
import { PushNotificationService } from '../../src/services/pushNotification'
import { S3Service } from '../../src/services/s3'
import { SNSService } from '../../src/services/sns'
import { AxiosFetcher } from '../../src/services/webcrawler/fetchers/axiosFetcher'
import { Fetcher } from '../../src/services/webcrawler/fetchers/fetcher'
import { PuppeteerFetcher } from '../../src/services/webcrawler/fetchers/puppeteerFetcher'
import { generateRandomAgency, generateRandomTeam } from '../utils/users'

import * as Amplitude from '@amplitude/analytics-node'
import { ChatOpenAI, OpenAIEmbeddings } from '@langchain/openai'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let slackClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let sesClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let googleServiceStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let s3ClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let snsClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let stripeClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let mediaConverterClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let sqsClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let amplitudeIdentifyStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let amplitudeTrackStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let openaiClientStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let pushNotificationServiceStub: any
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export let emailServiceStub: any

export let chatOpenAIStub: SinonStubbedInstance<typeof ChatOpenAI>
export let chatOpenAIInvokeStub: sinon.SinonStub

export let evaluateStub: sinon.SinonStub
export let puppeteerPageContentStub: sinon.SinonStub
export let puppeteerStub: sinon.SinonStub

export let streamFeedFollowStub: sinon.SinonStub
export let streamFeedUnfollowStub: sinon.SinonStub
export let streamFeedAddActivityStub: sinon.SinonStub
export let streamFeedRemoveActivityStub: sinon.SinonStub
// StreamChat.getInstance().upsertUsers
export let streamChatupsertUsers: sinon.SinonStub
// StreamChat.getInstance().createToken
export let streamChatcreateToken: sinon.SinonStub
//StreamFeed createUserToken
export let streamFeedCreateToken: sinon.SinonStub
// this.getStreamChatClient.updateAppSettings
export let updateAppSettingsStub: sinon.SinonStub
export let axiosGetStub: sinon.SinonStub

export const streamFeedMockToken = 'streamFeedMockToken'
export const streamFeedMockActivityId = 'streamFeedMockActivityId'

before(async () => {
  await boot

  await generateRandomTeam()
  await generateRandomAgency()

  nock.disableNetConnect()
  nock.enableNetConnect('localhost')

  // axios stub
  axiosGetStub = sinon.stub()

  sinon.stub(axios, 'create').returns({
    head: sinon.stub().resolves({
      headers: {
        'content-type': 'text/html',
      },
    }),
    get: axiosGetStub,
  } as unknown as AxiosInstance)

  slackClientStub = sinon.stub()
  slackClientStub.chat = {
    postMessage: sinon.stub(),
  }

  Container.set('slack.client', slackClientStub)

  // stub SES client
  sesClientStub = sinon.stub()
  sesClientStub.send = sinon.stub()

  Container.set('ses.client', sesClientStub)

  googleServiceStub = sinon.stub()
  googleServiceStub.verifyIdToken = sinon.stub()
  Container.set('google.oauth2', googleServiceStub)

  s3ClientStub = sinon.stub()
  s3ClientStub.send = sinon.stub()
  Container.set('s3.client', s3ClientStub)

  const s3Service = new S3Service()
  Container.set({ id: 's3.service', factory: () => s3Service })

  // stub sns client
  snsClientStub = sinon.stub()
  snsClientStub.createPlatformEndpoint = sinon.stub().resolves({
    EndpointArn: 'arn:aws:sns:us-east-1:123456789012:endpoint/APNS/MyApplication/',
  })

  snsClientStub.publish = sinon.stub()
  snsClientStub.deleteEndpoint = sinon.stub()
  Container.set('sns.client', snsClientStub)

  const snsService = new SNSService()
  Container.set('sns.service', snsService)

  // stub sqs client
  sqsClientStub = sinon.stub()
  sqsClientStub.send = sinon.stub()
  Container.set('sqs.client', sqsClientStub)

  stripeClientStub = sinon.stub()
  stripeClientStub.customers = { create: sinon.stub().resolves({ id: 'customerId' }) }
  stripeClientStub.paymentMethodDomains = {
    create: sinon.stub().resolves({}),
  }
  stripeClientStub.ephemeralKeys = {
    create: sinon.stub().resolves({ secret: 'empheralKeySecret' }),
  }
  stripeClientStub.paymentIntents = {
    create: sinon.stub().resolves({ id: 'paymentID', client_secret: 'paymentClientSecret' }),
  }

  stripeClientStub.accounts = {
    create: sinon.stub(),
    update: sinon.stub(),
    createExternalAccount: sinon.stub(),
    updateExternalAccount: sinon.stub(),
    deleteExternalAccount: sinon.stub(),
    retrieve: sinon.stub(),
    retrieveExternalAccount: sinon.stub(),
    deleteStripeExternalAccount: sinon.stub(),
  }

  stripeClientStub.balance = {
    retrieve: sinon.stub(),
  }

  stripeClientStub.payouts = {
    list: sinon.stub(),
  }

  stripeClientStub.webhooks = {
    constructEvent: sinon.stub(),
  }

  Container.set('stripe.client', stripeClientStub)

  mediaConverterClientStub = sinon.stub()
  mediaConverterClientStub.send = sinon.stub()

  Container.set('mediaConverter.client', mediaConverterClientStub)

  chatOpenAIStub = sinon.stub(ChatOpenAI)
  chatOpenAIStub.prototype.withStructuredOutput = sinon.stub().returnsThis()
  chatOpenAIStub.prototype.invoke = sinon.stub()

  chatOpenAIInvokeStub = chatOpenAIStub.prototype.invoke as sinon.SinonStub

  const openAIEmbeddingsStub = sinon.stub(OpenAIEmbeddings)
  openAIEmbeddingsStub.prototype.embedQuery = sinon.stub().resolves([0.1, 0.2, 0.3])

  evaluateStub = sinon.stub().resolves({})
  puppeteerPageContentStub = sinon.stub().resolves('')
  puppeteerStub = sinon.stub(puppeteer, 'launch').resolves({
    newPage: async () => {
      return {
        on: async () => {},
        goto: async () => {},
        exposeFunction: async () => {},
        evaluate: evaluateStub,
        close: async () => {},
        authenticate: async () => {},
        content: puppeteerPageContentStub,
        setRequestInterception: async () => {},
      } as unknown as Page
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } as any)

  const puppeteerFetcher = new PuppeteerFetcher()
  await puppeteerFetcher.setup()
  Container.set({ id: 'puppeteer.fetcher', factory: () => puppeteerFetcher })

  const axiosFetcher = new AxiosFetcher()
  Container.set({ id: 'axios.fetcher', factory: () => axiosFetcher })

  const fetcher = new Fetcher()
  Container.set({ id: 'fetcher', factory: () => fetcher })

  const scraperService = new Scraper()
  Container.set({ id: 'scraper', factory: () => scraperService })

  const syncer = new UserStatsSyncerService()
  Container.set({ id: 'userStatsSyncer.service', factory: () => syncer })

  pushNotificationServiceStub = sinon.stub(PushNotificationService)
  pushNotificationServiceStub.sendPushNotification = sinon.stub().resolves()

  Container.set('pushNotification.service', pushNotificationServiceStub)

  // StreamChat.getInstance().upsertUsers
  streamChatupsertUsers = sinon.stub()
  streamChatcreateToken = sinon.stub().returns(streamFeedMockToken)
  updateAppSettingsStub = sinon.stub().resolves(true)
  const streamChatStub = sinon.stub(StreamChat, 'getInstance').returns({
    upsertUsers: streamChatupsertUsers,
    createToken: streamChatcreateToken,
    updateAppSettings: updateAppSettingsStub,
    verifyWebhook: sinon.stub().returns(true),
  } as unknown as StreamChat)

  Container.set('streamChat.client', streamChatStub('11', '22'))

  // StreamFeed
  streamFeedCreateToken = sinon.stub().returns(streamFeedMockToken)
  streamFeedFollowStub = sinon.stub().resolves(true)
  streamFeedUnfollowStub = sinon.stub().resolves(true)
  streamFeedAddActivityStub = sinon.stub().resolves({ id: streamFeedMockActivityId })
  streamFeedRemoveActivityStub = sinon.stub().resolves(true)
  Container.set('streamFeed.client', {
    feed: sinon.stub().returns({
      follow: streamFeedFollowStub,
      unfollow: streamFeedUnfollowStub,
      addActivity: streamFeedAddActivityStub,
      removeActivity: streamFeedRemoveActivityStub,
    }),
    createUserToken: streamFeedCreateToken,
  })

  // Email service stub
  emailServiceStub = {
    sendEmail: sinon.stub().resolves(),
    sendSimpleEmail: sinon.stub().resolves(),
  }
  Container.set('email.service', emailServiceStub)
})

beforeEach(async () => {
  amplitudeTrackStub = sinon.stub(Amplitude, 'track').returns({
    promise: new Promise((resolves) => {
      resolves({ code: 200, event: { event_type: 'track' }, message: '' })
    }),
  })

  amplitudeIdentifyStub = sinon.stub(Amplitude, 'identify').returns({
    promise: new Promise((resolves) => {
      resolves({ code: 200, event: { event_type: 'identify' }, message: '' })
    }),
  })

  await cacheStorage.clear()
})

afterEach(() => {
  slackClientStub.chat.postMessage.resetHistory()
  sesClientStub.send.resetHistory()
  s3ClientStub.send.resetHistory()
  snsClientStub.createPlatformEndpoint.resetHistory()
  snsClientStub.publish.resetHistory()
  snsClientStub.deleteEndpoint.resetHistory()
  stripeClientStub.customers.create.resetHistory()
  stripeClientStub.paymentMethodDomains.create.resetHistory()
  stripeClientStub.ephemeralKeys.create.resetHistory()
  stripeClientStub.paymentIntents.create.resetHistory()
  stripeClientStub.balance.retrieve.resetHistory()
  stripeClientStub.payouts.list.resetHistory()
  stripeClientStub.accounts.create.resetHistory()
  stripeClientStub.accounts.update.resetHistory()
  stripeClientStub.accounts.createExternalAccount.resetHistory()
  stripeClientStub.accounts.updateExternalAccount.resetHistory()
  stripeClientStub.accounts.deleteExternalAccount.resetHistory()
  stripeClientStub.accounts.retrieve.resetHistory()
  stripeClientStub.accounts.retrieveExternalAccount.resetHistory()
  stripeClientStub.accounts.deleteStripeExternalAccount.resetHistory()
  sqsClientStub.send.resetHistory()
  mediaConverterClientStub.send.resetHistory()
  googleServiceStub.verifyIdToken.resetHistory()
  chatOpenAIInvokeStub.resetHistory()
  pushNotificationServiceStub.sendPushNotification.resetHistory()
  axiosGetStub.resetHistory()
  puppeteerPageContentStub.reset()

  sinon.restore()
  amplitudeTrackStub.restore()
  amplitudeIdentifyStub.restore()
  // StreamChat.getInstance().upsertUsers
  streamChatupsertUsers.resetHistory()
  // StreamChat.getInstance().createToken
  streamChatcreateToken.resetHistory()

  streamFeedFollowStub.resetHistory()
  streamFeedUnfollowStub.resetHistory()
  streamFeedAddActivityStub.resetHistory()
  streamFeedRemoveActivityStub.resetHistory()

  // Reset email and slack service stubs
  emailServiceStub.sendEmail.resetHistory()
  emailServiceStub.sendSimpleEmail.resetHistory()
})

after(async () => {
  await apolloServer.stop()

  await getMongoDBConnection().db?.dropDatabase()
})
