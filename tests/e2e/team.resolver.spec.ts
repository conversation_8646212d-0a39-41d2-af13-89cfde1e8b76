import assert from 'assert'
import axios from 'axios'
import config from 'config'
import dayjs from 'dayjs'
import { readFileSync } from 'fs'
import { Types } from 'mongoose'
import sinon from 'sinon'

import * as s3RequestPresigner from '../../src/helpers/getSignedUrl'
import { CURRENT_SEASON } from '../../src/helpers/league'
import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../src/modules/stats/models/game/game'
import { LeagueModel } from '../../src/modules/stats/models/league'
import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { TeamModel } from '../../src/modules/stats/models/team'
import { TeamFollowModel } from '../../src/modules/stats/models/teamFollow'
import { TeamViewModel } from '../../src/modules/stats/models/teamView'
import { UserStatsModel } from '../../src/modules/stats/models/userStats'
import { Role } from '../../src/modules/user/models/user'
import { StatusType } from '../graphql/components'
import { client } from '../utils/graphql'
import { generateDiscoverUrl, generateRandomGameStats } from '../utils/stats'
import {
  generateRandomAdminUser,
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from '../utils/users'

import { s3ClientStub, streamFeedFollowStub, streamFeedUnfollowStub } from '.'

describe('Team resolver', () => {
  describe('team query', () => {
    it('should return error if team not found by organization slug', async () => {
      try {
        await client().team({
          pagination: {
            first: 1,
          },
          orgSlug: 'invalid-organization',
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Organization not found.')
      }
    })

    it('should return error if team not found', async () => {
      const invalidTeamId = new Types.ObjectId()

      const response = await client().team({
        pagination: {
          first: 1,
        },
        teamId: invalidTeamId,
      })

      assert.strictEqual(response.team.errors?.[0].message, 'Team not found')
    })

    it('should return team by organization and team short slug and org slug', async () => {
      const randomOrganization = await generateRandomOrganization('Test team organization')

      const randomTeam = await generateRandomTeam({
        name: 'Test team0',
        organization: randomOrganization.id,
        shortNameSlug: 'shortSlugtest',
      })

      await generateRandomUser({ firstname: 'Boris', lastname: 'Brejcha', team: randomTeam.id })

      const response = await client().team({
        pagination: {
          first: 1,
        },
        orgSlug: randomOrganization.slug,
        slug: randomTeam.shortNameSlug,
      })

      assert.strictEqual(response.team?.team?.name, 'Test team0')
      assert.strictEqual(response.team.team.id, randomTeam.id)
      assert.strictEqual(response.team.team.organization?.id, randomOrganization._id.toString())
      assert.strictEqual(response.team.team.organization?.name, 'Test team organization')
      assert.strictEqual(response.team.errors, null)
      assert.strictEqual(response.team.users?.pageInfo.hasNextPage, false)
      assert.strictEqual(response.team.users?.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.team.users?.pageInfo.totalCount, 1)
      assert.strictEqual(response.team.users?.edges[0].node.name, 'Boris Brejcha')
      assert.strictEqual(response.team.users?.edges[0].node.currentTeam?.name, 'Test team0')
    })

    it('should return paginated users from filtered team by id', async () => {
      const randomOrganization = await generateRandomOrganization('Test team organization1')

      const randomTeam = await generateRandomTeam({
        name: 'Test team1',
        organization: randomOrganization.id,
      })

      const firstRandomUser = await generateRandomUser({
        firstname: 'Trevor',
        lastname: 'Speed',
        team: randomTeam.id,
      })

      for (let i = 0; i < 9; i++) {
        await generateRandomUser({
          firstname: `Test user team ${i}`,
          lastname: `test-user-team${i}`,
          team: randomTeam.id,
        })
      }
      const lastRandomUser = await generateRandomUser({
        firstname: 'Chuck',
        lastname: 'Linde',
        team: randomTeam.id,
      })

      const firstPageResponse = await client().team({
        teamId: randomTeam.id,
      })
      assert.strictEqual(firstPageResponse.team?.team?.name, 'Test team1')
      assert.strictEqual(firstPageResponse.team.team.id, randomTeam.id)
      assert.strictEqual(
        firstPageResponse.team.team.organization?.id,
        randomTeam.organization._id.toString()
      )
      assert.strictEqual(firstPageResponse.team.team.organization?.name, 'Test team organization1')
      assert.strictEqual(firstPageResponse.team.errors, null)
      assert.strictEqual(firstPageResponse.team.users?.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPageResponse.team.users?.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPageResponse.team.users?.pageInfo.totalCount, 11)
      assert.strictEqual(firstPageResponse.team.users?.edges[0].node.name, 'Chuck Linde')
      assert.strictEqual(
        firstPageResponse.team.users?.edges[0].node.id,
        lastRandomUser.user._id.toString()
      )
      assert.strictEqual(firstPageResponse.team.users.edges[0].node.currentTeam?.name, 'Test team1')

      const secondPageResponse = await client().team({
        pagination: {
          first: 1,
          after: firstPageResponse.team.users?.pageInfo.endCursor,
        },
        orgSlug: randomOrganization.slug,
        slug: randomTeam.slug,
        teamId: randomTeam.id,
      })

      assert.strictEqual(secondPageResponse.team.users?.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPageResponse.team.users?.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPageResponse.team.users?.edges.length, 1)
      assert.strictEqual(secondPageResponse.team.users?.edges[0].node.name, 'Trevor Speed')
      assert.strictEqual(
        secondPageResponse.team.users?.edges[0].node.id,
        firstRandomUser.user._id.toString()
      )
      assert.strictEqual(
        secondPageResponse.team.users.edges[0].node.currentTeam?.name,
        'Test team1'
      )
    })
  })

  describe('teams query', () => {
    it('should return teams with number of players in edge', async () => {
      await TeamModel.deleteMany({})

      const org = await generateRandomOrganization('Edge Organization')
      const team = await generateRandomTeam({ name: 'Edge', organization: org.id })
      await generateRandomUser({
        firstname: 'Test',
        lastname: 'User',
        role: Role.PLAYER,
        team: team.id,
      })

      await new Promise((resolve) => setTimeout(resolve, 1500)) // TODO: how to wait for the index to be updated?

      const response = await client().teams({
        filter: {
          searchQuery: '',
        },
      })

      assert.strictEqual(response.teams?.edges.length, 1)
      assert.strictEqual(response.teams?.edges[0].node.name, 'Edge')
      assert.strictEqual(response.teams?.edges[0].playersCount, 1)
    })

    it('should return teams without organization when filter hasOrganization is false for admin', async () => {
      const admin = await generateRandomAdminUser()
      const teamWithoutOrg = await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'NoOrg Team',
          country: 'SK',
          shortName: 'no-org',
        },
      })

      assert.strictEqual(teamWithoutOrg.createTeam.team?.organization, null)

      const org = await generateRandomOrganization('WithOrg Organization')

      await generateRandomTeam({ name: 'WithOrg Team', organization: org.id })

      const response = await client(admin.tokens.accessToken).teams({
        filter: {
          hasOrganization: false,
        },
      })

      assert.strictEqual(response.teams?.edges.length, 1)
      assert.strictEqual(response.teams?.edges[0].node.name, 'NoOrg Team')
      assert.strictEqual(response.teams?.edges[0].node.organization, null)
      assert.strictEqual(response.teams?.edges[0].node.id, teamWithoutOrg.createTeam.team?.id)
    })

    it('should return teams filtered by country for admin', async () => {
      await TeamModel.deleteMany({})

      const admin = await generateRandomAdminUser()

      await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'US Team',
          country: 'US',
          shortName: 'us-team',
        },
      })
      await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'CZ Team',
          country: 'CZ',
          shortName: 'cz-team',
        },
      })

      const response = await client(admin.tokens.accessToken).teams({
        filter: {
          country: 'CZ',
        },
      })
      assert.strictEqual(response.teams?.edges.length, 1)
      assert.strictEqual(response.teams?.edges[0].node.name, 'CZ Team')
      assert.strictEqual(response.teams?.edges[0].node.country, 'CZ')
    })

    it('should return teams filtered by country shortName for admin', async () => {
      const admin = await generateRandomAdminUser()

      await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'US shortName Team',
          country: 'US',
          shortName: 'us-name-team',
        },
      })

      await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'CZ shortName Team',
          country: 'CZ',
          shortName: 'cz-name-team',
        },
      })

      const response = await client(admin.tokens.accessToken).teams({
        filter: {
          shortName: 'us-name-team',
        },
      })

      assert.strictEqual(response.teams?.edges.length, 1)
      assert.strictEqual(response.teams?.edges[0].node.name, 'US shortName Team')
      assert.strictEqual(response.teams?.edges[0].node.country, 'US')
    })

    it('should return team filtered by max players count for admin', async () => {
      await TeamModel.deleteMany({})
      const admin = await generateRandomAdminUser()

      const randomOrganization = await generateRandomOrganization(
        'Test Organization filter by user count'
      )

      const teamWithThreePlayers = await generateRandomTeam({
        name: 'Test org teams user 3 players',
        organization: randomOrganization.id,
        shortNameSlug: 'shortSlugtest3Players',
      })

      for (let i = 0; i < 3; i++) {
        await generateRandomUser({
          firstname: 'Test' + i,
          lastname: 'User' + i,
          role: Role.PLAYER,
          team: teamWithThreePlayers.id,
        })
      }

      const randomOrganizationTwo = await generateRandomOrganization(
        'Test Organization filter by user count 2'
      )

      const teamWithOnePlayer = await generateRandomTeam({
        name: 'Test org teams user 1 player',
        organization: randomOrganizationTwo.id,
        shortNameSlug: 'shortSlugtest1Player',
      })

      await generateRandomUser({
        firstname: 'Test1',
        lastname: 'User1',
        role: Role.PLAYER,
        team: teamWithOnePlayer.id,
      })

      const responseFilterCountTwo = await client(admin.tokens.accessToken).teams({
        filter: {
          playersCount: 1,
        },
      })

      assert.strictEqual(responseFilterCountTwo.teams?.edges.length, 1)
      assert.strictEqual(
        responseFilterCountTwo.teams?.edges[0].node.name,
        'Test org teams user 1 player'
      )

      const responseFilterCountThree = await client(admin.tokens.accessToken).teams({
        filter: {
          playersCount: 3,
        },
      })

      assert.strictEqual(responseFilterCountThree.teams?.edges.length, 2)
      assert.strictEqual(
        responseFilterCountThree.teams?.edges[0].node.name,
        'Test org teams user 1 player'
      )
      assert.strictEqual(
        responseFilterCountThree.teams?.edges[1].node.name,
        'Test org teams user 3 players'
      )
    })

    it('should return response with checked', async () => {
      await TeamModel.deleteMany({})
      const admin = await generateRandomAdminUser()

      await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'Test team checked',
          country: 'US',
          shortName: 'test-team-checked',
          checked: true,
        },
      })

      await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'Test team not checked',
          country: 'US',
          shortName: 'test-team-not-checked',
          checked: false,
        },
      })

      await client(admin.tokens.accessToken).createTeam({
        input: {
          name: 'Test team notset checked',
          country: 'US',
          shortName: 'test-team-notset-checked',
        },
      })

      const responseCheck = await client(admin.tokens.accessToken).teams({
        filter: {
          checked: StatusType.True,
        },
      })

      assert.strictEqual(responseCheck.teams?.edges.length, 1)
      assert.strictEqual(responseCheck.teams?.edges[0].node.name, 'Test team checked')

      const responseNotCheck = await client(admin.tokens.accessToken).teams({
        filter: {
          checked: StatusType.False,
        },
      })

      assert.strictEqual(responseNotCheck.teams?.edges.length, 1)
      assert.strictEqual(responseNotCheck.teams?.edges[0].node.name, 'Test team not checked')

      const responseNotSetCheck = await client(admin.tokens.accessToken).teams({
        filter: {
          checked: StatusType.NotSet,
        },
      })

      assert.strictEqual(responseNotSetCheck.teams?.edges.length, 1)
      assert.strictEqual(responseNotSetCheck.teams?.edges[0].node.name, 'Test team notset checked')
    })
  })

  describe('organizationTeams query', () => {
    it('should return error if team not found by organization', async () => {
      try {
        await client().organizationTeams({
          slug: 'invalid-organization',
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Organization not found.')
      }
    })

    it('should return organization teams', async () => {
      const randomOrganization = await generateRandomOrganization(
        'Test Organizationteams organization'
      )

      const teams = []
      for (let i = 0; i < 30; i++) {
        const team = await generateRandomTeam({
          name: `Test Organizationteams team ${i}`,
          organization: randomOrganization.id,
        })
        teams.push(team)
      }

      const firstPageResponse = await client().organizationTeams({
        slug: randomOrganization.slug,
      })
      assert.strictEqual(firstPageResponse.organizationTeams?.edges.length, 25)
      assert.strictEqual(firstPageResponse.organizationTeams?.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPageResponse.organizationTeams?.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPageResponse.organizationTeams?.pageInfo.totalCount, 30)
      assert.strictEqual(
        firstPageResponse.organizationTeams?.edges[0].node.name,
        'Test Organizationteams team 0'
      )
      assert.strictEqual(firstPageResponse.organizationTeams?.edges[0].node.id, teams[0].id)
      assert.strictEqual(
        firstPageResponse.organizationTeams?.edges[0].node.organization?.id,
        randomOrganization.id
      )
      assert.strictEqual(
        firstPageResponse.organizationTeams?.edges[0].node.organization?.name,
        'Test Organizationteams organization'
      )
      assert.strictEqual(
        firstPageResponse.organizationTeams?.edges[0].node.organization.country,
        'CZ'
      )

      const secondPageResponse = await client().organizationTeams({
        pagination: {
          after: firstPageResponse.organizationTeams.pageInfo.endCursor,
          first: 10,
        },
        slug: randomOrganization.slug,
      })
      assert.strictEqual(secondPageResponse.organizationTeams?.edges.length, 5)
      assert.strictEqual(secondPageResponse.organizationTeams?.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPageResponse.organizationTeams?.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPageResponse.organizationTeams?.pageInfo.totalCount, 30)
      assert.strictEqual(
        secondPageResponse.organizationTeams?.edges[0].node.name,
        'Test Organizationteams team 25'
      )
      assert.strictEqual(secondPageResponse.organizationTeams?.edges[0].node.id, teams[25].id)
      assert.strictEqual(
        secondPageResponse.organizationTeams?.edges[0].node.organization?.id,
        randomOrganization.id
      )
      assert.strictEqual(
        secondPageResponse.organizationTeams?.edges[0].node.organization?.name,
        'Test Organizationteams organization'
      )
      assert.strictEqual(
        secondPageResponse.organizationTeams?.edges[0].node.organization.country,
        'CZ'
      )
    })

    it('should return response when users field is not requested', async () => {
      const randomOrganization = await generateRandomOrganization(
        'Test Organizationteams1 organization'
      )
      const randomTeam = await generateRandomTeam({
        name: 'Test org teams',
        organization: randomOrganization.id,
        shortNameSlug: 'shortSlugtest',
      })

      const response = await client().teamWithoutUserFields({
        pagination: {
          first: 1,
        },
        orgSlug: randomOrganization.slug,
        slug: randomTeam.shortNameSlug,
      })

      assert.strictEqual(response.team.errors, null)
      assert.strictEqual(response.team.team?.name, 'Test org teams')
      assert.strictEqual(response.team.team?.id, randomTeam.id)
      assert.strictEqual(response.team.team?.organization?.id, randomOrganization.id)
      assert.strictEqual(
        response.team.team?.organization?.name,
        'Test Organizationteams1 organization'
      )
    })

    it('should return user in team', async () => {
      const randomOrganization = await generateRandomOrganization(
        'Test Organizationteams1 organization user'
      )

      const teamWithPlayer = await generateRandomTeam({
        name: 'Test org teams user',
        organization: randomOrganization.id,
        shortNameSlug: 'shortSlugtest',
      })

      await generateRandomUser({
        firstname: 'Popeye',
        lastname: 'Sailor',
        role: Role.PLAYER,
        team: teamWithPlayer.id,
      })

      await generateRandomTeam({
        name: 'Test org teams no user',
        organization: randomOrganization.id,
        shortNameSlug: 'shortSlugTestNoUser',
      })

      const response = await client().organizationTeams({
        pagination: {
          first: 2,
        },
        organizationId: randomOrganization.id,
      })

      assert.strictEqual(response.organizationTeams.pageInfo.totalCount, 2)
      assert.strictEqual(response.organizationTeams.edges[0].node.name, 'Test org teams user')
      assert.strictEqual(response.organizationTeams.edges[0].playersCount, 1)
      assert.strictEqual(response.organizationTeams.edges[1].node.name, 'Test org teams no user')
      assert.strictEqual(response.organizationTeams.edges[1].playersCount, 0)
    })
  })

  describe('create team mutation', () => {
    it('should create a team', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization('Test create team organization')

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team',
          country: 'CZ',
          shortName: 'test-create-team',
          organization: randomOrganization.id,
          externalId: 'external-id',
          eliteProspectLink: 'https://www.eliteprospects.com/team/12345/test-team',
          abbreviation: 'TCT',
        },
      })
      assert.strictEqual(team.createTeam.team?.name, 'Test create team')
      assert.strictEqual(team.createTeam.team?.organization?.name, 'Test create team organization')
      assert.strictEqual(team.createTeam.team?.organization?.country, 'CZ')
      assert.strictEqual(team.createTeam.team?.shortName, 'test-create-team')
      assert.strictEqual(team.createTeam.team?.shortNameSlug, 'test-create-team')
      assert.strictEqual(team.createTeam.team?.externalId, 'external-id')
      assert.strictEqual(
        team.createTeam.team?.eliteProspectLink,
        'https://www.eliteprospects.com/team/12345/test-team'
      )
      assert.strictEqual(team.createTeam.team?.abbreviation, 'TCT')
      assert.strictEqual(team.createTeam.team?.country, 'CZ')
      assert.strictEqual(team.createTeam.errors, null)
    })

    it('should return error if team with the same shortSlug name exists in organization', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization('Test create team organization1')

      await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team duplicate',
          country: 'CZ',
          shortName: 'test-create-team-duplicate',
          organization: randomOrganization.id,
          externalId: 'external-id',
        },
      })
      const secondTeam = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team duplicate',
          country: 'CZ',
          shortName: 'test-create-team-duplicate',
          organization: randomOrganization.id,
        },
      })
      assert.strictEqual(secondTeam.createTeam.errors?.[0].message, 'Team already exists')
    })

    it('should throw error if database error occurs during team creation', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test create team organization database error'
      )
      const findOneStub = sinon.stub(TeamModel, 'findOne').throws(new Error('Database error'))

      const response = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team database error',
          country: 'CZ',
          shortName: 'test-create-team-database-error',
          organization: randomOrganization.id,
        },
      })
      assert.strictEqual(response.createTeam.errors?.[0].message, 'Team already exists')
      findOneStub.restore()
    })

    it('should return a CreateTeamAlreadyExistsError if a MongoServerError with a code other than 11000', async () => {
      const randomOrganization = await generateRandomOrganization(
        'Test create team organization mongo error'
      )
      const adminUser = await generateRandomAdminUser()

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mongoError = new Error('Other MongoDB error') as any
      mongoError.code = 12000

      sinon.stub(TeamModel, 'create').throws(mongoError)

      const response = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team',
          country: 'CZ',
          shortName: 'test-create-team',
          organization: randomOrganization.id,
        },
      })

      assert.strictEqual(response.createTeam.errors?.[0].message, 'Team already exists')
    })

    it('should create a team with an empty shortName', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test create team with undefined shortName'
      )

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team with undefined shortName',
          country: 'CZ',
          shortName: undefined,
          organization: randomOrganization.id,
          externalId: 'external-id',
        },
      })

      assert.strictEqual(team.createTeam.team?.name, 'Test create team with undefined shortName')
      assert.strictEqual(team.createTeam.team?.shortName, null)
      assert.strictEqual(team.createTeam.team?.shortNameSlug, '')
      assert.strictEqual(team.createTeam.errors, null)
    })

    it('should create team and auto assign leagues if there is a league with auto connect rules', async () => {
      await TeamModel.deleteMany({})
      await LeagueModel.deleteMany({})

      const adminUser = await generateRandomAdminUser()
      const league = await generateRandomLeague('Test league auto connect rules', 'CZ')

      await LeagueModel.updateOne(
        {
          _id: league.id,
        },
        {
          teamAutoConnectRules: {
            country: 'CZ',
            shortName: 'U 19',
            shortNameSlug: 'u-19',
            shortNameInclude: false,
          },
        }
      )

      const response = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team with undefined shortName',
          country: 'CZ',
          shortName: 'U 19',
          externalId: 'external-id',
        },
      })

      const team = await TeamModel.findById(response.createTeam.team?.id)
      assert.strictEqual(team?.leagues.length, 1)
      assert.strictEqual(team?.leagues[0].toString(), league.id.toString())
    })

    it('should create team and auto assign leagues if there is a league with auto connect rules - short name include', async () => {
      await TeamModel.deleteMany({})
      await LeagueModel.deleteMany({})

      const adminUser = await generateRandomAdminUser()
      const league = await generateRandomLeague('Test league auto connect rules', 'CZ')

      await LeagueModel.updateOne(
        {
          _id: league.id,
        },
        {
          teamAutoConnectRules: {
            country: 'CZ',
            shortName: 'test',
            shortNameSlug: 'test-slug',
            shortNameInclude: true,
          },
        }
      )

      const response = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test create team with undefined shortName - short name included',
          country: 'CZ',
          shortName: 'pre-test-slug-post',
          externalId: 'external-id',
        },
      })

      const team = await TeamModel.findById(response.createTeam.team?.id)
      assert.strictEqual(team?.leagues.length, 1)
      assert.strictEqual(team?.leagues[0].toString(), league.id.toString())
    })
  })

  describe('update team mutation', () => {
    it('should update a team', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization('Test update team organization')

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test update team',
          country: 'US',
          shortName: 'test-update-team',
          organization: randomOrganization.id,
          externalId: 'external-id',
          eliteProspectLink: 'https://www.eliteprospects.com/team/123456/test-team',
          abbreviation: 'TCT',
        },
      })

      const updatedTeam = await client(adminUser.tokens.accessToken).updateTeam({
        updateTeamId: team.createTeam.team?.id,
        input: {
          name: 'Test update team new name',
          country: 'CZ',
          shortName: 'test-update-team-new-name',
          organization: randomOrganization.id,
          abbreviation: 'TCTNM',
        },
      })
      assert.strictEqual(updatedTeam.updateTeam.team?.name, 'Test update team new name')
      assert.strictEqual(
        updatedTeam.updateTeam.team?.organization?.name,
        'Test update team organization'
      )
      assert.strictEqual(
        updatedTeam.updateTeam.team?.organization?.name,
        'Test update team organization'
      )
      assert.strictEqual(updatedTeam.updateTeam.team?.organization?.country, 'CZ')
      assert.strictEqual(updatedTeam.updateTeam.team?.shortName, 'test-update-team-new-name')
      assert.strictEqual(updatedTeam.updateTeam.team?.shortNameSlug, 'test-update-team-new-name')
      assert.strictEqual(updatedTeam.updateTeam.team?.externalId, 'external-id')
      assert.strictEqual(
        updatedTeam.updateTeam.team?.eliteProspectLink,
        'https://www.eliteprospects.com/team/123456/test-team'
      )
      assert.strictEqual(updatedTeam.updateTeam.team?.abbreviation, 'TCTNM')
      assert.strictEqual(updatedTeam.updateTeam.team?.country, 'CZ')
      assert.strictEqual(updatedTeam.updateTeam.errors, null)
    })

    it('should return error if team not found', async () => {
      const adminUser = await generateRandomAdminUser()

      const randomOrganization = await generateRandomOrganization(
        'Test update team organization not found'
      )
      const team = await generateRandomTeam({
        name: 'Test team update team not found',
        organization: randomOrganization.id,
      })

      const findByIdStub = sinon.stub(TeamModel, 'findById').resolves(null)

      try {
        await client(adminUser.tokens.accessToken).updateTeam({
          updateTeamId: team.id,
          input: {
            name: 'Test update team',
            country: 'CZ',
            shortName: 'test-update-team',
            organization: randomOrganization.id,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Team not found.')
      }

      findByIdStub.restore()
    })

    it('should return error if team with the same name and organization exists', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test update team organization duplicate'
      )

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test update team duplicate',
          country: 'US',
          shortName: 'test-update-team-duplicate',
          organization: randomOrganization.id,
          externalId: 'external-id',
        },
      })

      await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test update team duplicate 2',
          country: 'US',
          shortName: 'test-update-team-duplicate-2',
          organization: randomOrganization.id,
          externalId: 'external-id',
        },
      })

      try {
        await client(adminUser.tokens.accessToken).updateTeam({
          updateTeamId: team.createTeam.team?.id,
          input: {
            name: 'Test update team duplicate 2',
            country: 'US',
            shortName: 'test-update-team-duplicate-2',
            organization: randomOrganization.id,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Team with same short name already exists in this organization.'
        )
      }
    })

    it('should copy team logo to organization when organization has no logo', async () => {
      const adminUser = await generateRandomAdminUser()
      const organizationWithoutLogo = await generateRandomOrganization('Organization without logo')

      const teamWithLogo = await TeamModel.create({
        name: 'Team with logo',
        country: 'US',
        shortName: 'team-with-logo',
        organization: organizationWithoutLogo.id,
        logo: '/path/to/picture.jpeg',
      })

      const logoKey = `images/organizations/${teamWithLogo.id}/logo/${new Date().getTime()}.png`

      s3ClientStub.send.resolves({ Key: logoKey, Body: 'path/to/picture.jpeg' })

      await client(adminUser.tokens.accessToken).updateTeam({
        updateTeamId: teamWithLogo.id,
        input: {
          name: 'Team with new name',
          country: 'CZ',
          shortName: 'team-with-new-name',
          organization: organizationWithoutLogo.id,
        },
      })

      const logoKeyPattern = new RegExp(
        `images/organizations/${organizationWithoutLogo.id}/logo/\\d+\\.png$`
      )

      sinon.assert.calledOnce(s3ClientStub.send)
      assert.match(s3ClientStub.send.getCall(0).args[0].input.Key, logoKeyPattern)
    })

    it('should throw error if database error occurs during team creation', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test update team organization database error'
      )
      const team = await generateRandomTeam({
        name: 'Test team update team database error',
        organization: randomOrganization.id,
      })

      const findOneStub = sinon.stub(TeamModel, 'findOne').throws(new Error('Database error'))

      const response = await client(adminUser.tokens.accessToken).updateTeam({
        updateTeamId: team.id,
        input: {
          name: 'Test update team database error',
          country: 'CZ',
          shortName: 'test-update-team-database-error',
          organization: randomOrganization.id,
        },
      })
      assert.strictEqual(response.updateTeam.errors?.[0].message, 'Failed to update team')
      findOneStub.restore()
    })

    it('should return a error if a MongoServerError with a code other than 11000', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test update team organization mongo error'
      )
      const team = await generateRandomTeam({
        name: 'Test team update team mongo error',
        organization: randomOrganization.id,
      })

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mongoError = new Error('Other MongoDB error') as any
      mongoError.code = 12000

      sinon.stub(TeamModel, 'findOne').throws(mongoError)

      const response = await client(adminUser.tokens.accessToken).updateTeam({
        updateTeamId: team.id,
        input: {
          name: 'Test update team',
          country: 'CZ',
          shortName: 'test-update-team',
          organization: randomOrganization.id,
        },
      })

      assert.strictEqual(response.updateTeam.errors?.[0].message, 'Failed to update team')
    })
  })

  describe('delete team mutation', () => {
    beforeEach(async () => {
      await TeamModel.deleteMany({})
      await UserStatsModel.deleteMany({})
      await GameModel.deleteMany({})
    })

    it('should return true if team is deleted and set user/game stats to null for the team', async () => {
      const adminUser = await generateRandomAdminUser()

      const randomOrganization = await generateRandomOrganization('Test delete team organization')

      const team = await generateRandomTeam({
        name: 'Test delete team',
        country: 'US',
        shortName: 'test-delete-team',
        organization: randomOrganization.id,
      })

      await generateRandomGameStats({
        nameSuffix: 'a',
        gameStatsPayload: {
          homeTeam: team,
        },
      })

      await generateRandomGameStats({
        nameSuffix: 'b',
        gameStatsPayload: {
          awayTeam: team,
        },
      })

      const user = await generateRandomUser({})

      await UserStatsModel.create({
        user: user.user._id,
        team: team.id,
        season: '2021',
      })

      const response = await client(adminUser.tokens.accessToken).deleteTeam({
        deleteTeamId: team.id,
      })

      assert.strictEqual(response.deleteTeam, true)

      const games = await GameModel.find({})
      const userStats = await UserStatsModel.find({})

      assert.strictEqual(games.length, 2)
      assert.strictEqual(userStats.length, 1)

      assert.strictEqual(games[0].homeTeam, null)
      assert.strictEqual(games[1].awayTeam, null)
      assert.strictEqual(userStats[0].team, null)
    })

    it('should return error if team is in use', async () => {
      const adminUser = await generateRandomAdminUser()

      const randomOrganization = await generateRandomOrganization(
        'Test delete team organization team in use'
      )
      const team = await generateRandomTeam({
        name: 'Test delete team in use',
        country: 'US',
        shortName: 'test-delete-team-in-use',
        organization: randomOrganization.id,
      })

      await generateRandomUser({
        team,
      })

      try {
        await client(adminUser.tokens.accessToken).deleteTeam({
          deleteTeamId: team.id,
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Team is in use and cannot be deleted.')
        assert.strictEqual(err.response.errors[0].extensions?.code, 'TEAM_IN_USE')
      }
    })

    it('should throw error if failed to delete team', async () => {
      const adminUser = await generateRandomAdminUser()

      const randomOrganization = await generateRandomOrganization(
        'Test delete team organization failed'
      )

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test delete team failed',
          country: 'US',
          shortName: 'test-delete-team-failed',
          organization: randomOrganization.id,
        },
      })
      const findByIdAndDeleteStub = sinon.stub(TeamModel, 'findByIdAndDelete').throws(new Error())

      const response = await client(adminUser.tokens.accessToken).deleteTeam({
        deleteTeamId: team.createTeam.team?.id,
      })
      assert.strictEqual(response.deleteTeam, false)

      findByIdAndDeleteStub.restore()
    })
  })

  describe('upload team logo mutation', () => {
    it('should return response if upload successful', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test upload team logo organization'
      )
      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test upload team logo',
          country: 'US',
          shortName: 'test-upload-team-logo',
          organization: randomOrganization.id,
        },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadTeamLogo({
        uploadTeamLogoId: team.createTeam.team?.id,
      })

      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(response.uploadTeamLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(response.uploadTeamLogo.team?.name, 'Test upload team logo')
      assert.strictEqual(response.uploadTeamLogo.team?.logo, null)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const responseConfirmation = await client(adminUser.tokens.accessToken).uploadTeamLogo({
        uploadTeamLogoId: team.createTeam.team?.id,
        input: { uploadSuccessful: true },
      })
      assert.strictEqual(responseConfirmation.uploadTeamLogo.errors, null)

      const logoKeyPattern = new RegExp(`images/teams/${team.createTeam.team?.id}/logo/\\d+\\.png$`)

      const check = await TeamModel.findById(team.createTeam.team?.id)

      assert.match(check?.logo || '', logoKeyPattern)
      assert.strictEqual(check?.logoUnconfirmed, null)
    })

    it("should return error if couldn't create presigned url", async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test upload team logo organization error'
      )
      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test upload team logo error',
          country: 'US',
          shortName: 'test-upload-team-logo-error',
          organization: randomOrganization.id,
        },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .throws(new Error('Failed to create presigned url'))

      const response = await client(adminUser.tokens.accessToken).uploadTeamLogo({
        uploadTeamLogoId: team.createTeam.team?.id,
      })

      assert.strictEqual(
        response.uploadTeamLogo.errors?.[0].message,
        'Failed to create preSignedUrl'
      )

      s3RequestPresignerstub.restore()
    })

    it('should return response if upload unsuccessful', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test upload team logo organization failed'
      )
      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test upload team logo failed',
          country: 'US',
          shortName: 'test-upload-team-logo-failed',
          organization: randomOrganization.id,
        },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadTeamLogo({
        uploadTeamLogoId: team.createTeam.team?.id,
      })
      assert.strictEqual(response.uploadTeamLogo.errors, null)
      assert.strictEqual(response.uploadTeamLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(response.uploadTeamLogo.team?.name, 'Test upload team logo failed')
      assert.strictEqual(response.uploadTeamLogo.team.country, 'US')
      assert.strictEqual(response.uploadTeamLogo.team?.logo, null)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const logoKeyPattern = new RegExp(`images/teams/${team.createTeam.team?.id}/logo/\\d+\\.png$`)

      const firstCheck = await TeamModel.findById(team.createTeam.team?.id)
      assert.match(firstCheck?.logoUnconfirmed || '', logoKeyPattern)
      assert.strictEqual(firstCheck?.logo, undefined)

      const responseConfirmation = await client(adminUser.tokens.accessToken).uploadTeamLogo({
        uploadTeamLogoId: team.createTeam.team?.id,
        input: { uploadSuccessful: false },
      })

      assert.strictEqual(responseConfirmation.uploadTeamLogo.errors, null)

      const secondCheck = await TeamModel.findById(team.createTeam.team?.id)
      assert.strictEqual(secondCheck?.logo, undefined)
      assert.strictEqual(secondCheck?.logoUnconfirmed, null)

      s3RequestPresignerstub.restore()
    })

    it('should handle empty shortName and generate empty shortNameSlug', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test update team empty shortName'
      )

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test team with empty shortName',
          country: 'US',
          shortName: '',
          organization: randomOrganization.id,
        },
      })

      const updatedTeam = await client(adminUser.tokens.accessToken).updateTeam({
        updateTeamId: team.createTeam.team?.id,
        input: {
          name: 'Test team with updated empty shortName',
          country: 'CZ',
          shortName: '',
          organization: randomOrganization.id,
        },
      })

      assert.strictEqual(updatedTeam.updateTeam.team?.shortName, '')
      assert.strictEqual(updatedTeam.updateTeam.team?.shortNameSlug, '')
      assert.strictEqual(updatedTeam.updateTeam.errors, null)
    })

    it('should update with if name and shortName are empty', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization('Test update team no name')

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test update team without name and shortName',
          country: 'US',
          shortName: '',
          organization: randomOrganization.id,
        },
      })

      const updatedTeam = await client(adminUser.tokens.accessToken).updateTeam({
        updateTeamId: team.createTeam.team?.id,
        input: {
          country: 'CZ',
          name: '',
          shortName: '',
        },
      })

      assert.strictEqual(
        updatedTeam.updateTeam.team?.slug,
        'test-update-team-without-name-and-shortname'
      )
      assert.strictEqual(updatedTeam.updateTeam.team?.name, '')
      assert.strictEqual(updatedTeam.updateTeam.team?.shortNameSlug, '')
      assert.strictEqual(updatedTeam.updateTeam.errors, null)
    })

    it('should generate empty shortNameSlug when shortName is undefined', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test update team with undefined shortName'
      )
      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test team without shortName',
          country: 'US',
          organization: randomOrganization.id,
        },
      })

      const updatedTeam = await client(adminUser.tokens.accessToken).updateTeam({
        updateTeamId: team.createTeam.team?.id,
        input: {
          name: 'Updated team name',
          organization: randomOrganization.id,
        },
      })

      assert.strictEqual(updatedTeam.updateTeam.team?.shortNameSlug, '')
      assert.strictEqual(updatedTeam.updateTeam.errors, null)
    })
  })

  describe('change team order up mutation', () => {
    it('should change team order', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test change team order up organization'
      )

      const teams = []
      for (let i = 0; i < 3; i++) {
        const team = await client(adminUser.tokens.accessToken).createTeam({
          input: {
            name: `Test change team order up ${1 + i}`,
            country: 'US',
            shortName: `test-change-team-order-up-${1 + i}`,
            organization: randomOrganization.id,
          },
        })
        teams.push(team)
      }

      assert.strictEqual(teams.length, 3)
      assert.strictEqual(teams[0].createTeam.team?.order, null)
      assert.strictEqual(teams[1].createTeam.team?.order, null)
      assert.strictEqual(teams[2].createTeam.team?.order, null)

      const response = await client(adminUser.tokens.accessToken).changeTeamOrderUp({
        changeTeamOrderUpId: teams[1].createTeam.team?.id,
      })

      for (let i = 0; i < 2; i++) {
        await client(adminUser.tokens.accessToken).changeTeamOrderUp({
          changeTeamOrderUpId: teams[2].createTeam.team?.id,
        })
      }

      const updatedteam1 = await TeamModel.findById(teams[0].createTeam.team?.id)
      const updatedteam2 = await TeamModel.findById(teams[1].createTeam.team?.id)
      const updatedteam3 = await TeamModel.findById(teams[2].createTeam.team?.id)

      assert.strictEqual(updatedteam1?.order, undefined)
      assert.strictEqual(updatedteam1?.name, 'Test change team order up 1')
      assert.strictEqual(updatedteam2?.order, 2)
      assert.strictEqual(updatedteam2?.name, 'Test change team order up 2')
      assert.strictEqual(updatedteam3?.order, 1)
      assert.strictEqual(updatedteam3?.name, 'Test change team order up 3')

      assert.strictEqual(response.changeTeamOrderUp, true)
    })

    it('should return false if team not found', async () => {
      const adminUser = await generateRandomAdminUser()

      const randomOrganization = await generateRandomOrganization(
        'Test change team order up organization not found'
      )

      const findByIdStub = sinon.stub(TeamModel, 'findById').resolves(null)

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test change team order up not found',
          country: 'US',
          shortName: 'test-change-team-order-up-not-found',
          organization: randomOrganization.id,
        },
      })
      const response = await client(adminUser.tokens.accessToken).changeTeamOrderUp({
        changeTeamOrderUpId: team.createTeam.team?.id,
      })

      assert.strictEqual(response.changeTeamOrderUp, false)

      findByIdStub.restore()
    })

    it('should put team to the end of list if order is undefined', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test change team order up organization team order undefined'
      )

      const teams = []
      for (let i = 0; i < 3; i++) {
        const team = await client(adminUser.tokens.accessToken).createTeam({
          input: {
            name: `Test change team order up undefined ${1 + i}`,
            country: 'US',
            shortName: `test-change-team-order-up-undefined-${1 + i}`,
            organization: randomOrganization.id,
          },
        })
        teams.push(team)
      }

      assert.strictEqual(teams.length, 3)
      assert.strictEqual(teams[0].createTeam.team?.order, null)
      assert.strictEqual(teams[1].createTeam.team?.order, null)
      assert.strictEqual(teams[2].createTeam.team?.order, null)

      await client(adminUser.tokens.accessToken).changeTeamOrderUp({
        changeTeamOrderUpId: teams[1].createTeam.team?.id,
      })
      const response = await client(adminUser.tokens.accessToken).changeTeamOrderUp({
        changeTeamOrderUpId: teams[2].createTeam.team?.id,
      })

      const updatedteam1 = await TeamModel.findById(teams[0].createTeam.team?.id)
      const updatedteam2 = await TeamModel.findById(teams[1].createTeam.team?.id)
      const updatedteam3 = await TeamModel.findById(teams[2].createTeam.team?.id)

      assert.strictEqual(updatedteam1?.order, undefined)
      assert.strictEqual(updatedteam1?.name, 'Test change team order up undefined 1')
      assert.strictEqual(updatedteam2?.order, 1)
      assert.strictEqual(updatedteam2?.name, 'Test change team order up undefined 2')
      assert.strictEqual(updatedteam3?.order, 2)
      assert.strictEqual(updatedteam3?.name, 'Test change team order up undefined 3')

      assert.strictEqual(response.changeTeamOrderUp, true)
    })

    it("should return false if team don't have previous team", async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        "Test change team order up organization team don't have previous team"
      )
      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test change team order up no previous team',
          country: 'US',
          shortName: 'test-change-team-order-up-no-previous-team',
          organization: randomOrganization.id,
        },
      })

      await TeamModel.updateOne({ _id: team.createTeam.team?.id }, { order: 1 })

      const response = await client(adminUser.tokens.accessToken).changeTeamOrderUp({
        changeTeamOrderUpId: team.createTeam.team?.id,
      })

      assert.strictEqual(response.changeTeamOrderUp, false)
    })
  })

  describe('change team order down mutation', () => {
    it('should change team order down', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test change team order down organization'
      )

      const teams = []
      for (let i = 0; i < 3; i++) {
        const team = await client(adminUser.tokens.accessToken).createTeam({
          input: {
            name: `Test change team order down ${1 + i}`,
            country: 'US',
            shortName: `test-change-team-order-down-${1 + i}`,
            organization: randomOrganization.id,
          },
        })
        teams.push(team)
      }

      assert.strictEqual(teams.length, 3)
      assert.strictEqual(teams[0].createTeam.team?.order, null)
      assert.strictEqual(teams[1].createTeam.team?.order, null)
      assert.strictEqual(teams[2].createTeam.team?.order, null)

      for (let i = 0; i < 3; i++) {
        await TeamModel.updateOne({ _id: teams[i].createTeam.team?.id }, { order: 1 + i })
      }

      const team1 = await TeamModel.findById(teams[0].createTeam.team?.id)
      const team2 = await TeamModel.findById(teams[1].createTeam.team?.id)
      const team3 = await TeamModel.findById(teams[2].createTeam.team?.id)
      assert.strictEqual(team1?.order, 1)
      assert.strictEqual(team1?.name, 'Test change team order down 1')
      assert.strictEqual(team2?.order, 2)
      assert.strictEqual(team2?.name, 'Test change team order down 2')
      assert.strictEqual(team3?.order, 3)
      assert.strictEqual(team3?.name, 'Test change team order down 3')

      await client(adminUser.tokens.accessToken).changeTeamOrderDown({
        changeTeamOrderDownId: teams[0].createTeam.team?.id,
      })

      const updatedteam1 = await TeamModel.findById(teams[0].createTeam.team?.id)
      const updatedteam2 = await TeamModel.findById(teams[1].createTeam.team?.id)
      const updatedteam3 = await TeamModel.findById(teams[2].createTeam.team?.id)

      assert.strictEqual(updatedteam1?.order, 2)
      assert.strictEqual(updatedteam1?.name, 'Test change team order down 1')
      assert.strictEqual(updatedteam2?.order, 1)
      assert.strictEqual(updatedteam2?.name, 'Test change team order down 2')
      assert.strictEqual(updatedteam3?.order, 3)
      assert.strictEqual(updatedteam3?.name, 'Test change team order down 3')
    })

    it('should return false if team not found', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test change team order down organization team not found'
      )

      const findByIdStub = sinon.stub(TeamModel, 'findById').resolves(null)

      const response = await client(adminUser.tokens.accessToken).changeTeamOrderDown({
        changeTeamOrderDownId: randomOrganization.id,
      })

      assert.strictEqual(response.changeTeamOrderDown, false)

      findByIdStub.restore()
    })

    it('should set order if team order is undefined', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test change team order down organization team undefined order'
      )

      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test change team order down team undefined order',
          country: 'US',
          shortName: 'test-change-team-order-down-team-undefined-order',
          organization: randomOrganization.id,
        },
      })

      const response = await client(adminUser.tokens.accessToken).changeTeamOrderDown({
        changeTeamOrderDownId: team.createTeam.team?.id,
      })

      assert.strictEqual(response.changeTeamOrderDown, true)
    })

    it('should return true if team dont have team below to swap ', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomOrganization = await generateRandomOrganization(
        'Test change team order down organization no next team to swap'
      )
      const team = await client(adminUser.tokens.accessToken).createTeam({
        input: {
          name: 'Test change team order down no next team to swap',
          country: 'US',
          shortName: 'test-change-team-order-down-no-next-team-to-swap',
          organization: randomOrganization.id,
        },
      })

      await TeamModel.updateOne({ _id: team.createTeam.team?.id }, { order: 1 })

      const response = await client(adminUser.tokens.accessToken).changeTeamOrderDown({
        changeTeamOrderDownId: team.createTeam.team?.id,
      })

      assert.strictEqual(response.changeTeamOrderDown, true)
    })
  })

  describe('epTeam query', () => {
    it('should return team by id', async () => {
      const adminUser = await generateRandomAdminUser()

      const teamId = 'valid-elite-prospect-id'

      const axiosStub = sinon.stub(axios, 'get').resolves({
        data: {
          data: {
            id: teamId,
            name: 'Test ep team',
            slug: 'test-ep-team',
            logo: {
              large: 'https://path.to.logo',
            },
            country: {
              iso_3166_1_alpha_2: 'US',
            },
          },
        },
      })

      const response = await client(adminUser.tokens.accessToken).epTeam({
        epTeamId: teamId,
      })

      assert.strictEqual(response.epTeam?.id, teamId)
      assert.strictEqual(response.epTeam?.name, 'Test ep team')
      assert.strictEqual(response.epTeam?.slug, 'test-ep-team')
      assert.strictEqual(response.epTeam?.logo?.large, 'https://path.to.logo')
      assert.strictEqual(response.epTeam?.country.iso_3166_1_alpha_2, 'US')

      sinon.assert.calledOnce(axiosStub)

      assert(
        axiosStub.calledOnceWith(
          `https://api.eliteprospects.com/v1/teams/${teamId}?&fields=id,name,slug,logo.*,country.*&apiKey=${config.ep.apiKey}`
        )
      )

      axiosStub.restore()
    })

    it('should return error if failed to get data', async () => {
      const adminUser = await generateRandomAdminUser()

      const axiosStub = sinon.stub(axios, 'get').rejects(new Error('Failed to get data'))

      const response = await client(adminUser.tokens.accessToken).epTeam({
        epTeamId: 'invalid-ep-id',
      })

      assert.strictEqual(response.epTeam, null)

      sinon.assert.calledOnce(axiosStub)
      axiosStub.restore()
    })
  })

  describe('epTeams query', () => {
    it('should return teams', async () => {
      const adminUser = await generateRandomAdminUser()

      const axiosStub = sinon.stub(axios, 'get').resolves({
        data: {
          data: [
            {
              id: 'valid-elite-prospect-id',
              name: 'Test ep team',
              slug: 'test-ep-team',
              logo: {
                large: 'https://path.to.logo',
              },
              country: { iso_3166_1_alpha_2: 'US' },
            },
          ],
        },
      })

      const searchQuery = 'test'

      const response = await client(adminUser.tokens.accessToken).epTeams({
        searchQuery: searchQuery,
      })

      assert.strictEqual(response.epTeams?.length, 1)
      assert.strictEqual(response.epTeams[0].id, 'valid-elite-prospect-id')
      assert.strictEqual(response.epTeams[0].name, 'Test ep team')
      assert.strictEqual(response.epTeams[0].slug, 'test-ep-team')
      assert.strictEqual(response.epTeams[0].logo?.large, 'https://path.to.logo')
      assert.strictEqual(response.epTeams[0].country.iso_3166_1_alpha_2, 'US')

      const expectedUrl = `https://api.eliteprospects.com/v1/teams?q=${searchQuery}&fields=id,name,slug,logo.*,country.*&offset=0&limit=20&apiKey=${config.ep.apiKey}`

      sinon.assert.calledOnce(axiosStub)
      sinon.assert.calledWith(axiosStub, expectedUrl)

      axiosStub.restore()
    })

    it('should return error if failed to search teams', async () => {
      const adminUser = await generateRandomAdminUser()

      const axiosStub = sinon.stub(axios, 'get').rejects(new Error('Failed to search EP teams.'))

      await client(adminUser.tokens.accessToken).epTeams({
        searchQuery: 'test',
      })

      sinon.assert.calledOnce(axiosStub)
      axiosStub.restore()
    })
  })

  describe('Team follow/unfollow', () => {
    afterEach(async () => {
      await TeamFollowModel.deleteMany({})
      await TeamModel.deleteMany({})
    })

    it('should be possible for user to follow and unfollow team', async () => {
      const user = await generateRandomUser()
      const team = await generateRandomTeam({ name: 'Test team follow' })

      const response = await client(user.tokens.accessToken).followTeam({
        id: team.id,
      })

      sinon.assert.calledOnce(streamFeedFollowStub)

      assert.strictEqual(response.followTeam.team?.stats?.followers, 1)

      const follow = await TeamFollowModel.find()
      assert.strictEqual(follow.length, 1)
      assert.strictEqual(follow[0].team.toString(), team.id.toString())
      assert.strictEqual(follow[0].follower.toString(), user.user._id.toString())

      // now let's unfollow
      const response2 = await client(user.tokens.accessToken).unfollowTeam({
        id: team.id,
      })

      sinon.assert.calledOnce(streamFeedUnfollowStub)

      assert.strictEqual(response2.unfollowTeam.team?.stats?.followers, 0)

      const follow2 = await TeamFollowModel.find()
      assert.strictEqual(follow2.length, 0)
    })

    it('should return an error if team not found', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).followTeam({
        id: new Types.ObjectId(),
      })

      sinon.assert.notCalled(streamFeedFollowStub)

      assert.strictEqual(response.followTeam.errors?.[0].message, 'Team does not exist.')

      // try to unfollow non existent team

      const response2 = await client(user.tokens.accessToken).unfollowTeam({
        id: new Types.ObjectId(),
      })

      sinon.assert.notCalled(streamFeedUnfollowStub)

      assert.strictEqual(response2.unfollowTeam.errors?.[0].message, 'Team does not exist.')

      // team exists but there is no follow
      const team = await generateRandomTeam({
        name: 'Test team follow',
        slug: 'test-team-follow-2',
      })

      const response3 = await client(user.tokens.accessToken).unfollowTeam({
        id: team.id,
      })

      sinon.assert.notCalled(streamFeedUnfollowStub)

      assert.strictEqual(response3.unfollowTeam.errors?.[0].message, 'Team follow does not exist.')
    })

    it('should check if team is followed', async () => {
      const user = await generateRandomUser()
      const team = await generateRandomTeam({ name: 'Test team follow' })

      const response = await client(user.tokens.accessToken).followTeam({
        id: team.id,
      })

      sinon.assert.calledOnce(streamFeedFollowStub)

      assert.strictEqual(response.followTeam.team?.stats?.followers, 1)

      const response2 = await client(user.tokens.accessToken).isFollowingTeam({
        id: team.id,
      })

      assert.strictEqual(response2.isFollowingTeam, true)

      const response3 = await client(user.tokens.accessToken).unfollowTeam({
        id: team.id,
      })

      sinon.assert.calledOnce(streamFeedUnfollowStub)

      assert.strictEqual(response3.unfollowTeam.team?.stats?.followers, 0)

      const response4 = await client(user.tokens.accessToken).isFollowingTeam({
        id: team.id,
      })

      assert.strictEqual(response4.isFollowingTeam, false)
    })
  })

  describe('myFollowingTeams query', () => {
    it('will create 25 teams, then follow with them, page after 10  teams, and check pagination', async () => {
      const follower = await generateRandomUser()
      const teams = []
      for (let i = 0; i < 25; i++) {
        const followee = await generateRandomTeam({ name: `Team ${i + 1}` })
        await client(follower.tokens.accessToken).followTeam({
          id: followee._id.toString(),
        })

        teams.push(followee)
      }

      sinon.assert.callCount(streamFeedFollowStub, 25)

      const isFollowingTeam = await client(follower.tokens.accessToken).isFollowingTeam({
        id: teams[6].id?.toString(),
      })
      const response = await client(follower.tokens.accessToken).myFollowingTeams({
        pagination: {
          first: 10,
        },
      })

      assert.equal(isFollowingTeam.isFollowingTeam, true)
      assert.strictEqual(response.myFollowingTeams.pageInfo.hasNextPage, true)
      assert.strictEqual(response.myFollowingTeams.edges.length, 10)
      assert.strictEqual(response.myFollowingTeams.edges[0].node.name, 'Team 25')
      assert.strictEqual(response.myFollowingTeams.edges[9].node.name, 'Team 16')

      const response1 = await client(follower.tokens.accessToken).myFollowingTeams({
        pagination: {
          first: 10,
          after: response.myFollowingTeams.pageInfo.endCursor,
        },
      })

      assert.strictEqual(response1.myFollowingTeams.pageInfo.hasNextPage, true)
      assert.strictEqual(response1.myFollowingTeams.pageInfo.hasPreviousPage, true)
      assert.strictEqual(response1.myFollowingTeams.edges.length, 10)
      assert.strictEqual(response1.myFollowingTeams.edges[0].node.name, 'Team 15')
      assert.strictEqual(response1.myFollowingTeams.edges[9].node.name, 'Team 6')

      const response2 = await client(follower.tokens.accessToken).myFollowingTeams({
        pagination: {
          first: 10,
          after: response1.myFollowingTeams.pageInfo.endCursor,
        },
      })
      assert.strictEqual(response2.myFollowingTeams.pageInfo.hasNextPage, false)
      assert.strictEqual(response2.myFollowingTeams.pageInfo.hasPreviousPage, true)
      assert.strictEqual(response2.myFollowingTeams.edges.length, 5)
    })
  })

  describe('team query with shadow players and season filtering', () => {
    beforeEach(async () => {
      await GameModel.deleteMany({})
      await CrawlerDomainConfigModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
      await OrganizationModel.deleteMany({})
      await TeamModel.deleteMany({})
    })

    it('should include shadow players when includeShadowPlayers is true and filter by season', async () => {
      const randomOrganization = await generateRandomOrganization('Shadow Players Org')
      const team = await generateRandomTeam({
        name: 'Awesome Team',
        organization: randomOrganization.id,
      })

      const discoveredUrl = await generateDiscoverUrl()

      const regularPlayer = await generateRandomUser({
        firstname: 'Jarmil',
        lastname: 'Gumovic',
        team: team.id,
      })

      const shadowPlayer = await generateRandomUser({
        firstname: 'Jarda',
        lastname: 'Shadowovic',
        role: Role.SHADOW_PLAYER,
        team: null,
        externalConnections: [
          {
            externalId: 'shadow-player-external-id',
            crawlerDomainConfig: discoveredUrl.crawlerDomainConfig,
          },
        ],
      })

      const season1 = '2022-2023'
      const season2 = '2023-2024'

      await generateRandomGameStats({
        nameSuffix: 'Season1',
        gameStatsPayload: {
          discoveredUrl,
          homeTeam: team.id,
          season: season1,
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: shadowPlayer.user.name,
                playerExternalId: 'shadow-player-external-id',
              },
              player: null,
            },
            {
              externalInfo: {
                playerName: regularPlayer.user.name,
                playerExternalId: 'real-player-external-id',
              },
              player: null,
            },
          ],
        },
      })

      await generateRandomGameStats({
        nameSuffix: 'Season2',
        gameStatsPayload: {
          discoveredUrl,
          homeTeam: team.id,
          season: season2,
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: shadowPlayer.user.name,
                playerExternalId: 'shadow-player-external-id',
              },
              player: null,
            },
          ],
        },
      })

      // Query team without shadow players - should only return regular player
      const responseWithoutShadow = await client().team({
        pagination: {
          first: 10,
        },
        teamId: team.id,
        includeShadowPlayers: false,
      })

      assert.strictEqual(responseWithoutShadow.team.users?.edges.length, 1)
      assert.strictEqual(responseWithoutShadow.team.users?.pageInfo.totalCount, 1)
      assert.strictEqual(
        responseWithoutShadow.team.users?.edges[0].node.name,
        regularPlayer.user.name
      )

      // Query team with shadow players but without season - should include shadow player from all seasons
      const responseWithShadow = await client().team({
        pagination: {
          first: 10,
        },
        teamId: team.id,
        includeShadowPlayers: true,
      })

      assert.strictEqual(responseWithShadow.team.users?.edges.length, 2)
      assert.strictEqual(
        responseWithShadow.team.users?.edges.some(
          (edge) => edge.node.name === shadowPlayer.user.name
        ),
        true
      )
      assert.strictEqual(
        responseWithShadow.team.users?.edges.some(
          (edge) => edge.node.name === regularPlayer.user.name
        ),
        true
      )

      // Query team with shadow players for specific season 1
      const responseWithSeason1 = await client().team({
        pagination: {
          first: 10,
        },
        teamId: team.id,
        includeShadowPlayers: true,
        season: season1,
      })

      assert.strictEqual(responseWithSeason1.team.users?.edges.length, 2)
      assert.strictEqual(
        responseWithSeason1.team.users?.edges.some(
          (edge) => edge.node.name === shadowPlayer.user.name
        ),
        true
      )
      assert.strictEqual(
        responseWithSeason1.team.users?.edges.some(
          (edge) => edge.node.name === regularPlayer.user.name
        ),
        true
      )

      // Create another shadow player that appears only in season 2 games
      const anotherShadowPlayer = await generateRandomUser({
        firstname: 'Another',
        lastname: 'Shadow',
        role: Role.SHADOW_PLAYER,
        team: null,
        externalConnections: [
          {
            externalId: 'another-shadow-external-id',
            crawlerDomainConfig: discoveredUrl.crawlerDomainConfig,
          },
        ],
      })

      await generateRandomGameStats({
        nameSuffix: 'Season2Another',
        gameStatsPayload: {
          discoveredUrl,
          homeTeam: team.id,
          season: season2,
          homeTeamRoster: [
            {
              externalInfo: {
                playerName: anotherShadowPlayer.user.name,
                playerExternalId: 'another-shadow-external-id',
              },
              player: null,
            },
          ],
        },
      })

      // Query with season 2 filter - should include both shadow players
      const responseWithSeason2 = await client().team({
        pagination: {
          first: 10,
        },
        teamId: team.id,
        includeShadowPlayers: true,
        season: season2,
      })

      assert.strictEqual(responseWithSeason2.team.users?.edges.length, 3)
      assert.strictEqual(
        responseWithSeason2.team.users?.edges.some(
          (edge) => edge.node.name === shadowPlayer.user.name
        ),
        true
      )
      assert.strictEqual(
        responseWithSeason2.team.users?.edges.some(
          (edge) => edge.node.name === anotherShadowPlayer.user.name
        ),
        true
      )
      assert.strictEqual(
        responseWithSeason2.team.users?.edges.some(
          (edge) => edge.node.name === regularPlayer.user.name
        ),
        true
      )

      // Query with season 1 filter again
      const responseWithSeason1Again = await client().team({
        pagination: {
          first: 10,
        },
        teamId: team.id,
        includeShadowPlayers: true,
        season: season1,
      })

      assert.strictEqual(responseWithSeason1Again.team.users?.edges.length, 2)
      assert.strictEqual(
        responseWithSeason1Again.team.users?.edges.some(
          (edge) => edge.node.name === shadowPlayer.user.name
        ),
        true
      )
      assert.strictEqual(
        responseWithSeason1Again.team.users?.edges.some(
          (edge) => edge.node.name === regularPlayer.user.name
        ),
        true
      )
      assert.strictEqual(
        responseWithSeason1Again.team.users?.edges.some(
          (edge) => edge.node.name === anotherShadowPlayer.user.name
        ),
        false
      )
    })
  })

  describe('Team visit', () => {
    afterEach(async () => {
      await TeamModel.deleteMany({})
      await TeamViewModel.deleteMany({})
    })

    it('should be possible to view team', async () => {
      const user = await generateRandomUser()
      const team = await generateRandomTeam({ name: 'Test team visit' })
      const clock = sinon.useFakeTimers()

      const response = await client(user.tokens.accessToken).visitTeam({
        id: team.id,
      })

      assert.strictEqual(response.visitTeam.team?.stats?.views, 1)

      const visit = await TeamViewModel.find()
      assert.strictEqual(visit.length, 1)
      assert.strictEqual(visit[0].team.toString(), team.id.toString())

      // now let's visit again, it should fail to update stats, because user cannot view team twice in 4 hours
      const response2 = await client(user.tokens.accessToken).visitTeam({
        id: team.id,
      })

      assert.strictEqual(response2.visitTeam.team?.stats?.views, 1)

      // let's skew time to 4 hours later
      clock.tick(4 * 60 * 60 * 1000 + 1)

      // now it should work
      const response3 = await client(user.tokens.accessToken).visitTeam({
        id: team.id,
      })

      assert.strictEqual(response3.visitTeam.team?.stats?.views, 2)

      clock.restore()
    })

    it('should return an error if team not found', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).visitTeam({
        id: new Types.ObjectId(),
      })

      assert.strictEqual(response.visitTeam.errors?.[0].message, 'Team does not exist.')
    })

    describe('team hasGames', async () => {
      beforeEach(async () => {
        await GameModel.deleteMany({})
        await CrawlerDomainConfigModel.deleteMany({})
        await DiscoveredUrlModel.deleteMany({})
        await OrganizationModel.deleteMany({})
        await TeamModel.deleteMany({})
      })

      it('should only consider current season games for hasGames field', async () => {
        const discoveredUrl = await generateDiscoverUrl()
        const randomOrganization = await generateRandomOrganization('Test team organization')

        const teamId = new Types.ObjectId('67e2e36ea3c4000446182b4b')

        const randomTeam = await generateRandomTeam({
          name: 'Test team0',
          organization: randomOrganization.id,
          shortNameSlug: 'shortSlugtest',
          id: teamId,
        })

        // Create a game in the current season
        const currentSeasonGame = await generateRandomGameStats({
          nameSuffix: 'TeamHasCurrentSeasonGames',
          gameStatsPayload: {
            date: dayjs().subtract(1, 'days').toDate(),
            externalId: '5',
            discoveredUrl,
            season: CURRENT_SEASON,
            homeTeam: teamId,
            externalInfo: {
              homeTeamName: 'Home Team',
              awayTeamName: 'Away Team',
              leagueName: 'Test League',
            },
          },
        })

        // Also create a game in the previous season
        await generateRandomGameStats({
          nameSuffix: 'TeamHasPreviousSeasonGames',
          gameStatsPayload: {
            date: dayjs().subtract(1, 'days').toDate(),
            externalId: '6',
            discoveredUrl,
            season: '2023-2024',
            awayTeam: teamId,
            externalInfo: {
              homeTeamName: 'Home Team Previous',
              awayTeamName: 'Away Team Previous',
              leagueName: 'Test League Previous',
            },
          },
        })

        // First check - should return true because there's a current season game
        let response = await client().team({
          pagination: {
            first: 1,
          },
          orgSlug: randomOrganization.slug,
          slug: randomTeam.shortNameSlug,
        })
        assert.strictEqual(response.team?.team?.hasGames, true)

        // Now delete the current season game
        await GameModel.deleteOne({ _id: currentSeasonGame._id })

        // Second check - should return false even though there's still a previous season game
        response = await client().team({
          pagination: {
            first: 1,
          },
          orgSlug: randomOrganization.slug,
          slug: randomTeam.shortNameSlug,
        })
        assert.strictEqual(response.team?.team?.hasGames, false)
      })
    })
  })
})
