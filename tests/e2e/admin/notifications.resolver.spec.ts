import assert from 'assert'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { UserModel } from '../../../src/modules/user/models'
import { NotificationModel } from '../../../src/modules/user/models/notification'
import { PaginationRepository } from '../../../src/modules/user/repositories/pagination.repository'
import { PushNotificationType } from '../../graphql/components'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, generateRandomUser } from '../../utils/users'
import { pushNotificationServiceStub } from '..'

describe('Admin Notifications Resolver', () => {
  describe('Send push notification to user devices from admin', () => {
    it('should send push notification to user devices', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await UserModel.updateOne(
        { _id: user.user._id },
        {
          registeredDevices: [{ arn: 'arn:aws:sns:device1' }, { arn: 'arn:aws:sns:device2' }],
        }
      )

      const response = await client(admin.tokens.accessToken).sendPushNotification({
        userId: user.user._id,
        title: 'Test title',
        message: 'Test message',
        type: PushNotificationType.Follow,
        destinationPath: '/test',
        forcePush: true,
      })

      assert.strictEqual(response.admin.notifications.sendPushNotification.success, true)
      assert.strictEqual(response.admin.notifications.sendPushNotification.errors?.length, 0)

      sinon.assert.calledOnce(pushNotificationServiceStub.sendPushNotification)

      const pushNotificationCall = pushNotificationServiceStub.sendPushNotification.getCall(0)
      assert.strictEqual(pushNotificationCall.args[0].id, user.user._id.toString())
      assert.strictEqual(pushNotificationCall.args[1].title, 'Test title')
      assert.strictEqual(pushNotificationCall.args[1].message, 'Test message')
      assert.strictEqual(pushNotificationCall.args[1].type, PushNotificationType.Follow)
      assert.strictEqual(pushNotificationCall.args[1].params.destinationPath, '/test')
      assert.strictEqual(pushNotificationCall.args[1].forcePush, true)
    })

    it('should return an error if user is not found', async () => {
      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).sendPushNotification({
        userId: new Types.ObjectId(),
        title: 'Test title',
        message: 'Test message',
        type: PushNotificationType.NewTeamMember,
        destinationPath: '/test',
      })

      assert.strictEqual(response.admin.notifications.sendPushNotification.success, false)
      assert.strictEqual(response.admin.notifications.sendPushNotification.errors?.length, 1)
      assert.strictEqual(
        response.admin.notifications.sendPushNotification.errors[0].message,
        'User not found'
      )
    })

    it('should return an error if user has no registered devices', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const response = await client(admin.tokens.accessToken).sendPushNotification({
        userId: user.user._id,
        title: 'Test title',
        message: 'Test message',
        type: PushNotificationType.ViewAgent,
        destinationPath: '/test',
      })

      assert.strictEqual(response.admin.notifications.sendPushNotification.success, false)
      assert.strictEqual(response.admin.notifications.sendPushNotification.errors?.length, 1)
      assert.strictEqual(
        response.admin.notifications.sendPushNotification.errors[0].message,
        'User has no registered devices'
      )
    })

    it('should return an error if push notification fails to send', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await UserModel.updateOne(
        { _id: user.user._id },
        {
          registeredDevices: [{ arn: 'arn:aws:sns:device1' }],
        }
      )

      const originalStub = pushNotificationServiceStub.sendPushNotification
      const stub = (pushNotificationServiceStub.sendPushNotification = sinon.stub().throws('Error'))

      try {
        await client(admin.tokens.accessToken).sendPushNotification({
          userId: user.user._id,
          title: 'Test title',
          message: 'Test message',
          type: PushNotificationType.ViewInsights,
          destinationPath: '/test',
        })
      } catch (error) {
        assert.strictEqual(
          error.response.errors[0].message,
          'Failed to send push notification to user devices.'
        )
      }

      assert.strictEqual(stub.calledOnce, true)
      pushNotificationServiceStub.sendPushNotification = originalStub
    })
  })

  describe('notifications list', async () => {
    it('should return a list of notifications', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await NotificationModel.create({
        id: new Types.ObjectId(),
        title: 'Test title',
        body: 'Test body',
        type: PushNotificationType.Follow,
        destinationPath: '/test',
        sent: true,
        key: 'test-key',
        user: new Types.ObjectId(),
      })

      await NotificationModel.create({
        id: new Types.ObjectId(),
        title: 'Test title',
        body: 'Test body',
        type: PushNotificationType.NewTeamMember,
        destinationPath: '/test',
        sent: true,
        key: 'test-key1',
        user: user.user._id,
      })

      const notifications = await NotificationModel.find({})

      const responseFirstPage = await client(admin.tokens.accessToken).getNotifications({
        pagination: {
          first: 1,
        },
      })

      assert.strictEqual(
        responseFirstPage.admin.notifications.getNotifications.pageInfo.totalCount,
        2
      )
      assert.strictEqual(
        responseFirstPage.admin.notifications.getNotifications.pageInfo.hasNextPage,
        true
      )
      assert.strictEqual(
        responseFirstPage.admin.notifications.getNotifications.pageInfo.hasPreviousPage,
        false
      )
      assert.strictEqual(responseFirstPage.admin.notifications.getNotifications.edges.length, 1)
      assert.strictEqual(
        responseFirstPage.admin.notifications.getNotifications.edges[0].node.id,
        notifications[1].id.toString()
      )

      const responseSecondPage = await client(admin.tokens.accessToken).getNotifications({
        pagination: {
          first: 1,
          after: responseFirstPage.admin.notifications.getNotifications.pageInfo.endCursor,
        },
      })
      assert.strictEqual(
        responseSecondPage.admin.notifications.getNotifications.pageInfo.hasNextPage,
        false
      )
      assert.strictEqual(
        responseSecondPage.admin.notifications.getNotifications.pageInfo.hasPreviousPage,
        true
      )
      assert.strictEqual(responseSecondPage.admin.notifications.getNotifications.edges.length, 1)
      assert.strictEqual(
        responseSecondPage.admin.notifications.getNotifications.edges[0].node.id,
        notifications[0].id.toString()
      )

      const responseFilterType = await client(admin.tokens.accessToken).getNotifications({
        filter: {
          type: PushNotificationType.Follow,
        },
      })

      assert.strictEqual(
        responseFilterType.admin.notifications.getNotifications.pageInfo.totalCount,
        1
      )
      assert.strictEqual(
        responseFilterType.admin.notifications.getNotifications.pageInfo.hasNextPage,
        false
      )
      assert.strictEqual(
        responseFilterType.admin.notifications.getNotifications.pageInfo.hasPreviousPage,
        false
      )
      assert.strictEqual(responseFilterType.admin.notifications.getNotifications.edges.length, 1)
      assert.strictEqual(
        responseFilterType.admin.notifications.getNotifications.edges[0].node.id,
        notifications[0].id.toString()
      )

      const responseFilterUser = await client(admin.tokens.accessToken).getNotifications({
        filter: {
          user: user.user._id,
        },
      })

      assert.strictEqual(
        responseFilterUser.admin.notifications.getNotifications.pageInfo.totalCount,
        1
      )
      assert.strictEqual(
        responseFilterUser.admin.notifications.getNotifications.pageInfo.hasNextPage,
        false
      )
      assert.strictEqual(
        responseFilterUser.admin.notifications.getNotifications.pageInfo.hasPreviousPage,
        false
      )
      assert.strictEqual(responseFilterUser.admin.notifications.getNotifications.edges.length, 1)
      assert.strictEqual(
        responseFilterUser.admin.notifications.getNotifications.edges[0].node.id,
        notifications[1].id.toString()
      )
      assert.strictEqual(
        responseFilterUser.admin.notifications.getNotifications.edges[0].node.user?.name,
        user.user.name
      )
    })

    it('should return an error if failed to fetch notifications', async () => {
      const admin = await generateRandomAdminUser()
      const stub = sinon
        .stub(PaginationRepository.prototype, 'find')
        .throws(new Error('Database error'))

      try {
        await client(admin.tokens.accessToken).getNotifications()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response?.errors?.[0]?.message,
          'Failed to fetch all notifications. Please try again later.'
        )
      }
      assert.strictEqual(stub.calledOnce, true)
    })
  })
})
