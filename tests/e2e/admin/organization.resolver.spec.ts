import assert from 'assert'
import sinon from 'sinon'

import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, generateRandomOrganization } from '../../utils/users'
import { s3ClientStub } from '..'

describe('AdminOrganizationMutations - removeLogo', () => {
  beforeEach(async () => {
    await OrganizationModel.deleteMany({})
  })
  it('should remove team logo and return success', async () => {
    const adminUser = await generateRandomAdminUser()
    const randomOrganization = await generateRandomOrganization(
      'Test remove team logo organization'
    )

    await OrganizationModel.updateOne(
      { _id: randomOrganization.id },
      {
        logo: 'some/logo/path.png',
        ogLogo: 'some/logo/og.png',
        logoUnconfirmed: 'some/logo/unconfirmed.png',
      }
    )

    s3ClientStub.send.resetHistory()

    const response = await client(adminUser.tokens.accessToken).removeOrganizationLogo({
      removeOrganizationLogoId: randomOrganization.id,
    })

    assert.strictEqual(response.admin.organization.removeOrganizationLogo?.success, true)

    sinon.assert.callCount(s3ClientStub.send, 3)

    sinon.assert.calledWith(
      s3ClientStub.send.getCall(0),
      sinon.match.has('input', sinon.match.has('Key', 'some/logo/path.png'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(0),
      sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
    )

    sinon.assert.calledWith(
      s3ClientStub.send.getCall(1),
      sinon.match.has('input', sinon.match.has('Key', 'some/logo/og.png'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(1),
      sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(2),
      sinon.match.has('input', sinon.match.has('Key', 'some/logo/unconfirmed.png'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(2),
      sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
    )

    const updated = await OrganizationModel.findById(randomOrganization.id)
    assert.strictEqual(updated?.logo, null)
    assert.strictEqual(updated?.ogLogo, null)
    assert.strictEqual(updated?.logoUnconfirmed, null)
  })

  it('should return error if organization not found', async () => {
    const adminUser = await generateRandomAdminUser()
    const randomOrganization = await generateRandomOrganization(
      'Test remove team logo organization'
    )

    const stubById = sinon.stub(OrganizationModel, 'findById').resolves(null)

    const response = await client(adminUser.tokens.accessToken).removeOrganizationLogo({
      removeOrganizationLogoId: randomOrganization.id,
    })

    assert.strictEqual(response.admin.organization.removeOrganizationLogo?.success, false)
    assert.strictEqual(
      response.admin?.organization?.removeOrganizationLogo?.errors?.[0]?.message ?? 'Unknown error',
      'Organization not found'
    )

    stubById.restore()
  })

  it('should return error if db save fail', async () => {
    const adminUser = await generateRandomAdminUser()
    const randomOrganization = await generateRandomOrganization(
      'Test remove team logo organization'
    )

    const stubById = sinon
      .stub(OrganizationModel, 'updateOne')
      .throws(new Error('Expected error to be thrown'))

    try {
      await client(adminUser.tokens.accessToken).removeOrganizationLogo({
        removeOrganizationLogoId: randomOrganization.id,
      })
      assert.fail('Expected error to be thrown')
    } catch (err) {
      assert.strictEqual(err.response?.errors?.[0]?.message, 'Failed to remove organization logo')
    }

    stubById.restore()
  })
})
