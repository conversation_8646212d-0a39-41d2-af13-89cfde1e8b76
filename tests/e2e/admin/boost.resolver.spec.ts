import assert from 'assert'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { BoostModel, BoostState } from '../../../src/modules/user/models/boost'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, generateRandomUser } from '../../utils/users'

describe('Boosts query', () => {
  it('should return boosts for user with specific state and currency filter', async () => {
    await BoostModel.deleteMany({})
    const eurUser = await generateRandomUser()
    const usdUser = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    for (let i = 0; i < 10; i++) {
      await BoostModel.create({
        amount: 100 + i,
        currency: 'USD',
        user: usdUser.user._id,
        state: BoostState.CONFIRMED,
        message: `MessageUS ${i}`,
        paymentIntentId: `paymentIntentIdUS${i}`,
      })
    }

    for (let i = 0; i < 9; i++) {
      await BoostModel.create({
        amount: 100 + i,
        currency: 'EUR',
        user: eurUser.user._id,
        state: BoostState.CONFIRMED,
        message: `MessageEUR ${i}`,
        paymentIntentId: `paymentIntentIdEUR${i}`,
      })
    }

    for (let i = 0; i < 3; i++) {
      await BoostModel.create({
        amount: 100 + i,
        currency: 'USD',
        user: usdUser.user._id,
        state: BoostState.FAILED,
        message: `MessageUSFailed ${i}`,
        paymentIntentId: `paymentIntentIdUSFailed${i}`,
      })
    }

    for (let i = 0; i < 3; i++) {
      await BoostModel.create({
        amount: 100 + i,
        currency: 'USD',
        user: usdUser.user._id,
        state: BoostState.CREATED,
        message: `MessageUSCreated ${i}`,
        paymentIntentId: `paymentIntentIdUSCreated${i}`,
      })
    }

    for (let i = 0; i < 5; i++) {
      await BoostModel.create({
        amount: 100 + i,
        currency: 'EUR',
        user: eurUser.user._id,
        state: BoostState.WAITING_FOR_CONFIRMATION,
        message: `MessageEURWaiting ${i}`,
        paymentIntentId: `paymentIntentIdEURWaiting${i}`,
      })
    }

    const eurUserFilter = await client(adminUser.tokens.accessToken).boosts({
      filter: { user: eurUser.user._id },
      pagination: {
        first: 14,
      },
    })

    assert.strictEqual(eurUserFilter.admin.boost.list.edges.length, 14)
    assert.strictEqual(eurUserFilter.admin.boost.list.edges[0].node.currency, 'EUR')
    assert.strictEqual(eurUserFilter.admin.boost.list.edges[0].node.message, 'MessageEURWaiting 4')
    assert.strictEqual(eurUserFilter.admin.boost.list.edges[13].node.message, 'MessageEUR 0')
    assert.strictEqual(eurUserFilter.admin.boost.list.edges[13].node.currency, 'EUR')
    assert.strictEqual(eurUserFilter.admin.boost.list.pageInfo.hasNextPage, false)

    const usdConfirmedFirstPageResponse = await client(adminUser.tokens.accessToken).boosts({
      filter: { user: usdUser.user._id, state: BoostState.CONFIRMED, currency: 'USD' },
      pagination: {
        first: 5,
      },
    })
    assert.strictEqual(usdConfirmedFirstPageResponse.admin.boost.list.pageInfo.totalCount, 10)
    assert.strictEqual(usdConfirmedFirstPageResponse.admin.boost.list.edges.length, 5)
    assert.strictEqual(usdConfirmedFirstPageResponse.admin.boost.list.pageInfo.hasNextPage, true)
    assert.strictEqual(
      usdConfirmedFirstPageResponse.admin.boost.list.edges[0].node.state,
      BoostState.CONFIRMED
    )
    assert.strictEqual(usdConfirmedFirstPageResponse.admin.boost.list.edges[0].node.currency, 'USD')
    assert.strictEqual(
      usdConfirmedFirstPageResponse.admin.boost.list.edges[0].node.message,
      'MessageUS 9'
    )
    assert.strictEqual(
      usdConfirmedFirstPageResponse.admin.boost.list.edges[4].node.state,
      BoostState.CONFIRMED
    )
    assert.strictEqual(
      usdConfirmedFirstPageResponse.admin.boost.list.edges[4].node.message,
      'MessageUS 5'
    )
    assert.strictEqual(usdConfirmedFirstPageResponse.admin.boost.list.edges[4].node.currency, 'USD')
    assert.strictEqual(usdConfirmedFirstPageResponse.admin.boost.list.pageInfo.hasNextPage, true)

    const usdConfirmedSecondPage = await client(adminUser.tokens.accessToken).boosts({
      filter: { user: usdUser.user._id, state: BoostState.CONFIRMED, currency: 'USD' },
      pagination: {
        first: 5,
        after: usdConfirmedFirstPageResponse.admin.boost.list.pageInfo.endCursor,
      },
    })

    assert.strictEqual(usdConfirmedSecondPage.admin.boost.list.edges.length, 5)
    assert.strictEqual(
      usdConfirmedSecondPage.admin.boost.list.edges[0].node.state,
      BoostState.CONFIRMED
    )
    assert.strictEqual(usdConfirmedSecondPage.admin.boost.list.edges[0].node.currency, 'USD')
    assert.strictEqual(usdConfirmedSecondPage.admin.boost.list.edges[0].node.message, 'MessageUS 4')
    assert.strictEqual(
      usdConfirmedSecondPage.admin.boost.list.edges[4].node.state,
      BoostState.CONFIRMED
    )
    assert.strictEqual(usdConfirmedSecondPage.admin.boost.list.edges[4].node.message, 'MessageUS 0')
    assert.strictEqual(usdConfirmedSecondPage.admin.boost.list.edges[4].node.currency, 'USD')
    assert.strictEqual(usdConfirmedSecondPage.admin.boost.list.pageInfo.hasNextPage, false)

    const eurWaitingResponse = await client(adminUser.tokens.accessToken).boosts({
      filter: {
        user: eurUser.user._id,
        state: BoostState.WAITING_FOR_CONFIRMATION,
        currency: 'EUR',
      },
    })

    assert.strictEqual(eurWaitingResponse.admin.boost.list.edges.length, 5)
    assert.strictEqual(eurWaitingResponse.admin.boost.list.pageInfo.hasNextPage, false)
    assert.strictEqual(eurWaitingResponse.admin.boost.list.edges[0].node.currency, 'EUR')
    assert.strictEqual(
      eurWaitingResponse.admin.boost.list.edges[0].node.state,
      BoostState.WAITING_FOR_CONFIRMATION
    )

    const failedResponse = await client(adminUser.tokens.accessToken).boosts({
      filter: { state: BoostState.FAILED },
    })
    assert.strictEqual(failedResponse.admin.boost.list.edges.length, 3)
    assert.strictEqual(failedResponse.admin.boost.list.pageInfo.hasNextPage, false)
    assert.strictEqual(failedResponse.admin.boost.list.edges[0].node.currency, 'USD')
    assert.strictEqual(failedResponse.admin.boost.list.edges[0].node.state, BoostState.FAILED)
    assert.strictEqual(failedResponse.admin.boost.list.edges[0].node.message, 'MessageUSFailed 2')

    assert.strictEqual(failedResponse.admin.boost.list.edges[2].node.currency, 'USD')
    assert.strictEqual(failedResponse.admin.boost.list.edges[2].node.state, BoostState.FAILED)
    assert.strictEqual(failedResponse.admin.boost.list.edges[2].node.message, 'MessageUSFailed 0')

    const createdResponse = await client(adminUser.tokens.accessToken).boosts({
      filter: { state: BoostState.CREATED },
    })
    assert.strictEqual(createdResponse.admin.boost.list.edges.length, 3)
  })

  it('should throw an error if database query fails', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const aggregateStub = sinon.stub(BoostModel, 'aggregate').throws('DatabaseError')

    try {
      await client(adminUser.tokens.accessToken).boosts({
        filter: { user: user.user._id },
      })
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'Failed to fetch all boosts. Please try again later.'
      )
    }

    aggregateStub.restore()
  })
})

describe('Boost message update mutation', () => {
  it('should return error if boost not found', async () => {
    await BoostModel.deleteMany({})
    const adminUser = await generateRandomAdminUser()

    const response = await client(adminUser.tokens.accessToken).updateBoostMessage({
      boostId: new Types.ObjectId(),
      message: 'Updated message',
    })

    assert.strictEqual(response.admin.boost.updateBoostMessage.errors?.length, 1)
    assert.strictEqual(response.admin.boost.updateBoostMessage.errors[0].message, 'Boost not found')
  })

  it('should update the boost message successfully', async () => {
    await BoostModel.deleteMany({})
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const boost = await BoostModel.create({
      amount: 100,
      currency: 'USD',
      user: user.user._id,
      state: BoostState.CONFIRMED,
      message: 'Initial message',
    })

    const response = await client(adminUser.tokens.accessToken).updateBoostMessage({
      boostId: boost.id,
      message: 'Updated message',
    })

    assert.strictEqual(response.admin.boost.updateBoostMessage.errors?.length, 0)
    assert.strictEqual(response.admin.boost.updateBoostMessage.boost?.message, 'Updated message')
    assert.strictEqual(response.admin.boost.updateBoostMessage.boost?.id, boost.id)
  })

  it('should return error if database update fails', async () => {
    await BoostModel.deleteMany({})
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const boost = await BoostModel.create({
      amount: 100,
      currency: 'USD',
      user: user.user._id,
      state: BoostState.CONFIRMED,
      message: 'Initial message',
    })

    const findByIdStub = sinon.stub(BoostModel, 'findById').throws('DatabaseError')

    try {
      await client(adminUser.tokens.accessToken).updateBoostMessage({
        boostId: boost.id,
        message: 'Attempted message update',
      })
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'Failed to update boost message. Please try again later.'
      )
    }

    findByIdStub.restore()
  })
})
