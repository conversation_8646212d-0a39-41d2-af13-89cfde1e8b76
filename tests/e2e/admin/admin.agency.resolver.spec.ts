import assert from 'assert'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { UserModel } from '../../../src/modules/user/models'
import { AgencyModel } from '../../../src/modules/user/models/agency'
import { Role, SubRole } from '../../../src/modules/user/models/user'
import { client } from '../../utils/graphql'
import {
  generateRandomAdminUser,
  generateRandomAgency,
  generateRandomUser,
} from '../../utils/users'
describe('AdminAgencyMutations', () => {
  describe('addAgencyUser mutation', () => {
    it('should add user to agency successfully', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency')

      const response = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: user.user._id.toString(),
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(response.admin?.agency.addUser.errors?.length, 0)
      assert.strictEqual(
        response.admin.agency.addUser.agency?.id?.toString(),
        agency._id.toString()
      )
      assert.strictEqual(response.admin?.agency.addUser.agency.clientsCount, 1)

      // test agent added to agency
      const agentUser = await generateRandomUser({ role: Role.SPORTS_PROFESSIONAL })

      const response2 = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: agentUser.user._id.toString(),
        agencyId: agency._id,
      })

      assert.strictEqual(response2.admin?.agency.addUser.errors?.length, 0)
      assert.strictEqual(response2.admin.agency.addUser.agency?.agentsCount, 1)
    })

    it('should return error if user or agency not found', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency 2')
      const nonExistentId = new Types.ObjectId()

      try {
        await client(adminUser.tokens.accessToken).addAgencyUser({
          userId: nonExistentId.toString(),
          agencyId: agency._id.toString(),
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User or agency not found')
      }

      try {
        await client(adminUser.tokens.accessToken).addAgencyUser({
          userId: user.user._id.toString(),
          agencyId: nonExistentId.toString(),
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User or agency not found')
      }
    })

    it('should return error if user already belongs to agency', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency 3')

      await UserModel.findByIdAndUpdate(user.user._id, {
        bio: {
          agency: agency._id,
        },
      })

      const response = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: user.user._id.toString(),
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(
        response.admin.agency.addUser.errors?.[0]?.message ?? '',
        'User already belongs to this agency'
      )
    })

    it('should handle database errors gracefully', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency 4')

      const findByIdStub = sinon.stub(UserModel, 'findById').throws(new Error('Database error'))

      try {
        await client(adminUser.tokens.accessToken).addAgencyUser({
          userId: user.user._id.toString(),
          agencyId: agency._id.toString(),
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Database error')
      }

      findByIdStub.restore()
    })

    it('increment and decrease count agency user in new and old agency', async () => {
      const oldAgency = await generateRandomAgency('Test Agency update count old agency')
      const newAgency = await generateRandomAgency('Test Agency update count new agency')
      const user = await generateRandomUser()
      const agent = await generateRandomUser({ role: Role.SPORTS_PROFESSIONAL })
      const adminUser = await generateRandomAdminUser()

      const addClient = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: user.user._id.toString(),
        agencyId: oldAgency._id.toString(),
      })

      assert.strictEqual(addClient.admin.agency.addUser.agency?.clientsCount, 1)

      const addAgent = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: agent.user._id.toString(),
        agencyId: oldAgency._id.toString(),
      })

      assert.strictEqual(addAgent.admin.agency.addUser.agency?.agentsCount, 1)

      const moveClient = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: user.user._id.toString(),
        agencyId: newAgency._id.toString(),
      })

      assert.strictEqual(moveClient.admin.agency.addUser.agency?.clientsCount, 1)

      const moveAgent = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: agent.user._id.toString(),
        agencyId: newAgency._id.toString(),
      })

      assert.strictEqual(moveAgent.admin.agency.addUser.agency?.agentsCount, 1)

      const oldAgencyData = await AgencyModel.findById(oldAgency.id)
      assert.strictEqual(oldAgencyData?.agentsCount, 0)
      assert.strictEqual(oldAgencyData?.clientsCount, 0)
    })
  })

  describe('removeAgencyUser mutation', () => {
    it('should remove user from agency successfully', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency Remove')
      const agent = await generateRandomUser({
        role: Role.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
      })

      await UserModel.findByIdAndUpdate(user.user._id, {
        bio: {
          agency: agency._id,
        },
        agentV2: agent.user._id,
      })

      const response = await client(adminUser.tokens.accessToken).removeAgencyUser({
        userId: user.user._id.toString(),
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(response.admin?.agency.removeAgencyUser.errors?.length, 0)
      assert.strictEqual(
        response.admin.agency.removeAgencyUser.agency?.id?.toString(),
        agency._id.toString()
      )
      assert.strictEqual(response.admin?.agency.removeAgencyUser.agency.clientsCount, 0)

      const updatedUser = await UserModel.findById(user.user._id)
      assert.strictEqual(updatedUser?.bio?.agency, undefined)
      assert.strictEqual(updatedUser?.agentV2, undefined)
    })

    it('should return error if user or agency not found', async () => {
      const adminUser = await generateRandomAdminUser()
      const nonExistentId = new Types.ObjectId()
      const agency = await generateRandomAgency('Test Agency Remove Error')

      try {
        await client(adminUser.tokens.accessToken).removeAgencyUser({
          userId: nonExistentId.toString(),
          agencyId: agency._id.toString(),
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User or agency not found')
      }

      try {
        const user = await generateRandomUser()
        await client(adminUser.tokens.accessToken).removeAgencyUser({
          userId: user.user._id.toString(),
          agencyId: nonExistentId.toString(),
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User or agency not found')
      }
    })

    it('should return error if user does not belong to the agency', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency Remove Belong Error')

      const response = await client(adminUser.tokens.accessToken).removeAgencyUser({
        userId: user.user._id.toString(),
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(
        response.admin.agency.removeAgencyUser.errors?.[0]?.message ?? '',
        'User does not belong belong to this agency'
      )
    })

    it('should handle database errors gracefully', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test Agency Remove DB Error')

      const findByIdStub = sinon.stub(UserModel, 'findById').throws(new Error('Database error'))

      try {
        await client(adminUser.tokens.accessToken).removeAgencyUser({
          userId: user.user._id.toString(),
          agencyId: agency._id.toString(),
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Database error')
      }

      findByIdStub.restore()
    })

    it('should decrement count of agents and clients in the agency', async () => {
      const adminUser = await generateRandomAdminUser()
      const agency = await generateRandomAgency('Test Agency for Removal Count')
      const user = await generateRandomUser()
      const agent = await generateRandomUser({ role: Role.SPORTS_PROFESSIONAL })

      await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: user.user._id.toString(),
        agencyId: agency._id.toString(),
      })
      const responseAdd = await client(adminUser.tokens.accessToken).addAgencyUser({
        userId: agent.user._id.toString(),
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(responseAdd.admin.agency.addUser.agency?.clientsCount, 1)
      assert.strictEqual(responseAdd.admin.agency.addUser.agency?.agentsCount, 1)

      const removeClient = await client(adminUser.tokens.accessToken).removeAgencyUser({
        userId: user.user._id.toString(),
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(removeClient.admin.agency.removeAgencyUser.agency?.clientsCount, 0)

      const removeAgent = await client(adminUser.tokens.accessToken).removeAgencyUser({
        userId: agent.user._id.toString(),
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(removeAgent.admin.agency.removeAgencyUser.agency?.agentsCount, 0)

      const dbAgency = await AgencyModel.findById(agency._id)
      assert.strictEqual(dbAgency?.clientsCount, 0)
      assert.strictEqual(dbAgency?.agentsCount, 0)

      const updatedUser = await UserModel.findById(user.user._id)
      assert.strictEqual(updatedUser?.bio?.agency, undefined)
      assert.strictEqual(updatedUser?.subrole, undefined)
    })
  })
})
