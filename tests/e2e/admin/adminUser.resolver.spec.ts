import assert from 'assert'
import sinon from 'sinon'

import { AdminUserModel } from '../../../src/modules/admin/models/adminUser'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser } from '../../utils/users'
import { googleServiceStub, sesClientStub } from '..'

import { faker } from '@faker-js/faker'

describe('AdminUser Resolver', () => {
  describe('loginAdmin mutation', () => {
    it("should return errors if user doesn't exist", async () => {
      const findOneStub = sinon.stub(AdminUserModel, 'findOne').resolves(null)

      const response = await client().loginAdmin({ email: 'null' })
      assert.strictEqual(
        response.loginAdmin.errors[0].message,
        'User with email address null not found'
      )
      sinon.assert.calledOnce(findOneStub)
      findOneStub.restore()
    })

    it('should return empty errors if user exists', async () => {
      const adminUser = await generateRandomAdminUser()

      const response = await client().loginAdmin({ email: adminUser.user.email })

      assert.strictEqual(response.loginAdmin.errors.length, 0)

      const user = await AdminUserModel.findById({ _id: adminUser.user._id })

      const verificationCode = user?.verificationCode?.code

      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [adminUser.user.email]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'VerifyEmailStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('TemplateData', `{"code":"${verificationCode}","verifyUrl":""}`)
        )
      )
    })

    it("should error if failed to login admin user'", async () => {
      const findOneStub = sinon.stub(AdminUserModel, 'findOne').throws('error')

      try {
        await client().loginAdmin({ email: 'null' })
      } catch (error) {
        assert.strictEqual(
          error.response.errors[0].message,
          'Failed to login user by email. Please try again later.'
        )
      }
      sinon.assert.calledOnce(findOneStub)
      findOneStub.restore()
    })
  })
  describe('verifyAdminByEmail mutation', () => {
    it('should return errors if verification code is invalid', async () => {
      const adminUser = await generateRandomAdminUser()

      const response = await client().verifyAdminByEmail({
        email: adminUser.user.email,
        code: 'invalid',
      })

      assert.strictEqual(response.verifyAdminByEmail.errors[0].message, 'Invalid verification code')
    })

    it('should return errors if user does not exist', async () => {
      const email = faker.internet.email()

      const response = await client().verifyAdminByEmail({
        email: email,
        code: 'invalid',
      })

      assert.strictEqual(
        response.verifyAdminByEmail.errors[0].message,
        `User with email address ${email} not found`
      )
    })

    it("should return error if verification code is expired'", async () => {
      const adminUser = await generateRandomAdminUser()

      await client().loginAdmin({ email: adminUser.user.email })

      const user = await AdminUserModel.findByIdAndUpdate(
        { _id: adminUser.user._id },
        { 'verificationCode.expirationDate': new Date(Date.now() - 1000 * 60 * 60) }
      )

      const response = await client().verifyAdminByEmail({
        email: adminUser.user.email,
        code: user?.verificationCode?.code as string,
      })

      assert.strictEqual(
        response.verifyAdminByEmail.errors[0].message,
        'Verification code expired, new code sent'
      )
    })

    it("should return response if verification code is valid'", async () => {
      const adminUser = await generateRandomAdminUser()

      await client().loginAdmin({ email: adminUser.user.email })

      const user = await AdminUserModel.findByIdAndUpdate({ _id: adminUser.user._id })

      const response = await client().verifyAdminByEmail({
        email: adminUser.user.email,
        code: user?.verificationCode?.code as string,
      })

      assert.strictEqual(response.verifyAdminByEmail.errors.length, 0)
      assert.strictEqual(response.verifyAdminByEmail.user?.email, adminUser.user.email)
      assert.strictEqual(response.verifyAdminByEmail.user.firstname, adminUser.user.firstname)
      assert.strictEqual(response.verifyAdminByEmail.user.lastname, adminUser.user.lastname)
    })
  })

  describe('create admin mutation', () => {
    it('should return errors if user already exists', async () => {
      const adminUser = await generateRandomAdminUser()

      try {
        await client(adminUser.tokens.accessToken).createAdmin({
          input: {
            email: adminUser.user.email,
            firstname: 'Tomas',
            lastname: 'Remeta',
          },
        })
      } catch (error) {
        assert.strictEqual(
          error.response.errors[0].message,
          'Admin user with this email already exists'
        )
      }
    })

    it('should return errors if failed to create admin user', async () => {
      const adminUser = await generateRandomAdminUser()

      const createStub = sinon.stub(AdminUserModel, 'create').throws('error')

      try {
        await client(adminUser.tokens.accessToken).createAdmin({
          input: {
            email: '<EMAIL>',
            firstname: 'Fail',
            lastname: 'Create',
          },
        })
      } catch (error) {
        assert.strictEqual(
          error.response.errors[0].message,
          'Failed to create admin user. Please try again later.'
        )
      }

      sinon.assert.calledOnce(createStub)
    })

    it('should return response if admin user is created', async () => {
      const adminUser = await generateRandomAdminUser()

      const response = await client(adminUser.tokens.accessToken).createAdmin({
        input: {
          email: '<EMAIL>',
          firstname: 'Admin',
          lastname: 'Created',
        },
      })

      assert.strictEqual(response.createAdmin.user?.email, '<EMAIL>')
      assert.strictEqual(response.createAdmin.user?.firstname, 'Admin')
      assert.strictEqual(response.createAdmin.user?.lastname, 'Created')
    })

    describe('delete admin mutation', () => {
      it('should throw errors if admin user not found', async () => {
        const adminUser = await generateRandomAdminUser()

        const findByIdStub = sinon.stub(AdminUserModel, 'findById').resolves(null)

        try {
          await client(adminUser.tokens.accessToken).deleteAdmin({
            deleteAdminId: '000000000000000000000000',
          })
        } catch (error) {
          assert.strictEqual(error.response.errors[0].message, 'Admin user not found')
        }

        sinon.assert.calledOnce(findByIdStub)
      })

      it('should throw errors if failed to delete admin user', async () => {
        const adminUser = await generateRandomAdminUser()

        const deleteOneStub = sinon.stub(AdminUserModel, 'deleteOne').throws('error')

        try {
          await client(adminUser.tokens.accessToken).deleteAdmin({
            deleteAdminId: adminUser.user._id,
          })
        } catch (error) {
          assert.strictEqual(
            error.response.errors[0].message,
            'Failed to delete admin user. Please try again later.'
          )
        }

        sinon.assert.calledOnce(deleteOneStub)
      })

      it('should return true if admin user is deleted', async () => {
        const adminUser = await generateRandomAdminUser()

        const response = await client(adminUser.tokens.accessToken).deleteAdmin({
          deleteAdminId: adminUser.user._id,
        })

        assert.strictEqual(response.deleteAdmin, true)
      })
    })
  })

  describe('google sign in admin mutation', () => {
    it('should sign in adminUser with google token', async function () {
      const adminUser = await generateRandomAdminUser()

      googleServiceStub.verifyIdToken.resolves({
        getPayload: () => ({
          sub: '1234567890',
          email: adminUser.user.email,
        }),
      })

      const response = await client().googleSignInAdmin({
        input: {
          idToken: 'valid-token',
        },
      })

      assert.strictEqual(response.googleSignInAdmin.errors.length, 0)
      assert.strictEqual(response.googleSignInAdmin.user?.email, adminUser.user.email)

      sinon.assert.calledOnce(googleServiceStub.verifyIdToken)
    })

    it('should return Error when adminUser not found', async function () {
      const email = faker.internet.email()

      googleServiceStub.verifyIdToken.resolves({
        getPayload: () => ({
          sub: '1234567890',
          email: email,
        }),
      })

      const response = await client().googleSignInAdmin({
        input: {
          idToken: 'valid-token',
        },
      })

      assert.strictEqual(response.googleSignInAdmin.errors.length, 1)
      assert.strictEqual(
        response.googleSignInAdmin.errors[0].message,
        `User with email address ${email} not found`
      )

      sinon.assert.calledOnce(googleServiceStub.verifyIdToken)
      sinon.assert.calledWith(
        googleServiceStub.verifyIdToken,
        sinon.match.has('idToken', 'valid-token')
      )
    })

    it('should throw GraphQLError when verify id token function fails', async function () {
      googleServiceStub.verifyIdToken.throws('Error')

      try {
        await client().googleSignInAdmin({
          input: {
            idToken: 'invalid-token',
          },
        })
        assert.fail('Should have thrown an error')
      } catch (error) {
        assert.strictEqual(
          error.response.errors[0].message,
          'Failed to sign in with Google. Please try again later.'
        )
      }
    })
  })

  describe('currentAdminUser query', () => {
    it('should return current admin user', async () => {
      const adminUser = await generateRandomAdminUser()

      const response = await client(adminUser.tokens.accessToken).currentAdminUser()

      assert.strictEqual(response.currentAdminUser.email, adminUser.user.email)
      assert.strictEqual(response.currentAdminUser.firstname, adminUser.user.firstname)
      assert.strictEqual(response.currentAdminUser.lastname, adminUser.user.lastname)
    })

    it('should throw error if user not found', async () => {
      const adminUser = await generateRandomAdminUser()

      const findOneStub = sinon.stub(AdminUserModel, 'findOne').resolves(null)

      try {
        await client(adminUser.tokens.accessToken).currentAdminUser()
      } catch (error) {
        assert.strictEqual(error.response.errors[0].message, 'User does not exist.')
      }

      sinon.assert.calledOnce(findOneStub)
    })
  })

  describe('adminUsers query', () => {
    it('should return all admin users', async () => {
      await AdminUserModel.deleteMany()

      const adminUsers = []
      for (let i = 0; i < 11; i++) {
        const admin = await generateRandomAdminUser('Test', 'AdminQuery ' + i)

        adminUsers.push(admin)
      }

      const responseFirstPage = await client(adminUsers[0].tokens.accessToken).adminUsers()

      assert.strictEqual(responseFirstPage.adminUsers.edges.length, 10)
      assert.strictEqual(responseFirstPage.adminUsers.edges[0].node.firstname, 'Test')
      assert.strictEqual(responseFirstPage.adminUsers.edges[0].node.lastname, 'AdminQuery 10')
      assert.strictEqual(responseFirstPage.adminUsers.edges[9].node.firstname, 'Test')
      assert.strictEqual(responseFirstPage.adminUsers.edges[9].node.lastname, 'AdminQuery 1')
      assert.strictEqual(responseFirstPage.adminUsers.pageInfo.hasNextPage, true)
      assert.strictEqual(responseFirstPage.adminUsers.pageInfo.hasPreviousPage, false)

      const responseSecondPage = await client(adminUsers[0].tokens.accessToken).adminUsers({
        pagination: { first: 10, after: responseFirstPage.adminUsers.pageInfo.endCursor },
      })

      assert.strictEqual(responseSecondPage.adminUsers.edges.length, 1)
      assert.strictEqual(responseSecondPage.adminUsers.edges[0].node.firstname, 'Test')
      assert.strictEqual(responseSecondPage.adminUsers.edges[0].node.lastname, 'AdminQuery 0')
    })
  })
})
