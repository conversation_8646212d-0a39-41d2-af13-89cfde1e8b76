// deleteAllUserStats mutation test

import assert from 'assert'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { League, LeagueModel } from '../../../src/modules/stats/models/league'
import { Team, TeamModel } from '../../../src/modules/stats/models/team'
import { SeasonType, UserStatsModel } from '../../../src/modules/stats/models/userStats'
import { Role as dbrole } from '../../../src/modules/user/models/user'
import { MigrateUserStatsType } from '../../graphql/components'
import { client } from '../../utils/graphql'
import {
  generateRandomAdminUser,
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from '../../utils/users'

describe('admin user stats resolver', () => {
  describe('delete all user stats mutation', () => {
    it('should delete all user stats', async () => {
      const user = await generateRandomUser()
      const admin = await generateRandomAdminUser()

      // generate user stats
      await UserStatsModel.create({
        gamesPlayed: 1,
        goals: 1,
        assists: 1,
        pim: 1,
        plusMinus: 1,
        user: user.user._id,
        season: '2021-2022',
        seasonType: SeasonType.REGULAR,
      })

      const response = await client(admin.tokens.accessToken).deleteAllUserStats({
        userId: user.user._id,
      })

      const userStats = await UserStatsModel.find({ user: user.user._id })

      assert.strictEqual(userStats.length, 0)
      assert.strictEqual(response.admin.userStats.deleteAllUserStats.success, true)
    })

    it('should return an error if user does not exist', async () => {
      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).deleteAllUserStats({
        userId: '60b0e8c1e4b7e6f7c1b4c3c8',
      })

      assert.strictEqual(response.admin.userStats.deleteAllUserStats.errors?.length, 1)
      assert.strictEqual(
        response.admin.userStats.deleteAllUserStats.errors![0].message,
        'User not found'
      )
    })
  })

  describe('UserStats without team or league', () => {
    it('should return user stats without team or league', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const shadowPlayer = await generateRandomUser({
        firstname: 'shadow',
        lastname: 'priest',
        role: dbrole.SHADOW_PLAYER,
      })
      const organization = await generateRandomOrganization('Test Admin User Stats without team')
      const league = await generateRandomLeague('Test Admin User Stats League without team')
      const team = await generateRandomTeam({
        name: 'Test Admin User Stats without team',
        organization: organization._id.toString(),
      })

      await UserStatsModel.deleteMany({})

      await client(randomAdminUser.tokens.accessToken).createUserStats({
        input: {
          assists: 11,
          wins: 11,
          goals: 9,
          league: league._id,
          losses: 2,
          orderInSeason: 1,
          season: '2011',
          shutouts: 7,
          svp: 1,
          team: team._id,
          ties: 1,
          user: randomUser.user._id,
          gamesPlayed: 10,
          gaa: 2,
        },
      })

      await UserStatsModel.create({
        assists: 5,
        wins: 7,
        league: league._id,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
        externalInfo: {
          externalTeamName: 'Test team',
          externalTeamCountry: 'Test country',
          externalLeagueName: 'Test league',
          externalLeagueCountry: 'Test country',
        },
      })

      await UserStatsModel.create({
        assists: 1,
        wins: 2,
        team: team._id,
        goals: 3,
        losses: 4,
        orderInSeason: 5,
        season: '2020',
        user: shadowPlayer.user._id,
        gamesPlayed: 11,
        gaa: 2,
        externalInfo: {
          externalTeamName: 'Test team 2',
          externalTeamCountry: 'Test country 2',
          externalLeagueName: 'Test league 2',
          externalLeagueCountry: 'Test country 2',
        },
      })

      const response = await client(randomAdminUser.tokens.accessToken).adminUserStatsList({
        filter: {
          teamMissing: true,
        },
      })

      assert.strictEqual(response.admin.userStats.list.edges.length, 1)

      //stats without team
      assert.strictEqual(response.admin.userStats.list.edges[0].node.team, null)
      assert.strictEqual(
        response.admin.userStats.list.edges[0].node.league?.name,
        'Test Admin User Stats League without team'
      )
      assert.strictEqual(response.admin.userStats.list.edges[0].node.goals, 10)
      assert.strictEqual(response.admin.userStats.list.edges[0].node.assists, 5)
      assert.strictEqual(response.admin.userStats.list.edges[0].node.wins, 7)
      assert.strictEqual(response.admin.userStats.list.edges[0].node.losses, 3)
      assert.strictEqual(response.admin.userStats.list.edges[0].node.gamesPlayed, 15)
      assert.strictEqual(response.admin.userStats.list.edges[0].node.gaa, 2.1)

      const responseLeagueMissing = await client(
        randomAdminUser.tokens.accessToken
      ).adminUserStatsList({
        filter: {
          leagueMissing: true,
        },
      })

      //stats without league
      assert.strictEqual(
        responseLeagueMissing.admin.userStats.list.edges[0].node.team?.name,
        'Test Admin User Stats without team'
      )
      assert.strictEqual(responseLeagueMissing.admin.userStats.list.edges[0].node.league, null)
      assert.strictEqual(responseLeagueMissing.admin.userStats.list.edges[0].node.goals, 3)
      assert.strictEqual(responseLeagueMissing.admin.userStats.list.edges[0].node.assists, 1)
      assert.strictEqual(responseLeagueMissing.admin.userStats.list.edges[0].node.wins, 2)
      assert.strictEqual(responseLeagueMissing.admin.userStats.list.edges[0].node.losses, 4)
      assert.strictEqual(responseLeagueMissing.admin.userStats.list.edges[0].node.gamesPlayed, 11)
      assert.strictEqual(responseLeagueMissing.admin.userStats.list.edges[0].node.gaa, 2)
    })

    it('should return user stats filter by team or league', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const team = await generateRandomTeam({ name: 'Test team - user stats admin filters' })
      const league = await generateRandomLeague('Test league - user stats admin filters')

      await UserStatsModel.deleteMany({})

      await UserStatsModel.create({
        assists: 5,
        wins: 7,
        league: league._id,
        team: team._id,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
      })

      const response1 = await client(randomAdminUser.tokens.accessToken).adminUserStatsList({
        filter: {
          team: team._id,
        },
      })

      assert.strictEqual(response1.admin.userStats.list.edges.length, 1)
      assert.strictEqual(response1.admin.userStats.list.edges[0].node.team?.name, team.name)

      const response2 = await client(randomAdminUser.tokens.accessToken).adminUserStatsList({
        filter: {
          league: league._id,
        },
      })

      assert.strictEqual(response2.admin.userStats.list.edges.length, 1)
      assert.strictEqual(response2.admin.userStats.list.edges[0].node.league?.name, league.name)
    })
  })

  describe('User stats migration', () => {
    it('should migrate user stats from one team to another', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const team1 = await generateRandomTeam({ name: 'Test team 1 - user stats migration' })
      const team2 = await generateRandomTeam({ name: 'Test team 2 - user stats migration' })

      await UserStatsModel.deleteMany({})

      await UserStatsModel.create({
        assists: 5,
        wins: 7,
        team: team1._id,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
      })

      const response = await client(randomAdminUser.tokens.accessToken).migrateUserStats({
        input: {
          type: MigrateUserStatsType.Team,
          from: team1._id,
          to: team2._id,
        },
      })

      assert.strictEqual(response.admin.userStats.migrate.success, true)
      assert.strictEqual(response.admin.userStats.migrate.modifiedCount, 1)

      const userStats = await UserStatsModel.find({ user: randomUser.user._id })

      assert.strictEqual(userStats.length, 1)
      assert.strictEqual(userStats[0].team?.toString(), team2._id.toString())
    })

    it('should migrate user stats from one team to another also filtered by league', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const team1 = await generateRandomTeam({
        name: 'Test team 1 - user stats migration by league',
      })
      const team2 = await generateRandomTeam({
        name: 'Test team 2 - user stats migration by league',
      })
      const league = await generateRandomLeague('Test league - user stats migration by league')

      await UserStatsModel.deleteMany({})

      await UserStatsModel.create({
        assists: 5,
        wins: 7,
        team: team1._id,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
        league: league._id,
      })

      // without league
      await UserStatsModel.create({
        assists: 1,
        wins: 0,
        team: team1._id,
        goals: 0,
        losses: 0,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
      })

      const response = await client(randomAdminUser.tokens.accessToken).migrateUserStats({
        input: {
          type: MigrateUserStatsType.Team,
          from: team1._id,
          to: team2._id,
          league: league._id,
        },
      })

      assert.strictEqual(response.admin.userStats.migrate.success, true)
      assert.strictEqual(response.admin.userStats.migrate.modifiedCount, 1)

      const userStats = await UserStatsModel.find({ user: randomUser.user._id, team: team2._id })

      assert.strictEqual(userStats.length, 1)
      assert.strictEqual(userStats[0].team?.toString(), team2._id.toString())
      assert.strictEqual(userStats[0].league?.toString(), league._id.toString())
      assert.strictEqual(userStats[0].goals, 10)
    })

    it('should migrate user stats from one league to another', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const league1 = await generateRandomLeague('Test league 1 - user stats migration')
      const league2 = await generateRandomLeague('Test league 2 - user stats migration')

      await UserStatsModel.deleteMany({})

      await UserStatsModel.create({
        assists: 5,
        wins: 7,
        league: league1._id,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
      })

      const response = await client(randomAdminUser.tokens.accessToken).migrateUserStats({
        input: {
          type: MigrateUserStatsType.League,
          from: league1._id,
          to: league2._id,
        },
      })

      assert.strictEqual(response.admin.userStats.migrate.success, true)
      assert.strictEqual(response.admin.userStats.migrate.modifiedCount, 1)

      const userStats = await UserStatsModel.find({ user: randomUser.user._id })

      assert.strictEqual(userStats.length, 1)
      assert.strictEqual(userStats[0].league?.toString(), league2._id.toString())

      const alternativeNameOnTarget = await LeagueModel.findById(league2._id)
      assert.strictEqual(alternativeNameOnTarget?.alternativeNames.length, 1)
      assert.strictEqual(alternativeNameOnTarget?.alternativeNames[0].name, league1.name)
      assert.strictEqual(alternativeNameOnTarget?.alternativeNames[0].slug, league1.slug)
    })

    it('should return errors if migration fails', async () => {
      const randomAdminUser = await generateRandomAdminUser()

      const updateStub = sinon.stub(UserStatsModel, 'updateMany').throws('Error')

      const response = await client(randomAdminUser.tokens.accessToken).migrateUserStats({
        input: {
          type: MigrateUserStatsType.League,
          from: '60b0e8c1e4b7e6f7c1b4c3c8',
          to: '60b0e8c1e4b7e6f7c1b4c3c8',
        },
      })

      assert.strictEqual(response.admin.userStats.migrate.success, false)
      assert.strictEqual(response.admin.userStats.migrate.errors?.length, 1)
      assert.strictEqual(
        response.admin.userStats.migrate.errors![0].message,
        'Failed to update stats'
      )

      updateStub.restore()
    })

    it('ensures no duplicate alternative names are added to leagues', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const league1 = await generateRandomLeague('Test league 1 - user stats migration - duplicate')
      const league2 = await generateRandomLeague('Test league 2 - user stats migration - duplicate')

      await UserStatsModel.deleteMany({})
      await LeagueModel.findByIdAndUpdate(league2._id, {
        $addToSet: { alternativeNames: { name: league1.name, slug: league1.slug } },
      })

      await UserStatsModel.create({
        assists: 5,
        wins: 7,
        league: league1._id,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
      })

      const response = await client(randomAdminUser.tokens.accessToken).migrateUserStats({
        input: {
          type: MigrateUserStatsType.League,
          from: league1._id,
          to: league2._id,
        },
      })

      assert.strictEqual(response.admin.userStats.migrate.success, true)
      assert.strictEqual(response.admin.userStats.migrate.modifiedCount, 1)

      const userStats = await UserStatsModel.find({ user: randomUser.user._id })

      assert.strictEqual(userStats.length, 1)
      assert.strictEqual(userStats[0].league?.toString(), league2._id.toString())

      const alternativeNameOnTarget = await LeagueModel.findById(league2._id)
      assert.strictEqual(alternativeNameOnTarget?.alternativeNames.length, 1)
    })

    it('ensures no duplicate alternative names are added to teams', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const team1 = await generateRandomTeam({
        name: 'Test team 1 - user stats migration - duplicate',
      })
      const team2 = await generateRandomTeam({
        name: 'Test team 2 - user stats migration - duplicate',
      })

      await UserStatsModel.deleteMany({})
      await TeamModel.findByIdAndUpdate(team2._id, {
        $addToSet: { alternativeNames: { name: team1.name, slug: team1.slug } },
      })

      await UserStatsModel.create({
        assists: 5,
        wins: 7,
        team: team1._id,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
      })

      const response = await client(randomAdminUser.tokens.accessToken).migrateUserStats({
        input: {
          type: MigrateUserStatsType.Team,
          from: team1._id,
          to: team2._id,
        },
      })

      assert.strictEqual(response.admin.userStats.migrate.success, true)
      assert.strictEqual(response.admin.userStats.migrate.modifiedCount, 1)

      const userStats = await UserStatsModel.find({ user: randomUser.user._id })

      assert.strictEqual(userStats.length, 1)
      assert.strictEqual(userStats[0].team?.toString(), team2._id.toString())

      const alternativeNameOnTarget = await TeamModel.findById(team2._id)
      assert.strictEqual(alternativeNameOnTarget?.alternativeNames.length, 1)
    })
  })

  describe('sync user stats', () => {
    before(async () => {
      await UserStatsModel.deleteMany({})
      await TeamModel.deleteMany({})
      await LeagueModel.deleteMany({})
    })

    it('should sync user stats and update related stats', async () => {
      const randomUser = await generateRandomUser()
      const randomAdminUser = await generateRandomAdminUser()
      const league = await generateRandomLeague('Test league - user stats sync', 'CZ')
      await generateRandomTeam({
        name: 'Test team - user stats sync',
        country: 'CZ',
        leagues: [league.id],
      })

      // Create a user stats with external info but no team/league
      const userStats = await UserStatsModel.create({
        assists: 5,
        wins: 7,
        goals: 10,
        losses: 3,
        orderInSeason: 1,
        season: '2021',
        user: randomUser.user._id,
        gamesPlayed: 15,
        gaa: 2.1,
        externalInfo: {
          externalTeamName: 'Test team - user stats sync',
          externalTeamCountry: 'CZ',
          externalLeagueName: 'Test league - user stats sync',
          externalLeagueCountry: 'CZ',
        },
      })

      // Create another user stats with the same external info
      await UserStatsModel.create({
        assists: 3,
        wins: 2,
        goals: 5,
        losses: 1,
        orderInSeason: 2,
        season: '2022',
        user: randomUser.user._id,
        gamesPlayed: 8,
        gaa: 1.5,
        externalInfo: {
          externalTeamName: 'Test team - user stats sync',
          externalTeamCountry: 'Test country',
          externalLeagueName: 'Test league - user stats sync',
          externalLeagueCountry: 'Test country',
        },
      })

      // Use GraphQL client to call the sync mutation
      const response = await client(randomAdminUser.tokens.accessToken).syncAdminUserStats({
        id: userStats._id.toString(),
      })

      // Verify the response
      assert.ok(response.admin.userStats.sync.userStats, 'Response should have userStats')
      assert.strictEqual(
        response.admin.userStats.sync.userStats?.id,
        userStats._id.toString(),
        'UserStats ID should match'
      )

      // Verify the updated user stats
      const updatedUserStats = await UserStatsModel.find().populate(['team', 'league'])

      assert.strictEqual(
        (updatedUserStats[0]?.team as Team)?.name,
        'Test team - user stats sync',
        'Team name should match'
      )
      assert.strictEqual(
        (updatedUserStats[0]?.league as League)?.name,
        'Test league - user stats sync',
        'League name should match'
      )

      assert.strictEqual(
        updatedUserStats[1]?.externalInfo?.externalTeamName,
        'Test team - user stats sync',
        'External team name should match'
      )
      assert.strictEqual(
        updatedUserStats[1]?.externalInfo.externalLeagueName,
        'Test league - user stats sync',
        'External league name should match'
      )
    })

    it('should return error when user stats not found', async () => {
      const randomAdminUser = await generateRandomAdminUser()
      const nonExistentId = new Types.ObjectId().toString()

      // Use GraphQL client to call the sync mutation
      const response = await client(randomAdminUser.tokens.accessToken).syncAdminUserStats({
        id: nonExistentId,
      })

      assert.strictEqual(response.admin.userStats.sync.userStats, null, 'UserStats should be null')
      assert.ok(response.admin.userStats.sync.errors, 'Response should have errors')
      assert.strictEqual(
        response.admin.userStats.sync.errors?.length,
        1,
        'Response should have exactly one error'
      )
      assert.strictEqual(
        response.admin.userStats.sync.errors?.[0].message,
        'User stats not found',
        'Error message should be "User stats not found"'
      )
    })
  })
})
