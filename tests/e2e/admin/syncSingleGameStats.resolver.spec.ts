import assert from 'assert'
import { Types } from 'mongoose'

import {
  CrawlerDomainConfigModel,
  CrawlerEntity,
} from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../../src/modules/stats/models/game/game'
import { LeagueModel } from '../../../src/modules/stats/models/league'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, generateRandomOrganization } from '../../utils/users'

describe('Admin Game Stats Resolver - syncSingle mutation', () => {
  before(async () => {
    await GameModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
    await LeagueModel.deleteMany({})
  })

  it('should sync a single game successfully with force=true', async () => {
    const adminUser = await generateRandomAdminUser()

    const initialOrg = await generateRandomOrganization('Initial Org')

    const initialLeague = await LeagueModel.create({
      name: 'Initial League',
      slug: 'initial-league',
      countries: ['CZ'],
    })

    const initialHomeTeam = await TeamModel.create({
      name: 'Initial Home Team',
      slug: 'initial-home-team',
      shortName: 'Home',
      shortNameSlug: 'home',
      organization: initialOrg._id,
    })

    const initialAwayTeam = await TeamModel.create({
      name: 'Initial Away Team',
      slug: 'initial-away-team',
      shortName: 'Away',
      shortNameSlug: 'away',
      organization: initialOrg._id,
    })

    const crawlerDomainConfig = await CrawlerDomainConfigModel.create({
      domain: `example-sync-single-${Date.now()}.com`,
    })

    const discoveredUrl = await DiscoveredUrlModel.create({
      url: `https://example-sync-single-${Date.now()}.com/game/123`,
      externalId: '123',
      entity: CrawlerEntity.GAME,
      crawlerDomainConfig,
    })

    const game = await GameModel.create({
      date: new Date(),
      externalId: '12345',
      discoveredUrl: discoveredUrl._id,
      externalInfo: {
        homeTeamName: 'HC Sparta Praha',
        awayTeamName: 'HC Slavia Praha',
        leagueName: 'Czech Extraliga',
      },
      // Set initial teams and league
      homeTeam: initialHomeTeam._id,
      awayTeam: initialAwayTeam._id,
      league: initialLeague._id,
    })

    const correctOrg = await generateRandomOrganization('Correct Org')

    const correctLeague = await LeagueModel.create({
      name: 'Czech Extraliga',
      slug: 'czech-extraliga',
      countries: ['CZ'],
    })

    const correctHomeTeam = await TeamModel.create({
      name: 'HC Sparta Praha',
      slug: 'hc-sparta-praha',
      shortName: 'Sparta',
      shortNameSlug: 'sparta',
      organization: correctOrg._id,
      leagues: [correctLeague._id],
    })

    const correctAwayTeam = await TeamModel.create({
      name: 'HC Slavia Praha',
      slug: 'hc-slavia-praha',
      shortName: 'Slavia',
      shortNameSlug: 'slavia',
      organization: correctOrg._id,
      leagues: [correctLeague._id],
    })

    // Call the syncSingle mutation
    await client(adminUser.tokens.accessToken).syncSingleGameStats({
      id: game._id,
    })

    // Fetch the updated game from the database to verify the teams and league have been updated
    const updatedGame = await GameModel.findById(game._id).lean()

    assert.strictEqual(updatedGame?.homeTeam?.toString(), correctHomeTeam._id.toString())
    assert.strictEqual(updatedGame?.awayTeam?.toString(), correctAwayTeam._id.toString())
    assert.strictEqual(updatedGame?.league?.toString(), correctLeague._id.toString())
  })

  it('should return EntityNotFoundError when game does not exist', async () => {
    // Create an admin user for authentication
    const adminUser = await generateRandomAdminUser()

    // Generate a random ObjectId that doesn't exist in the database
    const nonExistentId = new Types.ObjectId()

    // Call the syncSingle mutation with a non-existent ID
    const response = await client(adminUser.tokens.accessToken).syncSingleGameStats({
      id: nonExistentId,
    })

    // Verify the response contains the expected error
    assert.strictEqual(response.admin.game.syncSingle.game, null)
    assert.ok(response.admin.game.syncSingle.errors, 'Errors should not be null')
    assert.strictEqual(response.admin.game.syncSingle.errors?.length ?? 0, 1)
    assert.strictEqual(response.admin.game.syncSingle.errors?.[0]?.message, 'Game not found')
    assert.strictEqual(response.admin.game.syncSingle.errors?.[0]?.path, 'syncSingle')
  })
})
