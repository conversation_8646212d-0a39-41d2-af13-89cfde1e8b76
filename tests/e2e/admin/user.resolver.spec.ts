import assert from 'assert'
import axios from 'axios'
import { readFileSync } from 'fs'
import { Types } from 'mongoose'
import sinon from 'sinon'

import * as s3RequestPresigner from '../../../src/helpers/getSignedUrl'
import { slugifyString } from '../../../src/helpers/string'
import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { UserInfoModel } from '../../../src/modules/stats/models/userInfo'
import { UserModel } from '../../../src/modules/user/models'
import { Role } from '../../../src/modules/user/models/user'
import { BioHandedness, BioPosition } from '../../graphql/components'
import { client } from '../../utils/graphql'
import { generateDiscoverUrl } from '../../utils/stats'
import {
  generateRandomAdminUser,
  generateRandomAgency,
  generateRandomTeam,
  generateRandomUser,
} from '../../utils/users'
import { s3ClientStub, snsClientStub } from '..'

describe('Admin User Resolver', () => {
  describe('SyncAgencyClients mutation', () => {
    it('should sync agency clients from EP', async () => {
      const admin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('EP import test')

      const axiosGetStub = sinon.stub(axios, 'get')
      axiosGetStub.onFirstCall().resolves({
        data: {
          data: [
            {
              id: 1,
              name: 'Test Client',
            },
          ],
          _links: {
            next: true,
          },
        },
      })

      axiosGetStub.onSecondCall().resolves({
        data: {
          data: [
            {
              id: 1,
              name: 'Johny Cash',
              firstName: 'Johny',
              lastName: 'Cash',
              yearOfBirth: 1932,
              nationality: {
                iso_3166_1_alpha_2: 'US',
              },
              shoots: 'R',
              height: {
                imperial: `6'1`,
                metrics: 188,
              },
              weight: {
                metrics: 84,
                imperial: 185,
              },
              links: {
                eliteprospectsUrl: 'https://www.eliteprospects.com/player/1/johny-cash',
              },
            },
            {
              id: 2,
              name: 'Tony Stark',
              dateOfBirth: '21-22-2334',
              firstName: 'Tony',
              lastName: 'Stark',
              yearOfBirth: null,
              nationality: {
                iso_3166_1_alpha_2: 'US',
              },
              shoots: 'R',
              height: {
                imperial: `6'1`,
                metrics: 188,
              },
              weight: {
                metrics: 84,
                imperial: 185,
              },
              links: {
                eliteprospectsUrl: 'https://www.eliteprospects.com/player/2/tony-stark',
              },
            },
          ],
          _links: {
            next: false,
          },
        },
      })

      // last call has wrong data, even though it should import players
      axiosGetStub.onThirdCall().resolves({})

      const response = await client(admin.tokens.accessToken).syncAgencyClients({
        agencyId: agency.id,
        epAgencyId: 1,
      })

      assert.strictEqual(response.admin.user.syncAgencyClients.errors?.length, 2)
      assert.strictEqual(
        response.admin.user.syncAgencyClients.errors[0].message,
        'Player Test Client (1) has invalid data, (name, nationality or yearOfBirth missing)'
      )
      assert.strictEqual(axiosGetStub.called, true)

      const users = await UserModel.find({ 'bio.agency': agency.id })
      assert.strictEqual(users.length, 1)
      assert.strictEqual(users[0].firstname, 'Johny')
      assert.strictEqual(users[0].lastname, 'Cash')
      assert.strictEqual(users[0].country, 'US')
      assert.deepStrictEqual(users[0].possibleYearsOfBirth, [1932])
      assert.strictEqual(users[0].bio?.position, BioPosition.Center)
      assert.strictEqual(users[0].bio?.handedness, BioHandedness.Right)
      assert.strictEqual(users[0].bio?.height?.feet, 6)
      assert.strictEqual(users[0].bio?.height?.inches, 1)
      assert.strictEqual(users[0].bio?.height?.centimeters, 188)
      assert.strictEqual(users[0].bio?.weight?.kilograms, 84)
      assert.strictEqual(users[0].bio?.weight?.pounds, 185)
      assert.strictEqual(users[0].userStats?.views, 0)
      assert.strictEqual(users[0].userStats?.boosts, 0)
      assert.strictEqual(users[0].userStats?.followers, 0)

      axiosGetStub.restore()

      await UserModel.deleteMany({
        _id: { $in: users.map((u) => u._id) },
      })
    })

    it('should return error if agency not found', async () => {
      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).syncAgencyClients({
        agencyId: '60f8a7a5b3d2e2b3e4c7d5c3',
        epAgencyId: 1,
      })

      assert.strictEqual(response.admin.user.syncAgencyClients.errors?.length, 1)
      assert.strictEqual(
        response.admin.user.syncAgencyClients.errors[0].message,
        'Agency not found'
      )
    })

    it('should only add user to agency if user already exists', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser({
        firstname: 'Peter',
        lastname: 'Parker',
        role: Role.PLAYER,

        country: 'US',
        dateOfBirth: new Date('1982-02-26'),
      })
      const agency = await generateRandomAgency('EP import test 2')

      const u = await UserModel.findOneAndUpdate(
        { _id: user.user._id },
        { $set: { slug: slugifyString(user.user.name) } },
        { new: true }
      )

      const axiosGetStub = sinon.stub(axios, 'get').resolves({
        data: {
          data: [
            {
              id: 1,
              name: u?.name,
              dateOfBirth: '1982-02-26',
              firstName: 'Peter',
              lastName: 'Parker',
              yearOfBirth: 1982,
              nationality: {
                iso_3166_1_alpha_2: 'US',
              },
              links: {
                eliteprospectsUrl: 'https://www.eliteprospects.com/player/1/peter-parker',
              },
            },
          ],
          _links: {
            next: false,
          },
        },
      })

      await client(admin.tokens.accessToken).syncAgencyClients({
        agencyId: agency.id,
        epAgencyId: 1,
      })

      const users = await UserModel.find({ 'bio.agency': agency.id })
      assert.strictEqual(users.length, 1)

      axiosGetStub.restore()

      await UserModel.deleteMany({
        _id: { $in: users.map((u) => u._id) },
      })
    })
  })

  describe('User Statistics by Year and Country', () => {
    it('should return statistics grouped by country and year', async () => {
      await UserModel.deleteMany({})
      const admin = await generateRandomAdminUser()

      await generateRandomUser({
        role: Role.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date('2010-05-10'),
      })
      await generateRandomUser({
        role: Role.PLAYER,
        country: 'CZ',
        dateOfBirth: new Date('2010-06-10'),
      })
      await generateRandomUser({
        role: Role.PLAYER,
        country: 'CA',
        dateOfBirth: new Date('2009-08-20'),
      })

      await generateRandomUser({
        role: Role.PLAYER,
        country: 'US',
        dateOfBirth: new Date('2004-11-05'),
      })
      await generateRandomUser({
        role: Role.PLAYER,
        country: 'US',
        dateOfBirth: new Date('2005-11-05'),
      })

      const response = await client(admin.tokens.accessToken).userStatisticsByYearAndCountry()

      assert.strictEqual(response.admin.userStatisticsByYearAndCountry.totalUsers, 5)

      assert.strictEqual(
        response.admin.userStatisticsByYearAndCountry.usersByCountryAndYear.find(
          (stat) => stat.year === '2010'
        )?.count,
        2
      )
      assert.strictEqual(
        response.admin.userStatisticsByYearAndCountry.usersByCountryAndYear.find(
          (stat) => stat.year === '2010'
        )?.target,
        1000
      )

      assert.strictEqual(
        response.admin.userStatisticsByYearAndCountry.usersByCountryAndYear.find(
          (stat) => stat.country === 'CA' && stat.year === '2009'
        )?.count,
        1
      )

      assert.strictEqual(
        response.admin.userStatisticsByYearAndCountry.usersByCountryAndYear.find(
          (stat) => stat.year === '2009'
        )?.target,
        9022
      )

      assert.strictEqual(
        response.admin.userStatisticsByYearAndCountry.usersByCountryAndYear.find(
          (stat) => stat.country === 'US' && stat.year === '2006+'
        )?.count,
        2
      )

      assert.strictEqual(
        response.admin.userStatisticsByYearAndCountry.usersByCountryAndYear.find(
          (stat) => stat.country === 'US' && stat.year === '2006+'
        )?.target,
        11918
      )
    })
  })

  describe('SyncAgencyClient mutation', () => {
    it('should sync a single player from EP with link', async () => {
      const admin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('EP single player test link')

      const axiosPostStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            player: {
              id: '1',
              views: 5000,
              firstName: 'Diana',
              lastName: 'Prince',
              position: 'CENTER',
              name: 'Diana Prince',
              dateOfBirth: '1990-03-15',
              yearOfBirth: 1990,
              nationality: { iso_3166_1_alpha_2: 'GB' },
              height: { metrics: 178, imperial: `5'10"` },
              weight: { metrics: 68, imperial: 150 },
              shoots: 'RIGHT',
              links: {
                eliteprospectsUrl: 'https://www.eliteprospects.com/player/1/diana-prince',
              },
            },
          },
        },
      })

      const response = await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: 'https://www.eliteprospects.com/player/45/diana-prince',
      })

      assert.strictEqual(response.admin.user.syncAgencyClient.errors?.length, 0)

      const users = await UserModel.find({ 'bio.agency': agency.id })
      assert.strictEqual(users.length, 1)
      assert.strictEqual(users[0].firstname, 'Diana')
      assert.strictEqual(users[0].lastname, 'Prince')
      assert.strictEqual(users[0].country, 'GB')
      assert.strictEqual(users[0].dateOfBirth?.toISOString(), '1990-03-15T00:00:00.000Z')
      assert.strictEqual(users[0].bio?.position, BioPosition.Center)
      assert.strictEqual(users[0].bio?.handedness, BioHandedness.Right)
      assert.strictEqual(users[0].bio?.height?.feet, 5)
      assert.strictEqual(users[0].bio?.height?.inches, 10)
      assert.strictEqual(users[0].bio?.height?.centimeters, 178)
      assert.strictEqual(users[0].bio?.weight?.kilograms, 68)
      assert.strictEqual(users[0].bio?.weight?.pounds, 150)

      axiosPostStub.restore()
    })

    it('should return an error if agency is not found', async () => {
      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: '60f8a7a5b3d2e2b3e4c7d5c3',
        epLink: 'https://www.eliteprospects.com/player/1/invalid-agency',
      })

      assert.strictEqual(response.admin.user.syncAgencyClient.errors?.length, 1)
      assert.strictEqual(response.admin.user.syncAgencyClient.errors[0].message, 'Agency not found')
    })

    it('should return an error if user is not found in EP', async () => {
      const admin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('EP user not found test')

      const axiosPostStub = sinon.stub(axios, 'post').resolves({
        data: null,
      })

      const response = await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: 'https://www.eliteprospects.com/player/999/karel-gott',
      })

      assert.strictEqual(response.admin.user.syncAgencyClient.errors?.length, 1)
      assert.strictEqual(
        response.admin.user.syncAgencyClient.errors[0].message,
        'User with epLink https://www.eliteprospects.com/player/999/karel-gott not found'
      )

      axiosPostStub.restore()
    })

    it('should return an error for invalid user data', async () => {
      const admin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('EP invalid user data test')

      const axiosPostStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            player: {
              id: 1,
              name: null,
              dateOfBirth: '1980-02-29',
              yearOfBirth: 1980,
              nationality: null,
            },
          },
        },
      })

      const response = await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: 'https://www.eliteprospects.com/player/1/jan-hrachovy',
      })

      assert.strictEqual(response.admin.user.syncAgencyClient.errors?.length, 1)
      assert.strictEqual(
        response.admin.user.syncAgencyClient.errors[0].message,
        'Player null (1) has invalid data, (name, nationality or yearOfBirth missing)'
      )

      axiosPostStub.restore()
    })

    it('should add player to the agency only if he already exists', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser({
        firstname: 'Bruce',
        lastname: 'Wayne',
        role: Role.PLAYER,
        country: 'US',
        dateOfBirth: new Date('1972-05-27'),
      })
      const agency = await generateRandomAgency('EP single player agency test')

      const existingUser = await UserModel.findOneAndUpdate(
        { _id: user.user._id },
        { $set: { slug: slugifyString(user.user.name) } },
        { new: true }
      )

      const axiosGetStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            player: {
              id: 2,
              name: existingUser?.name,
              dateOfBirth: '1972-05-27',
              firstName: 'Bruce',
              lastName: 'Wayne',
              yearOfBirth: 1972,
              nationality: {
                iso_3166_1_alpha_2: 'US',
              },
              links: {
                eliteprospectsUrl: 'https://www.eliteprospects.com/player/2/bruce-wayne',
              },
            },
          },
        },
      })

      await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: 'https://www.eliteprospects.com/player/2/bruce-wayne',
      })

      const users = await UserModel.find({ 'bio.agency': agency.id })
      assert.strictEqual(users.length, 1)
      assert.strictEqual(users[0]._id.toString(), existingUser?._id.toString())

      axiosGetStub.restore()
    })

    it('should skip user if they are marked for deletion', async () => {
      await UserModel.deleteMany()

      const admin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('EP user skip test deleted')
      const user = await generateRandomUser({
        firstname: 'Dan',
        lastname: 'Deleted',
        role: Role.PLAYER,

        country: 'US',
        dateOfBirth: new Date('1987-02-02'),
      })

      await UserModel.findByIdAndUpdate(
        { _id: user.user._id },
        { $set: { slug: slugifyString(user.user.name), markedForDeletion: true } },
        { new: true }
      )

      const axiosPostStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            player: {
              id: 1,
              name: 'Dan Deleted',
              dateOfBirth: '1987-02-02',
              firstName: 'Dan',
              lastName: 'Deleted',
              yearOfBirth: 1987,
              nationality: {
                iso_3166_1_alpha_2: 'US',
              },
              shoots: 'R',
              height: {
                imperial: `6'3`,
                metrics: 190,
              },
              weight: {
                metrics: 85,
                imperial: 187,
              },
              links: {
                eliteprospectsUrl: 'https://www.eliteprospects.com/player/1/dan-deleted',
              },
            },
          },
        },
      })

      await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: 'https://www.eliteprospects.com/player/1/dan-deleted',
      })

      const users = await UserModel.find({ 'bio.agency': agency.id })
      assert.strictEqual(users.length, 0, 'User should not be added to the agency')

      axiosPostStub.restore()
    })

    it('should skip user if they are marked as not public', async () => {
      await UserModel.deleteMany()

      const admin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('EP user skip test not public')
      const user = await generateRandomUser({
        firstname: 'Dan',
        lastname: 'NotPublic',
        role: Role.PLAYER,

        country: 'US',
        dateOfBirth: new Date('1987-02-02'),
      })

      await UserModel.findByIdAndUpdate(
        { _id: user.user._id },
        { $set: { slug: slugifyString(user.user.name), markedForDeletion: true, public: false } },
        { new: true }
      )

      const axiosPostStub = sinon.stub(axios, 'post').resolves({
        data: {
          data: {
            player: {
              id: 1,
              name: 'Dan NotPublic',
              dateOfBirth: '1987-02-02',
              firstName: 'Dan',
              lastName: 'NotPublic',
              yearOfBirth: 1987,
              nationality: {
                iso_3166_1_alpha_2: 'US',
              },
              shoots: 'R',
              height: {
                imperial: `6'3`,
                metrics: 190,
              },
              weight: {
                metrics: 85,
                imperial: 187,
              },
              links: {
                eliteprospectsUrl: 'https://www.eliteprospects.com/player/1/dan-notpublic',
              },
            },
          },
        },
      })

      await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: 'https://www.eliteprospects.com/player/1/dan-notpublic',
      })

      const users = await UserModel.find({ 'bio.agency': agency.id })
      assert.strictEqual(users.length, 0, 'User should not be added to the agency')

      axiosPostStub.restore()

      await UserModel.deleteMany()
    })

    it('should return an error for invalid epLink format', async () => {
      const admin = await generateRandomAdminUser()
      const agency = await generateRandomAgency('Invalid epLink test')

      const response = await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: '"https://www.eliteprospects.com/team/12345"',
      })

      assert.strictEqual(response.admin.user.syncAgencyClient.errors?.length, 1)
      assert.strictEqual(
        response.admin.user.syncAgencyClient.errors[0].message,
        'Invalid epLink format: "https://www.eliteprospects.com/team/12345"'
      )

      const responseId = await client(admin.tokens.accessToken).syncAgencyClient({
        agencyId: agency.id,
        epLink: '123456',
      })

      assert.strictEqual(responseId.admin.user.syncAgencyClient.errors?.length, 1)
      assert.strictEqual(
        responseId.admin.user.syncAgencyClient.errors[0].message,
        'Invalid epLink format: 123456'
      )
    })
  })

  describe('Mark user as deleted', () => {
    it('should mark user as deleted', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const response = await client(admin.tokens.accessToken).markUserAsDeleted({
        userId: user.user._id,
      })

      assert.strictEqual(response.admin.user.markUserAsDeleted.success, true)
      assert.strictEqual(response.admin.user.markUserAsDeleted.errors?.length, 0)
    })

    it('should return an error if user is not found', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      const response = await client(admin.tokens.accessToken).markUserAsDeleted({
        userId: user.user._id,
      })

      assert.strictEqual(response.admin.user.markUserAsDeleted.success, false)
      assert.strictEqual(response.admin.user.markUserAsDeleted.errors?.length, 1)
      assert.strictEqual(response.admin.user.markUserAsDeleted.errors[0].message, 'User not found')

      findByIdStub.restore()
    })

    it('should return an error if user is already marked for deletion ', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await client(admin.tokens.accessToken).markUserAsDeleted({
        userId: user.user._id,
      })

      const response = await client(admin.tokens.accessToken).markUserAsDeleted({
        userId: user.user._id,
      })

      assert.strictEqual(response.admin.user.markUserAsDeleted.success, false)
      assert.strictEqual(response.admin.user.markUserAsDeleted.errors?.length, 1)
      assert.strictEqual(
        response.admin.user.markUserAsDeleted.errors[0].message,
        'User is already marked for deletion'
      )
    })

    it('should return an error if failed to mark user as deleted', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const findById = sinon.stub(UserModel, 'findById').throws(new Error('Database error'))

      try {
        await client(admin.tokens.accessToken).markUserAsDeleted({
          userId: user.user._id,
        })
      } catch (error) {
        assert.strictEqual(error.response.errors[0].message, 'Failed to mark user as deleted.')
      }

      findById.restore()
    })

    it('should recalculate count in user team', async () => {
      const admin = await generateRandomAdminUser()
      const team = await generateRandomTeam({ name: 'Mark user as deleted team count test' })
      const user = await generateRandomUser({
        role: Role.PLAYER,
      })

      await client(admin.tokens.accessToken).updateUserAdmin({
        input: {
          currentTeam: team?.id,
        },
        updateUserAdminId: user.user._id,
      })

      const checkTeamCount = await TeamModel.findById(team.id)
      assert.strictEqual(checkTeamCount?.playersCount, 1)

      const response = await client(admin.tokens.accessToken).markUserAsDeleted({
        userId: user.user._id,
      })
      assert.strictEqual(response.admin.user.markUserAsDeleted.success, true)
      assert.strictEqual(response.admin.user.markUserAsDeleted.errors?.length, 0)

      const checkRecalculated = await TeamModel.findById(team.id)
      assert.strictEqual(checkRecalculated?.playersCount, 0)
    })

    it('should call deleteEndpoint for each registered device with arn', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await UserModel.updateOne(
        { _id: user.user._id },
        {
          registeredDevices: [
            { arn: 'arn:aws:sns:device1' },
            { arn: 'arn:aws:sns:device2' },
            { arn: null },
          ],
        }
      )

      const response = await client(admin.tokens.accessToken).markUserAsDeleted({
        userId: user.user._id,
      })

      assert.strictEqual(response.admin.user.markUserAsDeleted.success, true)

      sinon.assert.calledTwice(snsClientStub.deleteEndpoint)
      sinon.assert.calledWith(snsClientStub.deleteEndpoint, {
        EndpointArn: 'arn:aws:sns:device1',
      })
      sinon.assert.calledWith(snsClientStub.deleteEndpoint, {
        EndpointArn: 'arn:aws:sns:device2',
      })

      sinon.assert.notCalled(
        snsClientStub.deleteEndpoint.withArgs({
          EndpointArn: null,
        })
      )
    })

    it('should throw an error if user update fails to return the updated user', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const findOneAndUpdateStub = sinon.stub(UserModel, 'findOneAndUpdate').resolves(null)

      try {
        await client(admin.tokens.accessToken).markUserAsDeleted({ userId: user.user._id })
      } catch (error) {
        assert.strictEqual(error.response.errors[0].message, 'Failed to mark user as deleted.')
      }

      sinon.assert.calledOnce(findOneAndUpdateStub)

      findOneAndUpdateStub.restore()
    })
  })

  describe('AddExternalConnections mutation', () => {
    it('should add external connection to user', async () => {
      await CrawlerDomainConfigModel.deleteMany({})

      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const discoverUrl = await generateDiscoverUrl()

      const externalId = '12345'
      await UserInfoModel.create({
        name: 'Test Player',
        externalId,
        crawlerDomainConfig: discoverUrl.crawlerDomainConfig,
      })

      const response = await client(admin.tokens.accessToken).addExternalConnection({
        userId: user.user._id,
        externalId,
        crawlerDomainConfigId: discoverUrl.crawlerDomainConfig.id,
      })

      assert.strictEqual(response.admin.user.addExternalConnection.success, true)
      assert.strictEqual(response.admin.user.addExternalConnection.errors?.length, 0)

      const updatedUser = await UserModel.findById(user.user._id)
      assert.strictEqual(updatedUser?.externalConnections?.length, 1)
      assert.strictEqual(updatedUser?.externalConnections[0].externalId, externalId)
      assert.strictEqual(
        updatedUser?.externalConnections[0].crawlerDomainConfig.toString(),
        discoverUrl.crawlerDomainConfig.id
      )
    })

    it('should return error if user not found', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const finByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      const response = await client(admin.tokens.accessToken).addExternalConnection({
        userId: user.user._id,
        externalId: 'should-fail',
        crawlerDomainConfigId: new Types.ObjectId(),
      })

      assert.strictEqual(response.admin.user.addExternalConnection.success, false)
      assert.strictEqual(response.admin.user.addExternalConnection.errors?.length, 1)
      assert.strictEqual(
        response.admin.user.addExternalConnection.errors[0].message,
        'User not found'
      )

      finByIdStub.restore()
    })

    it('should throw an error if save fails', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const findByIdStub = sinon.stub(UserModel, 'findById').throws(new Error('Database error'))

      try {
        await client(admin.tokens.accessToken).addExternalConnection({
          userId: user.user._id,
          externalId: 'err-123',
          crawlerDomainConfigId: new Types.ObjectId(),
        })
        assert.fail('Should have thrown GraphQLError')
      } catch (error) {
        assert.strictEqual(error.response.errors?.[0].message, 'Failed to add external connection.')
      }

      findByIdStub.restore()
    })

    it('should return error if userInfo not found or missing domain config', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const response = await client(admin.tokens.accessToken).addExternalConnection({
        userId: user.user._id,
        externalId: 'nonexistent-id',
        crawlerDomainConfigId: new Types.ObjectId(),
      })

      assert.strictEqual(response.admin.user.addExternalConnection.success, false)
      assert.strictEqual(response.admin.user.addExternalConnection.errors?.length, 1)
      assert.strictEqual(
        response.admin.user.addExternalConnection.errors[0].message,
        'UserInfo not found'
      )
    })
  })

  describe('RemoveExternalConnection mutation', () => {
    it('should return error if user not found', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const stub = sinon.stub(UserModel, 'findById').resolves(null)

      const response = await client(admin.tokens.accessToken).removeExternalConnection({
        userId: user.user._id,
        externalId: 'noexist',
        crawlerDomainConfigId: new Types.ObjectId(),
      })

      assert.strictEqual(response.admin.user.removeExternalConnection.success, false)
      assert.strictEqual(
        response.admin.user.removeExternalConnection.errors?.[0].message,
        'User not found'
      )

      stub.restore()
    })

    it('should return error if userInfo not found', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const response = await client(admin.tokens.accessToken).removeExternalConnection({
        userId: user.user._id,
        externalId: 'notFoundIngo',
        crawlerDomainConfigId: new Types.ObjectId(),
      })

      assert.strictEqual(response.admin.user.removeExternalConnection.success, false)
      assert.strictEqual(
        response.admin.user.removeExternalConnection.errors?.[0].message,
        'UserInfo not found'
      )
    })

    it('should throw GraphQLError if something fails', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const stub = sinon.stub(UserModel, 'findById').throws(new Error('Unexpected DB error'))

      try {
        await client(admin.tokens.accessToken).removeExternalConnection({
          userId: user.user._id,
          externalId: 'id',
          crawlerDomainConfigId: new Types.ObjectId(),
        })
        assert.fail('Expected GraphQLError to be thrown')
      } catch (error) {
        assert.strictEqual(
          error.response.errors?.[0].message,
          'Failed to remove external connection.'
        )
      }

      stub.restore()
    })

    it('should keep externalConnections when removing one', async () => {
      await CrawlerDomainConfigModel.deleteMany({})
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()
      const discoverUrl = await generateDiscoverUrl()

      const externalIdToKeep = 'keep-me'
      const externalIdToRemove = 'remove-me'

      await UserInfoModel.create({
        name: 'Removable Player',
        externalId: externalIdToRemove,
        crawlerDomainConfig: discoverUrl.crawlerDomainConfig,
      })

      await UserInfoModel.create({
        name: 'Keep Player',
        externalId: externalIdToKeep,
        crawlerDomainConfig: discoverUrl.crawlerDomainConfig,
      })

      await UserModel.findByIdAndUpdate(user.user._id, {
        $set: {
          externalConnections: [
            {
              externalId: externalIdToKeep,
              crawlerDomainConfig: discoverUrl.crawlerDomainConfig._id,
            },
            {
              externalId: externalIdToRemove,
              crawlerDomainConfig: discoverUrl.crawlerDomainConfig._id,
            },
          ],
        },
      })

      const response = await client(admin.tokens.accessToken).removeExternalConnection({
        userId: user.user._id,
        externalId: externalIdToRemove,
        crawlerDomainConfigId: discoverUrl.crawlerDomainConfig.id,
      })

      assert.strictEqual(response.admin.user.removeExternalConnection.success, true)

      const updatedUser = await UserModel.findById(user.user._id)

      assert.strictEqual(updatedUser?.externalConnections.length, 1)
      assert.strictEqual(updatedUser?.externalConnections[0].externalId, externalIdToKeep)
    })
  })

  describe('Premium user functionality', () => {
    beforeEach(async () => {
      await UserModel.deleteMany({})
      await UserInfoModel.deleteMany({})
    })

    it('should set premium to true and set premiumSince to current date', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await UserModel.updateOne(
        { _id: user.user._id },
        { $set: { premium: false, premiumSince: null } }
      )

      const response = await client(admin.tokens.accessToken).updateUserAdmin({
        input: {
          premium: true,
        },
        updateUserAdminId: user.user._id,
      })

      assert.strictEqual(response.updateUserAdmin.errors?.length, 0)
      assert.strictEqual(response.updateUserAdmin.user?.premium, true)
      assert.ok(response.updateUserAdmin.user?.premiumSince)

      const updatedUser = await UserModel.findById(user.user._id)
      assert.strictEqual(updatedUser?.premium, true)
      assert.ok(updatedUser?.premiumSince instanceof Date)

      const now = new Date()
      const diff = now.getTime() - (updatedUser?.premiumSince?.getTime() || 0)
      assert.ok(diff < 60000, 'premiumSince should be set to a recent timestamp')
    })

    it('should set premium to false and clear premiumSince', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await UserModel.updateOne(
        { _id: user.user._id },
        { $set: { premium: true, premiumSince: new Date() } }
      )

      const initialUser = await UserModel.findById(user.user._id)
      assert.strictEqual(initialUser?.premium, true)
      assert.ok(initialUser?.premiumSince instanceof Date)

      const response = await client(admin.tokens.accessToken).updateUserAdmin({
        input: {
          premium: false,
        },
        updateUserAdminId: user.user._id,
      })

      assert.strictEqual(response.updateUserAdmin.errors?.length, 0)
      assert.strictEqual(response.updateUserAdmin.user?.premium, false)

      // Get the real user from DB to verify premiumSince is null
      const updatedUser = await UserModel.findById(user.user._id)
      assert.strictEqual(updatedUser?.premium, false)
    })

    it('should not affect other fields when updating premium status', async () => {
      const admin = await generateRandomAdminUser()
      const user = await generateRandomUser({
        firstname: 'Oleg',
        lastname: 'Stojko',
      })

      const response = await client(admin.tokens.accessToken).updateUserAdmin({
        input: {
          premium: true,
        },
        updateUserAdminId: user.user._id,
      })

      assert.strictEqual(response.updateUserAdmin.user?.firstname, 'Oleg')
      assert.strictEqual(response.updateUserAdmin.user?.lastname, 'Stojko')
      assert.strictEqual(response.updateUserAdmin.user?.premium, true)
    })

    it('should filter users by premium status', async () => {
      const admin = await generateRandomAdminUser()

      const premiumUser1 = await generateRandomUser()
      const premiumUser2 = await generateRandomUser()
      const nonPremiumUser1 = await generateRandomUser()
      const nonPremiumUser2 = await generateRandomUser()

      await UserModel.updateMany(
        { _id: { $in: [premiumUser1.user._id, premiumUser2.user._id] } },
        { $set: { premium: true, premiumSince: new Date() } }
      )

      await UserModel.updateMany(
        { _id: { $in: [nonPremiumUser1.user._id, nonPremiumUser2.user._id] } },
        { $set: { premium: false, premiumSince: null } }
      )

      const premiumUsersResponse = await client(admin.tokens.accessToken).users({
        pagination: { first: 10 },
        filter: { premium: true },
      })

      const premiumUserIds = premiumUsersResponse.users.edges.map((edge) => edge.node.id)

      assert.ok(premiumUserIds.includes(premiumUser1.user._id.toString()))
      assert.ok(premiumUserIds.includes(premiumUser2.user._id.toString()))
      assert.ok(!premiumUserIds.includes(nonPremiumUser1.user._id.toString()))
      assert.ok(!premiumUserIds.includes(nonPremiumUser2.user._id.toString()))

      const nonPremiumUsersResponse = await client(admin.tokens.accessToken).users({
        pagination: { first: 10 },
        filter: { premium: false },
      })

      // we want all users to be included
      const nonPremiumUserIds = nonPremiumUsersResponse.users.edges.map((edge) => edge.node.id)

      assert.ok(nonPremiumUserIds.includes(nonPremiumUser1.user._id.toString()))
      assert.ok(nonPremiumUserIds.includes(nonPremiumUser2.user._id.toString()))
      assert.ok(nonPremiumUserIds.includes(premiumUser1.user._id.toString()))
      assert.ok(nonPremiumUserIds.includes(premiumUser2.user._id.toString()))
    })
  })

  describe('uploadAvatar mutation', () => {
    it('should upload avatar and handle successful upload', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const s3RequestPresignerStub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
        userId: user.user._id,
      })
      assert.strictEqual(response.admin.user.uploadAvatar.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerStub.calledOnce, true)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const response1 = await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
        userId: user.user._id,
        input: {
          uploadSuccessful: true,
        },
      })
      assert.strictEqual(response1.admin.user.uploadAvatar.errors, null)

      const check = await UserModel.findById(user.user._id)
      assert.strictEqual(check?.avatars.tiny.includes('-tiny'), true)
      assert.strictEqual(check?.avatars.small.includes('-small'), true)
      assert.strictEqual(check?.avatars.medium.includes('-medium'), true)
      assert.strictEqual(check?.avatars.large.includes('-large'), true)

      s3RequestPresignerStub.restore()
    })

    it('should handle failed presigned URL creation', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const s3RequestPresignerStub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .throws(new Error('Failed to create presigned url'))

      try {
        await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
          userId: user.user._id,
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to upload avatar')
      }
      assert.strictEqual(s3RequestPresignerStub.calledOnce, true)

      s3RequestPresignerStub.restore()
    })

    it('should remove avatar if there was previously uploaded avatar already', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const s3RequestPresignerStub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
        userId: user.user._id,
      })
      assert.strictEqual(response.admin.user.uploadAvatar.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerStub.calledOnce, true)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const response1 = await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
        userId: user.user._id,
        input: {
          uploadSuccessful: true,
        },
      })
      assert.strictEqual(response1.admin.user.uploadAvatar.errors, null)
      assert.strictEqual(
        response1.admin.user.uploadAvatar.user?.avatars?.tiny.includes('-tiny'),
        true
      )

      const response2 = await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
        userId: user.user._id,
        input: {
          uploadSuccessful: true,
        },
      })

      assert.strictEqual(response2.admin.user.uploadAvatar.errors, null)
      assert.strictEqual(s3ClientStub.send.callCount, 18) // 6x remove, 6x upload of thumbnails (createImageThumbnails) twice

      s3RequestPresignerStub.restore()
    })

    it('should handle unsuccessful upload correctly', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()

      await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
        userId: user.user._id,
      })

      const response = await client(adminUser.tokens.accessToken).uploadAvatarAdmin({
        userId: user.user._id,
        input: {
          uploadSuccessful: false,
        },
      })
      assert.strictEqual(response.admin.user.uploadAvatar.user?.id, user.user._id.toString())

      const response1 = await UserModel.findById(user.user._id)

      assert.strictEqual(response1?.avatars, undefined)
      assert.strictEqual(response1?.avatarUnconfirmed, null)
    })
  })
})
