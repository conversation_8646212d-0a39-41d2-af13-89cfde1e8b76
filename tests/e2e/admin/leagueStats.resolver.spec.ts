import assert from 'assert'

import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { LeagueStatsModel } from '../../../src/modules/stats/models/leagueStats/leagueStats'
import { client } from '../../utils/graphql'
import { generateDiscoverUrl, generateLeagueStat } from '../../utils/stats'
import {
  generateRandomAdminUser,
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
} from '../../utils/users'
import { leagueStatsMock } from '../services/leagueStatsSyncer.helper'

describe('admin league stats resolver', () => {
  beforeEach(async () => {
    await LeagueStatsModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
  })

  it('should return league stats by league', async () => {
    const randomAdminUser = await generateRandomAdminUser()

    const organization = await generateRandomOrganization('Test Admin Leagues Stats without team')
    const league = await generateRandomLeague('Test Admin Leagues Stats League without team')
    const team1 = await generateRandomTeam({
      name: 'Test Admin Leagues Stats without team',
      organization: organization._id.toString(),
    })
    const team2 = await generateRandomTeam({
      name: 'Test Admin Leagues Stats without team 2',
      organization: organization._id.toString(),
    })

    const discoveredUrl = await generateDiscoverUrl()

    // will be omitted
    await generateLeagueStat({
      discoveredUrl,
      leagueStats: {
        ...leagueStatsMock.leagueStats[0],
        externalInfo: {
          teamName: 'Test team',
          leagueName: 'Test league',
        },
      },
    })

    await generateLeagueStat({
      discoveredUrl,
      leagueStats: {
        ...leagueStatsMock.leagueStats[0],
        team: team1,
        league,
        points: 0,
        group: 'A',
      },
    })

    await generateLeagueStat({
      discoveredUrl,
      leagueStats: {
        ...leagueStatsMock.leagueStats[0],
        team: team2,
        league,
        points: 0,
        group: 'B',
      },
    })

    // team missing
    const response = await client(randomAdminUser.tokens.accessToken).adminLeagueStatsList({
      filter: {
        leagueId: league.id,
      },
    })

    assert.strictEqual(response.admin.leagueStats.list.edges.length, 2)

    const leagueStats1 = response.admin.leagueStats.list.edges[0].node
    assert.strictEqual(leagueStats1.team?.name, team1.name)
    assert.strictEqual(leagueStats1.group, 'A')

    const leagueStats2 = response.admin.leagueStats.list.edges[1].node
    assert.strictEqual(leagueStats2.team?.name, team2.name)
    assert.strictEqual(leagueStats2.group, 'B')
  })
})
