import assert from 'assert'
import mongoose from 'mongoose'
import sinon from 'sinon'

import { UserModel, WallVideoModel } from '../../../src/modules/user/models'
import { WallVideoTags, WallVideoType } from '../../graphql/components'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, generateRandomUser } from '../../utils/users'
import { generateWallVideo, generateWallVideoWithSegmentation } from '../../utils/wallVideos'
import { s3ClientStub } from '..'

import { DeleteObjectCommand } from '@aws-sdk/client-s3'

describe('videosCount query', () => {
  beforeEach(async () => {
    await WallVideoModel.deleteMany({})
  })

  after(async () => {
    await WallVideoModel.deleteMany({})
  })

  it('should return total count of videos by category with a day filter', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const defensiveVideos = []
    for (let i = 0; i < 1; i++) {
      const defensiveVideo = await generateWallVideo({
        user: user.user,
        path: 'defensive video' + i,
        type: WallVideoType.Defensive,
      })
      defensiveVideos.push(defensiveVideo)
    }

    const gameSaveVideos = []
    for (let i = 0; i < 2; i++) {
      const gameSaveVideo = await generateWallVideo({
        user: user.user,
        path: 'game save video' + i,
        type: WallVideoType.GameSave,
      })
      gameSaveVideos.push(gameSaveVideo)
    }

    const highlighVideos = []
    for (let i = 0; i < 3; i++) {
      const highlighVideo = await generateWallVideo({
        user: user.user,
        path: 'game highlight video' + i,
        type: WallVideoType.Highlight,
      })
      highlighVideos.push(highlighVideo)
    }

    const interviewVideos = []
    for (let i = 0; i < 4; i++) {
      const interviewVideo = await generateWallVideo({
        user: user.user,
        path: 'game interview video' + i,
        type: WallVideoType.Interview,
      })
      interviewVideos.push(interviewVideo)
    }

    const offensiveVideos = []
    for (let i = 0; i < 5; i++) {
      const offensiveVideo = await generateWallVideo({
        user: user.user,
        path: 'game offensive video' + i,
        type: WallVideoType.Offensive,
      })
      offensiveVideos.push(offensiveVideo)
    }

    const otherVideos = []
    for (let i = 0; i < 6; i++) {
      const otherVideo = await generateWallVideo({
        user: user.user,
        path: 'game other video' + i,
        type: WallVideoType.Other,
      })
      otherVideos.push(otherVideo)
    }

    const puckHandlingVideos = []
    for (let i = 0; i < 7; i++) {
      const puckHandlingVideo = await generateWallVideo({
        user: user.user,
        path: 'game puck video' + i,
        type: WallVideoType.PuckHandling,
      })
      puckHandlingVideos.push(puckHandlingVideo)
    }

    const trainingVideos = []
    for (let i = 0; i < 8; i++) {
      const trainingVideo = await generateWallVideo({
        user: user.user,
        path: 'game training video' + i,
        type: WallVideoType.Training,
      })
      trainingVideos.push(trainingVideo)
    }

    await generateWallVideo({
      user: user.user,
      path: 'defensive video 8 days ago',
      type: WallVideoType.Defensive,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 8),
    })

    await generateWallVideo({
      user: user.user,
      path: 'game save video 14 days ago',
      type: WallVideoType.GameSave,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 14),
    })

    await generateWallVideo({
      user: user.user,
      path: 'highlight video 20 days ago',
      type: WallVideoType.Highlight,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20),
    })

    await generateWallVideo({
      user: user.user,
      path: 'interview video 40 days ago',
      type: WallVideoType.Interview,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 40),
    })

    await generateWallVideo({
      user: user.user,
      path: 'offensive video 50 days ago',
      type: WallVideoType.Offensive,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 50),
    })

    await generateWallVideo({
      user: user.user,
      path: 'other video 170 days ago',
      type: WallVideoType.Other,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 170),
    })

    await generateWallVideo({
      user: user.user,
      path: 'puck handling video 190 days ago',
      type: WallVideoType.PuckHandling,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 190),
    })

    await generateWallVideo({
      user: user.user,
      path: 'training video 370 days ago',
      type: WallVideoType.Training,
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 370),
    })

    const sevenDayResponse = await client(adminUser.tokens.accessToken).videosCount({ days: 7 })
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.defensiveVideosCount, 1)
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.gameSaveVideosCount, 2)
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.highlightVideosCount, 3)
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.interviewVideosCount, 4)
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.offensiveVideosCount, 5)
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.otherVideosCount, 6)
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.puckHandlingVideosCount, 7)
    assert.strictEqual(sevenDayResponse.admin.wallVideo.videosCount.trainingVideosCount, 8)

    const thirtyDayResponse = await client(adminUser.tokens.accessToken).videosCount({ days: 30 })
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.defensiveVideosCount, 2)
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.gameSaveVideosCount, 3)
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.highlightVideosCount, 4)
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.interviewVideosCount, 4)
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.offensiveVideosCount, 5)
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.otherVideosCount, 6)
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.puckHandlingVideosCount, 7)
    assert.strictEqual(thirtyDayResponse.admin.wallVideo.videosCount.trainingVideosCount, 8)

    const ninetyDayResponse = await client(adminUser.tokens.accessToken).videosCount({ days: 90 })
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.defensiveVideosCount, 2)
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.gameSaveVideosCount, 3)
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.highlightVideosCount, 4)
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.interviewVideosCount, 5)
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.offensiveVideosCount, 6)
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.otherVideosCount, 6)
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.puckHandlingVideosCount, 7)
    assert.strictEqual(ninetyDayResponse.admin.wallVideo.videosCount.trainingVideosCount, 8)

    const halfYearResponse = await client(adminUser.tokens.accessToken).videosCount({ days: 180 })
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.defensiveVideosCount, 2)
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.gameSaveVideosCount, 3)
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.highlightVideosCount, 4)
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.interviewVideosCount, 5)
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.offensiveVideosCount, 6)
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.otherVideosCount, 7)
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.puckHandlingVideosCount, 7)
    assert.strictEqual(halfYearResponse.admin.wallVideo.videosCount.trainingVideosCount, 8)

    const yearResponse = await client(adminUser.tokens.accessToken).videosCount({ days: 365 })
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.defensiveVideosCount, 2)
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.gameSaveVideosCount, 3)
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.highlightVideosCount, 4)
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.interviewVideosCount, 5)
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.offensiveVideosCount, 6)
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.otherVideosCount, 7)
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.puckHandlingVideosCount, 8)
    assert.strictEqual(yearResponse.admin.wallVideo.videosCount.trainingVideosCount, 8)

    //without day filter = all time videos
    const withoutDayFilterResponse = await client(adminUser.tokens.accessToken).videosCount({})
    assert.strictEqual(withoutDayFilterResponse.admin.wallVideo.videosCount.defensiveVideosCount, 2)
    assert.strictEqual(withoutDayFilterResponse.admin.wallVideo.videosCount.gameSaveVideosCount, 3)
    assert.strictEqual(withoutDayFilterResponse.admin.wallVideo.videosCount.highlightVideosCount, 4)
    assert.strictEqual(withoutDayFilterResponse.admin.wallVideo.videosCount.interviewVideosCount, 5)
    assert.strictEqual(withoutDayFilterResponse.admin.wallVideo.videosCount.offensiveVideosCount, 6)
    assert.strictEqual(withoutDayFilterResponse.admin.wallVideo.videosCount.otherVideosCount, 7)
    assert.strictEqual(
      withoutDayFilterResponse.admin.wallVideo.videosCount.puckHandlingVideosCount,
      8
    )
    assert.strictEqual(withoutDayFilterResponse.admin.wallVideo.videosCount.trainingVideosCount, 9)
  })
})

describe('Wall video pagination', async () => {
  beforeEach(async () => {
    // wallvideos are soft deleted, so we need to call deleteMany from connection, where we don't have soft delete plugin
    await mongoose.connection.collection('wallvideos').deleteMany()
  })

  after(async () => {
    await WallVideoModel.deleteMany({})
  })

  it('should return paginated results', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    await generateWallVideo({ user: user.user, path: 'video1' })
    await generateWallVideo({ user: user.user, path: 'video2' })
    await generateWallVideo({ user: user.user, path: 'video3' })
    await generateWallVideoWithSegmentation(user.user, 'video4')

    const response = await client(adminUser.tokens.accessToken).adminWallVideos({
      pagination: {
        first: 2,
      },
    })

    assert.strictEqual(response.admin.wallVideo.list.edges.length, 2)
    assert.strictEqual(response.admin.wallVideo.list.pageInfo.hasNextPage, true)
    assert.strictEqual(response.admin.wallVideo.list.pageInfo.hasPreviousPage, false)
    assert.strictEqual(response.admin.wallVideo.list.edges[0].node.path, 'video4')
    assert.strictEqual(response.admin.wallVideo.list.edges[1].node.path, 'video3')

    const response2 = await client(adminUser.tokens.accessToken).adminWallVideos({
      pagination: {
        first: 2,
        after: response.admin.wallVideo.list.pageInfo.endCursor,
      },
    })

    assert.strictEqual(response2.admin.wallVideo.list.edges.length, 2)
    assert.strictEqual(response2.admin.wallVideo.list.pageInfo.hasNextPage, false)
    assert.strictEqual(response2.admin.wallVideo.list.pageInfo.hasPreviousPage, true)
    assert.strictEqual(response2.admin.wallVideo.list.edges[0].node.path, 'video2')

    const responseSegmentation = await client(adminUser.tokens.accessToken).adminWallVideos({
      filter: {
        segmentation: true,
      },
    })

    assert.strictEqual(responseSegmentation.admin.wallVideo.list.edges.length, 1)
    assert.strictEqual(responseSegmentation.admin.wallVideo.list.pageInfo.hasNextPage, false)
    assert.strictEqual(responseSegmentation.admin.wallVideo.list.pageInfo.hasPreviousPage, false)
    assert.strictEqual(responseSegmentation.admin.wallVideo.list.edges[0].node.path, 'video4')
  })

  it("should return clips with tags if they're provided", async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const video1 = await generateWallVideo({ user: user.user, path: 'video1' })
    const video2 = await generateWallVideo({ user: user.user, path: 'video2' })

    await client(adminUser.tokens.accessToken).updateWallVideo({
      updateId: video1._id,
      input: {
        tags: [WallVideoTags.Correct, WallVideoTags.DidntSegment],
      },
    })

    await client(adminUser.tokens.accessToken).updateWallVideo({
      updateId: video2._id,
      input: {
        tags: [WallVideoTags.IncorrectObject, WallVideoTags.DidntSegment],
      },
    })

    const response = await client(adminUser.tokens.accessToken).adminWallVideos({
      filter: {
        tags: [WallVideoTags.Correct],
      },
    })

    assert.strictEqual(response.admin.wallVideo.list.edges.length, 1)
    assert.strictEqual(response.admin.wallVideo.list.edges[0].node.path, 'video1')

    const response2 = await client(adminUser.tokens.accessToken).adminWallVideos({
      filter: {
        tags: [WallVideoTags.DidntSegment],
      },
    })

    assert.strictEqual(response2.admin.wallVideo.list.edges.length, 2)
    assert.strictEqual(response2.admin.wallVideo.list.edges[0].node.path, 'video2')
    assert.strictEqual(response2.admin.wallVideo.list.edges[1].node.path, 'video1')
  })

  it('should return clips filtered by userId', async () => {
    const user1 = await generateRandomUser()
    const user2 = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    await generateWallVideo({ user: user1.user, path: 'video1' })
    await generateWallVideo({ user: user2.user, path: 'video2' })

    const response = await client(adminUser.tokens.accessToken).adminWallVideos({
      filter: {
        userId: user1.user._id.toString(),
      },
    })

    assert.strictEqual(response.admin.wallVideo.list.edges.length, 1)
    assert.strictEqual(response.admin.wallVideo.list.edges[0].node.path, 'video1')
  })

  it('should return clips filtered by type', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    await generateWallVideo({
      user: user.user,
      path: 'video1',
      type: WallVideoType.Defensive,
    })
    await generateWallVideo({ user: user.user, path: 'video2', type: WallVideoType.Interview })
    await generateWallVideo({ user: user.user, path: 'video3', type: WallVideoType.Training })

    const response = await client(adminUser.tokens.accessToken).adminWallVideos({
      filter: {
        type: [WallVideoType.Defensive],
      },
    })

    //sem se me dostava video z radku 62 a 237

    assert.strictEqual(response.admin.wallVideo.list.edges.length, 1)
    assert.strictEqual(response.admin.wallVideo.list.edges[0].node.path, 'video1')

    const response2 = await client(adminUser.tokens.accessToken).adminWallVideos({
      filter: {
        type: [WallVideoType.Training, WallVideoType.Interview],
      },
    })

    assert.strictEqual(response2.admin.wallVideo.list.edges.length, 2)

    assert.strictEqual(response2.admin.wallVideo.list.edges[0].node.path, 'video3')
    assert.strictEqual(response2.admin.wallVideo.list.edges[1].node.path, 'video2')
  })
})

describe('Update wall video', () => {
  beforeEach(async () => {
    await WallVideoModel.deleteMany({})
  })

  after(async () => {
    await WallVideoModel.deleteMany({})
  })
  it('should update tags of a wall video', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const video = await generateWallVideo({ user: user.user, path: 'video1' })

    const firstResponseCorrect = await client(adminUser.tokens.accessToken).updateWallVideo({
      updateId: video._id,
      input: {
        tags: [WallVideoTags.Correct],
      },
    })

    assert.strictEqual(
      firstResponseCorrect.admin.wallVideo.update.wallVideo?.tags?.[0],
      WallVideoTags.Correct
    )
    assert.strictEqual(firstResponseCorrect.admin.wallVideo.update.errors.length, 0)

    const secondResponseIncorrect = await client(adminUser.tokens.accessToken).updateWallVideo({
      updateId: video._id,
      input: {
        tags: [WallVideoTags.IncorrectObject],
      },
    })

    assert.strictEqual(
      secondResponseIncorrect.admin.wallVideo.update?.wallVideo?.tags?.[0],
      WallVideoTags.IncorrectObject
    )
    assert.strictEqual(secondResponseIncorrect.admin.wallVideo.update.errors.length, 0)

    const thirdResponseDidntSegment = await client(adminUser.tokens.accessToken).updateWallVideo({
      updateId: video._id,
      input: {
        tags: [WallVideoTags.DidntSegment, WallVideoTags.Correct],
      },
    })

    assert.strictEqual(
      thirdResponseDidntSegment.admin.wallVideo.update?.wallVideo?.tags?.[0],
      WallVideoTags.DidntSegment
    )
    assert.strictEqual(
      thirdResponseDidntSegment.admin.wallVideo.update?.wallVideo?.tags?.[1],
      WallVideoTags.Correct
    )
    assert.strictEqual(thirdResponseDidntSegment.admin.wallVideo.update.errors.length, 0)

    const fourthResponseNull = await client(adminUser.tokens.accessToken).updateWallVideo({
      updateId: video._id,
      input: {
        tags: [],
      },
    })

    assert.strictEqual(fourthResponseNull.admin.wallVideo.update?.wallVideo?.tags?.[0], undefined)
    assert.strictEqual(fourthResponseNull.admin.wallVideo.update.errors.length, 0)
  })

  it('should return error if wall video not found', async () => {
    const user = await generateRandomUser()
    const video = await generateWallVideo({ user: user.user, path: 'video1' })
    const adminUser = await generateRandomAdminUser()

    const findByIdStub = sinon.stub(WallVideoModel, 'findById').resolves(null)
    const response = await client(adminUser.tokens.accessToken).updateWallVideo({
      updateId: video.id,
      input: {
        tags: [WallVideoTags.Correct],
      },
    })

    assert.strictEqual(response.admin.wallVideo.update.errors.length, 1)
    assert.strictEqual(
      response.admin.wallVideo.update.errors[0].message,
      `Wall video with id ${video.id} not found`
    )

    findByIdStub.restore()
  })
})

describe('videosCountByTags query', () => {
  beforeEach(async () => {
    await WallVideoModel.deleteMany({})
  })

  after(async () => {
    await WallVideoModel.deleteMany({})
  })
  it('should return total count of videos by tags', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const videoTagCorrect = []
    for (let i = 0; i < 5; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.Correct],
        },
      })
      videoTagCorrect.push(video)
    }

    const videoTagDidntSegment = []
    for (let i = 0; i < 4; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.DidntSegment],
        },
      })
      videoTagDidntSegment.push(video)
    }

    const videoTagIncorrect = []
    for (let i = 0; i < 3; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.IncorrectObject],
        },
      })
      videoTagIncorrect.push(video)
    }

    const videoTagMissingInBeginning = []
    for (let i = 0; i < 2; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.MissingInBeginning],
        },
      })
      videoTagMissingInBeginning.push(video)
    }

    const videoTagMissingAtEnd = []
    for (let i = 0; i < 1; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.MissingAtEnd],
        },
      })
      videoTagMissingAtEnd.push(video)
    }

    const videoMissingInReplay = []
    for (let i = 0; i < 2; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.MissingInReplay],
        },
      })
      videoMissingInReplay.push(video)
    }

    const videoNotAccuratePosition = []
    for (let i = 0; i < 3; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.NotAccuratePosition],
        },
      })
      videoNotAccuratePosition.push(video)
    }

    const videoSegmentedMultiplePlayer = []
    for (let i = 0; i < 4; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.SegmentedMultiplePlayers],
        },
      })
      videoSegmentedMultiplePlayer.push(video)
    }

    const videoWithMoreTags = []
    for (let i = 0; i < 5; i++) {
      const video = await generateWallVideo({ user: user.user, path: 'video' + i })
      await client(adminUser.tokens.accessToken).updateWallVideo({
        updateId: video._id,
        input: {
          tags: [WallVideoTags.Correct, WallVideoTags.DidntSegment],
        },
      })
      videoWithMoreTags.push(video)
    }

    const response = await client(adminUser.tokens.accessToken).videosCountByTags()

    const expectedTags = [
      { tag: 'INCORRECT_OBJECT', count: 3, percentage: 10.34 },
      { tag: 'CORRECT', count: 10, percentage: 34.48 },
      { tag: 'MISSING_IN_BEGINNING', count: 2, percentage: 6.9 },
      { tag: 'MISSING_AT_END', count: 1, percentage: 3.45 },
      { tag: 'MISSING_IN_REPLAY', count: 2, percentage: 6.9 },
      { tag: 'NOT_ACCURATE_POSITION', count: 3, percentage: 10.34 },
      { tag: 'SEGMENTED_MULTIPLE_PLAYERS', count: 4, percentage: 13.79 },
      { tag: 'DIDNT_SEGMENT', count: 9, percentage: 31.03 },
    ]
    expectedTags.forEach((expectedTag) => {
      const actualTag = response.admin.wallVideo.videosCountByTags.videoTagsCount.find(
        (tagData) => tagData.tag === expectedTag.tag
      )

      assert.ok(actualTag, `Tag ${expectedTag.tag} should exist`)
      assert.strictEqual(actualTag.count, expectedTag.count)
      assert.strictEqual(actualTag.percentage, expectedTag.percentage)
    })

    assert.strictEqual(response.admin.wallVideo.videosCountByTags.totalTaggedVideosCount, 29)
  })
})

describe('wallvideo admin delete', () => {
  beforeEach(async () => {
    await WallVideoModel.deleteMany({})
  })

  after(async () => {
    await WallVideoModel.deleteMany({})
  })

  it('should delete wall video', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const video = await generateWallVideo({ user: user.user, path: 'video1' })

    const response = await client(adminUser.tokens.accessToken).deleteWallVideoAdmin({
      id: video._id.toString(),
    })

    assert.strictEqual(response.admin.wallVideo.delete.success, true)

    const dbVideo = await WallVideoModel.findOne({ _id: video._id })
    const dbVideoSoft = await WallVideoModel.findOne({ _id: video._id, deleted: true })

    assert.strictEqual(dbVideo, null)
    assert.strictEqual(dbVideoSoft?.deleted, true)
    assert.strictEqual(dbVideoSoft?.deletedBy.toString(), adminUser.user._id.toString())

    const videoAfterDelete = await WallVideoModel.findById(video._id)

    assert.strictEqual(videoAfterDelete, null)

    sinon.assert.calledTwice(s3ClientStub.send)
    sinon.assert.calledWith(
      s3ClientStub.send,
      sinon.match
        .instanceOf(DeleteObjectCommand)
        .and(sinon.match.has('input', sinon.match.has('Key', 'video1')))
    )

    sinon.assert.calledWith(
      s3ClientStub.send,
      sinon.match
        .instanceOf(DeleteObjectCommand)
        .and(sinon.match.has('input', sinon.match.has('Key', 'defaultThumbnail')))
    )
  })

  it('should return error if wall video not found', async () => {
    const user = await generateRandomUser()
    const video = await generateWallVideo({ user: user.user, path: 'video1' })
    const adminUser = await generateRandomAdminUser()

    const findByIdStub = sinon.stub(WallVideoModel, 'findById').resolves(null)
    const response = await client(adminUser.tokens.accessToken).deleteWallVideoAdmin({
      id: video.id.toString(),
    })

    assert.strictEqual(response.admin.wallVideo.delete.errors?.[0]?.message, `Wall video not found`)
    assert.strictEqual(response.admin.wallVideo.delete.success, false)

    findByIdStub.restore()
  })

  it('should return error if user not found', async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const video = await generateWallVideo({ user: user.user, path: 'video1' })

    await UserModel.deleteOne({ _id: user.user._id })

    try {
      await client(adminUser.tokens.accessToken).deleteWallVideoAdmin({
        id: video.id.toString(),
      })
    } catch (err) {
      assert.strictEqual(err.response.errors[0].message, 'User not found')
    }
  })

  it("should return error if delete video fails and don't delete video", async () => {
    const user = await generateRandomUser()
    const adminUser = await generateRandomAdminUser()

    const video = await generateWallVideo({ user: user.user, path: 'video1' })

    const deleteOneStub = sinon.stub(WallVideoModel, 'deleteOneByUser').throws('Error')

    const response = await client(adminUser.tokens.accessToken).deleteWallVideoAdmin({
      id: video._id.toString(),
    })

    assert.strictEqual(deleteOneStub.calledOnce, true)
    assert.strictEqual(response.admin.wallVideo.delete.errors?.length, 1)
    assert.strictEqual(
      response.admin.wallVideo.delete.errors?.[0]?.message,
      `Failed to delete wall video`
    )
    assert.strictEqual(response.admin.wallVideo.delete.success, false)

    deleteOneStub.restore()
  })
})
