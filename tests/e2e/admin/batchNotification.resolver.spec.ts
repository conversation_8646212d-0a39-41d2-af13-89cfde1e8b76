import assert from 'assert'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { BatchNotificationModel } from '../../../src/modules/admin/models/batchNotification'
import { BatchNotificationState } from '../../../src/modules/admin/types/batchNotification'
import { PaginationRepository } from '../../../src/modules/user/repositories/pagination.repository'
import { PushNotificationType, Role } from '../../graphql/components'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser } from '../../utils/users'

describe('Admin Batch Notifications Resolver', () => {
  describe('BatchNotification create', () => {
    it('should create a new batch notification successfully', async () => {
      await BatchNotificationModel.deleteMany({})

      const admin = await generateRandomAdminUser()
      const response = await client(admin.tokens.accessToken).createBatchNotification({
        input: {
          role: Role.Player,
          title: 'Test title',
          message: 'Test message',
          type: PushNotificationType.Follow,
          destinationPath: '/test',
        },
      })

      assert.strictEqual(
        response.admin.batchNotification.create.notification?.state,
        BatchNotificationState.SCHEDULED
      )
      assert.strictEqual(response.admin.batchNotification.create.notification?.title, 'Test title')
      assert.strictEqual(
        response.admin.batchNotification.create.notification?.message,
        'Test message'
      )
      assert.strictEqual(
        response.admin.batchNotification.create.notification?.type,
        PushNotificationType.Follow
      )
      assert.strictEqual(response.admin.batchNotification.create.notification?.forcePush, false)
      assert.strictEqual(
        response.admin.batchNotification.create.notification?.destinationPath,
        '/test'
      )
      assert.strictEqual(response.admin.batchNotification.create.notification?.totalUsers, 0)
      assert.strictEqual(response.admin.batchNotification.create.notification?.numberOfSent, 0)
      assert.strictEqual(
        response.admin.batchNotification.create.notification?.filter?.role,
        Role.Player
      )
    })

    it('should return an error when batch notification creation fails', async () => {
      const admin = await generateRandomAdminUser()

      const stub = sinon.stub(BatchNotificationModel, 'create').throws('error')

      try {
        await client(admin.tokens.accessToken).createBatchNotification({
          input: {
            role: Role.Player,
            title: 'Test title',
            message: 'Test message',
            type: PushNotificationType.Follow,
            destinationPath: '/test',
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response?.errors?.[0]?.message,
          'Failed to create batch notification'
        )

        sinon.assert.calledOnce(stub)
        stub.restore()
      }
    })

    it('should create a batch notification with filters', async () => {
      await BatchNotificationModel.deleteMany({})
      const now = new Date()

      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).createBatchNotification({
        input: {
          role: Role.Player,
          title: 'Test title',
          message: 'Test message',
          type: PushNotificationType.Follow,
          destinationPath: '/test',
          forcePush: true,
          nationalities: ['US', 'CA'],
          numberOfVideos: [1, 2],
          firstNameInitials: ['A', 'B'],
          scheduledAt: now,
          yearOfBirth: [1990, 1991],
        },
      })

      assert.strictEqual(
        response.admin.batchNotification.create.notification?.state,
        BatchNotificationState.SCHEDULED
      )
      assert.strictEqual(response.admin.batchNotification.create.notification?.title, 'Test title')
      assert.strictEqual(
        response.admin.batchNotification.create.notification?.message,
        'Test message'
      )
      assert.strictEqual(
        response.admin.batchNotification.create.notification?.type,
        PushNotificationType.Follow
      )
      assert.strictEqual(response.admin.batchNotification.create.notification?.forcePush, true)
      assert.strictEqual(
        response.admin.batchNotification.create.notification?.destinationPath,
        '/test'
      )
      assert.deepStrictEqual(
        response.admin.batchNotification.create.notification?.filter?.nationalities,
        ['US', 'CA']
      )
      assert.deepStrictEqual(
        response.admin.batchNotification.create.notification?.filter?.numberOfVideos,
        [1, 2]
      )
      assert.deepStrictEqual(
        response.admin.batchNotification.create.notification?.filter?.firstNameInitials,
        ['A', 'B']
      )

      assert.strictEqual(
        response.admin.batchNotification.create.notification?.scheduledAt,
        now.toISOString()
      )
      assert.deepStrictEqual(
        response.admin.batchNotification.create.notification?.filter?.yearOfBirth,
        [1990, 1991]
      )
    })
  })

  describe('BatchNotificationList', () => {
    it('should return a list of batch notifications', async () => {
      await BatchNotificationModel.deleteMany({})

      const admin = await generateRandomAdminUser()

      await BatchNotificationModel.create({
        title: 'Test title',
        message: 'Test message',
        type: PushNotificationType.Follow,
        destinationPath: '/test',
        forcePush: false,
        filter: {
          role: Role.Player,
        },
      })
      await BatchNotificationModel.create({
        title: 'Test title Parent',
        message: 'Test message Parent',
        type: PushNotificationType.Follow,
        destinationPath: '/test Parent',
        forcePush: false,
        filter: {
          role: Role.Parent,
        },
      })

      const response = await client(admin.tokens.accessToken).getBatchNotifications({
        pagination: {
          first: 1,
        },
      })

      assert.strictEqual(response.admin.batchNotification.list.pageInfo.totalCount, 2)
      assert.strictEqual(response.admin.batchNotification.list.pageInfo.hasNextPage, true)
      assert.strictEqual(response.admin.batchNotification.list.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.admin.batchNotification.list.edges.length, 1)
      assert.strictEqual(
        response.admin.batchNotification.list.edges[0].node.title,
        'Test title Parent'
      )
      assert.strictEqual(
        response.admin.batchNotification.list.edges[0].node.message,
        'Test message Parent'
      )
      assert.strictEqual(
        response.admin.batchNotification.list.edges[0].node.type,
        PushNotificationType.Follow
      )
      assert.strictEqual(response.admin.batchNotification.list.edges[0].node.forcePush, false)
      assert.strictEqual(
        response.admin.batchNotification.list.edges[0].node.destinationPath,
        '/test Parent'
      )
      assert.strictEqual(response.admin.batchNotification.list.edges[0].node.totalUsers, 0)
      assert.strictEqual(response.admin.batchNotification.list.edges[0].node.numberOfSent, 0)
      assert.strictEqual(
        response.admin.batchNotification.list.edges[0].node.filter?.role,
        Role.Parent
      )

      const responseSecondPage = await client(admin.tokens.accessToken).getBatchNotifications({
        pagination: {
          first: 1,
          after: response.admin.batchNotification.list.pageInfo.endCursor,
        },
      })

      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.pageInfo.hasNextPage,
        false
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.pageInfo.hasPreviousPage,
        true
      )
      assert.strictEqual(responseSecondPage.admin.batchNotification.list.edges.length, 1)
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.title,
        'Test title'
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.message,
        'Test message'
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.type,
        PushNotificationType.Follow
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.forcePush,
        false
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.destinationPath,
        '/test'
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.totalUsers,
        0
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.numberOfSent,
        0
      )
      assert.strictEqual(
        responseSecondPage.admin.batchNotification.list.edges[0].node.filter?.role,
        Role.Player
      )
    })

    it('should return an empty list when there are no notifications', async () => {
      await BatchNotificationModel.deleteMany({})

      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).getBatchNotifications({
        pagination: { first: 10 },
      })

      assert.strictEqual(response.admin.batchNotification.list.edges.length, 0)
      assert.strictEqual(response.admin.batchNotification.list.pageInfo.hasNextPage, false)
    })

    it('should return an error if failed to fetch batch notifications', async () => {
      const admin = await generateRandomAdminUser()
      const stub = sinon
        .stub(PaginationRepository.prototype, 'find')
        .throws(new Error('Database error'))

      try {
        await client(admin.tokens.accessToken).getBatchNotifications()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response?.errors?.[0]?.message,
          'Failed to fetch all batch notifications. Please try again later.'
        )
      }
      assert.strictEqual(stub.calledOnce, true)
    })
  })

  describe('BatchNotificationCancel', () => {
    it('should successfully cancel a batch notification', async () => {
      await BatchNotificationModel.deleteMany({})

      const admin = await generateRandomAdminUser()

      const notification = await BatchNotificationModel.create({
        title: 'Test Notification',
        message: 'Test message',
        type: PushNotificationType.Follow,
        destinationPath: '/test',
        forcePush: false,
        state: BatchNotificationState.SCHEDULED,
        filter: { role: Role.Player },
      })

      const response = await client(admin.tokens.accessToken).cancelBatchNotification({
        batchId: notification.id,
      })

      assert.strictEqual(
        response.admin.batchNotification.cancel.notification?.id,
        notification._id.toString()
      )
      assert.strictEqual(
        response.admin.batchNotification.cancel.notification?.state,
        BatchNotificationState.CANCELED
      )
    })

    it('should return an error when trying to cancel a SENT notification', async () => {
      await BatchNotificationModel.deleteMany({})

      const admin = await generateRandomAdminUser()

      const sentNotification = await BatchNotificationModel.create({
        title: 'Sent Notification',
        message: 'This was already sent',
        type: PushNotificationType.Follow,
        destinationPath: '/',
        forcePush: true,
        state: BatchNotificationState.SENT,
        filter: { role: Role.Parent },
      })

      const response = await client(admin.tokens.accessToken).cancelBatchNotification({
        batchId: sentNotification.id,
      })

      assert.strictEqual(response.admin.batchNotification.cancel.notification, null)
      assert.strictEqual(
        response.admin.batchNotification.cancel.errors?.[0].message,
        'Cannot cancel a sent or failed batch notification'
      )
    })

    it('should return an error when the notification is not found', async () => {
      await BatchNotificationModel.deleteMany({})

      const admin = await generateRandomAdminUser()

      const response = await client(admin.tokens.accessToken).cancelBatchNotification({
        batchId: new Types.ObjectId(),
      })

      assert.strictEqual(response.admin.batchNotification.cancel.notification, null)
      assert.strictEqual(
        response.admin.batchNotification.cancel.errors?.[0].message,
        'batch Notification not found'
      )
    })

    it('should return an error if failed to cancel a batch notification', async () => {
      const admin = await generateRandomAdminUser()
      const stub = sinon.stub(BatchNotificationModel, 'findById').throws('error')

      try {
        await client(admin.tokens.accessToken).cancelBatchNotification({
          batchId: new Types.ObjectId(),
        })
      } catch (err) {
        assert.strictEqual(
          err.response?.errors?.[0]?.message,
          'Failed to cancel batch notification.'
        )
      }

      sinon.assert.calledOnce(stub)
      stub.restore()
    })
  })
})
