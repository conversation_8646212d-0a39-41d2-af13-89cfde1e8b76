import assert from 'assert'
import { Types } from 'mongoose'

import {
  CrawlerDomainConfig,
  CrawlerDomainConfigModel,
} from '../../../src/modules/admin/models/crawlerDomainConfig'
import {
  CrawlerRunStats,
  CrawlerRunStatsModel,
} from '../../../src/modules/admin/models/crawlerRunStats'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, GenerateRandomAdminUserResponse } from '../../utils/users'

describe('CrawlerRunStats resolver', () => {
  let randomAdmin: GenerateRandomAdminUserResponse
  let crawlerDomainConfig: CrawlerDomainConfig
  let crawlerRunStats: CrawlerRunStats[]

  beforeEach(async () => {
    await CrawlerRunStatsModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})

    randomAdmin = await generateRandomAdminUser()

    // Create a crawler domain config
    crawlerDomainConfig = await CrawlerDomainConfigModel.create({
      domain: 'example.com',
      urlConfigs: [
        {
          entity: 'player',
          initialUrls: ['https://example.com/players'],
          urlPatternRegExp: '^https://example\\.com/player/\\d+$',
        },
      ],
      fetchType: 'axios',
    })

    // Create some crawler run stats
    const now = new Date()
    crawlerRunStats = []

    for (let i = 0; i < 15; i++) {
      const startTime = new Date(now.getTime() - i * 3600000) // 1 hour apart
      const finishTime = new Date(startTime.getTime() + 300000) // 5 minutes later

      const stats = await CrawlerRunStatsModel.create({
        crawlerDomainConfig: crawlerDomainConfig._id,
        startedAt: startTime,
        finishedAt: finishTime,
        durationMs: 300000,
        totalVisitedUrls: 100 + i,
        totalDiscoveredUrls: 50 + i,
        newlyDiscoveredUrls: 10 + i,
        urlPatternStats: [
          {
            urlPattern: '^https://example\\.com/player/\\d+$',
            matchCount: 50 + i,
          },
        ],
      })
      crawlerRunStats.push(stats)
    }
  })

  describe('admin.crawlerRunStats query', () => {
    it('should return paginated crawler run stats', async () => {
      const response = await client(randomAdmin.tokens.accessToken).admin_crawlerRunStats_list({
        first: 10,
        filter: {},
      })

      assert.strictEqual(response.admin.crawlerRunStats.list.edges.length, 10)
      assert.strictEqual(response.admin.crawlerRunStats.list.pageInfo.hasNextPage, true)
      assert.strictEqual(response.admin.crawlerRunStats.list.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.admin.crawlerRunStats.list.pageInfo.totalCount, 15)
    })

    it('should filter crawler run stats by crawlerDomainConfig', async () => {
      // Create a crawler domain config with a different domain
      const otherDomainConfig = await CrawlerDomainConfigModel.create({
        domain: 'other-domain.com',
        urlConfigs: [
          {
            entity: 'player',
            initialUrls: ['https://other-domain.com/players'],
            urlPatternRegExp: '^https://other-domain\\.com/player/\\d+$',
          },
        ],
        fetchType: 'axios',
      })

      // Create a stat with the different domain config
      await CrawlerRunStatsModel.create({
        crawlerDomainConfig: otherDomainConfig._id,
        startedAt: new Date(),
        finishedAt: new Date(),
        durationMs: 300000,
        totalVisitedUrls: 100,
        totalDiscoveredUrls: 50,
        newlyDiscoveredUrls: 10,
      })

      // Test filtering by crawlerDomainConfig directly
      const response = await client(randomAdmin.tokens.accessToken).admin_crawlerRunStats_list({
        first: 10,
        filter: {
          crawlerDomainConfig: otherDomainConfig._id.toString(),
        },
      })

      assert.strictEqual(response.admin.crawlerRunStats.list.edges.length, 1)
      assert.strictEqual(
        response.admin.crawlerRunStats.list.edges[0].node.crawlerDomainConfig?.domain,
        'other-domain.com'
      )
    })

    it('should handle pagination with cursor', async () => {
      const firstPage = await client(randomAdmin.tokens.accessToken).admin_crawlerRunStats_list({
        first: 5,
        filter: {},
      })

      assert.strictEqual(firstPage.admin.crawlerRunStats.list.edges.length, 5)
      assert.strictEqual(firstPage.admin.crawlerRunStats.list.pageInfo.hasNextPage, true)

      const secondPage = await client(randomAdmin.tokens.accessToken).admin_crawlerRunStats_list({
        first: 5,
        after: firstPage.admin.crawlerRunStats.list.pageInfo.endCursor,
        filter: {},
      })

      assert.strictEqual(secondPage.admin.crawlerRunStats.list.edges.length, 5)
      assert.strictEqual(secondPage.admin.crawlerRunStats.list.pageInfo.hasPreviousPage, true)
      assert.strictEqual(secondPage.admin.crawlerRunStats.list.pageInfo.hasNextPage, true)

      // Make sure we got different items
      const firstPageIds = firstPage.admin.crawlerRunStats.list.edges.map(
        (edge: { node: { id: string } }) => edge.node.id
      )
      const secondPageIds = secondPage.admin.crawlerRunStats.list.edges.map(
        (edge: { node: { id: string } }) => edge.node.id
      )

      for (const id of secondPageIds) {
        assert.strictEqual(firstPageIds.includes(id), false)
      }
    })
  })

  describe('admin.crawlerRunStats.get query', () => {
    it('should return a specific crawler run stat by ID', async () => {
      const statId = crawlerRunStats[0]._id.toString()

      const response = await client(randomAdmin.tokens.accessToken).admin_crawlerRunStats_get({
        id: statId,
      })

      const crawlerRunStat = response.admin.crawlerRunStats.get
      assert.ok(crawlerRunStat, 'Crawler run stat should exist')
      assert.strictEqual(crawlerRunStat.id, statId)
      assert.ok(crawlerRunStat.crawlerDomainConfig, 'Crawler domain config should exist')
      assert.strictEqual(crawlerRunStat.crawlerDomainConfig.domain, 'example.com')
      assert.strictEqual(crawlerRunStat.totalVisitedUrls, 100)
      assert.strictEqual(crawlerRunStat.totalDiscoveredUrls, 50)
      assert.strictEqual(crawlerRunStat.newlyDiscoveredUrls, 10)
    })

    it('should return null for non-existent ID', async () => {
      const nonExistentId = new Types.ObjectId().toString()

      const response = await client(randomAdmin.tokens.accessToken).admin_crawlerRunStats_get({
        id: nonExistentId,
      })

      assert.strictEqual(response.admin.crawlerRunStats.get, null)
    })
  })
})
