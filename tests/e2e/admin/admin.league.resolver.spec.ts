import assert from 'assert'
import { readFileSync } from 'fs'
import sinon from 'sinon'

import * as s3RequestPresigner from '../../../src/helpers/getSignedUrl'
import { LeagueModel } from '../../../src/modules/stats/models/league'
import { LeagueGenderCategory, LeagueLevel, LeagueType, StatusType } from '../../graphql/components'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, generateRandomLeague } from '../../utils/users'
import { s3ClientStub } from '..'

describe('admin.league', () => {
  describe('query admin.league.get', () => {
    it('should return league', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league admin query', countries: ['US'] },
      })

      const response = await client(adminUser.tokens.accessToken).getLeague({
        leagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(response.admin.league?.get?.name, 'Test league admin query')
      assert.deepStrictEqual(response.admin.league?.get.countries, ['US'])
    })

    it('should return null if league not found', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league not found admin query', countries: ['US'] },
      })

      const findByIdStub = sinon.stub(LeagueModel, 'findById').throws(new Error('Database error'))

      const response = await client(adminUser.tokens.accessToken).getLeague({
        leagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(response.admin.league.get, null)

      findByIdStub.restore
    })
  })

  describe('mutation - uploadLeagueLogo', () => {
    beforeEach(async () => {
      await LeagueModel.deleteMany({})
    })

    it('should return response if upload successful', async () => {
      const adminUser = await generateRandomAdminUser()
      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: {
          name: 'Test upload team logo',
          countries: ['US'],
        },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadLeagueLogo({
        uploadLeagueLogoId: league.createLeague.league?.id,
      })

      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(response.admin.league.uploadLeagueLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(
        response.admin.league.uploadLeagueLogo.league?.name,
        'Test upload team logo'
      )
      assert.strictEqual(response.admin.league.uploadLeagueLogo.league?.logo, null)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const responseConfirmation = await client(adminUser.tokens.accessToken).uploadLeagueLogo({
        uploadLeagueLogoId: league.createLeague.league?.id,
        input: { uploadSuccessful: true },
      })
      assert.strictEqual(responseConfirmation.admin.league.uploadLeagueLogo.errors, null)

      const logoKeyPattern = new RegExp(
        `images/leagues/${league.createLeague.league?.id}/logo/\\d+\\.png$`
      )

      const check = await LeagueModel.findById(league.createLeague.league?.id)

      assert.match(check?.logo || '', logoKeyPattern)
      assert.strictEqual(check?.logoUnconfirmed, null)
    })

    it("should return error if couldn't create presigned url", async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: {
          name: 'Test failed upload team logo',
          countries: ['US'],
        },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .throws(new Error('Failed to create presigned url'))

      const response = await client(adminUser.tokens.accessToken).uploadLeagueLogo({
        uploadLeagueLogoId: league.createLeague.league?.id,
      })

      assert.strictEqual(
        response.admin.league.uploadLeagueLogo.errors?.[0].message,
        'Failed to create preSignedUrl'
      )

      s3RequestPresignerstub.restore()
    })

    it('should return response if upload unsuccessful', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: {
          name: 'Test upload team logo failed',
          countries: ['US'],
        },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadLeagueLogo({
        uploadLeagueLogoId: league.createLeague.league?.id,
      })
      assert.strictEqual(response.admin.league.uploadLeagueLogo.errors, null)
      assert.strictEqual(response.admin.league.uploadLeagueLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(
        response.admin.league.uploadLeagueLogo.league?.name,
        'Test upload team logo failed'
      )
      assert.strictEqual(
        response.admin.league.uploadLeagueLogo.league.countries?.includes('US'),
        true
      )
      assert.strictEqual(response.admin.league.uploadLeagueLogo.league?.logo, null)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const logoKeyPattern = new RegExp(
        `images/leagues/${league.createLeague.league?.id}/logo/\\d+\\.png$`
      )

      const firstCheck = await LeagueModel.findById(league.createLeague.league?.id)
      assert.match(firstCheck?.logoUnconfirmed || '', logoKeyPattern)
      assert.strictEqual(firstCheck?.logo, undefined)

      const responseConfirmation = await client(adminUser.tokens.accessToken).uploadLeagueLogo({
        uploadLeagueLogoId: league.createLeague.league?.id,
        input: { uploadSuccessful: false },
      })

      assert.strictEqual(responseConfirmation.admin.league.uploadLeagueLogo.errors, null)

      const secondCheck = await LeagueModel.findById(league.createLeague.league?.id)
      assert.strictEqual(secondCheck?.logo, undefined)
      assert.strictEqual(secondCheck?.logoUnconfirmed, null)

      s3RequestPresignerstub.restore()
    })
  })

  describe('league query', () => {
    beforeEach(async () => {
      await LeagueModel.deleteMany({})
    })

    it('should return league by id or slug', async () => {
      const randomLeague = await generateRandomLeague('Test League')

      const response = await client().league({
        slug: randomLeague.slug,
      })

      assert.strictEqual(response.league?.name, 'Test League')
      assert.deepStrictEqual(response.league?.countries, ['CZ'])
    })

    it('should return null if league not found', async () => {
      const randomLeague = await generateRandomLeague('Test League')

      const findByIdStub = sinon.stub(LeagueModel, 'findOne').throws(new Error('Database error'))

      const response = await client().league({
        slug: randomLeague.slug,
      })

      assert.strictEqual(response.league, null)

      findByIdStub.restore
    })
  })

  describe('query admin.league.list', () => {
    it('should return all leagues if user is admin', async () => {
      const adminUser = await generateRandomAdminUser()

      for (let i = 0; i < 12; i++) {
        await client(adminUser.tokens.accessToken).createLeague({
          input: { name: `Test leagues admin query unique ${i}`, countries: ['US'] },
        })
      }

      const firstPageResponse = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          searchQuery: 'unique',
        },
      })
      assert.strictEqual(firstPageResponse.admin.league.list.edges.length, 10)
      assert.strictEqual(firstPageResponse.admin.league.list.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPageResponse.admin.league.list.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPageResponse.admin.league.list.pageInfo.totalCount, 12)
      assert.strictEqual(
        firstPageResponse.admin.league.list.edges[0].node.name,
        'Test leagues admin query unique 11'
      )
      assert.strictEqual(
        firstPageResponse.admin.league.list.edges[9].node.name,
        'Test leagues admin query unique 2'
      )

      const secondPageResponse = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          searchQuery: 'unique',
        },
        pagination: { after: firstPageResponse.admin.league.list.pageInfo.endCursor, first: 2 },
      })

      assert.strictEqual(secondPageResponse.admin.league.list.edges.length, 2)
      assert.strictEqual(secondPageResponse.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPageResponse.admin.league.list.pageInfo.hasPreviousPage, true)
      assert.strictEqual(
        secondPageResponse.admin.league.list.edges[0].node.name,
        'Test leagues admin query unique 1'
      )
      assert.strictEqual(
        secondPageResponse.admin.league.list.edges[1].node.name,
        'Test leagues admin query unique 0'
      )
    })

    it('should return leagues based on filters', async () => {
      await LeagueModel.deleteMany({})

      const adminUser = await generateRandomAdminUser()
      await generateRandomLeague(
        'SE league',
        'SE',
        'se-league',
        LeagueGenderCategory.Men,
        LeagueLevel.Professional,
        LeagueType.League,
        true
      )

      await generateRandomLeague(
        'SE Tournament 2',
        'SE',
        'se-tournament-2',
        LeagueGenderCategory.Women,
        LeagueLevel.Junior,
        LeagueType.Tournament,
        true
      )

      await generateRandomLeague(
        'FI league 1',
        'FI',
        'fi-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.College,
        LeagueType.League,
        false
      )

      const countryResponse = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          country: 'SE',
        },
      })

      assert.strictEqual(countryResponse.admin.league.list.edges.length, 2)
      assert.strictEqual(countryResponse.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(countryResponse.admin.league.list.edges[0].node.name, 'SE Tournament 2')
      assert.strictEqual(countryResponse.admin.league.list.edges[1].node.name, 'SE league')

      const genderCategoryResponse = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          genderCategory: LeagueGenderCategory.Men,
        },
      })

      assert.strictEqual(genderCategoryResponse.admin.league.list.edges.length, 1)
      assert.strictEqual(genderCategoryResponse.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(genderCategoryResponse.admin.league.list.edges[0].node.name, 'SE league')

      const levelResponse = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          level: LeagueLevel.College,
        },
      })

      assert.strictEqual(levelResponse.admin.league.list.edges.length, 1)
      assert.strictEqual(levelResponse.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(levelResponse.admin.league.list.edges[0].node.name, 'FI league 1')

      const typeResponse = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          type: LeagueType.League,
        },
      })

      assert.strictEqual(typeResponse.admin.league.list.edges.length, 2)
      assert.strictEqual(typeResponse.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(typeResponse.admin.league.list.edges[0].node.name, 'FI league 1')
      assert.strictEqual(typeResponse.admin.league.list.edges[1].node.name, 'SE league')

      const scrapedResponse = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          scraped: StatusType.True,
        },
      })

      assert.strictEqual(scrapedResponse.admin.league.list.edges.length, 2)
      assert.strictEqual(scrapedResponse.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(scrapedResponse.admin.league.list.edges[0].node.name, 'SE Tournament 2')
      assert.strictEqual(scrapedResponse.admin.league.list.edges[1].node.name, 'SE league')

      const searchQueryResponse = await client(adminUser.tokens.accessToken).adminLeagueList()

      assert.strictEqual(searchQueryResponse.admin.league.list.edges.length, 3)
      assert.strictEqual(searchQueryResponse.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(searchQueryResponse.admin.league.list.edges[0].node.name, 'FI league 1')
      assert.strictEqual(
        searchQueryResponse.admin.league.list.edges[1].node.name,
        'SE Tournament 2'
      )
      assert.strictEqual(searchQueryResponse.admin.league.list.edges[2].node.name, 'SE league')
    })

    it('should return response based on filter checked', async () => {
      await LeagueModel.deleteMany({})

      const adminUser = await generateRandomAdminUser()

      const leagues = []

      for (let i = 0; i < 2; i++) {
        const leagueUndefined = await generateRandomLeague(
          'Test league' + i,
          'ES',
          'test-league' + i,
          LeagueGenderCategory.Men,
          LeagueLevel.Professional,
          LeagueType.League
        )
        leagues.push(leagueUndefined)
      }

      for (let i = 0; i < 6; i++) {
        const leagueChecked = await generateRandomLeague(
          'Test checked league' + i,
          'CZ',
          'test-checked-league' + i,
          LeagueGenderCategory.Men,
          LeagueLevel.Professional,
          LeagueType.League,
          true,
          true
        )
        leagues.push(leagueChecked)
      }

      for (let i = 0; i < 7; i++) {
        const leagueNotChecked = await generateRandomLeague(
          'Test not checked league' + i,
          'SK',
          'test-not-checked-league' + i,
          LeagueGenderCategory.Men,
          LeagueLevel.Professional,
          LeagueType.League,
          false,
          false
        )
        leagues.push(leagueNotChecked)
      }

      const responseFirstPageChecked = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          checked: StatusType.True,
        },
        pagination: {
          first: 5,
        },
      })

      assert.strictEqual(responseFirstPageChecked.admin.league.list.pageInfo.totalCount, 6)
      assert.strictEqual(responseFirstPageChecked.admin.league.list.edges.length, 5)
      assert.strictEqual(responseFirstPageChecked.admin.league.list.pageInfo.hasNextPage, true)
      assert.strictEqual(
        responseFirstPageChecked.admin.league.list.edges[0].node.name,
        'Test checked league5'
      )
      assert.strictEqual(
        responseFirstPageChecked.admin.league.list.edges[4].node.name,
        'Test checked league1'
      )

      const responseSecondPageChecked = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          checked: StatusType.True,
        },
        pagination: {
          first: 5,
          after: responseFirstPageChecked.admin.league.list.pageInfo.endCursor,
        },
      })

      assert.strictEqual(responseSecondPageChecked.admin.league.list.edges.length, 1)
      assert.strictEqual(responseSecondPageChecked.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(
        responseSecondPageChecked.admin.league.list.edges[0].node.name,
        'Test checked league0'
      )

      const responseFirstPageNotChecked = await client(
        adminUser.tokens.accessToken
      ).adminLeagueList({
        filter: {
          checked: StatusType.False,
        },
        pagination: {
          first: 5,
        },
      })

      assert.strictEqual(responseFirstPageNotChecked.admin.league.list.edges.length, 5)
      assert.strictEqual(responseFirstPageNotChecked.admin.league.list.pageInfo.hasNextPage, true)
      assert.strictEqual(
        responseFirstPageNotChecked.admin.league.list.edges[0].node.name,
        'Test not checked league6'
      )
      assert.strictEqual(
        responseFirstPageNotChecked.admin.league.list.edges[4].node.name,
        'Test not checked league2'
      )

      const responseSecondPageNotChecked = await client(
        adminUser.tokens.accessToken
      ).adminLeagueList({
        filter: {
          checked: StatusType.False,
        },
        pagination: {
          first: 5,
          after: responseFirstPageNotChecked.admin.league.list.pageInfo.endCursor,
        },
      })

      assert.strictEqual(responseSecondPageNotChecked.admin.league.list.edges.length, 2)
      assert.strictEqual(responseSecondPageNotChecked.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(
        responseSecondPageNotChecked.admin.league.list.edges[0].node.name,
        'Test not checked league1'
      )
      assert.strictEqual(
        responseSecondPageNotChecked.admin.league.list.edges[1].node.name,
        'Test not checked league0'
      )

      const responseNotSet = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          checked: StatusType.NotSet,
        },
      })

      assert.strictEqual(responseNotSet.admin.league.list.pageInfo.totalCount, 2)
      assert.strictEqual(responseNotSet.admin.league.list.edges.length, 2)
      assert.strictEqual(responseNotSet.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(responseNotSet.admin.league.list.pageInfo.hasPreviousPage, false)
      assert.strictEqual(responseNotSet.admin.league.list.edges[1].node.name, 'Test league0')
      assert.strictEqual(responseNotSet.admin.league.list.edges[0].node.name, 'Test league1')

      const responseAll = await client(adminUser.tokens.accessToken).adminLeagueList({
        pagination: {
          first: 20,
        },
      })
      assert.strictEqual(responseAll.admin.league.list.pageInfo.totalCount, 15)
      assert.strictEqual(responseAll.admin.league.list.edges.length, 15)
      assert.strictEqual(responseAll.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(responseAll.admin.league.list.pageInfo.hasPreviousPage, false)
      assert.strictEqual(
        responseAll.admin.league.list.edges[0].node.name,
        'Test not checked league6'
      )
      assert.strictEqual(responseAll.admin.league.list.edges[14].node.name, 'Test league0')
    })

    it('should return response based on filter scraped', async () => {
      await LeagueModel.deleteMany({})

      const adminUser = await generateRandomAdminUser()

      await generateRandomLeague(
        'Test undefined league',
        'ES',
        'test-undefined-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.Professional,
        LeagueType.League
      )

      await generateRandomLeague(
        'Test scraped league',
        'CZ',
        'test-scraped-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.Professional,
        LeagueType.League,
        true,
        true
      )

      await generateRandomLeague(
        'Test not scraped league',
        'SK',
        'test-not-scraped-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.Professional,
        LeagueType.League,
        false,
        false
      )

      const responseScraped = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          scraped: StatusType.True,
        },
      })

      assert.strictEqual(responseScraped.admin.league.list.edges.length, 1)
      assert.strictEqual(responseScraped.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(
        responseScraped.admin.league.list.edges[0].node.name,
        'Test scraped league'
      )

      const responseNotScrapedAndUndefined = await client(
        adminUser.tokens.accessToken
      ).adminLeagueList({
        filter: {
          scraped: StatusType.False,
        },
      })

      assert.strictEqual(responseNotScrapedAndUndefined.admin.league.list.edges.length, 1)
      assert.strictEqual(
        responseNotScrapedAndUndefined.admin.league.list.pageInfo.hasNextPage,
        false
      )
      assert.strictEqual(
        responseNotScrapedAndUndefined.admin.league.list.edges[0].node.name,
        'Test not scraped league'
      )

      const responseNotSet = await client(adminUser.tokens.accessToken).adminLeagueList({
        filter: {
          scraped: StatusType.NotSet,
        },
      })

      assert.strictEqual(responseNotSet.admin.league.list.edges.length, 1)
      assert.strictEqual(responseNotSet.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(
        responseNotSet.admin.league.list.edges[0].node.name,
        'Test undefined league'
      )

      const responseAll = await client(adminUser.tokens.accessToken).adminLeagueList()

      assert.strictEqual(responseAll.admin.league.list.edges.length, 3)
      assert.strictEqual(responseAll.admin.league.list.pageInfo.hasNextPage, false)
      assert.strictEqual(
        responseAll.admin.league.list.edges[0].node.name,
        'Test not scraped league'
      )
      assert.strictEqual(responseAll.admin.league.list.edges[1].node.name, 'Test scraped league')
      assert.strictEqual(responseAll.admin.league.list.edges[2].node.name, 'Test undefined league')
    })
  })

  describe('AdminLeagueMutations - removeLogo', () => {
    beforeEach(async () => {
      await LeagueModel.deleteMany({})
    })

    it('should remove league logo and return success', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomLeague = await generateRandomLeague(
        'Test remove logo league',
        'ES',
        'test-remove-logo-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.Professional,
        LeagueType.League
      )

      await LeagueModel.updateOne(
        { _id: randomLeague.id },
        {
          logo: 'some/logo/path.png',
          ogLogo: 'some/logo/og.png',
          logoUnconfirmed: 'some/logo/unconfirmed.png',
        }
      )

      s3ClientStub.send.resetHistory()

      const response = await client(adminUser.tokens.accessToken).removeLeagueLogo({
        removeLeagueLogoId: randomLeague.id,
      })

      assert.strictEqual(response.admin.league.removeLeagueLogo?.success, true)

      sinon.assert.callCount(s3ClientStub.send, 3)

      sinon.assert.calledWith(
        s3ClientStub.send.getCall(0),
        sinon.match.has('input', sinon.match.has('Key', 'some/logo/path.png'))
      )
      sinon.assert.calledWith(
        s3ClientStub.send.getCall(0),
        sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
      )

      sinon.assert.calledWith(
        s3ClientStub.send.getCall(1),
        sinon.match.has('input', sinon.match.has('Key', 'some/logo/og.png'))
      )
      sinon.assert.calledWith(
        s3ClientStub.send.getCall(1),
        sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
      )
      sinon.assert.calledWith(
        s3ClientStub.send.getCall(2),
        sinon.match.has('input', sinon.match.has('Key', 'some/logo/unconfirmed.png'))
      )
      sinon.assert.calledWith(
        s3ClientStub.send.getCall(2),
        sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
      )

      const updated = await LeagueModel.findById(randomLeague.id)
      assert.strictEqual(updated?.logo, null)
      assert.strictEqual(updated?.ogLogo, null)
      assert.strictEqual(updated?.logoUnconfirmed, null)
    })

    it('should return error if league not found', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomLeague = await generateRandomLeague(
        'Test remove logo league',
        'ES',
        'test-remove-logo-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.Professional,
        LeagueType.League
      )

      const stubById = sinon.stub(LeagueModel, 'findById').resolves(null)

      const response = await client(adminUser.tokens.accessToken).removeLeagueLogo({
        removeLeagueLogoId: randomLeague.id,
      })

      assert.strictEqual(response.admin.league.removeLeagueLogo?.success, false)
      assert.strictEqual(
        response.admin?.league?.removeLeagueLogo?.errors?.[0]?.message ?? 'Unknown error',
        'League not found'
      )

      stubById.restore()
    })

    it('should return error if db save fail', async () => {
      const adminUser = await generateRandomAdminUser()
      const randomLeague = await generateRandomLeague(
        'Test remove logo league',
        'ES',
        'test-remove-logo-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.Professional,
        LeagueType.League
      )

      const stubById = sinon
        .stub(LeagueModel, 'updateOne')
        .throws(new Error('Expected error to be thrown'))

      try {
        await client(adminUser.tokens.accessToken).removeLeagueLogo({
          removeLeagueLogoId: randomLeague.id,
        })
        assert.fail('Expected error to be thrown')
      } catch (err) {
        assert.strictEqual(err.response?.errors?.[0]?.message, 'Failed to remove league logo')
      }

      stubById.restore()
    })
  })
})
