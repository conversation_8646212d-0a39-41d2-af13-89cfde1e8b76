import assert from 'assert'

import { UserModel } from '../../../src/modules/user/models'
import { Role as dbrole } from '../../../src/modules/user/models/user'
import { client } from '../../utils/graphql'
import {
  generateRandomAdminUser,
  generateRandomAgency,
  generateRandomUser,
} from '../../utils/users'

import { faker } from '@faker-js/faker/locale/af_ZA'

describe('Admin Resolver', () => {
  it('should return an empty object for the admin query', async () => {
    const admin = await generateRandomAdminUser()

    const resolve = await client(admin.tokens.accessToken).adminUser({
      userId: admin.user._id,
    })
    assert.strictEqual(resolve.admin.user, null)
  })

  it('should return an object for the admin query for a provided user', async () => {
    const randomAdmin = await generateRandomAdminUser()

    const user = await generateRandomUser({ firstname: '<PERSON>' })

    const response = await client(randomAdmin.tokens.accessToken).adminUser({
      userId: user.user._id,
    })

    assert.strictEqual(response.admin.user?.firstname, 'John')
  })

  it('should return an object for the admin query for a provided user with agency', async () => {
    const randomAdmin = await generateRandomAdminUser()

    const agency = await generateRandomAgency('agency124')
    const agencyId = agency._id.toString()

    const randomUser = await generateRandomUser({
      role: dbrole.PLAYER,
      agency: agency._id,
    })

    const agencyUnverified = await generateRandomAgency('agency125')
    const agencyUnverifiedId = agencyUnverified._id.toString()

    await client(randomUser.tokens.accessToken).updateUserWithAgencyUnverified({
      input: {
        bio: {
          agencyUnverified: agencyUnverifiedId,
        },
      },
    })

    const response = await client(randomAdmin.tokens.accessToken).adminUser({
      userId: randomUser.user._id,
    })

    assert.strictEqual(response.admin.user?.bio?.agency?.id, agencyId)
    assert.strictEqual(response.admin.user?.bio?.agency?.name, agency.name)
    assert.strictEqual(response.admin.user?.bio?.agencyUnverified?.id, agencyUnverifiedId)
    assert.strictEqual(response.admin.user?.bio?.agencyUnverified?.name, agencyUnverified.name)
  })

  describe('User count query', async () => {
    it('should return the total count of user without day filter', async () => {
      await UserModel.deleteMany({})
      const randomAdmin = await generateRandomAdminUser()

      const players = []
      for (let i = 0; i < 4; i++) {
        const user = await generateRandomUser()
        players.push(user)
      }

      const parents = []
      for (let i = 0; i < 5; i++) {
        const parent = await generateRandomUser({ role: dbrole.PARENT })
        parents.push(parent)
      }

      const sportsProfesionals = []
      for (let i = 0; i < 2; i++) {
        const sportsProfesional = await generateRandomUser({ role: dbrole.SPORTS_PROFESSIONAL })
        sportsProfesionals.push(sportsProfesional)
      }

      const spotsFans = []
      for (let i = 0; i < 10; i++) {
        const sportsFan = await generateRandomUser({ role: dbrole.SPORTS_FAN })
        spotsFans.push(sportsFan)
      }

      const shadowProfiles = []
      for (let i = 0; i < 3; i++) {
        const shadowProfile = await generateRandomUser({ role: dbrole.SHADOW_PLAYER })
        shadowProfiles.push(shadowProfile)
      }

      const response = await client(randomAdmin.tokens.accessToken).userCount()

      assert.strictEqual(response.admin.userCount?.playersCount, 4)
      assert.strictEqual(response.admin.userCount?.parentsCount, 5)
      assert.strictEqual(response.admin.userCount?.sportsProfsCount, 2)
      assert.strictEqual(response.admin.userCount?.sportsFansCount, 10)
      assert.strictEqual(response.admin.userCount?.shadowPlayersCount, 3)
    })

    it('should return the total count of users with day filter', async () => {
      await UserModel.deleteMany({})
      const randomAdmin = await generateRandomAdminUser()

      const players = []
      for (let i = 0; i < 4; i++) {
        const user = await generateRandomUser()
        players.push(user)
      }

      const parents = []
      for (let i = 0; i < 5; i++) {
        const parent = await generateRandomUser({ role: dbrole.PARENT })
        parents.push(parent)
      }

      const sportsProfesionals = []
      for (let i = 0; i < 2; i++) {
        const sportsProfesional = await generateRandomUser({
          role: dbrole.SPORTS_PROFESSIONAL,
        })
        sportsProfesionals.push(sportsProfesional)
      }

      const spotsFans = []
      for (let i = 0; i < 10; i++) {
        const sportsFan = await generateRandomUser({ role: dbrole.SPORTS_FAN })
        spotsFans.push(sportsFan)
      }

      const shadowProfiles = []
      for (let i = 0; i < 3; i++) {
        const shadowProfile = await generateRandomUser({ role: dbrole.SHADOW_PLAYER })
        shadowProfiles.push(shadowProfile)
      }

      //user player created 8 days ago
      await UserModel.create({
        name: 'pastPlayer',
        email: faker.internet.email(),
        role: dbrole.PLAYER,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 8),
      })

      //user parent created 14 days ago
      await UserModel.create({
        name: 'pastParent',
        email: faker.internet.email(),
        role: dbrole.PARENT,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 14),
      })

      // user sport professional created 50 days ago
      await UserModel.create({
        name: 'pastSportProfessional',
        email: faker.internet.email(),
        role: dbrole.SPORTS_PROFESSIONAL,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 50),
      })

      // user sport fan created 170 days ago
      await UserModel.create({
        name: 'pastSportFan',
        email: faker.internet.email(),
        role: dbrole.SPORTS_FAN,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 170),
      })

      // user shadow player created 175 days ago
      await UserModel.create({
        name: 'pastShadowPlayer',
        email: faker.internet.email(),
        role: dbrole.SHADOW_PLAYER,
        createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 175),
      })

      const sevenDayResponse = await client(randomAdmin.tokens.accessToken).userCount({ days: 7 })

      assert.strictEqual(sevenDayResponse.admin.userCount?.playersCount, 4)
      assert.strictEqual(sevenDayResponse.admin.userCount?.parentsCount, 5)
      assert.strictEqual(sevenDayResponse.admin.userCount?.sportsProfsCount, 2)
      assert.strictEqual(sevenDayResponse.admin.userCount?.sportsFansCount, 10)
      assert.strictEqual(sevenDayResponse.admin.userCount?.shadowPlayersCount, 3)

      const thirtyDayResponse = await client(randomAdmin.tokens.accessToken).userCount({ days: 30 })
      assert.strictEqual(thirtyDayResponse.admin.userCount?.playersCount, 5)
      assert.strictEqual(thirtyDayResponse.admin.userCount?.parentsCount, 6)
      assert.strictEqual(thirtyDayResponse.admin.userCount?.sportsProfsCount, 2)
      assert.strictEqual(thirtyDayResponse.admin.userCount?.sportsFansCount, 10)
      assert.strictEqual(sevenDayResponse.admin.userCount?.shadowPlayersCount, 3)

      const ninetyDayResponse = await client(randomAdmin.tokens.accessToken).userCount({ days: 90 })
      assert.strictEqual(ninetyDayResponse.admin.userCount?.playersCount, 5)
      assert.strictEqual(ninetyDayResponse.admin.userCount?.parentsCount, 6)
      assert.strictEqual(ninetyDayResponse.admin.userCount?.sportsProfsCount, 3)
      assert.strictEqual(ninetyDayResponse.admin.userCount?.sportsFansCount, 10)
      assert.strictEqual(sevenDayResponse.admin.userCount?.shadowPlayersCount, 3)

      const yearResponse = await client(randomAdmin.tokens.accessToken).userCount({ days: 180 })
      assert.strictEqual(yearResponse.admin.userCount?.playersCount, 5)
      assert.strictEqual(yearResponse.admin.userCount?.parentsCount, 6)
      assert.strictEqual(yearResponse.admin.userCount?.sportsProfsCount, 3)
      assert.strictEqual(yearResponse.admin.userCount?.sportsFansCount, 11)
      assert.strictEqual(yearResponse.admin.userCount?.shadowPlayersCount, 4)
    })
  })
})
