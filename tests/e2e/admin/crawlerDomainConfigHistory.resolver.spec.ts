import assert from 'assert'
import { Types } from 'mongoose'

import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { CrawlerDomainConfigHistoryModel } from '../../../src/modules/admin/models/crawlerDomainConfigHistory'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, GenerateRandomAdminUserResponse } from '../../utils/users'

describe('CrawlerDomainConfigHistory Resolver', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigHistoryModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
  })

  it('should list crawler domain config history', async () => {
    const admin = await generateRandomAdminUser()

    // Create a crawler domain config
    const config = await CrawlerDomainConfigModel.create({
      domain: 'example.com',
      urlConfigs: [
        {
          entity: 'player',
          initialUrls: ['https://example.com/players'],
          urlPatternRegExp: '^https://example\\.com/player/\\d+$',
        },
      ],
      fetchType: 'axios',
    })

    // Create some history entries manually (normally created by patchHistoryPlugin)
    await CrawlerDomainConfigHistoryModel.create({
      op: 'findOneAndUpdate',
      modelName: 'CrawlerDomainConfig',
      collectionName: 'crawlerdomainconfigs',
      collectionId: config._id,
      patch: [
        {
          op: 'test',
          path: '/domain',
          value: 'example.com',
        },
        {
          op: 'replace',
          path: '/domain',
          value: 'updated-example.com',
        },
      ],
      user: {
        id: admin.user.id,
        email: admin.user.email,
        admin: true,
        role: 'ADMIN',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      },
      version: 0,
    })

    const response = await client(admin.tokens.accessToken).getCrawlerDomainConfigHistory({
      first: 10,
    })

    expect(response.admin.crawlerDomainConfigHistory.list.edges).toHaveLength(1)
    expect(response.admin.crawlerDomainConfigHistory.list.edges[0].node.modelName).toBe(
      'CrawlerDomainConfig'
    )
    expect(response.admin.crawlerDomainConfigHistory.list.edges[0].node.collectionId).toBe(
      config._id.toString()
    )
    expect(response.admin.crawlerDomainConfigHistory.list.edges[0].node.patch).toHaveLength(2)
  })

  it('should filter crawler domain config history by collectionId', async () => {
    const admin = await generateRandomAdminUser()

    // Create two crawler domain configs
    const config1 = await CrawlerDomainConfigModel.create({
      domain: 'example1.com',
      urlConfigs: [
        {
          entity: 'player',
          initialUrls: ['https://example1.com/players'],
          urlPatternRegExp: '^https://example1\\.com/player/\\d+$',
        },
      ],
      fetchType: 'axios',
    })

    const config2 = await CrawlerDomainConfigModel.create({
      domain: 'example2.com',
      urlConfigs: [
        {
          entity: 'player',
          initialUrls: ['https://example2.com/players'],
          urlPatternRegExp: '^https://example2\\.com/player/\\d+$',
        },
      ],
      fetchType: 'axios',
    })

    // Create history entries for both configs
    await CrawlerDomainConfigHistoryModel.create({
      op: 'findOneAndUpdate',
      modelName: 'CrawlerDomainConfig',
      collectionName: 'crawlerdomainconfigs',
      collectionId: config1._id,
      patch: [{ op: 'test', path: '/domain', value: 'example1.com' }],
      user: {
        id: admin.user.id,
        email: admin.user.email,
        admin: true,
        role: 'ADMIN',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      },
      version: 0,
    })

    await CrawlerDomainConfigHistoryModel.create({
      op: 'findOneAndUpdate',
      modelName: 'CrawlerDomainConfig',
      collectionName: 'crawlerdomainconfigs',
      collectionId: config2._id,
      patch: [{ op: 'test', path: '/domain', value: 'example2.com' }],
      user: {
        id: admin.user.id,
        email: admin.user.email,
        admin: true,
        role: 'ADMIN',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      },
      version: 0,
    })

    // Filter by config1 ID
    const response = await client(admin.tokens.accessToken).getCrawlerDomainConfigHistory({
      first: 10,
      filter: {
        collectionId: config1._id,
      },
    })

    expect(response.admin.crawlerDomainConfigHistory.list.edges).toHaveLength(1)
    expect(response.admin.crawlerDomainConfigHistory.list.edges[0].node.collectionId).toBe(
      config1._id.toString()
    )
  })

  it('should get single crawler domain config history item', async () => {
    const admin = await generateRandomAdminUser()

    const config = await CrawlerDomainConfigModel.create({
      domain: 'example.com',
      urlConfigs: [
        {
          entity: 'player',
          initialUrls: ['https://example.com/players'],
          urlPatternRegExp: '^https://example\\.com/player/\\d+$',
        },
      ],
      fetchType: 'axios',
    })

    const historyItem = await CrawlerDomainConfigHistoryModel.create({
      op: 'findOneAndUpdate',
      modelName: 'CrawlerDomainConfig',
      collectionName: 'crawlerdomainconfigs',
      collectionId: config._id,
      patch: [
        {
          op: 'test',
          path: '/domain',
          value: 'example.com',
        },
      ],
      user: {
        id: admin.user.id,
        email: admin.user.email,
        admin: true,
        role: 'ADMIN',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      },
      version: 0,
    })

    const response = await client(admin.tokens.accessToken).getCrawlerDomainConfigHistoryItem({
      id: historyItem._id.toString(),
    })

    expect(response.admin.crawlerDomainConfigHistory.get).toBeTruthy()
    expect(response.admin.crawlerDomainConfigHistory.get!.id).toBe(historyItem._id.toString())
    expect(response.admin.crawlerDomainConfigHistory.get!.modelName).toBe('CrawlerDomainConfig')
  })
})
