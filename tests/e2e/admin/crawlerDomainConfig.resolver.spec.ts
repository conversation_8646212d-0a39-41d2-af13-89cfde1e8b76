import assert from 'assert'

import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { CrawlerEntity, FetchType } from '../../graphql/components'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser } from '../../utils/users'

describe('CrawlerDomainConfigResolver', () => {
  after(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
  })
  it('should create crawler domain config', async () => {
    const admin = await generateRandomAdminUser()

    const response = await client(admin.tokens.accessToken).createCrawlerDomainConfig({
      input: {
        domain: 'example.com',
        relevantUrlPatterns: ['example', 'test'],
        fetchType: FetchType.Axios,
        parallelism: 10,
        urlConfigs: [
          {
            urlPatternRegExp: 'example.com/player/.*',
            entity: CrawlerEntity.Player,
            initialUrls: ['example.com/player/1', 'example.com/player/2'],
            externalIdPatternRegExp: 'example.com/player/(\\d+)',
            extractionScript: 'return { name: "John Doe" }',
            fetchType: FetchType.Axios,
            addToQueue: true,
            paramExtractors: [
              {
                selector: 'select[name="season"]',
                queryParam: 'seasonId',
              },
            ],
          },
        ],
      },
    })

    assert.strictEqual(response.admin.crawlerDomainConfig.create.config.domain, 'example.com')
    assert.deepStrictEqual(response.admin.crawlerDomainConfig.create.config.relevantUrlPatterns, [
      'example',
      'test',
    ])
    assert.strictEqual(response.admin.crawlerDomainConfig.create.config.fetchType, FetchType.Axios)
    assert.strictEqual(response.admin.crawlerDomainConfig.create.config.parallelism, 10)
    assert.deepStrictEqual(response.admin.crawlerDomainConfig.create.config.urlConfigs, [
      {
        urlPatternRegExp: 'example.com/player/.*',
        entity: CrawlerEntity.Player,
        initialUrls: ['example.com/player/1', 'example.com/player/2'],
        externalIdPatternRegExp: 'example.com/player/(\\d+)',
        extractionScript: 'return { name: "John Doe" }',
        fetchType: FetchType.Axios,
        addToQueue: true,
        paramExtractors: [
          {
            selector: 'select[name="season"]',
            queryParam: 'seasonId',
          },
        ],
      },
    ])
  })

  it('should update crawler domain config', async () => {
    await CrawlerDomainConfigModel.deleteMany({})

    const admin = await generateRandomAdminUser()

    const createResponse = await client(admin.tokens.accessToken).createCrawlerDomainConfig({
      input: {
        domain: 'example.com',
        relevantUrlPatterns: ['example', 'test'],
        fetchType: FetchType.Axios,
        urlConfigs: [
          {
            urlPatternRegExp: 'example.com/player/.*',
            entity: CrawlerEntity.Player,
            initialUrls: ['example.com/player/1', 'example.com/player/2'],
            externalIdPatternRegExp: 'example.com/player/(\\d+)',
            extractionScript: 'return { name: "John Doe" }',
            fetchType: FetchType.Axios,
            addToQueue: true,
            paramExtractors: [
              {
                selector: 'select[name="season"]',
                queryParam: 'seasonId',
              },
            ],
          },
        ],
      },
    })

    const response = await client(admin.tokens.accessToken).updateCrawlerDomainConfig({
      id: createResponse.admin.crawlerDomainConfig.create.config.id,
      input: {
        domain: 'ceskyhokej.cz',
        relevantUrlPatterns: ['relevantUrlPattern1', 'relevantUrlPattern2'],
        fetchType: FetchType.Puppeteer,
        parallelism: 15,
        urlConfigs: [
          {
            urlPatternRegExp: 'ceskyhokej.cz/player/.*',
            entity: CrawlerEntity.Player,
            initialUrls: ['ceskyhokej.cz/player/1', 'ceskyhokej.cz/player/2'],
            externalIdPatternRegExp: 'ceskyhokej.cz/player/(\\d+)',
            extractionScript: 'return { name: "John Omacka" }',
            fetchType: FetchType.Puppeteer,
            addToQueue: true,
            paramExtractors: [
              {
                selector: 'select[name="season"]',
                queryParam: 'seasonId',
              },
            ],
          },
        ],
      },
    })

    assert.strictEqual(response.admin.crawlerDomainConfig.update.config.domain, 'ceskyhokej.cz')
    assert.deepStrictEqual(response.admin.crawlerDomainConfig.update.config.relevantUrlPatterns, [
      'relevantUrlPattern1',
      'relevantUrlPattern2',
    ])
    assert.strictEqual(response.admin.crawlerDomainConfig.update.config.parallelism, 15)
    assert.deepStrictEqual(response.admin.crawlerDomainConfig.update.config.urlConfigs, [
      {
        urlPatternRegExp: 'ceskyhokej.cz/player/.*',
        entity: CrawlerEntity.Player,
        initialUrls: ['ceskyhokej.cz/player/1', 'ceskyhokej.cz/player/2'],
        externalIdPatternRegExp: 'ceskyhokej.cz/player/(\\d+)',
        extractionScript: 'return { name: "John Omacka" }',
        fetchType: FetchType.Puppeteer,
        addToQueue: true,
        paramExtractors: [
          {
            selector: 'select[name="season"]',
            queryParam: 'seasonId',
          },
        ],
      },
    ])

    // partial update
    const partialUpdateResponse = await client(admin.tokens.accessToken).updateCrawlerDomainConfig({
      id: createResponse.admin.crawlerDomainConfig.create.config.id,
      input: {
        parallelism: 20,
        urlConfigs: [
          {
            urlPatternRegExp: 'ceskyhokej.cz/game/.*',
            entity: CrawlerEntity.Game,
            initialUrls: ['ceskyhokej.cz/game/1', 'ceskyhokej.cz/game/2'],
            externalIdPatternRegExp: 'ceskyhokej.cz/game/(\\d+)',
            extractionScript: 'return { name: "Sparta vs Liberec" }',
            fetchType: FetchType.Puppeteer,
            addToQueue: true,
            paramExtractors: [
              {
                selector: 'select[name="season"]',
                queryParam: 'seasonId',
              },
            ],
          },
          {
            urlPatternRegExp: 'ceskyhokej.cz/game/.*',
            entity: CrawlerEntity.League,
            initialUrls: ['ceskyhokej.cz/game/2', 'ceskyhokej.cz/game/2'],
            externalIdPatternRegExp: 'ceskyhokej.cz/game/(\\d+)',
            extractionScript: 'return { name: "Sparta vs Liberec" }',
            fetchType: FetchType.Puppeteer,
            addToQueue: true,
            paramExtractors: [
              {
                selector: 'select[name="season"]',
                queryParam: 'seasonId',
              },
            ],
          },
        ],
      },
    })

    assert.strictEqual(
      partialUpdateResponse.admin.crawlerDomainConfig.update.config.parallelism,
      20
    )
    assert.deepStrictEqual(
      partialUpdateResponse.admin.crawlerDomainConfig.update.config.urlConfigs,
      [
        {
          urlPatternRegExp: 'ceskyhokej.cz/game/.*',
          entity: CrawlerEntity.Game,
          initialUrls: ['ceskyhokej.cz/game/1', 'ceskyhokej.cz/game/2'],
          externalIdPatternRegExp: 'ceskyhokej.cz/game/(\\d+)',
          extractionScript: 'return { name: "Sparta vs Liberec" }',
          fetchType: FetchType.Puppeteer,
          addToQueue: true,
          paramExtractors: [
            {
              selector: 'select[name="season"]',
              queryParam: 'seasonId',
            },
          ],
        },

        {
          urlPatternRegExp: 'ceskyhokej.cz/game/.*',
          entity: CrawlerEntity.League,
          initialUrls: ['ceskyhokej.cz/game/2', 'ceskyhokej.cz/game/2'],
          externalIdPatternRegExp: 'ceskyhokej.cz/game/(\\d+)',
          extractionScript: 'return { name: "Sparta vs Liberec" }',
          fetchType: FetchType.Puppeteer,
          addToQueue: true,
          paramExtractors: [
            {
              selector: 'select[name="season"]',
              queryParam: 'seasonId',
            },
          ],
        },
      ]
    )
  })

  it('should get crawler domain config', async () => {
    await CrawlerDomainConfigModel.deleteMany({})

    const admin = await generateRandomAdminUser()

    const createResponse = await client(admin.tokens.accessToken).createCrawlerDomainConfig({
      input: {
        domain: 'example.com',
        urlConfigs: [
          {
            urlPatternRegExp: 'example.com/player/.*',
            entity: CrawlerEntity.Player,
            initialUrls: ['example.com/player/1', 'example.com/player/2'],
            externalIdPatternRegExp: 'example.com/player/(\\d+)',
            extractionScript: 'return { name: "John Doe" }',
            fetchType: FetchType.Axios,
            addToQueue: true,
            paramExtractors: [
              {
                selector: 'select[name="season"]',
                queryParam: 'seasonId',
              },
            ],
          },
        ],
      },
    })

    const response = await client(admin.tokens.accessToken).getCrawlerDomainConfig({
      id: createResponse.admin.crawlerDomainConfig.create.config.id,
    })

    assert.strictEqual(response.admin.crawlerDomainConfig.get!.domain, 'example.com')
    assert.deepStrictEqual(response.admin.crawlerDomainConfig.get!.urlConfigs, [
      {
        urlPatternRegExp: 'example.com/player/.*',
        entity: CrawlerEntity.Player,
        initialUrls: ['example.com/player/1', 'example.com/player/2'],
        externalIdPatternRegExp: 'example.com/player/(\\d+)',
        extractionScript: 'return { name: "John Doe" }',
        fetchType: FetchType.Axios,
        addToQueue: true,
        paramExtractors: [
          {
            selector: 'select[name="season"]',
            queryParam: 'seasonId',
          },
        ],
      },
    ])
  })

  it('should list crawler domain configs', async () => {
    await CrawlerDomainConfigModel.deleteMany({})

    const admin = await generateRandomAdminUser()

    for (let i = 0; i < 10; i++) {
      await client(admin.tokens.accessToken).createCrawlerDomainConfig({
        input: {
          domain: `example-${i + 1}.com`,
          urlConfigs: [
            {
              urlPatternRegExp: 'example.com/player/.*',
              entity: CrawlerEntity.Player,
              initialUrls: ['example.com/player/1', 'example.com/player/2'],
              externalIdPatternRegExp: 'example.com/player/(\\d+)',
              extractionScript: 'return { name: "John Doe" }',
              fetchType: FetchType.Axios,
              addToQueue: true,
              paramExtractors: [
                {
                  selector: 'select[name="season"]',
                  queryParam: 'seasonId',
                },
              ],
            },
          ],
        },
      })
    }

    const response = await client(admin.tokens.accessToken).getCrawlerDomainConfigs({
      pagination: {
        first: 5,
      },
    })

    assert.strictEqual(response.admin.crawlerDomainConfig.list!.pageInfo.hasNextPage, true)
    assert.strictEqual(response.admin.crawlerDomainConfig.list!.pageInfo.totalCount, 10)

    assert.strictEqual(response.admin.crawlerDomainConfig.list!.edges.length, 5)
    assert.strictEqual(
      response.admin.crawlerDomainConfig.list!.edges[0].node.domain,
      'example-10.com'
    )
  })
})
