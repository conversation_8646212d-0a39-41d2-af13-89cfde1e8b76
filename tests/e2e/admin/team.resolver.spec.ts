import assert from 'assert'
import sinon from 'sinon'

import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { client } from '../../utils/graphql'
import { generateRandomAdminUser, generateRandomOrganization } from '../../utils/users'
import { s3ClientStub } from '..'

describe('AdminTeamMutations - removeLogo', () => {
  beforeEach(async () => {
    await OrganizationModel.deleteMany({})
  })

  it('should remove team logo and return success', async () => {
    const adminUser = await generateRandomAdminUser()
    const randomOrganization = await generateRandomOrganization(
      'Test remove team logo organization'
    )

    const team = await client(adminUser.tokens.accessToken).createTeam({
      input: {
        name: 'Team with logo to remove',
        country: 'US',
        shortName: 'team-remove-logo',
        organization: randomOrganization.id,
      },
    })

    await TeamModel.findByIdAndUpdate(team.createTeam.team?.id, {
      logo: 'some/logo/path.png',
      ogLogo: 'some/logo/og.png',
      logoUnconfirmed: 'some/logo/unconfirmed.png',
    })

    s3ClientStub.send.resetHistory()

    const response = await client(adminUser.tokens.accessToken).removeTeamLogo({
      removeTeamLogoId: team.createTeam.team?.id,
    })

    assert.strictEqual(response.admin.team.removeTeamLogo?.success, true)

    sinon.assert.callCount(s3ClientStub.send, 3)

    sinon.assert.calledWith(
      s3ClientStub.send.getCall(0),
      sinon.match.has('input', sinon.match.has('Key', 'some/logo/path.png'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(0),
      sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
    )

    sinon.assert.calledWith(
      s3ClientStub.send.getCall(1),
      sinon.match.has('input', sinon.match.has('Key', 'some/logo/og.png'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(1),
      sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(2),
      sinon.match.has('input', sinon.match.has('Key', 'some/logo/unconfirmed.png'))
    )
    sinon.assert.calledWith(
      s3ClientStub.send.getCall(2),
      sinon.match.has('input', sinon.match.has('Bucket', 'graet-staging-assets'))
    )

    const updated = await TeamModel.findById(team.createTeam.team?.id)
    assert.strictEqual(updated?.logo, null)
    assert.strictEqual(updated?.ogLogo, null)
    assert.strictEqual(updated?.logoUnconfirmed, null)
  })

  it('should return error if team not found', async () => {
    const adminUser = await generateRandomAdminUser()
    const randomOrganization = await generateRandomOrganization(
      'Test remove team logo organization'
    )

    const team = await client(adminUser.tokens.accessToken).createTeam({
      input: {
        name: 'Team with logo to remove not found',
        country: 'US',
        shortName: 'team-remove-logo-not-found',
        organization: randomOrganization.id,
      },
    })

    const stubById = sinon.stub(TeamModel, 'findById').resolves(null)

    const response = await client(adminUser.tokens.accessToken).removeTeamLogo({
      removeTeamLogoId: team.createTeam.team?.id,
    })

    assert.strictEqual(response.admin.team.removeTeamLogo?.success, false)
    assert.strictEqual(
      response.admin?.team?.removeTeamLogo?.errors?.[0]?.message ?? 'Unknown error',
      'Team not found'
    )
    stubById.restore()
  })

  it('should return error if db save fail', async () => {
    const adminUser = await generateRandomAdminUser()
    const randomOrganization = await generateRandomOrganization(
      'Test remove team logo organization'
    )

    const team = await client(adminUser.tokens.accessToken).createTeam({
      input: {
        name: 'Team with logo to remove db fail',
        country: 'US',
        shortName: 'team-remove-logo',
        organization: randomOrganization.id,
      },
    })

    const stubById = sinon
      .stub(TeamModel, 'updateOne')
      .throws(new Error('Expected error to be thrown'))

    try {
      await client(adminUser.tokens.accessToken).removeTeamLogo({
        removeTeamLogoId: team.createTeam.team?.id,
      })
      assert.fail('Expected error to be thrown')
    } catch (err) {
      assert.strictEqual(err.response?.errors?.[0]?.message, 'Failed to remove team logo')
    }

    stubById.restore()
  })
})
