import assert from 'assert'

import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { UserInfoModel } from '../../../src/modules/stats/models/userInfo'
import { client } from '../../utils/graphql'
import { generateDiscoverUrl } from '../../utils/stats'
import { generateRandomAdminUser } from '../../utils/users'

describe('AdminUserInfoQueries', () => {
  describe('userInfos query', () => {
    it('should return paginated userInfos matching search query', async () => {
      await CrawlerDomainConfigModel.deleteMany({})

      const admin = await generateRandomAdminUser()

      const discoverUrl = await generateDiscoverUrl()
      await UserInfoModel.create({
        name: 'Test UserInfo Person',
        externalId: 'test-external-id',
        crawlerDomainConfig: discoverUrl.crawlerDomainConfig,
      })

      await new Promise((resolve) => setTimeout(resolve, 1000))

      const response = await client(admin.tokens.accessToken).userInfo({
        filter: { searchQuery: 'UserInfo' },
        pagination: {
          first: 10,
        },
      })

      assert.strictEqual(
        response.admin.userInfo.userInfos.edges[0].node.name,
        'Test UserInfo Person'
      )
      assert.strictEqual(
        response.admin.userInfo.userInfos.edges[0].node.crawlerDomainConfig?.domain,
        'example.com'
      )
    })
  })
})
