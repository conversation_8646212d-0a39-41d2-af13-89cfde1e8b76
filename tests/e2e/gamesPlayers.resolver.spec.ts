import assert from 'assert'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../src/modules/stats/models/game/game'
import { RosterPosition } from '../../src/modules/stats/models/game/rosterPlayer'
import { LeagueModel } from '../../src/modules/stats/models/league'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserModel } from '../../src/modules/user/models'
import { client } from '../utils/graphql'
import { generateDiscoverUrl, generateRandomGameStats } from '../utils/stats'
import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from '../utils/users'

describe('GamesPlayers resolver', () => {
  beforeEach(async () => {
    await LeagueModel.deleteMany({})
    await TeamModel.deleteMany({})
    await GameModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
    await UserModel.deleteMany({})
  })

  afterEach(() => {
    sinon.restore()
  })

  it('should return null when an error occurs in the resolver', async () => {
    // Stub find to throw an error
    sinon.stub(GameModel, 'find').throws(new Error('Test error'))

    const response = await client().gamesPlayers({
      input: {
        gameIds: [new Types.ObjectId().toString()],
        addGamePerformanceLikes: false,
      },
    })

    // Expect null response
    assert.strictEqual(response.gamesPlayers, null)
  })

  it('should return games with players when valid gameIds are provided', async () => {
    // Create test data
    const discoveredUrl = await generateDiscoverUrl()
    const organization = await generateRandomOrganization('Test Organization')
    const league = await generateRandomLeague('Test League')
    const homeTeam = await generateRandomTeam({
      name: 'Home Team',
      organization: organization._id.toString(),
      leagues: [league._id.toString()],
    })
    const awayTeam = await generateRandomTeam({
      name: 'Away Team',
      organization: organization._id.toString(),
      leagues: [league._id.toString()],
    })

    await generateRandomUser({
      externalConnections: [
        { externalId: 'home-player-1', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })
    await generateRandomUser({
      externalConnections: [
        { externalId: 'home-player-2', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })
    await generateRandomUser({
      externalConnections: [
        { externalId: 'away-player-1', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })
    await generateRandomUser({
      externalConnections: [
        { externalId: 'away-player-2', crawlerDomainConfig: discoveredUrl.crawlerDomainConfig },
      ],
    })

    // Create first game stats with players
    await generateRandomGameStats({
      nameSuffix: 'Test1',
      gameStatsPayload: {
        date: new Date(),
        externalId: '1',
        discoveredUrl: discoveredUrl._id,
        league: league._id,
        homeTeam: homeTeam._id,
        awayTeam: awayTeam._id,
        externalInfo: {
          homeTeamName: 'Home Team',
          awayTeamName: 'Away Team',
          leagueName: 'Test League',
        },
        homeTeamRoster: [
          {
            externalInfo: {
              playerName: 'Home Player 1',
              playerExternalId: 'home-player-1',
            },
            goals: 1,
            assists: 2,
            points: 3,
            pim: 4,
            plusMinus: 1,
            position: RosterPosition.Forward,
            jerseyNumber: 10,
            player: null,
          },
          {
            externalInfo: {
              playerName: 'Home Player 2',
              playerExternalId: 'home-player-2',
            },
            goals: 0,
            assists: 1,
            points: 1,
            pim: 2,
            plusMinus: -1,
            position: RosterPosition.Defenseman,
            jerseyNumber: 20,
            player: null,
          },
        ],
        awayTeamRoster: [
          {
            externalInfo: {
              playerName: 'Away Player 1',
              playerExternalId: 'away-player-1',
            },
            goals: 2,
            assists: 1,
            points: 3,
            pim: 0,
            plusMinus: 2,
            position: RosterPosition.Forward,
            jerseyNumber: 11,
            player: null,
          },
          {
            externalInfo: {
              playerName: 'Away Player 2',
              playerExternalId: 'away-player-2',
            },
            goals: 0,
            assists: 0,
            points: 0,
            pim: 6,
            plusMinus: -2,
            position: RosterPosition.Goaltender,
            jerseyNumber: 30,
            saves: 25,
            goalsAgainst: 3,
            svp: 0.893,
            player: null,
          },
        ],
      },
    })

    // Get the created game
    const gameStats = await GameModel.findOne({ externalId: '1' })
    assert.ok(gameStats, 'Game stats should exist')

    // Create a second game to test multiple games
    await generateRandomGameStats({
      nameSuffix: 'Test2',
      gameStatsPayload: {
        date: new Date(),
        externalId: '2',
        discoveredUrl: discoveredUrl._id,
        league: league._id,
        homeTeam: awayTeam._id, // Swap teams for variety
        awayTeam: homeTeam._id,
        externalInfo: {
          homeTeamName: 'Away Team', // Swapped
          awayTeamName: 'Home Team', // Swapped
          leagueName: 'Test League',
        },
        homeTeamRoster: [
          {
            externalInfo: {
              playerName: 'Away Player 1',
              playerExternalId: 'away-player-1',
            },
            goals: 0,
            assists: 0,
            points: 0,
            pim: 2,
            plusMinus: -1,
            position: RosterPosition.Forward,
            jerseyNumber: 11,
            player: null,
          },
        ],
        awayTeamRoster: [
          {
            externalInfo: {
              playerName: 'Home Player 1',
              playerExternalId: 'home-player-1',
            },
            goals: 1,
            assists: 0,
            points: 1,
            pim: 0,
            plusMinus: 1,
            position: RosterPosition.Forward,
            jerseyNumber: 10,
            player: null,
          },
        ],
      },
    })

    // Get the second created game
    const gameStats2 = await GameModel.findOne({ externalId: '2' })
    assert.ok(gameStats2, 'Game stats 2 should exist')

    // Query for a single game
    const response = await client().gamesPlayers({
      input: {
        gameIds: [gameStats._id.toString()],
        addGamePerformanceLikes: false,
      },
    })

    // Assertions for single game
    assert.ok(response.gamesPlayers)
    assert.ok(response.gamesPlayers.games)
    assert.strictEqual(response.gamesPlayers.games.length, 1)
    assert.strictEqual(response.gamesPlayers.games[0].id, gameStats._id.toString())

    // Check home team roster
    assert.ok(response.gamesPlayers.games[0].homeTeamRoster)
    assert.strictEqual(response.gamesPlayers.games[0].homeTeamRoster.length, 2)
    assert.strictEqual(
      response.gamesPlayers.games[0].homeTeamRoster[0].externalInfo.playerExternalId,
      'home-player-1'
    )
    assert.ok(response.gamesPlayers.games[0].homeTeamRoster[0].player)
    // Just check that the player ID exists, not its specific value
    assert.ok(response.gamesPlayers.games[0].homeTeamRoster[0].player.id)
    assert.strictEqual(response.gamesPlayers.games[0].homeTeamRoster[0].goals, 1)
    assert.strictEqual(response.gamesPlayers.games[0].homeTeamRoster[0].assists, 2)

    // Check away team roster
    assert.ok(response.gamesPlayers.games[0].awayTeamRoster)
    assert.strictEqual(response.gamesPlayers.games[0].awayTeamRoster.length, 2)
    assert.strictEqual(
      response.gamesPlayers.games[0].awayTeamRoster[1].externalInfo.playerExternalId,
      'away-player-2'
    )
    assert.ok(response.gamesPlayers.games[0].awayTeamRoster[1].player)
    // Just check that the player name exists, not its specific value
    assert.ok(response.gamesPlayers.games[0].awayTeamRoster[1].player.name)
    assert.strictEqual(
      response.gamesPlayers.games[0].awayTeamRoster[1].position,
      RosterPosition.Goaltender
    )
    assert.strictEqual(response.gamesPlayers.games[0].awayTeamRoster[1].svp, 0.893)

    // Query for multiple games
    const multipleGamesResponse = await client().gamesPlayers({
      input: {
        gameIds: [gameStats._id.toString(), gameStats2._id.toString()],
        addGamePerformanceLikes: false,
      },
    })

    // Assertions for multiple games
    assert.ok(multipleGamesResponse.gamesPlayers)
    assert.ok(multipleGamesResponse.gamesPlayers.games)
    assert.strictEqual(multipleGamesResponse.gamesPlayers.games.length, 2)

    // Verify the second game data
    const secondGame = multipleGamesResponse.gamesPlayers.games.find(
      (game) => game.id === gameStats2._id.toString()
    )
    assert.ok(secondGame)
    assert.ok(secondGame.homeTeamRoster)
    assert.strictEqual(secondGame.homeTeamRoster.length, 1)
    assert.strictEqual(secondGame.homeTeamRoster[0].externalInfo.playerExternalId, 'away-player-1')
    assert.ok(secondGame.homeTeamRoster[0].player)
    // Just check that the player name exists, not its specific value
    assert.ok(secondGame.homeTeamRoster[0].player.name)
    assert.ok(secondGame.awayTeamRoster)
    assert.strictEqual(secondGame.awayTeamRoster.length, 1)
    assert.strictEqual(secondGame.awayTeamRoster[0].externalInfo.playerExternalId, 'home-player-1')
    assert.ok(secondGame.awayTeamRoster[0].player)
    // Just check that the player name exists, not its specific value
    assert.ok(secondGame.awayTeamRoster[0].player.name)
  })

  it('should return empty rosters when no players match the external IDs', async () => {
    // Create test data
    const discoveredUrl = await generateDiscoverUrl()
    const organization = await generateRandomOrganization('Test Organization 2')
    const league = await generateRandomLeague('Test League 2')
    const homeTeam = await generateRandomTeam({
      name: 'Home Team 2',
      organization: organization._id.toString(),
      leagues: [league._id.toString()],
    })
    const awayTeam = await generateRandomTeam({
      name: 'Away Team 2',
      organization: organization._id.toString(),
      leagues: [league._id.toString()],
    })

    // Create game stats with players that don't match any users
    await generateRandomGameStats({
      nameSuffix: 'Test3',
      gameStatsPayload: {
        date: new Date(),
        externalId: '3',
        discoveredUrl: discoveredUrl._id,
        league: league._id,
        homeTeam: homeTeam._id,
        awayTeam: awayTeam._id,
        externalInfo: {
          homeTeamName: 'Home Team 2',
          awayTeamName: 'Away Team 2',
          leagueName: 'Test League 2',
        },
        homeTeamRoster: [
          {
            externalInfo: {
              playerName: 'Non-existent Player 1',
              playerExternalId: 'non-existent-1',
            },
            goals: 1,
            assists: 2,
            points: 3,
            position: RosterPosition.Forward,
            player: null,
          },
        ],
        awayTeamRoster: [
          {
            externalInfo: {
              playerName: 'Non-existent Player 2',
              playerExternalId: 'non-existent-2',
            },
            goals: 2,
            assists: 1,
            points: 3,
            position: RosterPosition.Defenseman,
            player: null,
          },
        ],
      },
    })

    // Get the created game
    const gameStats = await GameModel.findOne({ externalId: '3' })
    assert.ok(gameStats, 'Game stats should exist')

    // Query for the game
    const response = await client().gamesPlayers({
      input: {
        gameIds: [gameStats._id.toString()],
        addGamePerformanceLikes: false,
      },
    })

    // Assertions
    assert.ok(response.gamesPlayers)
    assert.ok(response.gamesPlayers.games)
    assert.strictEqual(response.gamesPlayers.games.length, 1)
    assert.strictEqual(response.gamesPlayers.games[0].id, gameStats._id.toString())

    // Check home team roster - players should exist but without linked user data
    assert.ok(response.gamesPlayers.games[0].homeTeamRoster)
    assert.strictEqual(response.gamesPlayers.games[0].homeTeamRoster.length, 1)
    assert.strictEqual(
      response.gamesPlayers.games[0].homeTeamRoster[0].externalInfo.playerExternalId,
      'non-existent-1'
    )
    assert.strictEqual(response.gamesPlayers.games[0].homeTeamRoster[0].player, null)

    // Check away team roster - players should exist but without linked user data
    assert.ok(response.gamesPlayers.games[0].awayTeamRoster)
    assert.strictEqual(response.gamesPlayers.games[0].awayTeamRoster.length, 1)
    assert.strictEqual(
      response.gamesPlayers.games[0].awayTeamRoster[0].externalInfo.playerExternalId,
      'non-existent-2'
    )
    assert.strictEqual(response.gamesPlayers.games[0].awayTeamRoster[0].player, null)
  })

  it('should return empty array when no games match the provided IDs', async () => {
    const nonExistentId = new Types.ObjectId().toString()

    const response = await client().gamesPlayers({
      input: {
        gameIds: [nonExistentId],
        addGamePerformanceLikes: false,
      },
    })

    // Expect empty games array
    assert.ok(response.gamesPlayers)
    assert.ok(response.gamesPlayers.games)
    assert.strictEqual(response.gamesPlayers.games.length, 0)
  })
})
