import { deepStrictEqual } from 'assert'
import config from 'config'
import sinon from 'sinon'

import { UserModel } from '../../src/modules/user/models'
import { Agency } from '../../src/modules/user/models/agency'
import { GraetChatUser } from '../../src/modules/user/types/streamChat'
import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'

import {
  streamChatcreateToken,
  streamChatupsertUsers,
  streamFeedCreateToken,
  streamFeedMockToken,
} from '.'

describe('User resolver, GetStream, Chat', () => {
  after(async () => {
    await UserModel.deleteMany({})
  })
  it('creates chat users', async () => {
    const users = [await generateRandomUser(), await generateRandomUser()]

    const userIds = users.map(({ user: { _id: id } }) => id.toString())

    const user = await generateRandomUser()

    const response = await client(user.tokens.accessToken).createChatUsers({ input: { userIds } })

    sinon.assert.calledWith(
      streamChatupsertUsers as sinon.SinonSpy,
      users.map(
        ({
          user: { _id, name, firstname, lastname, slug, email, avatar: image, role, subrole, bio },
        }) =>
          ({
            id: _id.toString(),
            name: name || email,
            firstname,
            lastname,
            slug,
            image: !image
              ? null
              : !image?.startsWith(config.assetsUrl)
                ? `${config.assetsUrl}${image}`
                : image,
            graet_role: role,
            graet_sub_role: subrole,
            ...(bio?.agency ? { graet_agent_agency_name: (bio.agency as Agency)?.name } : {}),
          }) as GraetChatUser
      )
    )

    deepStrictEqual({ createChatUsers: true }, response)
  })
  it('retrieves chat user token', async () => {
    const user = await generateRandomUser()

    const response = await client(user.tokens.accessToken).createChatUserToken()

    sinon.assert.calledWith(streamChatcreateToken as sinon.SinonSpy, user.user._id.toString())

    deepStrictEqual({ createChatUserToken: streamFeedMockToken }, response)
  })
  it('retrieves streamFeed user token', async () => {
    const user = await generateRandomUser()

    const response = await client(user.tokens.accessToken).createStreamFeedUserToken()

    sinon.assert.calledWith(streamFeedCreateToken as sinon.SinonSpy, user.user._id.toString())

    deepStrictEqual({ createStreamFeedUserToken: streamFeedMockToken }, response)
  })
})
