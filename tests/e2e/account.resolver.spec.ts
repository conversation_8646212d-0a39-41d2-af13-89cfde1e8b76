import assert from 'assert'
import config from 'config'
import sinon from 'sinon'

import { UserModel } from '../../src/modules/user/models'
import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'

import { amplitudeIdentifyStub, slackClientStub, stripeClientStub } from '.'

describe('Account resolver', () => {
  describe('Create stripe account mutation', () => {
    it('should return error if user not found', async () => {
      const user = await generateRandomUser()
      const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      try {
        await client(user.tokens.accessToken).createStripeAccount({
          input: {
            firstname: null,
            lastname: null,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User not found')
      }
      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it('should return error if user already has a Stripe account', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      const response = await client(user.tokens.accessToken).createStripeAccount({
        input: {
          firstname: null,
          lastname: null,
        },
      })
      assert.strictEqual(response.createStripeAccount.errors?.length, 1)
      assert.strictEqual(response.createStripeAccount.errors[0].message, 'Account already exists')
    })

    it('should return successful response for valid account creation', async () => {
      stripeClientStub.accounts.create.resolves({
        id: 'acct_1',
        individual: {
          first_name: 'Stripe',
          last_name: 'Miocic',
          dob: {
            day: 1,
            month: 1,
            year: 2000,
          },
          address: {
            city: 'Test City',
            line1: '123 Test St',
            postal_code: '12345',
            state: 'Test State',
          },
        },
        country: 'US',
        default_currency: 'usd',
      })

      const user = await generateRandomUser({ firstname: 'Stripe', lastname: 'Miocic' })
      const response = await client(user.tokens.accessToken).createStripeAccount({
        input: {
          firstname: 'Stripe',
          lastname: 'Miocic',
          email: user.user.email,
          dob: new Date('2000-01-01'),
          address: {
            city: 'Jaromer',
            line1: 'Josefovska 1',
            postalCode: '12345',
            state: 'Czech Republic',
          },
          currency: 'EUR',
          phone: '+************',
          country: 'CZ',
          ssnLast4: '1234',
        },
      })

      assert.strictEqual(stripeClientStub.accounts.create.calledOnce, true)
      sinon.assert.calledWith(stripeClientStub.accounts.create, {
        type: 'custom',
        country: 'CZ',
        email: user.user.email,
        business_type: 'individual',
        business_profile: {
          mcc: '7299',
          url: 'https://www.graet.com/' + user.user.slug,
          product_description: 'Graet - boost player.',
        },
        individual: {
          email: user.user.email,
          first_name: 'Stripe',
          last_name: 'Miocic',
          address: {
            country: 'CZ',
            city: 'Jaromer',
            line1: 'Josefovska 1',
            postal_code: '12345',
            state: 'Czech Republic',
          },
          dob: {
            day: 1,
            month: 1,
            year: 2000,
          },
          phone: '+************',
        },
        tos_acceptance: {
          date: Math.floor(Date.now() / 1000),
          ip: '127.0.0.1',
          service_agreement: 'full',
          user_agent: 'node-fetch/1.0 (+https://github.com/bitinn/node-fetch)',
        },
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
          link_payments: { requested: true },
        },
        default_currency: 'EUR',
        settings: {
          payouts: {
            schedule: {
              interval: 'weekly',
              weekly_anchor: 'monday',
            },
          },
          payments: {
            statement_descriptor: `GRAET ${user.user.name}`.substring(0, 22),
          },
        },
      })

      sinon.assert.calledOnce(slackClientStub.chat.postMessage)
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `New stripe registration with name: ${user.user.name}, <http://admin.${config.domain}/dashboard/users/form/?slug=${user.user.slug}|admin panel>`
          )
        )
      )

      sinon.assert.calledOnce(stripeClientStub.accounts.create)

      assert.strictEqual(response.createStripeAccount.errors.length, 0)
      assert.strictEqual(response.createStripeAccount.account?.id, 'acct_1')
      assert.strictEqual(response.createStripeAccount.account.firstname, 'Stripe')
      assert.strictEqual(response.createStripeAccount.account?.lastname, 'Miocic')
      assert.strictEqual(response.createStripeAccount.account?.country, 'US')
    })

    it('should return error if account creation fails', async () => {
      stripeClientStub.accounts.create.throws('Error')

      const user = await generateRandomUser()

      try {
        await client(user.tokens.accessToken).createStripeAccount({
          input: {
            firstname: 'Stripe',
            lastname: 'Miocic',
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to create account. Please try again later.'
        )
      }
    })

    it('should return error if Stripe throws invalid request error with iOS device', async () => {
      const user = await generateRandomUser()

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.create.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/80 CFNetwork/1410.1 Darwin/22.6.0',
      }).createStripeAccount({
        input: {
          firstname: 'Stripe',
          lastname: 'Miocic',
        },
      })

      assert.strictEqual(response.createStripeAccount.errors?.length, 1)
      assert.strictEqual(response.createStripeAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.createStripeAccount.errors[0].path, 'stripeAccountId')
    })

    it('should return error if Stripe throws invalid request error with Android OS', async () => {
      const user = await generateRandomUser()

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.create.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/80 CFNetwork/1410.1',
        'x-build-number': '80',
      }).createStripeAccount({
        input: {
          firstname: 'Stripe',
          lastname: 'Miocic',
        },
      })

      assert.strictEqual(response.createStripeAccount.errors?.length, 1)
      assert.strictEqual(response.createStripeAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.createStripeAccount.errors[0].path, 'stripeAccountId')
    })

    it('should return error if Stripe throws invalid request error with specific mobile client version', async () => {
      const user = await generateRandomUser()

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.create.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/1 CFNetwork/1410.1',
        'x-build-number': '80',
      }).createStripeAccount({
        input: {
          firstname: 'Stripe',
          lastname: 'Miocic',
        },
      })

      assert.strictEqual(response.createStripeAccount.errors?.length, 1)
      assert.strictEqual(response.createStripeAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.createStripeAccount.errors[0].path, 'stripeAccountId')
    })
  })

  describe('Update Stripe Account mutation', async () => {
    it('should return error if user not found', async () => {
      const user = await generateRandomUser()
      const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      try {
        await client(user.tokens.accessToken).updateStripeAccount({
          input: {
            firstname: null,
            lastname: null,
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User not found')
      }
      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it("should return error if user doesn't have stripe account", async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).updateStripeAccount({
        input: {
          firstname: 'Dona',
          lastname: 'Without Stripe',
        },
      })
      assert.strictEqual(response.updateStripeAccount.errors?.length, 1)
      assert.strictEqual(response.updateStripeAccount.errors[0].message, 'Account does not exist')
      assert.strictEqual(response.updateStripeAccount.errors[0].path, 'stripeAccountId')
    })

    it('should return response if account have enabled payments', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )
      stripeClientStub.accounts.update.resolves({
        id: 'acct_2',
        individual: {
          first_name: 'Active',
          last_name: 'Payments',
        },
        country: 'CZ',
        default_currency: 'EUR',
        payouts_enabled: true,
        payments_enabled: true,
        capabilities: {
          card_payments: 'active',
        },
      })

      const response = await client(user.tokens.accessToken).updateStripeAccount({
        input: {
          firstname: user.user.firstname,
          lastname: user.user.lastname,
          email: user.user.email,
          dob: new Date('1990-01-01'),
          address: {
            city: 'Prague',
            line1: 'Na Prikope 123',
            postalCode: '11000',
            state: 'Czech Republic',
          },
          currency: 'CZK',
          phone: '+420*********',
          ssnLast4: '6789',
          jobTitle: 'manager',
        },
      })

      assert.strictEqual(stripeClientStub.accounts.update.calledOnce, true)
      sinon.assert.calledWith(stripeClientStub.accounts.update, 'valid-stripe-account-id', {
        individual: {
          ssn_last_4: '6789',
          email: user.user.email,
          first_name: user.user.firstname,
          last_name: user.user.lastname,
          relationship: {
            title: 'manager',
          },
          address: {
            city: 'Prague',
            line1: 'Na Prikope 123',
            postal_code: '11000',
            state: 'Czech Republic',
          },
          dob: {
            day: 1,
            month: 1,
            year: 1990,
          },
          phone: '+420*********',
        },
        email: user.user.email,
        default_currency: 'CZK',
        settings: {
          payouts: {
            schedule: {
              interval: 'weekly',
              weekly_anchor: 'monday',
            },
          },
          payments: {
            statement_descriptor: `GRAET ${user.user.name}`.substring(0, 22),
          },
        },
      })

      assert.strictEqual(response.updateStripeAccount.errors.length, 0)
      assert.strictEqual(response.updateStripeAccount.account?.id, 'acct_2')
      assert.strictEqual(response.updateStripeAccount.account.firstname, 'Active')
      assert.strictEqual(response.updateStripeAccount.account?.lastname, 'Payments')
      assert.strictEqual(response.updateStripeAccount.account?.country, 'CZ')
      assert.strictEqual(response.updateStripeAccount.account?.currency, 'EUR')
      assert.strictEqual(response.updateStripeAccount.account?.paymentsEnabled, true)

      sinon.assert.calledOnce(amplitudeIdentifyStub)
      sinon.assert.calledWith(
        amplitudeIdentifyStub,
        sinon.match.has(
          '_properties',
          sinon.match.has(
            '$set',
            sinon.match({
              stripe_verified: true,
            })
          )
        ),
        sinon.match({ user_id: user.user._id.toString() })
      )
    })

    it('should return response if account have disabled payments', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      stripeClientStub.accounts.update.resolves({
        id: 'acct_3',
        individual: {
          first_name: 'Inactive',
          last_name: 'Payments',
        },
        country: 'CZ',
        default_currency: 'EUR',
        payouts_enabled: true,
        payments_enabled: false,
        capabilities: {
          card_payments: 'inactive',
        },
      })

      const response = await client(user.tokens.accessToken).updateStripeAccount({
        input: {
          firstname: user.user.name,
          lastname: user.user.lastname,
          email: user.user.email,
          dob: new Date('1985-05-15'), // Náhodné datum
          address: {
            city: 'Berlin',
            line1: '123 Hauptstraße',
            postalCode: '10115',
            state: 'Germany',
          },
          currency: 'EUR',
          phone: '+49*********0',
          ssnLast4: '1234',
          jobTitle: 'developer',
        },
      })

      assert.strictEqual(stripeClientStub.accounts.update.calledOnce, true)

      sinon.assert.calledWith(stripeClientStub.accounts.update, 'valid-stripe-account-id', {
        individual: {
          ssn_last_4: '1234',
          email: user.user.email,
          first_name: user.user.name,
          last_name: user.user.lastname,
          relationship: {
            title: 'developer',
          },
          address: {
            city: 'Berlin',
            line1: '123 Hauptstraße',
            postal_code: '10115',
            state: 'Germany',
          },
          dob: {
            day: 15,
            month: 5,
            year: 1985,
          },
          phone: '+49*********0',
        },
        email: user.user.email,
        default_currency: 'EUR',
        settings: {
          payouts: {
            schedule: {
              interval: 'weekly',
              weekly_anchor: 'monday',
            },
          },
          payments: {
            statement_descriptor: `GRAET ${user.user.name}`.substring(0, 22),
          },
        },
      })

      assert.strictEqual(response.updateStripeAccount.errors.length, 0)
      assert.strictEqual(response.updateStripeAccount.account?.id, 'acct_3')
      assert.strictEqual(response.updateStripeAccount.account.firstname, 'Inactive')
      assert.strictEqual(response.updateStripeAccount.account?.lastname, 'Payments')
      assert.strictEqual(response.updateStripeAccount.account?.country, 'CZ')
      assert.strictEqual(response.updateStripeAccount.account?.currency, 'EUR')
      assert.strictEqual(response.updateStripeAccount.account?.paymentsEnabled, false)

      sinon.assert.calledOnce(amplitudeIdentifyStub)
      sinon.assert.calledWith(
        amplitudeIdentifyStub,
        sinon.match.has(
          '_properties',
          sinon.match.has(
            '$set',
            sinon.match({
              stripe_verified: false,
            })
          )
        ),
        sinon.match({ user_id: user.user._id.toString() })
      )
    })

    it('should return error if account update fails', async () => {
      const user = await generateRandomUser()

      stripeClientStub.accounts.update.throws('Error')

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      try {
        await client(user.tokens.accessToken).updateStripeAccount({
          input: {
            firstname: 'Kenny',
            lastname: 'Error',
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to update account. Please try again later.'
        )
      }
    })

    it('should return error if Stripe throws invalid request error with iOS device', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.update.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/80 CFNetwork/1410.1 Darwin/22.6.0',
        'x-build-number': '80',
      }).updateStripeAccount({
        input: {
          firstname: user.user.name,
          lastname: user.user.lastname,
        },
      })

      assert.strictEqual(response.updateStripeAccount.errors?.length, 1)
      assert.strictEqual(response.updateStripeAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.updateStripeAccount.errors[0].path, 'stripeAccountId')
    })

    it('should return error if Stripe throws invalid request error with Android OS', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.update.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/80 CFNetwork/1410.1',
        'x-build-number': '80',
      }).updateStripeAccount({
        input: {
          firstname: user.user.name,
          lastname: user.user.lastname,
        },
      })

      assert.strictEqual(response.updateStripeAccount.errors?.length, 1)
      assert.strictEqual(response.updateStripeAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.updateStripeAccount.errors[0].path, 'stripeAccountId')
    })

    it('should return error if Stripe throws invalid request error with specific mobile client version', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.update.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/1 CFNetwork/1410.1',
        'x-build-number': '80',
      }).updateStripeAccount({
        input: {
          firstname: user.user.name,
          lastname: user.user.lastname,
        },
      })

      assert.strictEqual(response.updateStripeAccount.errors?.length, 1)
      assert.strictEqual(response.updateStripeAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.updateStripeAccount.errors[0].path, 'stripeAccountId')
    })
  })

  describe('Create stripe external account mutation', () => {
    it('should return error if user doesnt exist', async () => {
      const user = await generateRandomUser()
      const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      try {
        await client(user.tokens.accessToken).createStripeExternalAccount({
          input: {
            bankAccount: {
              country: 'US',
              currency: 'usd',
              firstname: 'Tomas',
              lastname: 'NewExtAccount',
            },
            routingNumber: '123456',
            accountNumber: '*********',
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User not found')
      }
      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it("should return error if user doesn't have a stripe account", async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).createStripeExternalAccount({
        input: {
          bankAccount: {
            country: 'US',
            currency: 'usd',
            firstname: 'Tomas',
            lastname: 'NewExtAccount',
          },
          routingNumber: '123456',
          accountNumber: '*********',
        },
      })

      assert.strictEqual(response.createStripeExternalAccount.errors.length, 1)
      assert.strictEqual(
        response.createStripeExternalAccount.errors[0].message,
        'Account does not exist'
      )
    })

    it('should return response for account creation', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      stripeClientStub.accounts.createExternalAccount.resolves({
        id: 'external-account-id',
        country: 'US',
        currency: 'USD',
        last4: '6789',
      })

      const response = await client(user.tokens.accessToken).createStripeExternalAccount({
        input: {
          accountNumber: '*********',
          bankAccount: {
            country: 'US',
            currency: 'usd',
            firstname: 'Tomas',
            lastname: 'Testik',
          },
          routingNumber: '123456',
        },
      })

      assert.strictEqual(stripeClientStub.accounts.createExternalAccount.calledOnce, true)
      sinon.assert.calledWith(
        stripeClientStub.accounts.createExternalAccount,
        'valid-stripe-account-id',
        {
          external_account: {
            object: 'bank_account',
            country: 'US',
            currency: 'usd',
            account_holder_name: 'Tomas Testik',
            account_holder_type: 'individual',
            routing_number: '123456',
            account_number: '*********',
          },
        }
      )

      assert.strictEqual(response.createStripeExternalAccount.errors.length, 0)
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.id,
        'external-account-id'
      )
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.bankAccount?.country,
        'US'
      )
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.bankAccount.currency,
        'USD'
      )
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.accountNumber,
        '6789'
      )
    })

    it('should return response for user with existing external account id ', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
          stripeExternalAccountId: 'external-account-id',
        }
      )

      stripeClientStub.accounts.createExternalAccount.resolves({
        id: 'external-account-id',
        country: 'US',
        currency: 'USD',
        last4: '6789',
      })

      const response = await client(user.tokens.accessToken).createStripeExternalAccount({
        input: {
          accountNumber: '*********',
          bankAccount: {
            country: 'US',
            currency: 'usd',
            firstname: 'Tomas',
            lastname: 'Testik',
          },
          routingNumber: '123456',
        },
      })

      assert.strictEqual(stripeClientStub.accounts.createExternalAccount.calledOnce, true)
      sinon.assert.calledWith(
        stripeClientStub.accounts.createExternalAccount,
        'valid-stripe-account-id',
        {
          external_account: {
            object: 'bank_account',
            country: 'US',
            currency: 'usd',
            account_holder_name: 'Tomas Testik',
            account_holder_type: 'individual',
            routing_number: '123456',
            account_number: '*********',
          },
        }
      )

      assert.strictEqual(response.createStripeExternalAccount.errors.length, 0)
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.id,
        'external-account-id'
      )
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.bankAccount?.country,
        'US'
      )
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.bankAccount.currency,
        'USD'
      )
      assert.strictEqual(
        response.createStripeExternalAccount.externalAccount?.accountNumber,
        '6789'
      )
    })

    it('should return error if account creation fails', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      stripeClientStub.accounts.createExternalAccount.throws('Error')

      try {
        await client(user.tokens.accessToken).createStripeExternalAccount({
          input: {
            accountNumber: '*********',
            bankAccount: {
              country: 'US',
              currency: 'usd',
              firstname: 'Tomas',
              lastname: 'Testik',
            },
            routingNumber: '123456',
          },
        })
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to create external account. Please try again later.'
        )
      }
    })

    it('should return error if Stripe throws invalid request error with iOS device', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.createExternalAccount.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/80 CFNetwork/1410.1 Darwin/22.6.0',
        'x-build-number': '80',
      }).createStripeExternalAccount({
        input: {
          accountNumber: '*********',
          bankAccount: {
            country: 'US',
            currency: 'usd',
            firstname: 'Tomas',
            lastname: 'Testik',
          },
          routingNumber: '123456',
        },
      })

      assert.strictEqual(response.createStripeExternalAccount.errors?.length, 1)
      assert.strictEqual(response.createStripeExternalAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.createStripeExternalAccount.errors[0].path, 'stripeAccountId')
    })

    it('should return error if Stripe throws invalid request error with Android OS', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.createExternalAccount.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/80 CFNetwork/1410.1',
        'x-build-number': '80',
      }).createStripeExternalAccount({
        input: {
          accountNumber: '*********',
          bankAccount: {
            country: 'US',
            currency: 'usd',
            firstname: 'Tomas',
            lastname: 'Testik',
          },
          routingNumber: '123456',
        },
      })

      assert.strictEqual(response.createStripeExternalAccount.errors?.length, 1)
      assert.strictEqual(response.createStripeExternalAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.createStripeExternalAccount.errors[0].path, 'stripeAccountId')
    })

    it('should return error if Stripe throws invalid request error with specific mobile client version', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      const stripeError = {
        statusCode: 400,
        message: 'Invalid request',
      }
      stripeClientStub.accounts.createExternalAccount.throws(stripeError)
      const response = await client(user.tokens.accessToken, {
        'user-agent': 'GRAET/1 CFNetwork/1410.1',
      }).createStripeExternalAccount({
        input: {
          accountNumber: '*********',
          bankAccount: {
            country: 'US',
            currency: 'usd',
            firstname: 'Tomas',
            lastname: 'Testik',
          },
          routingNumber: '123456',
        },
      })

      assert.strictEqual(response.createStripeExternalAccount.errors?.length, 1)
      assert.strictEqual(response.createStripeExternalAccount.errors[0].message, 'Invalid request')
      assert.strictEqual(response.createStripeExternalAccount.errors[0].path, 'stripeAccountId')
    })
  })

  describe('Stripe account query', () => {
    it('should return error if user not found', async () => {
      const user = await generateRandomUser()
      const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      try {
        await client(user.tokens.accessToken).stripeAccount()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User not found')
      }
      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it('should return null if user does not have a stripe account', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).stripeAccount()
      assert.strictEqual(response.stripeAccount, null)
    })

    it('should return stripe account if user has one', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      stripeClientStub.accounts.retrieve.resolves({
        id: 'acc-1',
        individual: {
          first_name: 'Test',
          last_name: 'Account',
          dob: {
            day: 1,
            month: 1,
            year: 2000,
          },
          address: {
            city: 'Maple Town',
            line1: '456 Oak Avenue',
            line2: 'Apartment 101',
            postal_code: '67890',
            state: 'Springfield',
          },
          phone: '+*********0',
          email: '<EMAIL>',
          ssn_last_4_provided: true,
          relationship: {
            title: 'manager',
          },
        },
        country: 'US',
        default_currency: 'USD',
        payouts_enabled: true,
        payments_enabled: true,
        capabilities: {
          card_payments: 'active',
        },
      })

      const response = await client(user.tokens.accessToken).stripeAccount()

      const actualDateStr = new Date(response.stripeAccount?.dob).toISOString().slice(0, 10)

      sinon.assert.calledOnce(stripeClientStub.accounts.retrieve)
      sinon.assert.calledWith(stripeClientStub.accounts.retrieve, 'valid-stripe-account-id')

      assert.strictEqual(response.stripeAccount?.id, 'acc-1')
      assert.strictEqual(response.stripeAccount?.firstname, 'Test')
      assert.strictEqual(response.stripeAccount?.lastname, 'Account')
      assert.strictEqual(actualDateStr, '2000-01-01')
      assert.strictEqual(response.stripeAccount.address?.city, 'Maple Town')
      assert.strictEqual(response.stripeAccount.address?.line1, '456 Oak Avenue')
      assert.strictEqual(response.stripeAccount.address?.line2, 'Apartment 101')
      assert.strictEqual(response.stripeAccount.address?.postalCode, '67890')
      assert.strictEqual(response.stripeAccount.address?.state, 'Springfield')
      assert.strictEqual(response.stripeAccount.ssnLast4Provided, true)
      assert.strictEqual(response.stripeAccount.jobTitle, 'manager')
      assert.strictEqual(response.stripeAccount.currency, 'USD')
      assert.strictEqual(response.stripeAccount.phone, '+*********0')
      assert.strictEqual(response.stripeAccount.email, '<EMAIL>')
      assert.strictEqual(response.stripeAccount.country, 'US')
      assert.strictEqual(response.stripeAccount.payoutsEnabled, true)
      assert.strictEqual(response.stripeAccount.paymentsEnabled, true)
    })
  })

  describe('Stripe account external accounts query', () => {
    it("should return null if user doesn't exist", async () => {
      const user = await generateRandomUser()

      const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      const response = await client(user.tokens.accessToken).stripeExternalAccount()
      assert.strictEqual(response.stripeExternalAccount, null)

      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it("should return null if user doesn't have a external stripe account", async () => {
      const user = await generateRandomUser()

      const findByIdStub = sinon.stub(UserModel, 'findById').resolves({
        id: user.user._id,
        stripeExternalAccountId: null,
      })

      const response = await client(user.tokens.accessToken).stripeExternalAccount()
      assert.strictEqual(response.stripeExternalAccount, null)

      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it("should return null if user doesn't have stripe account", async () => {
      const user = await generateRandomUser()

      const findByIdStub = sinon.stub(UserModel, 'findById').resolves({
        id: user.user._id,
        stripeExternalAccountId: 'some-external-account-id',
        stripeAccountId: null,
      })

      const response = await client(user.tokens.accessToken).stripeExternalAccount()
      assert.strictEqual(response.stripeExternalAccount, null)

      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it('should return external account if contains routing number', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeExternalAccountId: 'external-account-id',
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      stripeClientStub.accounts.retrieveExternalAccount.resolves({
        id: 'external-account-id',
        country: 'US',
        currency: 'USD',
        last4: '6789',
        routing_number: '123456',
        status: 'validated',
      })

      const response = await client(user.tokens.accessToken).stripeExternalAccount()

      sinon.assert.calledOnce(stripeClientStub.accounts.retrieveExternalAccount)
      sinon.assert.calledWith(
        stripeClientStub.accounts.retrieveExternalAccount,
        'valid-stripe-account-id',
        'external-account-id'
      )

      assert.strictEqual(response.stripeExternalAccount?.id, 'external-account-id')
      assert.strictEqual(response.stripeExternalAccount?.bankAccount?.country, 'US')
      assert.strictEqual(response.stripeExternalAccount?.bankAccount.currency, 'USD')
      assert.strictEqual(response.stripeExternalAccount?.accountNumber, '6789')
      assert.strictEqual(response.stripeExternalAccount?.routingNumber, '123456')
      assert.strictEqual(response.stripeExternalAccount?.status, 'validated')
    })
  })

  describe('Delete stripe external account mutation', () => {
    it("should return error if user doesn't exist", async () => {
      const user = await generateRandomUser()
      const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

      try {
        await client(user.tokens.accessToken).deleteStripeExternalAccount()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'User not found')
      }
      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it("should throw error if user doesn't have a stripe external account", async () => {
      const user = await generateRandomUser()

      const findByIdStub = sinon.stub(UserModel, 'findById').resolves({
        id: user.user._id,
        stripeExternalAccountId: null,
      })

      try {
        await client(user.tokens.accessToken).deleteStripeExternalAccount()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        const actualMessage = err.response?.errors[0]?.message.match(/message: "([^"]+)"/)[1]
        const expectedMessage = 'External account does not exist'

        assert.strictEqual(actualMessage, expectedMessage)
      }
      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it("should throw error if user doesn't have a stripe account", async () => {
      const user = await generateRandomUser()

      const findByIdStub = sinon.stub(UserModel, 'findById').resolves({
        id: user.user._id,
        stripeExternalAccountId: 'external-account-id',
        stripeAccountId: null,
      })

      try {
        await client(user.tokens.accessToken).deleteStripeExternalAccount()
        assert.fail('Expected error was not thrown')
      } catch (err) {
        const actualMessage = err.response?.errors[0]?.message.match(/message: "([^"]+)"/)[1]
        const expectedMessage = 'External account does not exist'

        assert.strictEqual(actualMessage, expectedMessage)
      }
      sinon.assert.calledOnce(findByIdStub)
      findByIdStub.restore()
    })

    it('should return response for successful account deletion', async () => {
      const user = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: user.user._id,
        },
        {
          stripeExternalAccountId: 'external-account-id',
          stripeAccountId: 'valid-stripe-account-id',
        }
      )

      stripeClientStub.accounts.deleteExternalAccount.resolves({
        id: 'external-account-id',
        deleted: true,
      })

      const response = await client(user.tokens.accessToken).deleteStripeExternalAccount()

      sinon.assert.calledOnce(stripeClientStub.accounts.deleteExternalAccount)
      sinon.assert.calledWith(
        stripeClientStub.accounts.deleteExternalAccount,
        'valid-stripe-account-id',
        'external-account-id'
      )

      assert.strictEqual(response.deleteStripeExternalAccount, true)
    })
  })
})
