import assert from 'assert'
import { Types } from 'mongoose'

import {
  CrawlerDomainConfigModel,
  CrawlerEntity,
} from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../src/modules/stats/models/game/game'
import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserModel } from '../../src/modules/user/models'
import { Role } from '../../src/modules/user/models/user'
import { client } from '../utils/graphql'
import { generateDiscoverUrl } from '../utils/stats'
import { generateRandomOrganization, generateRandomTeam, generateRandomUser } from '../utils/users'

describe('SuggestInvites resolver', () => {
  describe('suggestInvites Query', () => {
    beforeEach(async () => {
      await GameModel.deleteMany({})
      await TeamModel.deleteMany({})
      await OrganizationModel.deleteMany({})
      await UserModel.deleteMany({ role: Role.SHADOW_PLAYER })
      await DiscoveredUrlModel.deleteMany({})
      await CrawlerDomainConfigModel.deleteMany({})
    })

    it('should return empty array if team does not exist', async () => {
      const randomUser = await generateRandomUser()
      const nonExistingTeamId = new Types.ObjectId()

      const response = await client(randomUser.tokens.accessToken).suggestInvites({
        teamId: nonExistingTeamId,
      })

      assert.strictEqual(response.suggestInvites.edges.length, 0)
    })

    it('should return team shadow players first, followed by organization shadow players', async () => {
      const randomUser = await generateRandomUser()

      const discoveredUrl = await generateDiscoverUrl({ entity: CrawlerEntity.GAME })
      const organization = await generateRandomOrganization('Test Organization', 'CZ')

      const mainTeam = await generateRandomTeam({
        name: 'Main Team',
        slug: 'main-team',
        organization: organization._id.toString(),
      })

      const orgTeam = await generateRandomTeam({
        name: 'Org Team',
        slug: 'org-team',
        organization: organization._id.toString(),
      })

      const mainTeamShadowPlayers = []
      for (let i = 0; i < 3; i++) {
        const { user } = await generateRandomUser({
          firstname: `Main${i}`,
          lastname: `Player${i}`,
          role: Role.SHADOW_PLAYER,
          externalConnections: [
            {
              externalId: `main-player-${i}`,
              crawlerDomainConfig: discoveredUrl.crawlerDomainConfig._id,
            },
          ],
        })
        mainTeamShadowPlayers.push(user)
      }

      const orgTeamShadowPlayers = []
      for (let i = 0; i < 3; i++) {
        const { user } = await generateRandomUser({
          firstname: `Org${i}`,
          lastname: `Player${i}`,
          role: Role.SHADOW_PLAYER,
          externalConnections: [
            {
              externalId: `org-player-${i}`,
              crawlerDomainConfig: discoveredUrl.crawlerDomainConfig._id,
            },
          ],
        })
        orgTeamShadowPlayers.push(user)
      }

      await GameModel.create({
        homeTeam: mainTeam._id,
        awayTeam: new Types.ObjectId(), // Some other team
        date: new Date(),
        externalId: 'main-team-game-1',
        externalInfo: {
          homeTeamName: 'Main Team',
          awayTeamName: 'Away Team',
          leagueName: 'Test League',
        },
        discoveredUrl: discoveredUrl._id,
        homeTeamRoster: [
          {
            externalInfo: {
              playerExternalId: 'main-player-0',
            },
          },
          {
            externalInfo: {
              playerExternalId: 'main-player-1',
            },
          },
          {
            externalInfo: {
              playerExternalId: 'main-player-2',
            },
          },
        ],
      })

      await GameModel.create({
        homeTeam: orgTeam._id,
        awayTeam: new Types.ObjectId(), // Some other team
        date: new Date(),
        externalId: 'org-team-game-1',
        externalInfo: {
          homeTeamName: 'Org Team',
          awayTeamName: 'Away Team 2',
          leagueName: 'Test League',
        },
        discoveredUrl: discoveredUrl._id,
        homeTeamRoster: [
          {
            externalInfo: {
              playerExternalId: 'org-player-0',
            },
          },
          {
            externalInfo: {
              playerExternalId: 'org-player-1',
            },
          },
          {
            externalInfo: {
              playerExternalId: 'org-player-2',
            },
          },
        ],
      })

      const response = await client(randomUser.tokens.accessToken).suggestInvites({
        teamId: mainTeam._id,
      })

      assert.ok(response.suggestInvites.edges.length > 0, 'Should return some shadow players')

      const returnedPlayerIds = response.suggestInvites.edges.map((edge) => edge.node.id)

      const mainTeamPlayerIds = mainTeamShadowPlayers.map((p) => p._id.toString())
      const orgTeamPlayerIds = orgTeamShadowPlayers.map((p) => p._id.toString())

      // Verify that main team players come first
      for (let i = 0; i < mainTeamPlayerIds.length; i++) {
        assert.ok(
          returnedPlayerIds.indexOf(mainTeamPlayerIds[i]) <
            returnedPlayerIds.indexOf(orgTeamPlayerIds[0]),
          `Main team player ${i} should come before any org team player`
        )
      }

      // Verify that all main team players are included
      for (const playerId of mainTeamPlayerIds) {
        assert.ok(
          returnedPlayerIds.includes(playerId),
          `Response should include main team player ${playerId}`
        )
      }

      // Verify that all org team players are included
      for (const playerId of orgTeamPlayerIds) {
        assert.ok(
          returnedPlayerIds.includes(playerId),
          `Response should include org team player ${playerId}`
        )
      }
    })
  })
})
