import assert from 'node:assert'

import { UserModel, WallVideoModel } from '../../src/modules/user/models'
import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'
import { generateWallVideo } from '../utils/wallVideos'

describe('Wall Videos by Video IDs - Populated with Users and Teams', () => {
  beforeEach(async () => {
    await UserModel.deleteMany({})
    await WallVideoModel.deleteMany({})
  })

  after(async () => {
    await UserModel.deleteMany({})
    await WallVideoModel.deleteMany({})
  })

  it('Should retrieve videos by their IDs and ensure users are correctly associated', async () => {
    // Generate mock user data
    const users = [await generateRandomUser(), await generateRandomUser()]

    // Generate mock video data
    let numVideos = 20
    const videos = []
    while (numVideos--) {
      videos.push(
        await generateWallVideo({
          title: 'Video',
          user: users[numVideos & 1].user,
          path: `video${numVideos}`,
        })
      )
    }

    const videoIds = videos.map(({ _id }) => _id.toString())

    // Query the database for videos by their IDs
    const response = await client(users[0].tokens.accessToken).videosByIds({ input: { videoIds } })

    // Assert the correct number of videos is retrieved
    assert.strictEqual(response.videosByIds.videos.length, 20)

    // Assert the correct user association for the first two videos
    assert.strictEqual(response.videosByIds?.videos?.[0]?.user?.id, users[1].user._id.toString())
    assert.strictEqual(response.videosByIds?.videos?.[1]?.user?.id, users[0].user._id.toString())
  })
})
