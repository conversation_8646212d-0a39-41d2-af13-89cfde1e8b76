import assert from 'node:assert'

import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserModel } from '../../src/modules/user/models'
import { SubRoleSpecificationType } from '../../src/modules/user/models/user'
import { client } from '../utils/graphql'
import { generateRandomOrganization, generateRandomTeam, generateRandomUser } from '../utils/users'

describe('User resolver', () => {
  beforeEach(async () => {
    await UserModel.deleteMany({})
    await TeamModel.deleteMany({})
    await OrganizationModel.deleteMany({})
  })
  describe('Sub Role Specification', () => {
    it('should update user with subroleSpecifications', async () => {
      const user = await generateRandomUser()
      const organization = await generateRandomOrganization('Test Organization')
      const team = await generateRandomTeam({ name: 'Test Team', organization: organization.id })

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          subroleSpecifications: [
            {
              team: team.id,
              type: SubRoleSpecificationType.HeadCoach,
              current: true,
            },
          ],
        },
      })

      assert.ok(response.updateUser.user, 'User should be returned')
      assert.strictEqual(response.updateUser.errors.length, 0, 'No errors should be returned')

      const updatedUser = await UserModel.findById(user.user._id)
      assert.ok(updatedUser?.subroleSpecifications, 'subroleSpecifications should be saved in DB')
      assert.strictEqual(
        updatedUser?.subroleSpecifications.length,
        1,
        'Should have one subroleSpecification in DB'
      )
      assert.strictEqual(
        updatedUser?.subroleSpecifications[0].type,
        SubRoleSpecificationType.HeadCoach,
        'DB type should match'
      )
      assert.strictEqual(
        updatedUser?.subroleSpecifications[0].current,
        true,
        'DB current flag should match'
      )
      assert.strictEqual(
        updatedUser?.subroleSpecifications[0].team.toString(),
        team.id.toString(),
        'DB team should match'
      )
    })

    it('should update/overwrite existing subroleSpecifications', async () => {
      const user = await generateRandomUser()
      const team1 = await generateRandomTeam({ name: 'Team One' })
      const team2 = await generateRandomTeam({ name: 'Team Two' })

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          subroleSpecifications: [
            {
              team: team1.id,
              type: SubRoleSpecificationType.HeadCoach,
              current: true,
            },
          ],
        },
      })

      assert.ok(response.updateUser.user, 'User should be returned')
      assert.strictEqual(response.updateUser.user?.subroleSpecifications?.length, 1)

      const responseUpdated = await client(user.tokens.accessToken).updateUser({
        input: {
          subroleSpecifications: [
            {
              team: team1.id,
              type: SubRoleSpecificationType.AssistantCoach,
              current: false,
            },
            {
              team: team2.id,
              type: SubRoleSpecificationType.GoalieCoach,
              current: true,
            },
          ],
        },
      })

      assert.ok(responseUpdated.updateUser.user, 'User should be returned')

      const updatedUser = await UserModel.findById(user.user._id)
      assert.ok(updatedUser?.subroleSpecifications, 'subroleSpecifications should be saved in DB')
      assert.strictEqual(updatedUser?.subroleSpecifications.length, 2)

      const team1SpecInDb = updatedUser.subroleSpecifications.find(
        (spec) => spec.team.toString() === team1.id.toString()
      )
      assert.ok(team1SpecInDb, 'Team1 specification should exist in DB')
      assert.strictEqual(
        team1SpecInDb.type,
        SubRoleSpecificationType.AssistantCoach,
        'DB type should be updated'
      )
      assert.strictEqual(team1SpecInDb.current, false)

      const team2SpecInDb = updatedUser.subroleSpecifications.find(
        (spec) => spec.team.toString() === team2.id.toString()
      )
      assert.ok(team2SpecInDb, 'Team2 specification should exist in DB')
      assert.strictEqual(
        team2SpecInDb.type,
        SubRoleSpecificationType.GoalieCoach,
        'DB type should match'
      )
      assert.strictEqual(team2SpecInDb.current, true)
    })

    it('should clear subroleSpecifications when empty array is provided', async () => {
      const user = await generateRandomUser()
      const team = await generateRandomTeam({ name: 'Test Team' })

      await client(user.tokens.accessToken).updateUser({
        input: {
          subroleSpecifications: [
            {
              team: team.id,
              type: SubRoleSpecificationType.HeadCoach,
              current: true,
            },
          ],
        },
      })

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          subroleSpecifications: [],
        },
      })

      assert.ok(response.updateUser.user, 'User should be returned')
      assert.strictEqual(response.updateUser.errors.length, 0, 'No errors should be returned')

      const updatedUser = await UserModel.findById(user.user._id)
      assert.ok(
        !updatedUser?.subroleSpecifications || updatedUser.subroleSpecifications.length === 0,
        'subroleSpecifications should be cleared in DB'
      )
    })

    it('should handle multiple subroleSpecifications with different types', async () => {
      const user = await generateRandomUser()
      const team = await generateRandomTeam({ name: 'Multi-role Team' })

      await client(user.tokens.accessToken).updateUser({
        input: {
          subroleSpecifications: [
            {
              team: team.id,
              type: SubRoleSpecificationType.HeadCoach,
              current: true,
            },
            {
              team: team.id,
              type: SubRoleSpecificationType.ConditioningCoach,
              current: false,
            },
          ],
        },
      })

      const updatedUser = await UserModel.findById(user.user._id)
      assert.ok(updatedUser?.subroleSpecifications)
      assert.strictEqual(updatedUser?.subroleSpecifications.length, 2)

      const headCoachSpec = updatedUser.subroleSpecifications.find(
        (spec) => spec.type === SubRoleSpecificationType.HeadCoach
      )
      const conditioningCoachSpec = updatedUser.subroleSpecifications.find(
        (spec) => spec.type === SubRoleSpecificationType.ConditioningCoach
      )

      assert.ok(headCoachSpec, 'Head coach specification should exist in DB')
      assert.ok(conditioningCoachSpec, 'Conditioning coach specification should exist in DB')
      assert.strictEqual(headCoachSpec.team.toString(), team.id.toString())
      assert.strictEqual(conditioningCoachSpec.team.toString(), team.id.toString())
    })
  })

  it('should retrieve organization from team in subroleSpecifications', async () => {
    const user = await generateRandomUser()
    const organization = await generateRandomOrganization('New Test Organization')
    const team = await generateRandomTeam({
      name: 'Test Team',
      organization: organization.id,
    })

    await client(user.tokens.accessToken).updateUser({
      input: {
        subroleSpecifications: [
          {
            team: team.id,
            type: SubRoleSpecificationType.HeadCoach,
            current: true,
          },
        ],
      },
    })

    const response = await client(user.tokens.accessToken).currentUser()

    assert.ok(response.currentUser.subroleSpecifications, 'User should have subroleSpecifications')
    assert.strictEqual(response.currentUser.subroleSpecifications.length, 1)

    const subroleSpec = response.currentUser.subroleSpecifications[0]
    assert.ok(subroleSpec.team, 'Team should be included in subroleSpecification')
    assert.strictEqual(subroleSpec.team.id, team.id.toString(), 'Team ID should match')
    assert.ok(subroleSpec.team.organization, 'Organization should be included in team')
    assert.strictEqual(subroleSpec.team.organization.id, organization.id.toString())
    assert.strictEqual(subroleSpec.team.organization.name, organization.name)
  })
})
