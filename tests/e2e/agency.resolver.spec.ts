import assert from 'assert'
import { readFileSync } from 'fs'
import { Types } from 'mongoose'
import sinon from 'sinon'

import * as s3RequestPresigner from '../../src/helpers/getSignedUrl'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserModel } from '../../src/modules/user/models'
import { AgencyModel } from '../../src/modules/user/models/agency'
import { AgencyFollowModel } from '../../src/modules/user/models/agencyFollow'
import {
  AgentsInvitationModel,
  InvitationStatus,
} from '../../src/modules/user/models/agentsInvitation'
import { Role, SubRole, User } from '../../src/modules/user/models/user'
import { AgencyUsersRole, BioPosition, SportCategory } from '../graphql/components'
import { client } from '../utils/graphql'
import {
  generateRandomAdminUser,
  generateRandomAgency,
  generateRandomTeam,
  generateRandomUser,
} from '../utils/users'

import { s3ClientStub, sesClientStub } from '.'

describe('Agency Resolver', () => {
  it('agencies query', async () => {
    await AgencyModel.deleteMany({})

    const agencies = []

    for (let i = 0; i < 11; i++) {
      const agency = await generateRandomAgency('Agency ' + i, 'https://www.example.com', true)
      agencies.push(agency)
    }

    const responseFirstPage = await client().agencies({
      filter: {
        searchQuery: null,
      },
      pagination: {
        first: 10,
      },
    })

    assert.strictEqual(responseFirstPage.agencies.pageInfo.hasPreviousPage, false)
    assert.strictEqual(responseFirstPage.agencies.edges.length, 10)
    assert.strictEqual(responseFirstPage.agencies.pageInfo.hasNextPage, true)
    assert.strictEqual(responseFirstPage.agencies.edges[0].node.name, 'Agency 10')
    assert.strictEqual(responseFirstPage.agencies.edges[0].node.website, 'https://www.example.com')
    assert.deepStrictEqual(responseFirstPage.agencies.edges[0].node.categories, [
      SportCategory.Hockey,
    ])
    assert.strictEqual(responseFirstPage.agencies.edges[9].node.name, 'Agency 1')
    assert.strictEqual(responseFirstPage.agencies.edges[9].node.website, 'https://www.example.com')
    assert.deepStrictEqual(responseFirstPage.agencies.edges[9].node.categories, [
      SportCategory.Hockey,
    ])

    const responseSecondPage = await client().agencies({
      filter: {
        searchQuery: null,
      },
      pagination: {
        first: 10,
        after: responseFirstPage.agencies.pageInfo.endCursor,
      },
    })

    assert.strictEqual(responseSecondPage.agencies.pageInfo.hasNextPage, false)
    assert.strictEqual(responseSecondPage.agencies.pageInfo.hasPreviousPage, true)
    assert.strictEqual(responseSecondPage.agencies.edges.length, 1)
    assert.strictEqual(responseSecondPage.agencies.edges[0].node.name, 'Agency 0')

    // search query
    await new Promise((resolve) => setTimeout(resolve, 1000))
    const responseSearch = await client().agencies({
      filter: {
        searchQuery: 'Agency 5',
      },
    })

    assert.strictEqual(responseSearch.agencies.edges.length, 10)
    assert.strictEqual(responseSearch.agencies.edges[0].node.name, 'Agency 5')
  })

  it('return checked agency', async () => {
    const adminUser = await generateRandomAdminUser()

    const agency = await client(adminUser.tokens.accessToken).createAgency({
      input: { name: 'Test agency checked', country: 'US', categories: [SportCategory.Hockey] },
    })
    await client(adminUser.tokens.accessToken).createAgency({
      input: { name: 'Test agency unchecked', country: 'US', categories: [SportCategory.Hockey] },
    })

    await client(adminUser.tokens.accessToken).updateAgency({
      input: { checked: true },
      updateAgencyId: agency.createAgency.agency?.id,
    })

    const response = await client(adminUser.tokens.accessToken).agencies({
      filter: {
        searchQuery: null,
        checked: true,
      },
    })

    assert.strictEqual(response.agencies.edges.length, 1)
    assert.strictEqual(response.agencies.edges[0].node.name, 'Test agency checked')
    assert.strictEqual(response.agencies.edges[0].node.slug, 'test-agency-checked')
  })

  it('should return only integrated agencies to non-admin users', async () => {
    await AgencyModel.deleteMany({})

    const agencies = []

    for (let i = 0; i < 10; i++) {
      const isIntegrated = i % 2 === 0
      const agency = await generateRandomAgency(
        `Agency ${i} ${isIntegrated ? 'integrated' : ''} ${i}`,
        'https://www.example.com',
        isIntegrated,
        undefined,
        {
          licences: ['FBI'],
          drafts: { NHL: 0, NCAA: 11, CHL: 5, USHL: 6 },
        }
      )
      agencies.push(agency)
    }

    const response = await client().agencies({
      filter: {
        searchQuery: null,
      },
      pagination: {
        first: 10,
      },
    })

    assert.strictEqual(response.agencies.pageInfo.hasPreviousPage, false)
    assert.strictEqual(response.agencies.pageInfo.hasNextPage, false)
    assert.strictEqual(response.agencies.edges.length, 5)
    assert.strictEqual(response.agencies.edges.filter((a) => a.node.integrated).length, 5)
  })

  it('should return only agencies for specific category', async () => {
    await AgencyModel.deleteMany({})

    const agencies = []

    for (let i = 0; i < 10; i++) {
      const agency = await generateRandomAgency(
        `Agency ${i}`,
        'https://www.example.com',
        true,
        i % 2 === 0 ? [SportCategory.Hockey] : [SportCategory.Volleyball]
      )
      agencies.push(agency)
    }

    const response = await client().agencies({
      filter: {
        searchQuery: null,
        category: SportCategory.Hockey,
      },
      pagination: {
        first: 10,
      },
    })

    assert.strictEqual(response.agencies.pageInfo.hasPreviousPage, false)
    assert.strictEqual(response.agencies.pageInfo.hasNextPage, false)
    assert.strictEqual(response.agencies.edges.length, 5)
    assert.strictEqual(
      response.agencies.edges.filter((a) => a.node.categories?.includes(SportCategory.Hockey))
        .length,
      5
    )

    // check other category
    const response2 = await client().agencies({
      filter: {
        searchQuery: null,
        category: SportCategory.Volleyball,
      },
      pagination: {
        first: 10,
      },
    })

    assert.strictEqual(response2.agencies.pageInfo.hasPreviousPage, false)
    assert.strictEqual(response2.agencies.pageInfo.hasNextPage, false)
    assert.strictEqual(response2.agencies.edges.length, 5)
    assert.strictEqual(
      response2.agencies.edges.filter((a) => a.node.categories?.includes(SportCategory.Volleyball))
        .length,
      5
    )
  })

  describe('create agency mutation', () => {
    it('should return created agency', async () => {
      const adminUser = await generateRandomAdminUser()

      const randomAgencies = [
        await generateRandomAgency('Test Agency 11'),
        await generateRandomAgency('Test Agency 12'),
      ]

      const response = await client(adminUser.tokens.accessToken).createAgency({
        input: {
          name: 'Test agency admin create',
          country: 'US',
          website: 'www.test.com',
          address: 'Test address',
          email: '<EMAIL>',
          phone: '123456789',
          epUrl: 'www.ep.com',
          founded: 1996,
          externalNumberOfClients: 10,
          instagramUrl: 'www.instagram.com',
          facebookUrl: 'www.facebook.com',
          twitterUrl: 'www.twitter.com',
          linkedinUrl: 'www.linkedin.com',
          tiktokUrl: 'www.tiktok.com',
          instagramFollowers: 100,
          categories: [SportCategory.Volleyball],
          licences: ['PHL', 'CCH', 'MIT'],
          drafts: { NHL: 99, USHL: 4, CHL: 1, NCAA: 4 },
          affiliateAgencies: randomAgencies.map(({ _id }) => _id),
        },
      })

      assert.strictEqual(response.createAgency.agency?.name, 'Test agency admin create')
      assert.strictEqual(response.createAgency.agency?.country, 'US')
      assert.strictEqual(response.createAgency.agency?.website, 'www.test.com')
      assert.strictEqual(response.createAgency.agency?.slug, 'test-agency-admin-create')
      assert.strictEqual(response.createAgency.agency?.address, 'Test address')
      assert.strictEqual(response.createAgency.agency?.email, '<EMAIL>')
      assert.strictEqual(response.createAgency.agency?.phone, '123456789')
      assert.strictEqual(response.createAgency.agency?.epUrl, 'www.ep.com')
      assert.strictEqual(response.createAgency.agency?.founded, 1996)
      assert.strictEqual(response.createAgency.agency?.externalNumberOfClients, 10)
      assert.strictEqual(response.createAgency.agency?.instagramUrl, 'www.instagram.com')
      assert.strictEqual(response.createAgency.agency?.facebookUrl, 'www.facebook.com')
      assert.strictEqual(response.createAgency.agency?.twitterUrl, 'www.twitter.com')
      assert.strictEqual(response.createAgency.agency?.linkedinUrl, 'www.linkedin.com')
      assert.strictEqual(response.createAgency.agency?.tiktokUrl, 'www.tiktok.com')
      assert.strictEqual(response.createAgency.agency?.instagramFollowers, 100)
      assert.strictEqual(response.createAgency.agency?.clientsCount, 0)
      assert.strictEqual(response.createAgency.agency?.agentsCount, 0)
      assert.deepStrictEqual(response.createAgency.agency?.categories, [SportCategory.Volleyball])

      assert.deepStrictEqual(response.createAgency.agency?.licences, ['PHL', 'CCH', 'MIT'])

      assert.deepStrictEqual(response.createAgency.agency?.drafts, {
        NHL: 99,
        USHL: 4,
        CHL: 1,
        NCAA: 4,
      })
      assert.strictEqual(response.createAgency.agency?.affiliateAgencies?.length, 2)

      assert.strictEqual(
        response.createAgency.agency?.affiliateAgencies?.[0].name,
        randomAgencies[0].name
      )
      assert.strictEqual(
        response.createAgency.agency?.affiliateAgencies?.[1].name,
        randomAgencies[1].name
      )
    })

    it('should catch error if agency exists', async () => {
      const adminUser = await generateRandomAdminUser()

      await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin create', country: 'US' },
      })

      try {
        await client(adminUser.tokens.accessToken).createAgency({
          input: { name: 'Test agency admin create', country: 'US' },
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Agency already exists')
      }
    })
  })

  describe('update agency mutation', () => {
    it('should return updated agency', async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin update', country: 'SK' },
      })

      const randomAgencies = [
        await generateRandomAgency('Test Agency 13'),
        await generateRandomAgency('Test Agency 14'),
      ]

      const response = await client(adminUser.tokens.accessToken).updateAgency({
        input: {
          name: 'Test agency admin updated',
          country: 'CZ',
          website: 'www.test.com',
          address: 'Test address',
          email: '<EMAIL>',
          phone: '123456789',
          epUrl: 'www.ep.com',
          founded: 2004,
          externalNumberOfClients: 10,
          instagramUrl: 'www.instagram.com',
          facebookUrl: 'www.facebook.com',
          twitterUrl: 'www.twitter.com',
          linkedinUrl: 'www.linkedin.com',
          tiktokUrl: 'www.tiktok.com',
          instagramFollowers: 100,
          licences: ['PHL', 'CCH', 'MIT'],
          drafts: { NHL: 99, USHL: 4, CHL: 1, NCAA: 4 },
          affiliateAgencies: randomAgencies.map(({ _id }) => _id),
        },
        updateAgencyId: agency.createAgency.agency?.id,
      })

      assert.strictEqual(response.updateAgency.agency?.name, 'Test agency admin updated')
      assert.strictEqual(response.updateAgency.agency?.country, 'CZ')
      assert.strictEqual(response.updateAgency.agency?.website, 'www.test.com')
      assert.strictEqual(response.updateAgency.agency?.slug, 'test-agency-admin-updated')
      assert.strictEqual(response.updateAgency.agency?.address, 'Test address')
      assert.strictEqual(response.updateAgency.agency?.email, '<EMAIL>')
      assert.strictEqual(response.updateAgency.agency?.phone, '123456789')
      assert.strictEqual(response.updateAgency.agency?.epUrl, 'www.ep.com')
      assert.strictEqual(response.updateAgency.agency?.founded, 2004)
      assert.strictEqual(response.updateAgency.agency?.externalNumberOfClients, 10)
      assert.strictEqual(response.updateAgency.agency?.instagramUrl, 'www.instagram.com')
      assert.strictEqual(response.updateAgency.agency?.facebookUrl, 'www.facebook.com')
      assert.strictEqual(response.updateAgency.agency?.twitterUrl, 'www.twitter.com')
      assert.strictEqual(response.updateAgency.agency?.linkedinUrl, 'www.linkedin.com')
      assert.strictEqual(response.updateAgency.agency?.tiktokUrl, 'www.tiktok.com')
      assert.strictEqual(response.updateAgency.agency?.instagramFollowers, 100)

      assert.deepStrictEqual(response.updateAgency.agency?.licences, ['PHL', 'CCH', 'MIT'])

      assert.deepStrictEqual(response.updateAgency.agency?.drafts, {
        NHL: 99,
        USHL: 4,
        CHL: 1,
        NCAA: 4,
      })
      assert.strictEqual(response.updateAgency.agency?.affiliateAgencies?.length, 2)

      assert.strictEqual(
        response.updateAgency.agency?.affiliateAgencies?.[0].name,
        randomAgencies[0].name
      )
      assert.strictEqual(
        response.updateAgency.agency?.affiliateAgencies?.[1].name,
        randomAgencies[1].name
      )
    })

    it('should catch error if agency not found', async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'test', country: 'US' },
      })

      const findByIdAndUpdateStub = sinon
        .stub(AgencyModel, 'findByIdAndUpdate')
        .throws(new Error('Database error'))

      const response = await client(adminUser.tokens.accessToken).updateAgency({
        updateAgencyId: agency.createAgency.agency?.id,
        input: { name: 'Test agency admin updated', country: 'US' },
      })

      assert.strictEqual(response.updateAgency.errors?.[0].message, 'Agency not found')

      findByIdAndUpdateStub.restore()
    })
  })

  describe('delete agency mutation', () => {
    it('should return true if agency was deleted', async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin delete', country: 'US' },
      })

      const response = await client(adminUser.tokens.accessToken).deleteAgency({
        deleteAgencyId: agency.createAgency.agency?.id,
      })

      assert.strictEqual(response.deleteAgency, true)
    })

    it('should return false if agency was not deleted', async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin delete', country: 'US' },
      })

      const findByIdAndDeleteStub = sinon
        .stub(AgencyModel, 'findByIdAndDelete')
        .throws(new Error('Database error'))

      const response = await client(adminUser.tokens.accessToken).deleteAgency({
        deleteAgencyId: agency.createAgency.agency?.id,
      })

      assert.strictEqual(response.deleteAgency, false)

      findByIdAndDeleteStub.restore()
    })
  })

  describe('agency query', () => {
    it('should return agency', async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin query', country: 'US' },
      })

      const response = await client(adminUser.tokens.accessToken).agency({
        agencyId: agency.createAgency.agency?.id,
      })

      assert.strictEqual(response.agency?.name, 'Test agency admin query')
      assert.strictEqual(response.agency?.country, 'US')
      assert.strictEqual(response.agency?.slug, 'test-agency-admin-query')
      assert.strictEqual(response.agency?.website, null)

      // it should be able to find agency by slug
      const responseBySlug = await client().agency({
        slug: 'test-agency-admin-query',
      })

      assert.strictEqual(responseBySlug.agency?.name, 'Test agency admin query')
      assert.strictEqual(responseBySlug.agency?.country, 'US')
      assert.strictEqual(responseBySlug.agency?.slug, 'test-agency-admin-query')
      assert.strictEqual(responseBySlug.agency?.website, null)
    })

    it('should return null if agency not found', async () => {
      const adminUser = await generateRandomAdminUser()

      const nonExistentId = new Types.ObjectId()

      const findByIdStub = sinon.stub(AgencyModel, 'findById').resolves(null)

      const result = await client(adminUser.tokens.accessToken).agency({
        agencyId: nonExistentId.toString(),
      })
      assert.strictEqual(result.agency, null)
      sinon.assert.calledOnceWithExactly(findByIdStub, nonExistentId)

      findByIdStub.restore()
    })

    it('should return null if an error occurs', async () => {
      const adminUser = await generateRandomAdminUser()

      const agencyId = new Types.ObjectId()

      const findByIdStub = sinon.stub(AgencyModel, 'findById').throws(new Error('Database error'))

      const result = await client(adminUser.tokens.accessToken).agency({
        agencyId: agencyId.toString(),
      })

      sinon.assert.calledOnceWithExactly(findByIdStub, agencyId)

      assert.strictEqual(result.agency, null)

      findByIdStub.restore()
    })
  })

  describe('upload agency logo mutation', () => {
    it('should return response if upload successful', async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin upload logo', country: 'US' },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadAgencyLogo({
        uploadAgencyLogoId: agency.createAgency.agency?.id,
      })
      assert.strictEqual(response.uploadAgencyLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(response.uploadAgencyLogo.agency?.name, 'Test agency admin upload logo')
      assert.strictEqual(response.uploadAgencyLogo.agency?.country, 'US')
      assert.strictEqual(response.uploadAgencyLogo.agency?.logo, null)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const responseConfirmation = await client(adminUser.tokens.accessToken).uploadAgencyLogo({
        uploadAgencyLogoId: agency.createAgency.agency?.id,
        input: { uploadSuccessful: true },
      })
      assert.strictEqual(responseConfirmation.uploadAgencyLogo.errors, null)

      const logoKeyPattern = new RegExp(
        `images/agencies/${agency.createAgency.agency?.id}/logo/\\d+\\.png`
      )
      const check = await AgencyModel.findById(agency.createAgency.agency?.id)
      assert.match(check?.logo || '', logoKeyPattern)
      assert.strictEqual(check?.logoUnconfirmed, null)

      s3RequestPresignerstub.restore()
    })

    it('should return response if upload unsuccessful', async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin upload logo unsuccessful', country: 'US' },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .resolves('presignedUrl')

      const response = await client(adminUser.tokens.accessToken).uploadAgencyLogo({
        uploadAgencyLogoId: agency.createAgency.agency?.id,
      })
      assert.strictEqual(response.uploadAgencyLogo.preSignedUrl, 'presignedUrl')
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)
      assert.strictEqual(
        response.uploadAgencyLogo.agency?.name,
        'Test agency admin upload logo unsuccessful'
      )
      assert.strictEqual(response.uploadAgencyLogo.agency?.country, 'US')
      assert.strictEqual(response.uploadAgencyLogo.agency?.logo, null)

      const fileStr = new Uint8Array(readFileSync('tests/assets/1x1.png'))
      s3ClientStub.send.resolves({
        Body: {
          transformToByteArray: sinon.stub().resolves(fileStr),
        },
      })

      const logoKeyPattern = new RegExp(
        `images/agencies/${agency.createAgency.agency?.id}/logo/\\d+\\.png`
      )

      const firstCheck = await AgencyModel.findById(agency.createAgency.agency?.id)
      assert.match(firstCheck?.logoUnconfirmed || '', logoKeyPattern)
      assert.strictEqual(firstCheck?.logo, undefined)

      const responseConfirmation = await client(adminUser.tokens.accessToken).uploadAgencyLogo({
        uploadAgencyLogoId: agency.createAgency.agency?.id,
        input: { uploadSuccessful: false },
      })
      assert.strictEqual(responseConfirmation.uploadAgencyLogo.errors, null)

      const secondCheck = await AgencyModel.findById(agency.createAgency.agency?.id)
      assert.strictEqual(secondCheck?.logoUnconfirmed, null)
      assert.strictEqual(secondCheck?.logo, undefined)

      s3RequestPresignerstub.restore()
    })

    it("should return error if couldn't create presigned url", async () => {
      const adminUser = await generateRandomAdminUser()

      const agency = await client(adminUser.tokens.accessToken).createAgency({
        input: { name: 'Test agency admin upload logo error', country: 'US' },
      })

      const s3RequestPresignerstub = await sinon
        .stub(s3RequestPresigner, 'getSignedUrl')
        .throws(new Error('Failed to create preSignedUrl'))

      try {
        await client(adminUser.tokens.accessToken).uploadAgencyLogo({
          uploadAgencyLogoId: agency.createAgency.agency?.id,
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to create preSignedUrl')
      }
      assert.strictEqual(s3RequestPresignerstub.calledOnce, true)

      s3RequestPresignerstub.restore()
    })
  })

  describe('agency users query', () => {
    beforeEach(async () => {
      await AgencyModel.deleteMany({})
      await UserModel.deleteMany({})
      await TeamModel.deleteMany({})

      await generateRandomTeam({ name: 'Team 1' })
    })

    it('should return users in agency', async () => {
      const agency = await generateRandomAgency('test users agency')

      const users = []
      for (let i = 0; i < 11; i++) {
        const user = await generateRandomUser({
          dateOfBirth: new Date(2005, 11, 11),
        })
        await UserModel.findByIdAndUpdate(user.user._id, {
          bio: {
            agency: agency?.id,
            position: BioPosition.Center,
          },
        })

        users.push(user)
      }

      const response = await client(users[0].tokens.accessToken).agencyUsers({
        agencyId: agency?._id,
        filter: {
          includeAggregation: true,
          years: ['2006+'],
        },
      })

      assert.strictEqual(response.agencyUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.agencyUsers.pageInfo.hasNextPage, true)
      assert.strictEqual(response.agencyUsers.edges.length, 10)
      assert.strictEqual(response.agencyUsers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.deepStrictEqual(response.agencyUsers.aggregatedInfo, [
        {
          name: 'positions',
          items: [
            {
              key: 'CENTER',
              value: 11,
            },
          ],
        },
        {
          name: 'years',
          items: [
            {
              key: '2006+',
              value: 11,
            },
          ],
        },
      ])
    })

    it('should filter users in agency based on year', async () => {
      const agency = await generateRandomAgency('test users agency 2')

      const users = []
      for (let i = 0; i < 11; i++) {
        const user = await generateRandomUser({
          dateOfBirth: new Date(2000 + i, 11, 11),
        })

        await UserModel.findByIdAndUpdate(user.user._id, {
          bio: {
            agency: agency?.id,
            position: BioPosition.Center,
          },
        })

        users.push(user)
      }

      const response = await client(users[0].tokens.accessToken).agencyUsers({
        agencyId: agency?._id,
        filter: {
          includeAggregation: true,
          years: ['2010', '2006+'],
        },
      })

      assert.strictEqual(response.agencyUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.agencyUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(response.agencyUsers.edges.length, 8)
      assert.strictEqual(response.agencyUsers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.deepStrictEqual(response.agencyUsers.aggregatedInfo, [
        {
          name: 'positions',
          items: [
            {
              key: 'CENTER',
              value: 8,
            },
          ],
        },
        {
          name: 'years',
          items: [
            {
              key: '2007',
              value: 1,
            },
            {
              key: '2008',
              value: 1,
            },
            {
              key: '2009',
              value: 1,
            },
            {
              key: '2010',
              value: 1,
            },
            {
              key: '2006+',
              value: 7,
            },
          ],
        },
      ])
    })

    it('should filter users in agency based on agentV2', async () => {
      const agency = await generateRandomAgency('test users agency 3')

      const agent = await generateRandomUser({
        role: Role.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
      })

      // add slug
      agent.user = (await UserModel.findByIdAndUpdate(
        agent.user._id,
        {
          slug: 'test-agent-v2-slug',
        },
        { new: true }
      )) as User

      const agent2 = await generateRandomUser({
        role: Role.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
      })

      // add slug
      agent2.user = (await UserModel.findByIdAndUpdate(
        agent2.user._id,
        {
          slug: 'test-agent-v2-2-slug',
        },
        { new: true }
      )) as User

      const users = []
      for (let i = 0; i < 11; i++) {
        const user = await generateRandomUser({
          dateOfBirth: new Date(2000 + i, 11, 11),
        })

        await UserModel.findByIdAndUpdate(user.user._id, {
          bio: {
            agency: agency?.id,
            position: BioPosition.Center,
          },
          agentV2: (i & 1 ? agent : agent2).user._id,
        })

        users.push(user)
      }

      const response = await client(users[0].tokens.accessToken).agencyUsers({
        agencyId: agency?._id,
        filter: {
          includeAggregation: true,
          agentV2: agent.user._id,
        },
      })

      assert.strictEqual(response.agencyUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.agencyUsers.pageInfo.hasNextPage, false)
      assert.strictEqual(response.agencyUsers.edges.length, 5)
      assert.strictEqual(response.agencyUsers.edges[0].node.currentTeam?.name, 'Team 1')
      assert.deepStrictEqual(response.agencyUsers.aggregatedInfo, [
        {
          name: 'positions',
          items: [
            {
              key: 'CENTER',
              value: 5,
            },
          ],
        },
        {
          name: 'years',
          items: [
            {
              key: '2007',
              value: 1,
            },
            {
              key: '2009',
              value: 1,
            },
            {
              key: '2006+',
              value: 3,
            },
          ],
        },
      ])
    })

    it('should return agents in agency', async () => {
      const agency = await generateRandomAgency('test agents agency')

      const users = []
      for (let i = 0; i < 11; i++) {
        const user = await generateRandomUser({
          role: Role.SPORTS_PROFESSIONAL,
          subrole: SubRole.AGENT,
        })
        await UserModel.findByIdAndUpdate(user.user._id, {
          bio: {
            agency: agency?.id,
          },
        })

        users.push(user)
      }

      const response = await client(users[0].tokens.accessToken).agencyUsers({
        agencyId: agency?._id,
        filter: {
          role: AgencyUsersRole.Agent,
        },
      })

      assert.strictEqual(response.agencyUsers.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.agencyUsers.pageInfo.hasNextPage, true)
      assert.strictEqual(response.agencyUsers.edges.length, 10)
      assert.strictEqual(response.agencyUsers.edges[0].node.currentTeam?.name, 'Team 1')
    })
  })

  describe('visit agency mutation', () => {
    it('increases number of agency views', async () => {
      const randomUser = await generateRandomUser()
      const agency = await generateRandomAgency('Agency test 1')

      const response = await client(randomUser.tokens.accessToken).visitAgency({
        id: agency._id,
      })
      assert.strictEqual(response.visitAgency.agency?.stats?.views, 1)
      assert.strictEqual(response.visitAgency.agency.id, agency._id.toString())
    })

    it('does not increase number of agency views if user is the same (increase after 4 hours)', async () => {
      const randomUser = await generateRandomUser()
      const agency = await generateRandomAgency('Agency test 2')

      const response = await client(randomUser.tokens.accessToken).visitAgency({
        id: agency._id,
      })
      assert.strictEqual(response.visitAgency.agency?.stats?.views, 1)

      const response1 = await client(randomUser.tokens.accessToken).visitAgency({
        id: agency._id,
      })
      assert.strictEqual(response1.visitAgency.agency?.stats?.views, 1)

      // after 4 hours it should increase again (4 hours + 1 second)
      const clock = sinon.useFakeTimers(new Date(Date.now() + 1000 + 1000 * 60 * 60 * 4).getTime())
      const response2 = await client(randomUser.tokens.accessToken).visitAgency({
        id: agency._id,
      })
      assert.strictEqual(response2.visitAgency.agency?.stats?.views, 2)

      clock.restore()
    })

    it('throws error when agency does not exist', async () => {
      const response = await client().visitAgency({
        id: new Types.ObjectId(),
      })

      assert.strictEqual(response.visitAgency.errors?.[0].message, 'Agency does not exist.')
    })

    it('throws error when agency fails to update', async () => {
      const agency = await generateRandomAgency('Agency test 3')

      const findOneAndUpdateStub = sinon
        .stub(AgencyModel, 'findOneAndUpdate')
        .throws(new Error('Database error'))

      try {
        await client().visitAgency({
          id: agency._id,
        })

        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to visit agency')
      }

      sinon.assert.calledOnce(findOneAndUpdateStub)

      findOneAndUpdateStub.restore()
    })
  })

  describe('follow agency mutation', () => {
    it('increases number of followers', async () => {
      const follower = await generateRandomUser()
      const agency = await generateRandomAgency('Agency test 4')

      const response = await client(follower.tokens.accessToken).followAgency({
        id: agency._id,
      })

      assert.strictEqual(response.followAgency.agency?.stats?.followers, 1)

      const updatedAgency = await AgencyModel.findById(agency._id)
      assert.strictEqual(updatedAgency?.stats?.followers, 1)

      const subscription = await AgencyFollowModel.findOne({
        agency: agency._id,
        follower: follower.user._id,
      })

      assert.strictEqual(subscription?.agency._id.toString(), agency._id.toString())
      assert.strictEqual(subscription?.follower.toString(), follower.user._id.toString())

      // one more
      const follower2 = await generateRandomUser()
      const response2 = await client(follower2.tokens.accessToken).followAgency({
        id: agency._id,
      })

      assert.strictEqual(response2.followAgency.agency?.stats?.followers, 2)
    })

    it('throws error when agency does not exist', async () => {
      const randomUser = await generateRandomUser()
      const response = await client(randomUser.tokens.accessToken).followAgency({
        id: new Types.ObjectId().toString(),
      })

      assert.strictEqual(response.followAgency.errors?.[0].message, 'Agency does not exist.')
    })

    it('should return error if a MongoServerError with a code other than 11000', async () => {
      const follower = await generateRandomUser()
      const agency = await generateRandomAgency('Agency test 5')

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mongoError = new Error('Duplicate key error') as any
      mongoError.code = 12000

      const findOneAndUpdateStub = sinon.stub(AgencyModel, 'findOneAndUpdate').throws(mongoError)

      try {
        await client(follower.tokens.accessToken).followAgency({
          id: agency._id.toString(),
        })

        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to follow agency')
      }

      sinon.assert.calledOnce(findOneAndUpdateStub)

      findOneAndUpdateStub.restore()
    })
  })

  describe('unfollow agency mutation', () => {
    it('decreases number of followers', async () => {
      const follower = await generateRandomUser()
      const agency = await generateRandomAgency('Agency test 6')
      const response = await client(follower.tokens.accessToken).followAgency({
        id: agency._id,
      })

      assert.strictEqual(response.followAgency.agency?.stats?.followers, 1)

      const updatedResponse = await client(follower.tokens.accessToken).unfollowAgency({
        id: agency._id,
      })

      assert.strictEqual(updatedResponse.unfollowAgency.agency?.stats?.followers, 0)
      assert.strictEqual(
        updatedResponse.unfollowAgency?.agency?.id.toString(),
        agency._id.toString()
      )

      const updatedAgency = await AgencyModel.findById(agency._id)
      assert.strictEqual(updatedAgency?.stats?.followers, 0)

      const subscription = await AgencyFollowModel.findOne({
        agency: agency._id,
        follower: follower.user._id,
      })

      assert.strictEqual(subscription, null)
    })

    it('throws error when agency to unfollow does not exist', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).unfollowAgency({
        id: new Types.ObjectId().toString(),
      })

      assert.strictEqual(response.unfollowAgency.errors?.[0].message, 'Agency does not exist')
    })

    it('throws an error when you try to unfollow agency you havent followed', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Agency test 7')
      const response = await client(user.tokens.accessToken).unfollowAgency({
        id: agency._id,
      })
      assert.strictEqual(response.unfollowAgency.errors?.[0].message, 'Agency follow not found')
    })

    it('should return error if a MongoServerError with a code other than 11000', async () => {
      const follower = await generateRandomUser()
      const agency = await generateRandomAgency('Agency test 8')

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mongoError = new Error('Duplicate key error') as any
      mongoError.code = 12000

      const findOneAndUpdateStub = sinon.stub(AgencyFollowModel, 'findOne').throws(mongoError)

      try {
        await client(follower.tokens.accessToken).unfollowAgency({
          id: agency._id.toString(),
        })

        assert.fail('Expected error was not thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to unfollow agency')
      }

      sinon.assert.calledOnce(findOneAndUpdateStub)

      findOneAndUpdateStub.restore()
    })
  })

  describe('invite agents mutation', () => {
    it('should return response if invitation was sent by agency member for multiple users', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test agency invite agents by admin')

      const emails = ['<EMAIL>', '<EMAIL>']

      await UserModel.findByIdAndUpdate(user.user._id, {
        'bio.agency': agency?.id,
      })

      const response = await client(user.tokens.accessToken).inviteAgents({
        input: {
          agencyId: agency.id,
          emails: emails,
        },
      })

      assert.strictEqual(response.inviteAgents.success, true)
      assert.strictEqual(response.inviteAgents.errors?.length, 0)

      const searchByAgencyId = await AgentsInvitationModel.find({ agencyId: agency.id })

      assert.strictEqual(searchByAgencyId.length, 2)
      assert.strictEqual(searchByAgencyId[0].email, '<EMAIL>')
      assert.strictEqual(searchByAgencyId[0].agencyId.toString(), agency.id)
      assert.strictEqual(searchByAgencyId[0].status, 'pending')
      assert.strictEqual(searchByAgencyId[1].email, '<EMAIL>')
      assert.strictEqual(searchByAgencyId[1].agencyId.toString(), agency.id)
      assert.strictEqual(searchByAgencyId[1].status, 'pending')

      sinon.assert.calledTwice(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send.getCall(0),
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [emails[0]]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send.getCall(0),
        sinon.match.has('input', sinon.match.has('Template', 'AgentInvitationStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send.getCall(0),
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send.getCall(1),
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [emails[1]]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send.getCall(1),
        sinon.match.has('input', sinon.match.has('Template', 'AgentInvitationStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send.getCall(1),
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
    })

    it('should return response if invitation was sent by admin user for one user', async () => {
      const adminUser = await generateRandomAdminUser()
      const agency = await generateRandomAgency('Test agency invite agents by agency member')
      const email = '<EMAIL>'

      const response = await client(adminUser.tokens.accessToken).inviteAgents({
        input: { agencyId: agency.id, emails: ['<EMAIL>'] },
      })

      assert.strictEqual(response.inviteAgents.errors?.length, 0)
      assert.strictEqual(response.inviteAgents.success, true)

      const searchByEmail = await AgentsInvitationModel.find({
        email: email,
      })

      assert.strictEqual(searchByEmail.length, 1)
      assert.strictEqual(searchByEmail[0].email, '<EMAIL>')
      assert.strictEqual(searchByEmail[0].agencyId.toString(), agency.id)
      assert.strictEqual(searchByEmail[0].status, 'pending')

      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', [email]))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'AgentInvitationStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
    })

    it('should return error if invitation sender is not admin or agency member', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test agency invite agents by user')

      try {
        await client(user.tokens.accessToken).inviteAgents({
          input: { agencyId: agency.id, emails: ['<EMAIL>'] },
        })
        assert.fail('Expected error to be thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'You are not allowed to invite agents to this agency'
        )
        assert.strictEqual(err.response.errors[0].extensions.code, 'FORBIDDEN')
      }
    })

    it('should throw error if failed to invite agents', async () => {
      const adminUser = await generateRandomAdminUser()
      const agency = await generateRandomAgency('Test agency invite agents by admin error')

      const createStub = sinon.stub(AgentsInvitationModel, 'create').throws(new Error())

      try {
        await client(adminUser.tokens.accessToken).inviteAgents({
          input: { agencyId: agency.id, emails: ['<EMAIL>'] },
        })
        assert.fail('Expected error to be thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Failed to invite agents')
      }

      createStub.restore()
    })

    it('should delete expired invitations, if new invite has been sent', async () => {
      const adminUser = await generateRandomAdminUser()
      const agency = await generateRandomAgency('Test agency invite agents by admin status change')

      const now = new Date()

      const expiredInvitation = await AgentsInvitationModel.create({
        email: '<EMAIL>',
        agencyId: agency._id,
        token: 'expiredToken',
        status: 'pending',
        expiresAt: new Date(now.getTime() - 1000 * 60), // Expired 1 minute ago
      })

      const responseOnExpired = await client(adminUser.tokens.accessToken).inviteAgents({
        input: { agencyId: agency.id, emails: [expiredInvitation.email] },
      })

      assert.strictEqual(responseOnExpired.inviteAgents.errors?.length, 0)
      assert.strictEqual(responseOnExpired.inviteAgents.success, true)

      const searchAfterAction = await AgentsInvitationModel.find({
        email: '<EMAIL>',
      })
      assert.strictEqual(searchAfterAction.length, 1)
      assert.strictEqual(searchAfterAction[0].status, 'pending')
      assert.notEqual(searchAfterAction[0].token, 'expiredToken')

      sinon.assert.calledOnce(sesClientStub.send)
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Destination', sinon.match.has('ToAddresses', ['<EMAIL>']))
        )
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has('input', sinon.match.has('Template', 'AgentInvitationStaging'))
      )
      sinon.assert.calledWith(
        sesClientStub.send,
        sinon.match.has(
          'input',
          sinon.match.has('Source', 'GRAET-STAGING <<EMAIL>>')
        )
      )
    })

    it('should throw error if there is pending invitation already', async () => {
      const adminUser = await generateRandomAdminUser()
      const agency = await generateRandomAgency('Test agency invite agents by admin expired error')
      const email = '<EMAIL>'

      const now = new Date()

      const validInvitation = await AgentsInvitationModel.create({
        email: email,
        agencyId: agency._id,
        token: 'validToken',
        status: InvitationStatus.PENDING,
        expiresAt: new Date(now.getTime() + 1000 * 60 * 10), // Expires in 10 minutes
      })

      const searchByEmail = await AgentsInvitationModel.find({
        email: email,
      })
      assert.strictEqual(searchByEmail.length, 1)
      assert.strictEqual(searchByEmail[0].status, 'pending')
      assert.strictEqual(searchByEmail[0].token, 'validToken')

      const responseOnValid = await client(adminUser.tokens.accessToken).inviteAgents({
        input: { agencyId: agency.id, emails: [validInvitation.email] },
      })

      assert.strictEqual(responseOnValid.inviteAgents.errors?.length, 1)
      assert.strictEqual(
        responseOnValid.inviteAgents.errors?.[0].message,
        `An active invitation for ${email} already exists, email was not sent.`
      )
    })

    it('should throw error if email is not valid', async () => {
      const adminUser = await generateRandomAdminUser()
      const agency = await generateRandomAgency('Test agency invite agents by admin invalid email')

      try {
        await client(adminUser.tokens.accessToken).inviteAgents({
          input: { agencyId: agency.id, emails: ['invalidEmail'] },
        })
        assert.fail('Expected error to be thrown')
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'Argument Validation Error')
      }
    })
  })

  describe('accept agency invitation mutation', () => {
    it('should accept a valid invitation', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test agency accept invitation')

      await AgentsInvitationModel.create({
        email: user.user.email,
        agencyId: agency._id,
        token: 'valid-token',
        status: 'pending',
        expiresAt: new Date(Date.now() + 1000 * 60 * 15), // Expires in 15 minutes
      })

      assert.strictEqual(user.user.bio?.agency?.toString(), undefined)
      assert.strictEqual(user.user.subrole, undefined)

      const response = await client(user.tokens.accessToken).acceptAgencyInvitation({
        token: 'valid-token',
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(response.acceptAgencyInvitation.success, true)
      assert.strictEqual(response.acceptAgencyInvitation.errors, null)

      const updatedUser = await UserModel.findById(user.user._id)

      assert.strictEqual(updatedUser?.subrole, 'AGENT')
      assert.strictEqual(updatedUser?.bio?.agency?._id?.toString(), agency.id.toString())
    })

    it('should return an error if the invitation has already been accepted', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test agency accept invitation accepted error')

      await AgentsInvitationModel.create({
        email: user.user.email,
        agencyId: agency._id,
        token: 'already-accepted-token',
        status: 'accepted',
        expiresAt: new Date(Date.now() + 1000 * 60 * 15),
      })

      const response = await client(user.tokens.accessToken).acceptAgencyInvitation({
        token: 'already-accepted-token',
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(response.acceptAgencyInvitation.success, false)
      assert.strictEqual(response.acceptAgencyInvitation.errors?.length, 1)
      assert.strictEqual(
        response.acceptAgencyInvitation.errors[0].message,
        'This invitation has already been accepted'
      )
    })

    it('should return an error if the invitation has expired', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test agency accept invitation expired error')

      await AgentsInvitationModel.create({
        email: user.user.email,
        agencyId: agency._id,
        token: 'expired-token',
        status: 'pending',
        expiresAt: new Date(Date.now() - 1000 * 60 * 15), // Expired 15 minutes ago
      })

      const response = await client(user.tokens.accessToken).acceptAgencyInvitation({
        token: 'expired-token',
        agencyId: agency._id.toString(),
      })

      assert.strictEqual(response.acceptAgencyInvitation.success, false)
      assert.strictEqual(response.acceptAgencyInvitation.errors?.length, 1)
      assert.strictEqual(
        response.acceptAgencyInvitation.errors[0].message,
        'This invitation has expired'
      )
    })

    it('should return an error if the invitation token is invalid', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).acceptAgencyInvitation({
        token: 'invalid-token',
        agencyId: new Types.ObjectId().toString(),
      })

      assert.strictEqual(response.acceptAgencyInvitation.success, false)
      assert.strictEqual(response.acceptAgencyInvitation.errors?.length, 1)
      assert.strictEqual(
        response.acceptAgencyInvitation.errors[0].message,
        'Invitation token is invalid or does not match the agency'
      )
    })

    it("should return an error if invitation token doesn't match the agency", async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test agency accept invitation invalid token')

      await AgentsInvitationModel.create({
        email: user.user.email,
        agencyId: new Types.ObjectId(),
        token: 'valid-token',
        status: 'pending',
        expiresAt: new Date(Date.now() + 1000 * 60 * 15), // Expires in 15 minutes
      })

      const response = await client(user.tokens.accessToken).acceptAgencyInvitation({
        token: 'valid-token',
        agencyId: agency.id,
      })

      assert.strictEqual(response.acceptAgencyInvitation.success, false)
      assert.strictEqual(response.acceptAgencyInvitation.errors?.length, 1)
      assert.strictEqual(
        response.acceptAgencyInvitation.errors[0].message,
        'Invitation token is invalid or does not match the agency'
      )
    })

    it('should throw error if failed to accept invitation due to internal error', async () => {
      const user = await generateRandomUser()
      const agency = await generateRandomAgency('Test agency accept invitation internal error')

      const findOneAndUpdateStub = sinon.stub(UserModel, 'findOneAndUpdate').throws(new Error())

      await AgentsInvitationModel.create({
        email: user.user.email,
        agencyId: agency._id,
        token: 'valid-token',
        status: 'pending',
        expiresAt: new Date(Date.now() + 1000 * 60 * 15), // Expires in 15 minutes
      })

      try {
        await client(user.tokens.accessToken).acceptAgencyInvitation({
          token: 'valid-token',
          agencyId: agency.id,
        })
        assert.fail('Expected error to be thrown')
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'Failed to accept invitation due to internal error'
        )
      }

      findOneAndUpdateStub.restore()
    })
  })

  describe('agents clients query', () => {
    it('should return agents clients', async () => {
      const agent = await generateRandomUser({
        role: Role.SPORTS_PROFESSIONAL,
        subrole: SubRole.AGENT,
      })

      // add slug
      agent.user = (await UserModel.findByIdAndUpdate(
        agent.user._id,
        {
          slug: 'test-agent-slug',
        },
        { new: true }
      )) as User

      const clients = []
      for (let i = 0; i < 11; i++) {
        const client = await generateRandomUser()
        await UserModel.findByIdAndUpdate(client.user._id, {
          agentV2: agent.user._id,
        })

        clients.push(client)
      }

      const response = await client(agent.tokens.accessToken).agentsClients({
        slug: agent.user.slug as string,
        first: 10,
      })

      assert.strictEqual(response.agentsClients.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.agentsClients.pageInfo.hasNextPage, true)
      assert.strictEqual(response.agentsClients.edges.length, 10)
      assert.strictEqual(response.agentsClients.edges[0].node.name, clients[10].user.name)

      const response2 = await client(agent.tokens.accessToken).agentsClients({
        slug: agent.user.slug as string,
        first: 10,
        after: response.agentsClients.pageInfo.endCursor,
      })

      assert.strictEqual(response2.agentsClients.pageInfo.hasPreviousPage, true)
      assert.strictEqual(response2.agentsClients.pageInfo.hasNextPage, false)
      assert.strictEqual(response2.agentsClients.edges.length, 1)
      assert.strictEqual(response2.agentsClients.edges[0].node.name, clients[0].user.name)
    })
  })
})
