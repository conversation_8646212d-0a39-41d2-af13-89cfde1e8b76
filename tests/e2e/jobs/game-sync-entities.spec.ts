import assert from 'assert'

import { logger } from '../../../src/container'
import { gameSyncEntities } from '../../../src/jobs/game-sync-entities'
import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../../src/modules/stats/models/game/game'
import { LeagueModel } from '../../../src/modules/stats/models/league'
import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { TeamModel } from '../../../src/modules/stats/models/team'
import {
  GameEventType,
  LeagueGenderCategory,
  LeagueLevel,
  LeagueType,
} from '../../graphql/components'
import { generateDiscoverUrl, generateRandomGameStats } from '../../utils/stats'
import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
} from '../../utils/users'

describe('Sync game entities', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await GameModel.deleteMany({})
    await TeamModel.deleteMany({})
    await LeagueModel.deleteMany({})
    await OrganizationModel.deleteMany({})
  })

  it('should sync game which were not synced before', async () => {
    const league = await generateRandomLeague(
      'Czech U15',
      'CZ',
      'u15',
      LeagueGenderCategory.Men,
      LeagueLevel.Youth,
      LeagueType.League,
      true,
      true,
      [{ name: 'Extraliga 9. trid', slug: 'extraliga-9-trid' }]
    )

    const homeOrganization = await generateRandomOrganization(
      'HC Litvinov',
      'CZ',
      true,
      'hc-litvinov'
    )
    const awayOrganization = await generateRandomOrganization('HC Pisek', 'CZ', true, 'hc-pisek')

    const homeTeam = await generateRandomTeam({
      name: 'HC Litvinov U15',
      organization: homeOrganization.id,
      shortNameSlug: 'u15',
      alternativeNames: [],
      leagues: [league.id],
    })
    const awayTeam = await generateRandomTeam({
      name: 'HC Pisek U15',
      organization: awayOrganization.id,
      shortNameSlug: 'u15',
      alternativeNames: [],
      leagues: [league.id],
    })

    const url = await generateDiscoverUrl()

    await generateRandomGameStats({
      gameStatsPayload: {
        externalId: '123',
        discoveredUrl: url,
        date: new Date(),
        externalInfo: {
          homeTeamName: 'HC Litvinov',
          awayTeamName: 'HC Pisek',
          leagueName: 'Extraliga 9. trid',
        },
        homeTeam: null,
        awayTeam: null,
        events: [],
        homeTeamRoster: [],
        awayTeamRoster: [],
      },
    })

    await gameSyncEntities({ logger })

    const game = await GameModel.findOne({ externalId: '123' })

    assert.strictEqual(game?.homeTeam?.toString(), homeTeam.id.toString())
    assert.strictEqual(game?.awayTeam?.toString(), awayTeam.id.toString())
    assert.strictEqual(game?.league?.toString(), league.id.toString())
  })

  it('should sync game which were not synced before', async () => {
    const league = await generateRandomLeague(
      'Czech U15',
      'CZ',
      'u15',
      LeagueGenderCategory.Men,
      LeagueLevel.Youth,
      LeagueType.League,
      true,
      true,
      [{ name: 'Extraliga 9. trid', slug: 'extraliga-9-trid' }]
    )

    const homeOrganization = await generateRandomOrganization(
      'HC Litvinov',
      'CZ',
      true,
      'hc-litvinov'
    )
    const awayOrganization = await generateRandomOrganization('HC Pisek', 'CZ', true, 'hc-pisek')

    const homeTeam = await generateRandomTeam({
      name: 'HC Litvinov U15',
      organization: homeOrganization.id,
      shortNameSlug: 'hlu15',
      alternativeNames: ['hlu15'],
      country: 'CZ',
      leagues: [league.id],
    })
    const awayTeam = await generateRandomTeam({
      name: 'HC Pisek U15',
      organization: awayOrganization.id,
      shortNameSlug: 'hpu15',
      alternativeNames: ['hpu15'],
      country: 'CZ',
      leagues: [league.id],
    })

    const url = await generateDiscoverUrl()

    await generateRandomGameStats({
      gameStatsPayload: {
        externalId: '123',
        discoveredUrl: url,
        date: new Date(),
        externalInfo: {
          homeTeamName: 'hlu15',
          awayTeamName: 'hpu15',
          leagueName: 'u15',
        },
        events: [
          {
            time: '00:00',
            type: GameEventType.Goal,
            externalInfo: {
              teamName: 'hlu15',
              playerName: 'John Doe',
              playerExternalId: '123',
            },
            player: null,
          },
        ],
        homeTeamRoster: [],
        awayTeamRoster: [],
        homeTeam: null,
        awayTeam: null,
      },
    })

    await gameSyncEntities({ logger })

    const game = await GameModel.findOne({ externalId: '123' })

    assert.ok(game?.homeTeam, 'Home team should be set')
    assert.ok(game?.awayTeam, 'Away team should be set')
    assert.ok(game?.league, 'League should be set')

    // Check that the teams and league are set correctly
    assert.strictEqual(game?.homeTeam?.toString(), homeTeam.id.toString())
    assert.strictEqual(game?.awayTeam?.toString(), awayTeam.id.toString())
    assert.strictEqual(game?.league?.toString(), league.id.toString())
  })
})
