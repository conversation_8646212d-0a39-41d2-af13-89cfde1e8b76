import assert from 'assert'
import axios from 'axios'
import sinon from 'sinon'
import Container from 'typedi'

import { logger } from '../../../src/container'
import { removeDeletedUsers } from '../../../src/jobs/remove-deleted-users'
import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { UserInfoModel } from '../../../src/modules/stats/models/userInfo'
import { UserStatsModel } from '../../../src/modules/stats/models/userStats'
import { UserModel, WallVideoModel } from '../../../src/modules/user/models'
import { BoostModel } from '../../../src/modules/user/models/boost'
import { NotificationModel } from '../../../src/modules/user/models/notification'
import { CreationSource, Role } from '../../../src/modules/user/models/user'
import { UserSubscriptionModel } from '../../../src/modules/user/models/userSubscription'
import { UserViewModel } from '../../../src/modules/user/models/userViews'
import { UserViewAggregatedModel } from '../../../src/modules/user/models/userViewsAggregated'
import { S3Service } from '../../../src/services/s3'
import { generateDiscoverUrl } from '../../utils/stats'
import { generateRandomUser } from '../../utils/users'
import { generateWallVideo } from '../../utils/wallVideos'
import { streamFeedRemoveActivityStub, streamFeedUnfollowStub } from '..'

describe('Remove deleted users job', () => {
  beforeEach(async () => {
    await UserModel.deleteMany({})
    await CrawlerDomainConfigModel.deleteMany({})
    await UserInfoModel.deleteMany({})

    // because of EP call
    sinon.stub(axios, 'post').rejects()
  })

  it('should remove users marked for deletion and cleanup account assets', async () => {
    const deletedUser = await generateRandomUser()

    const DAY = 1000 * 60 * 60 * 24
    await UserModel.findOneAndUpdate(
      { _id: deletedUser.user._id },
      {
        $set: {
          markedForDeletion: true,
          updatedAt: new Date(Date.now() - 2 * DAY),
        },
      },
      {
        timestamps: false, // disable auto update of timestamps
      }
    )

    // add wall video
    await generateWallVideo({
      user: { _id: deletedUser.user._id },
      path: 'path',
    })

    // add followers
    const follower = await generateRandomUser()
    await UserSubscriptionModel.create({
      user: follower.user._id,
      follower: deletedUser.user._id,
    })

    // follow someone
    const following = await generateRandomUser()
    await UserSubscriptionModel.create({
      user: deletedUser.user._id,
      follower: following.user._id,
    })

    const s3Service = Container.get(S3Service)
    const s3ServiceStub = sinon.stub(s3Service, 'emptyS3Directory')
    Container.set('s3.service', s3ServiceStub)

    const userModelStub = sinon.stub(UserModel, 'deleteOne').resolves()
    const userViewAggregatedModelStub = sinon.stub(UserViewAggregatedModel, 'deleteMany').resolves()
    const userViewModelStub = sinon.stub(UserViewModel, 'deleteMany').resolves()
    const wallVideoModelStub = sinon.stub(WallVideoModel, 'deleteMany').resolves()
    const userStatsModelStub = sinon.stub(UserStatsModel, 'deleteMany').resolves()
    const userSubscriptionModelStub = sinon.stub(UserSubscriptionModel, 'deleteMany').resolves()
    const notificationModelStub = sinon.stub(NotificationModel, 'deleteMany').resolves()
    const boostModelStub = sinon.stub(BoostModel, 'deleteMany').resolves()

    await removeDeletedUsers({ logger })

    // called twice, follower + following
    assert.strictEqual(streamFeedUnfollowStub.calledTwice, true)
    assert.strictEqual(streamFeedRemoveActivityStub.calledOnce, true)

    assert.strictEqual(userModelStub.calledOnce, true)
    assert.strictEqual(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (userModelStub.firstCall.args[0] as any)._id.toString(),
      deletedUser.user._id.toString()
    )

    assert.strictEqual(userViewAggregatedModelStub.calledOnce, true)
    assert.strictEqual(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (userViewAggregatedModelStub.firstCall.args[0] as any).user.toString(),
      deletedUser.user._id.toString()
    )

    assert.strictEqual(userViewModelStub.calledOnce, true)
    assert.strictEqual(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (userViewModelStub.firstCall.args[0] as any).user.toString(),
      deletedUser.user._id.toString()
    )

    assert.strictEqual(wallVideoModelStub.calledOnce, true)
    assert.strictEqual(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (wallVideoModelStub.firstCall.args[0] as any).user.toString(),
      deletedUser.user._id.toString()
    )

    assert.strictEqual(userStatsModelStub.calledOnce, true)
    assert.strictEqual(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (userStatsModelStub.firstCall.args[0] as any).user.toString(),
      deletedUser.user._id.toString()
    )

    assert.strictEqual(userSubscriptionModelStub.calledOnce, true)
    assert.deepEqual(userSubscriptionModelStub.firstCall.args[0], {
      $or: [{ user: deletedUser.user._id }, { follower: deletedUser.user._id }],
    })

    assert.strictEqual(notificationModelStub.calledOnce, true)
    assert.strictEqual(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (notificationModelStub.firstCall.args[0] as any).user.toString(),
      deletedUser.user._id.toString()
    )

    assert.strictEqual(boostModelStub.calledOnce, true)
    assert.strictEqual(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (boostModelStub.firstCall.args[0] as any).user.toString(),
      deletedUser.user._id.toString()
    )

    userModelStub.restore()
    userViewAggregatedModelStub.restore()
    userViewModelStub.restore()
    wallVideoModelStub.restore()
    userStatsModelStub.restore()
    userSubscriptionModelStub.restore()
    notificationModelStub.restore()
    boostModelStub.restore()

    Container.set('s3.service', s3Service)
  })

  it('should remove user, but create shadow account if role is Player and has external connections', async () => {
    const discoveredUrl = await generateDiscoverUrl()

    const deletedUser = await generateRandomUser({
      role: Role.PLAYER,
      possibleYearsOfBirth: [1990, 1991],
      externalConnections: [
        {
          externalId: 'externalId',
          crawlerDomainConfig: (await discoveredUrl).crawlerDomainConfig,
        },
      ],
    })

    await UserInfoModel.create({
      name: deletedUser.user.name,
      slug: deletedUser.user.slug,
      country: deletedUser.user.country,
      possibleYearsOfBirth: deletedUser.user.possibleYearsOfBirth,
      externalId: 'externalId',
      discoveredUrl: discoveredUrl._id,
      crawlerDomainConfig: discoveredUrl.crawlerDomainConfig,
    })

    await UserModel.findOneAndUpdate(
      { _id: deletedUser.user._id },
      {
        $set: {
          markedForDeletion: true,
        },
      }
    )

    const s3Service = Container.get(S3Service)
    const s3ServiceStub = sinon.stub(s3Service, 'emptyS3Directory')
    Container.set('s3.service', s3ServiceStub)

    await removeDeletedUsers({ logger })

    const shadowUser = await UserModel.findOne()

    assert.strictEqual(shadowUser?.role, Role.SHADOW_PLAYER)
    assert.strictEqual(shadowUser?.externalConnections.length, 1)
    assert.strictEqual(
      shadowUser?.externalConnections[0].externalId,
      deletedUser.user.externalConnections[0].externalId
    )
    assert.strictEqual(shadowUser.creationSource, CreationSource.AccountDeletion)

    Container.set('s3.service', s3Service)
  })
})
