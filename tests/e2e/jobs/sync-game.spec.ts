import assert from 'assert'
import sinon from 'sinon'

import { logger } from '../../../src/container'
import { syncGames } from '../../../src/jobs/sync-games'
import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../../src/modules/stats/models/game/game'
import { GameEventType } from '../../../src/modules/stats/models/game/gameEvent'
import { LeagueModel } from '../../../src/modules/stats/models/league'
import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { UserInfoModel } from '../../../src/modules/stats/models/userInfo'
import { UserModel } from '../../../src/modules/user/models'
import { Platform } from '../../../src/modules/user/models/user'
import { generateDiscoverUrl, generateRandomGameStats } from '../../utils/stats'
import { generateRandomUser } from '../../utils/users'
import { snsClientStub } from '..'

describe('Sync game job', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await GameModel.deleteMany({})
    await UserModel.deleteMany({})
    await UserInfoModel.deleteMany({})
    await OrganizationModel.deleteMany({})
    await LeagueModel.deleteMany({})
    await TeamModel.deleteMany({})
  })

  it('should sync game which were not synced before', async () => {
    const url = await generateDiscoverUrl()

    const userScraper1 = await generateRandomUser({
      firstname: 'Game',
      lastname: 'Scraper',
      dateOfBirth: new Date('2010-01-01'),
      registeredDevices: [
        {
          arn: 'test',
          platform: Platform.IOS,
          token: 'test-token',
        },
      ],
      externalConnections: [{ externalId: '1', crawlerDomainConfig: url.crawlerDomainConfig }],
    })

    const userScraper2 = await generateRandomUser({
      firstname: 'Game',
      lastname: 'Scraper 2',
      dateOfBirth: new Date('2011-01-01'),
      registeredDevices: [
        {
          arn: 'test',
          platform: Platform.IOS,
          token: 'test-token',
        },
      ],
      externalConnections: [{ externalId: '123', crawlerDomainConfig: url.crawlerDomainConfig }],
    })

    await UserInfoModel.create({
      crawlerDomainConfig: url.crawlerDomainConfig,
      externalId: '1',
      name: 'Game Scraper',
      possibleYearsOfBirth: [2011, 2010],
    })

    await UserInfoModel.create({
      crawlerDomainConfig: url.crawlerDomainConfig,
      externalId: '123',
      name: 'Game Scraper 2',
      possibleYearsOfBirth: [2011, 2010],
    })

    // this user should not be synced, because there is external info, but year of birth does not match
    await generateRandomUser({
      firstname: 'Game',
      lastname: 'Scraper 3',
      dateOfBirth: new Date('2000-01-01'),
    })

    await UserInfoModel.create({
      crawlerDomainConfig: url.crawlerDomainConfig,
      externalId: '1234',
      name: 'Game Scraper 3',
      possibleYearsOfBirth: [2010, 2009],
      age: 14,
    })

    await generateRandomGameStats({
      gameStatsPayload: {
        externalId: '123',
        discoveredUrl: url,
        date: new Date(Date.now() - 24 * 60 * 60 * 1000),
        externalInfo: {
          homeTeamName: 'Home Team',
          awayTeamName: 'Away Team',
          leagueName: 'Test League',
        },
        events: [
          {
            type: GameEventType.Goal,
            time: '01:02',
            externalInfo: {
              teamName: 'Home Team',
              playerName: 'Test Player',
              playerExternalId: '123',
            },
            player: null,
          },
        ],
        homeTeamRoster: [
          {
            externalInfo: {
              playerName: 'Patrick Smith',
            },
            goals: 0,
            assists: 2,
            points: 2,
            player: null,
          },
          {
            externalInfo: {
              playerExternalId: '123',
              playerName: 'Game Scraper 2',
            },
            goals: 0,
            assists: 2,
            points: 2,
            player: null,
          },
          {
            externalInfo: {
              playerExternalId: '1234',
              playerName: 'Game Scraper 3',
            },
            goals: 2,
            assists: 2,
            points: 4,
            player: null,
          },
        ],
        awayTeamRoster: [
          {
            externalInfo: {
              playerExternalId: '1',
              playerName: 'Game Scraper',
            },
            goals: 1,
            assists: 2,
            points: 3,
            player: null,
          },
        ],
      },
    })

    await syncGames({ logger })

    sinon.assert.calledTwice(snsClientStub.publish)
    const firstCallArgs = JSON.parse(snsClientStub.publish.args[0][0].Message)
    const secondCallArgs = JSON.parse(snsClientStub.publish.args[1][0].Message)

    sinon.assert.match(
      firstCallArgs.default,
      'Congrats! You scored 1 goal against Home Team. Upload a clip so everyone can see you scoring.'
    )

    sinon.assert.match(
      secondCallArgs.default,
      'Game, you made a difference against Away Team. Keep it up.'
    )

    const game = await GameModel.findOne({ externalId: '123' })

    assert.strictEqual(game?.syncedAt !== null, true)

    assert.strictEqual(game?.awayTeamRoster[0].player?.toString(), userScraper1.user._id.toString())
    assert.strictEqual(game?.homeTeamRoster[1].player?.toString(), userScraper2.user._id.toString())

    assert.ok(game.streamFeedActivityId)
  })

  it('should be able to sync also by externalConnections info, not only by slug and year of birth', async () => {
    const url = await generateDiscoverUrl()
    const user = await generateRandomUser({
      firstname: 'John',
      lastname: 'Orange',
      registeredDevices: [
        {
          arn: 'test',
          platform: Platform.IOS,
          token: 'test-token',
        },
      ],
      externalConnections: [{ crawlerDomainConfig: url.crawlerDomainConfig, externalId: '123' }],
    })

    await generateRandomGameStats({
      gameStatsPayload: {
        externalId: '123',
        discoveredUrl: url,
        date: new Date(Date.now() - 24 * 60 * 60 * 1000),
        externalInfo: {
          homeTeamName: 'Home Team',
          awayTeamName: 'Away Team',
          leagueName: 'Test League',
        },
        events: [
          {
            type: GameEventType.Goal,
            time: '01:02',
            externalInfo: {
              teamName: 'Home Team',
              playerName: 'Test Player',
              playerExternalId: '123',
            },
            player: null,
          },
        ],
        homeTeamRoster: [
          {
            externalInfo: {
              playerExternalId: '123',
              playerName: 'John Patrick Orange', // player can have different name, but still matches by id
            },
            goals: 0,
            assists: 2,
            points: 2,
            player: null,
          },
        ],
        awayTeamRoster: [],
      },
    })

    await syncGames({ logger })

    sinon.assert.calledOnce(snsClientStub.publish)
    const firstCallArgs = JSON.parse(snsClientStub.publish.args[0][0].Message)

    sinon.assert.match(
      firstCallArgs.default,
      'John, you made a difference against Away Team. Keep it up.'
    )

    const game = await GameModel.findOne({ externalId: '123' })

    assert.strictEqual(game?.syncedAt !== null, true)
    assert.strictEqual(game?.homeTeamRoster[0].player?.toString(), user.user._id.toString())

    assert.strictEqual(game?.streamFeedActivityId, undefined)
  })
})
