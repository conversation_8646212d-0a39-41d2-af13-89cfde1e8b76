import assert from 'assert'

import { logger } from '../../../src/container'
import { leagueStatsSyncEntities } from '../../../src/jobs/league-stats-sync-entities'
import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { LeagueModel } from '../../../src/modules/stats/models/league'
import { LeagueStatsModel } from '../../../src/modules/stats/models/leagueStats/leagueStats'
import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { LeagueGenderCategory, LeagueLevel, LeagueType } from '../../graphql/components'
import { generateDiscoverUrl, generateLeagueStat } from '../../utils/stats'
import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
} from '../../utils/users'
import { leagueStatsMock } from '../services/leagueStatsSyncer.helper'

describe('Sync league stats entities', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await LeagueStatsModel.deleteMany({})

    await OrganizationModel.deleteMany({})
    await LeagueModel.deleteMany({})
    await TeamModel.deleteMany({})
  })

  it('should sync league stats which were not synced before', async () => {
    const league = await generateRandomLeague(
      'Czech U15',
      'CZ',
      'u15',
      LeagueGenderCategory.Men,
      LeagueLevel.Youth,
      LeagueType.League,
      true,
      true,
      [{ name: 'Extraliga 9. trid', slug: 'extraliga-9-trid' }]
    )

    const org = await generateRandomOrganization('HC Litvinov', 'CZ', true, 'hc-litvinov')

    const team = await generateRandomTeam({
      name: 'HC Litvinov U15',
      organization: org.id,
      shortNameSlug: 'u15',
      alternativeNames: [],
      leagues: [league.id],
    })

    const url = await generateDiscoverUrl()

    await generateLeagueStat({
      discoveredUrl: url,
      leagueStats: {
        ...leagueStatsMock.leagueStats[0],
        externalInfo: {
          teamName: 'HC Litvinov',
          leagueName: 'Extraliga 9. trid',
        },
      },
    })

    await leagueStatsSyncEntities({ logger })

    const leagueStats = await LeagueStatsModel.find()

    assert.strictEqual(leagueStats[0]?.team?.toString(), team.id.toString())
    assert.strictEqual(leagueStats[0]?.league?.toString(), league.id.toString())
  })
})
