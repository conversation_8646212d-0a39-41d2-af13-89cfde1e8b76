import assert from 'assert'
import sinon from 'sinon'

import { logger } from '../../../src/container'
import { sendBatchNotification } from '../../../src/jobs/send-batch-notification'
import { BatchNotificationModel } from '../../../src/modules/admin/models/batchNotification'
import { BatchNotificationState } from '../../../src/modules/admin/types/batchNotification'
import { UserModel } from '../../../src/modules/user/models'
import { Platform, Role } from '../../../src/modules/user/models/user'
import { generateRandomUser } from '../../utils/users'
import { pushNotificationServiceStub } from '..'

describe('Cron Job: sendBatchNotification', () => {
  beforeEach(() => {
    sinon.restore()
    UserModel.deleteMany({})
    BatchNotificationModel.deleteMany({})
  })

  before(async () => {
    await UserModel.deleteMany({})
    await BatchNotificationModel.deleteMany({})
  })

  after(async () => {
    await UserModel.deleteMany({})
    await BatchNotificationModel.deleteMany({})
  })

  it('should process scheduled batch notifications and send push notifications', async () => {
    const userCreatePromises = []
    for (let i = 0; i < 700; i++) {
      userCreatePromises.push(
        UserModel.create({
          role: Role.PLAYER,
          email: `email+${i}@graet.com`,
          registeredDevices: [{ token: `token${i}` }],
        })
      )
    }

    await BatchNotificationModel.create({
      title: 'Test Batch Notification',
      message: 'Test Batch Message',
      type: 'test',
      state: BatchNotificationState.SCHEDULED,
      filter: { role: Role.PLAYER },
    })

    await Promise.all(userCreatePromises)

    const clock = sinon.useFakeTimers({
      advanceTimeDelta: 1,
      toFake: ['setTimeout'],
    })

    const interval = setInterval(() => clock.runAllAsync(), 1)

    await sendBatchNotification({ logger })

    assert.strictEqual(pushNotificationServiceStub.sendPushNotification.callCount, 700)

    clock.restore()
    clearInterval(interval)
  })

  it('Should not process batch notifications if none are scheduled', async () => {
    const batchFindStub = sinon.stub(BatchNotificationModel, 'findOne').resolves(null)
    const loggerSpy = sinon.spy(logger, 'info')

    await sendBatchNotification({ logger })

    assert.strictEqual(pushNotificationServiceStub.sendPushNotification.callCount, 0)
    assert.strictEqual(
      loggerSpy.calledWith('No scheduled batch notifications found, skipping...'),
      true
    )

    batchFindStub.restore()
    loggerSpy.restore()
  })

  it('Should log an error when batch notification processing fails', async () => {
    const batchFindStub = sinon.stub(BatchNotificationModel, 'findOne').resolves({
      _id: 'batch123',
      filter: { role: Role.PLAYER },
    })

    const batchUpdateStub = sinon
      .stub(BatchNotificationModel, 'updateOne')
      .rejects(new Error('DB update failed'))

    const loggerSpy = sinon.spy(logger, 'error')

    await sendBatchNotification({ logger })

    assert.strictEqual(loggerSpy.calledWithMatch('Error processing batch notification'), true)

    batchFindStub.restore()
    batchUpdateStub.restore()
    loggerSpy.restore()
  })

  it('Should log an error when sending notification fails', async () => {
    const loggerSpy = sinon.spy(logger, 'error')
    UserModel.create({
      role: Role.PLAYER,
      email: `<EMAIL>`,
    })

    await BatchNotificationModel.create({
      title: 'Test Batch Notification failToSend',
      message: 'Test Batch Message failToSend',
      type: 'test',
      state: BatchNotificationState.SCHEDULED,
      filter: { role: Role.PLAYER },
    })

    const clock = sinon.useFakeTimers({
      advanceTimeDelta: 1,
      toFake: ['setTimeout'],
    })

    const interval = setInterval(() => clock.runAllAsync(), 1)

    sinon.replace(
      pushNotificationServiceStub,
      'sendPushNotification',
      sinon.stub().rejects(new Error('Failed to send'))
    )
    await sendBatchNotification({ logger })

    assert.strictEqual(loggerSpy.calledWithMatch('Failed to send notification to user'), true)

    clock.restore()
    clearInterval(interval)
  })

  it('Should stop processing if batch notification is cancelled', async () => {
    const batch = await BatchNotificationModel.create({
      title: 'Test Batch Notification cancelled',
      message: 'Test Batch Message cancelled',
      type: 'test',
      state: BatchNotificationState.SCHEDULED,
      filter: { role: Role.PLAYER },
    })

    const userCreatePromises = []
    for (let i = 0; i < 501; i++) {
      userCreatePromises.push(
        UserModel.create({
          role: Role.PLAYER,
          email: `email_cancel+${i}@graet.com`,
          registeredDevices: [{ token: `token${i}` }],
        })
      )
    }
    await Promise.all(userCreatePromises)

    const clock = sinon.useFakeTimers({
      advanceTimeDelta: 1,
      toFake: ['setTimeout'],
    })

    const interval = setInterval(() => clock.runAllAsync(), 1)

    const batchFindStub = sinon.stub(BatchNotificationModel, 'findById')
    batchFindStub.onFirstCall().resolves(batch)
    batchFindStub
      .onSecondCall()
      .resolves({ ...batch.toObject(), state: BatchNotificationState.CANCELED })

    const loggerSpy = sinon.spy(logger, 'info')

    await sendBatchNotification({ logger })

    assert.strictEqual(
      loggerSpy.calledWithMatch(`Batch notification ${batch._id} was cancelled, stopping...`),
      true
    )

    batchFindStub.restore()
    loggerSpy.restore()

    clock.restore()
    clearInterval(interval)
  })

  it('should match users with exact numberOfVideos in the list', async () => {
    for (let i = 0; i < 3; i++) {
      await generateRandomUser({
        registeredDevices: [{ token: `token ${i}`, platform: Platform.IOS, arn: 'arn' }],
        numberOfVideos: 2 + i,
      })
    }

    await BatchNotificationModel.create({
      title: 'numberOfVideos filter',
      message: 'message',
      type: 'test',
      state: BatchNotificationState.SCHEDULED,
      filter: { role: Role.PLAYER, numberOfVideos: ['4', '2'] },
    })

    const clock = sinon.useFakeTimers({
      advanceTimeDelta: 1,
      toFake: ['setTimeout'],
    })
    const interval = setInterval(() => clock.runAllAsync(), 1)

    await sendBatchNotification({ logger })
    assert.strictEqual(pushNotificationServiceStub.sendPushNotification.callCount, 2)

    clock.restore()
    clearInterval(interval)
  })

  it('should match user witch exact yearOfBirth in list', async () => {
    for (let i = 0; i < 3; i++) {
      await generateRandomUser({
        registeredDevices: [{ token: `token ${i}`, platform: Platform.IOS, arn: 'arn' }],
        dateOfBirth: new Date(`200${i}-01-01`),
      })
    }

    await BatchNotificationModel.create({
      title: 'yearOfBirth filter',
      message: 'message',
      type: 'test',
      state: BatchNotificationState.SCHEDULED,
      filter: { role: Role.PLAYER, yearOfBirth: [2001, 2002] },
    })
    const clock = sinon.useFakeTimers({
      advanceTimeDelta: 1,
      toFake: ['setTimeout'],
    })
    const interval = setInterval(() => clock.runAllAsync(), 1)
    await sendBatchNotification({ logger })
    assert.strictEqual(pushNotificationServiceStub.sendPushNotification.callCount, 2)
    clock.restore()
    clearInterval(interval)
  })

  it('should match users based on combined filters', async () => {
    await generateRandomUser({
      role: Role.PLAYER,
      firstname: 'Tomas',
      registeredDevices: [{ token: 'token1', platform: Platform.IOS, arn: 'arn' }],
      numberOfVideos: 2,
      country: 'CZ',
      dateOfBirth: new Date('2001-01-01'),
    })

    await generateRandomUser({
      role: Role.PLAYER,
      firstname: 'Tomas',
      registeredDevices: [{ token: 'token2', platform: Platform.IOS, arn: 'arn' }],
      numberOfVideos: 8,
      country: 'SK',
      dateOfBirth: new Date('2001-01-01'),
    })

    await generateRandomUser({
      role: Role.PLAYER,
      firstname: 'Petr',
      registeredDevices: [{ token: 'token3', platform: Platform.ANDROID, arn: 'arn' }],
      numberOfVideos: 10,
      country: 'SK',
      dateOfBirth: new Date('2002-01-01'),
    })

    await generateRandomUser({
      role: Role.PLAYER,
      firstname: 'Adam',
      registeredDevices: [{ token: 'token4', platform: Platform.ANDROID, arn: 'arn' }],
      numberOfVideos: 5,
      country: 'DE',
      dateOfBirth: new Date('2002-01-01'),
    })

    await BatchNotificationModel.create({
      title: 'combined filters',
      message: 'test',
      type: 'test',
      state: BatchNotificationState.SCHEDULED,
      filter: {
        role: Role.PLAYER,
        firstNameInitials: ['T', 'P'],
        nationalities: ['SK'],
        numberOfVideos: ['8'],
        yearOfBirth: [2001, 2002],
      },
    })

    const clock = sinon.useFakeTimers({ advanceTimeDelta: 1, toFake: ['setTimeout'] })
    const interval = setInterval(() => clock.runAllAsync(), 1)

    await sendBatchNotification({ logger })

    assert.strictEqual(pushNotificationServiceStub.sendPushNotification.callCount, 2)
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(0).args[0].firstname,
      'Tomas'
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(0).args[0].numberOfVideos,
      8
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(0).args[0].country,
      'SK'
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(0).args[0].dateOfBirth.getFullYear(),
      2001
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(0).args[0].registeredDevices[0]
        .platform,
      Platform.IOS
    )

    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(1).args[0].firstname,
      'Petr'
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(1).args[0].numberOfVideos,
      10
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(1).args[0].country,
      'SK'
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(1).args[0].dateOfBirth.getFullYear(),
      2002
    )
    assert.strictEqual(
      pushNotificationServiceStub.sendPushNotification.getCall(1).args[0].registeredDevices[0]
        .platform,
      Platform.ANDROID
    )

    clock.restore()
    clearInterval(interval)
  })
})
