import sinon from 'sinon'
import Container from 'typedi'

import { logger } from '../../../src/container'
import { generateSitemaps } from '../../../src/jobs/generate-sitemaps'
import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { UserModel } from '../../../src/modules/user/models'
import { AgencyModel } from '../../../src/modules/user/models/agency'
import { S3Service } from '../../../src/services/s3'
import {
  generateAgencies,
  generatePlayers,
  generateTeams,
} from '../services/sitemap.generator.spec'

describe('Generate sitemap job', () => {
  after(async () => {
    await UserModel.deleteMany({})
    await AgencyModel.deleteMany({})
    await TeamModel.deleteMany({})
    await OrganizationModel.deleteMany({})
  })

  beforeEach(async () => {
    await UserModel.deleteMany({})
    await AgencyModel.deleteMany({})
    await TeamModel.deleteMany({})
    await OrganizationModel.deleteMany({})
  })

  it('should generate sitempas files', async () => {
    await generatePlayers()
    await generateAgencies()
    await generateTeams()

    const s3Service = Container.get(S3Service)
    const uploadFromBuffer = sinon.stub(s3Service, 'uploadFromBuffer')
    const downloadToBuffer = sinon.stub(s3Service, 'downloadToBuffer')
    Container.set('s3.service', {
      uploadFromBuffer,
      downloadToBuffer,
    })

    await generateSitemaps({ logger })

    const s3ServiceStub: S3Service = Container.get('s3.service')

    sinon.assert.calledThrice(s3ServiceStub.uploadFromBuffer as sinon.SinonSpy)

    Container.set('s3.service', s3Service)
  }).timeout(60 * 10000)
})
