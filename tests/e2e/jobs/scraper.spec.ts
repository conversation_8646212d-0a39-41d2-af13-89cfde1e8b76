import assert from 'assert'

import { logger } from '../../../src/container'
import { rescrapeScheduled } from '../../../src/jobs/rescrape-scheduled'
import { scraper } from '../../../src/jobs/scraper'
import {
  CrawlerDomainConfigModel,
  CrawlerEntity,
  FetchType,
} from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../../src/modules/stats/models/game/game'
import { LeagueModel } from '../../../src/modules/stats/models/league'
import {
  generateLeagueStatsKey,
  LeagueStatsModel,
} from '../../../src/modules/stats/models/leagueStats/leagueStats'
import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { Team, TeamModel } from '../../../src/modules/stats/models/team'
import { UserInfoModel } from '../../../src/modules/stats/models/userInfo'
import { UserStatsModel } from '../../../src/modules/stats/models/userStats'
import { UserStatsResponseFormat } from '../../../src/modules/stats/services/scraper/response'
import { UserModel } from '../../../src/modules/user/models'
import { Role } from '../../../src/modules/user/models/user'
import { generateDiscoverUrl } from '../../utils/stats'
import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from '../../utils/users'
import { checkGameStats, gameStatsMock, userInfoMock } from '../services/gameSyncer.helper'
import { leagueStatsMock } from '../services/leagueStatsSyncer.helper'
import { axiosGetStub, evaluateStub } from '..'

describe('Scraper job', () => {
  describe('Player scraper', () => {
    beforeEach(async () => {
      await CrawlerDomainConfigModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
      await GameModel.deleteMany({})
      await UserModel.deleteMany({})
      await UserInfoModel.deleteMany({})
    })

    it('should go through discovered URLs for user infos which are not scraped and create shadow user', async () => {
      await generateDiscoverUrl({ entity: CrawlerEntity.PLAYER })

      evaluateStub.resolves(userInfoMock)

      await scraper({ logger })

      const userInfo = await UserInfoModel.findOne({ externalId: '123' })
      const discoveredUrl = await DiscoveredUrlModel.findOne({ externalId: '123' })

      assert.strictEqual(userInfo?.externalId, '123')
      assert.strictEqual(userInfo?.name, 'John Doe')
      assert.deepStrictEqual(userInfo?.possibleYearsOfBirth, [2000, 1999])

      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeAttempts, 1)
      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeSuccessful, true)

      const shadowUser = await UserModel.findOne()

      assert.strictEqual(shadowUser?.name, 'John Doe')
      assert.strictEqual(shadowUser?.slug, 'john-doe')
      assert.strictEqual(shadowUser?.shadowSlug, 'john-doe')
      assert.strictEqual(shadowUser?.country, 'US')
      assert.strictEqual(shadowUser?.role, Role.SHADOW_PLAYER)
    })

    it('should go through discovered URLs for user infos which are not scraped and create shadow user with axios fetcher', async () => {
      await generateDiscoverUrl({
        entity: CrawlerEntity.PLAYER,
        fetchType: FetchType.Axios,
        extractionScript: `;(async function () {
          const userInfo = {
            id: '123',
            name: 'John Doe',
            country: await window.getCountryCode('United States'),
          }

          return { userInfo }
        })()`,
      })

      axiosGetStub.reset()
      // Mock the axios response to include the userInfo data
      axiosGetStub.resolves({
        data: ``,
        status: 200,
      })

      await scraper({ logger })

      const userInfo = await UserInfoModel.findOne({ externalId: '123' })
      const discoveredUrl = await DiscoveredUrlModel.findOne({ externalId: '123' })

      assert.strictEqual(userInfo?.externalId, '123')
      assert.strictEqual(userInfo?.name, 'John Doe')
      assert.strictEqual(userInfo?.country, 'US')

      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeAttempts, 1)
      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeSuccessful, true)

      const shadowUser = await UserModel.findOne()

      assert.strictEqual(shadowUser?.name, 'John Doe')
      assert.strictEqual(shadowUser?.slug, 'john-doe')
      assert.strictEqual(shadowUser?.shadowSlug, 'john-doe')
      assert.strictEqual(shadowUser?.country, 'US')
      assert.strictEqual(shadowUser?.role, Role.SHADOW_PLAYER)
    })

    it('should scrape player and add external connections if user already exists', async () => {
      const discoveredUrl = await generateDiscoverUrl({ entity: CrawlerEntity.PLAYER })

      evaluateStub.resolves(userInfoMock)

      await generateRandomUser({
        name: 'John Doe',
        slug: 'john-doe',
        country: 'US',
        possibleYearsOfBirth: [2000, 1999],
        role: Role.PLAYER,
      })

      await scraper({ logger })

      const user = await UserModel.findOne({ slug: 'john-doe' })

      assert.strictEqual(user?.externalConnections.length, 1)
      assert.strictEqual(user?.externalConnections[0].externalId, '123')
      assert.strictEqual(
        user?.externalConnections[0].crawlerDomainConfig._id.toString(),
        discoveredUrl.crawlerDomainConfig._id.toString()
      )
    })

    it('should scrape player and add external connections if user already exists with shadow role - without duplication', async () => {
      const discoveredUrl = await generateDiscoverUrl({ entity: CrawlerEntity.PLAYER })

      evaluateStub.resolves(userInfoMock)

      await UserModel.create({
        name: 'John Doe',
        slug: 'john-doe',
        shadowSlug: 'john-doe',
        country: 'US',
        possibleYearsOfBirth: [2000, 1999],
        role: Role.PLAYER,
        externalConnections: [
          {
            externalId: '123',
            crawlerDomainConfig: discoveredUrl.crawlerDomainConfig._id,
          },
        ],
      })

      await scraper({ logger })

      const user = await UserModel.findOne({ slug: 'john-doe' })

      assert.strictEqual(user?.externalConnections.length, 1)
      assert.strictEqual(user?.externalConnections[0].externalId, '123')
      assert.strictEqual(
        user?.externalConnections[0].crawlerDomainConfig._id.toString(),
        discoveredUrl.crawlerDomainConfig._id.toString()
      )
    })

    it('should handle failed scraping when scraper returns null', async () => {
      // Arrange
      await generateDiscoverUrl({ entity: CrawlerEntity.PLAYER })
      evaluateStub.resolves(null)

      // Act
      await scraper({ logger })

      // Assert
      const discoveredUrl = await DiscoveredUrlModel.findOne({ externalId: '123' })
      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeAttempts, 1)
      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeSuccessful, false)
      assert.notStrictEqual(discoveredUrl?.scraperInfo?.lastScrapedAt, null)
      assert.strictEqual(discoveredUrl?.scraperInfo?.scheduledForScrapeAt, null)

      // Check no userInfo was created
      const userInfo = await UserInfoModel.findOne({ externalId: '123' })
      assert.strictEqual(userInfo, null)
    })
  })

  describe('Game stats scraper', () => {
    beforeEach(async () => {
      await CrawlerDomainConfigModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
      await GameModel.deleteMany({})
    })

    it('should go through discovered URLs for games which are not scraped', async () => {
      await generateDiscoverUrl()

      evaluateStub.resolves(gameStatsMock)

      await scraper({ logger })

      const gameStats = await GameModel.findOne({ externalId: '123' })
      const discoveredUrl = await DiscoveredUrlModel.findOne({ externalId: '123' })

      checkGameStats(gameStats!)

      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeAttempts, 1)
      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeSuccessful, true)
    })
  })

  describe('League stats scraper', () => {
    beforeEach(async () => {
      await CrawlerDomainConfigModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
      await LeagueStatsModel.deleteMany({})
      await TeamModel.deleteMany({})
      await OrganizationModel.deleteMany({})
      await LeagueModel.deleteMany({})
    })

    it('should go through discovered URLs for leagues which are not scraped', async () => {
      await generateDiscoverUrl({ entity: CrawlerEntity.LEAGUE })

      evaluateStub.resolves(leagueStatsMock)

      await scraper({ logger })

      const leagueStats = await LeagueStatsModel.find()

      assert.strictEqual(leagueStats.length, 1)
      assert.strictEqual(leagueStats[0].externalId, '123')
      assert.strictEqual(leagueStats[0].season, '2021-2022')
      assert.strictEqual(leagueStats[0].group, 'A')
      assert.strictEqual(leagueStats[0].standing, 8)
      assert.strictEqual(leagueStats[0].games, 10)
      assert.strictEqual(leagueStats[0].wins, 5)
      assert.strictEqual(leagueStats[0].losses, 5)
      assert.strictEqual(leagueStats[0].ties, 0)
      assert.strictEqual(leagueStats[0].overtimeWins, 0)
      assert.strictEqual(leagueStats[0].overtimeLosses, 0)
      assert.strictEqual(leagueStats[0].goalsFor, 40)
      assert.strictEqual(leagueStats[0].goalsAgainst, 38)
      assert.strictEqual(leagueStats[0].points, 10)
      assert.strictEqual(leagueStats[0].key, generateLeagueStatsKey(leagueStats[0]))
    })

    it('should go through discovered URLs for leagues and assign team and league entities', async () => {
      const league = await generateRandomLeague(
        'NHL',
        'CZ',
        'nhl',
        undefined,
        undefined,
        undefined,
        true,
        true,
        [{ name: 'NHL', slug: 'nhl' }]
      )

      const org = await generateRandomOrganization(
        'Toronto Maple Leafs',
        'CZ',
        true,
        'toronto-maple-leafs'
      )

      const team = await generateRandomTeam({
        name: 'Toronto Maple Leafs',
        organization: org.id,
        country: 'CZ',
        alternativeNames: [],
        leagues: [league.id],
      })

      await generateDiscoverUrl({ entity: CrawlerEntity.LEAGUE })

      evaluateStub.resolves(leagueStatsMock)

      await scraper({ logger })

      const leagueStats = await LeagueStatsModel.find()
      assert.strictEqual(leagueStats.length, 1)
      assert.strictEqual(leagueStats[0].externalId, '123')
      assert.strictEqual(leagueStats[0].season, '2021-2022')

      const updatedLeagueStats = await LeagueStatsModel.findOne({
        _id: leagueStats[0]._id,
      })
      const discoveredUrl = await DiscoveredUrlModel.findOne({ externalId: '123' })

      assert.strictEqual(updatedLeagueStats?.team?.toString(), team._id.toString())
      assert.strictEqual(updatedLeagueStats?.league?.toString(), league._id.toString())

      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeAttempts, 1)
      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeSuccessful, true)
    })

    it('should not schedule scrape if scraping failed', async () => {
      await generateDiscoverUrl({
        entity: CrawlerEntity.LEAGUE,
      })

      evaluateStub.resolves(null)

      await scraper({ logger })

      const updatedDiscoveredUrl = await DiscoveredUrlModel.findOne({ externalId: '123' })

      assert.strictEqual(updatedDiscoveredUrl?.scraperInfo?.scheduledForScrapeAt, null)
    })
  })

  describe('User stats scraper', () => {
    beforeEach(async () => {
      await CrawlerDomainConfigModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
      await UserStatsModel.deleteMany({})
      await TeamModel.deleteMany({})
      await OrganizationModel.deleteMany({})
      await LeagueModel.deleteMany({})
    })

    it('should go through discovered URLs for user stats which are not scraped', async () => {
      await generateDiscoverUrl({ entity: CrawlerEntity.PLAYER_STATS })

      const league = await generateRandomLeague('Extraliga', 'CZ', 'extraliga')
      const organization = await generateRandomOrganization('HC Slavia Praha', 'CZ')
      await generateRandomTeam({
        name: 'HC Slavia Praha',
        country: 'CZ',
        organization: organization._id.toString(),
        leagues: [league._id.toString()],
      })

      evaluateStub.resolves({
        userStats: [
          {
            userExternalId: '1',

            team: 'HC Slavia Praha',
            teamCountry: 'CZ',
            league: 'Extraliga',
            leagueCountry: 'CZ',

            seasonType: 'REGULAR',
            season: '2021-2022',
            gamesPlayed: 2,
            goals: 2,
            assists: 1,
            pim: 2,
            plusMinus: 1,
          },
        ],
      } as typeof UserStatsResponseFormat._type)

      await scraper({ logger })

      const userStats = await UserStatsModel.find().populate(['team', 'league'])
      const discoveredUrl = await DiscoveredUrlModel.findOne().populate(['crawlerDomainConfig'])

      assert.strictEqual(userStats.length, 1)
      assert.strictEqual(userStats[0].userExternalId, '1')
      assert.strictEqual(userStats[0].season, '2021-2022')
      assert.strictEqual(userStats[0].seasonType, 'REGULAR')
      assert.strictEqual(userStats[0].gamesPlayed, 2)
      assert.strictEqual(userStats[0].goals, 2)
      assert.strictEqual(userStats[0].assists, 1)
      assert.strictEqual(userStats[0].pim, 2)
      assert.strictEqual(userStats[0].plusMinus, 1)
      assert.strictEqual((userStats[0].team as Team).name, 'HC Slavia Praha')
      assert.strictEqual(
        userStats[0].crawlerDomainConfig?.toString(),
        discoveredUrl?.crawlerDomainConfig?.id.toString()
      )

      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeAttempts, 1)
      assert.strictEqual(discoveredUrl?.scraperInfo?.scrapeSuccessful, true)
    })

    it('should update existing user stats when scraped again with different values', async () => {
      // Create the discovered URL for user stats
      const discoveredUrl = await generateDiscoverUrl({ entity: CrawlerEntity.PLAYER_STATS })

      // Create team and league for the test
      const league = await generateRandomLeague('Extraliga', 'CZ', 'extraliga')
      const organization = await generateRandomOrganization('HC Slavia Praha', 'CZ')
      await generateRandomTeam({
        name: 'HC Slavia Praha',
        country: 'CZ',
        organization: organization._id.toString(),
        leagues: [league._id.toString()],
      })

      // First scrape - create initial stats
      evaluateStub.resolves({
        userStats: [
          {
            userExternalId: '1',
            team: 'HC Slavia Praha',
            teamCountry: 'CZ',
            league: 'Extraliga',
            leagueCountry: 'CZ',
            seasonType: 'REGULAR',
            season: '2021-2022',
            gamesPlayed: 2,
            goals: 2,
            assists: 1,
            pim: 2,
            plusMinus: 1,
            stage: { value: '123', label: 'Regular Season' },
          },
        ],
      } as typeof UserStatsResponseFormat._type)

      await scraper({ logger })

      // Verify initial stats were created
      let userStats = await UserStatsModel.find().populate(['team', 'league'])
      assert.strictEqual(userStats.length, 1)
      assert.strictEqual(userStats[0].userExternalId, '1')
      assert.strictEqual(userStats[0].gamesPlayed, 2)
      assert.strictEqual(userStats[0].goals, 2)
      assert.strictEqual(userStats[0].assists, 1)

      // Store the original ID to verify it's the same record after update
      const originalStatsId = userStats[0]._id.toString()

      // Second scrape - update with different values
      evaluateStub.resolves({
        userStats: [
          {
            userExternalId: '1',
            team: 'HC Slavia Praha',
            teamCountry: 'CZ',
            league: 'Extraliga',
            leagueCountry: 'CZ',
            seasonType: 'REGULAR',
            season: '2021-2022',
            gamesPlayed: 5, // Updated values
            goals: 4, // Updated values
            assists: 3, // Updated values
            pim: 4, // Updated values
            plusMinus: 2, // Updated values
            stage: { value: '123', label: 'Regular Season' },
          },
        ],
      } as typeof UserStatsResponseFormat._type)

      // Reset the scraper info to simulate a new scrape
      await DiscoveredUrlModel.updateOne(
        { _id: discoveredUrl._id },
        {
          $set: {
            'scraperInfo.scheduledForScrapeAt': new Date(),
          },
        }
      )

      // Run the scraper again
      await rescrapeScheduled({ logger })

      // Verify stats were updated
      userStats = await UserStatsModel.find().populate(['team', 'league'])

      assert.strictEqual(userStats.length, 1, 'Should still have only one stats record')
      assert.strictEqual(
        userStats[0]._id.toString(),
        originalStatsId,
        'Should be the same record (updated, not new)'
      )
      assert.strictEqual(userStats[0].userExternalId, '1')
      assert.strictEqual(userStats[0].gamesPlayed, 5, 'Games played should be updated')
      assert.strictEqual(userStats[0].goals, 4, 'Goals should be updated')
      assert.strictEqual(userStats[0].assists, 3, 'Assists should be updated')
      assert.strictEqual(userStats[0].pim, 4, 'PIM should be updated')
      assert.strictEqual(userStats[0].plusMinus, 2, 'Plus/minus should be updated')
      assert.strictEqual(userStats[0].key, '2021-2022-1-hc-slavia-praha-extraliga-123')
    })

    it('should not schedule scrape if scraping failed', async () => {
      await generateDiscoverUrl({
        entity: CrawlerEntity.PLAYER_STATS,
      })

      evaluateStub.resolves(null)

      await scraper({ logger })

      const updatedDiscoveredUrl = await DiscoveredUrlModel.findOne({ externalId: '123' })

      assert.strictEqual(updatedDiscoveredUrl?.scraperInfo?.scheduledForScrapeAt, null)
    })
  })
})
