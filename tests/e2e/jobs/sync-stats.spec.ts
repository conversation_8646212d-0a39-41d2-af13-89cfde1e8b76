import assert from 'assert'
import axios from 'axios'
import sinon from 'sinon'

import { logger } from '../../../src/container'
import { syncStats } from '../../../src/jobs/sync-stats'
import { UserStatsModel } from '../../../src/modules/stats/models/userStats'
import { UserModel } from '../../../src/modules/user/models'
import { generateRandomUser, userStatsQuery } from '../../utils/users'

describe('syncStats job', () => {
  it('should sync stats for users with EP link', async () => {
    const epUser = await generateRandomUser()

    const axiosPostStub = sinon.stub(axios, 'post').resolves(userStatsQuery('team-slug-123'))

    await UserModel.updateOne(
      { _id: epUser.user._id },
      {
        $set: {
          externalLinks: [
            {
              type: 'ELITE_PROSPECTS',
              url: 'https://www.eliteprospects.com/player/12345/player-name',
            },
          ],
        },
      }
    )

    await syncStats({ logger })

    const stats = await UserStatsModel.find({ user: epUser.user._id })

    assert.strictEqual(stats.length, 1)
    assert.strictEqual(stats[0].gamesPlayed, 82)
    assert.strictEqual(stats[0].goals, 30)
    assert.strictEqual(stats[0].assists, 45)
    assert.strictEqual(stats[0].pim, 25)
    assert.strictEqual(stats[0].plusMinus, 20)
    assert.strictEqual(stats[0].wins, 0)
    assert.strictEqual(stats[0].losses, 0)
    assert.strictEqual(stats[0].ties, 0)
    assert.strictEqual(stats[0].gaa, 2.5)
    assert.strictEqual(stats[0].svp, 0.915)
    assert.strictEqual(stats[0].shutouts, 5)

    assert.strictEqual(axiosPostStub.calledOnce, true)

    axiosPostStub.restore()

    // and check if id has changed
    const axiosPostStub2 = sinon.stub(axios, 'post').resolves(userStatsQuery('team-slug-123', '2'))

    await UserModel.updateOne(
      { _id: epUser.user._id },
      {
        $unset: {
          statsSyncedAt: 1,
        },
      }
    )

    await syncStats({ logger })

    const stats2 = await UserStatsModel.find({ user: epUser.user._id })

    assert.strictEqual(stats2.length, 1)

    axiosPostStub2.restore()
  })

  it('should sync stats with leionat link (Skater)', async () => {
    const leijonatUser = await generateRandomUser()

    const axiosPost = sinon.stub(axios, 'post')
    axiosPost.onCall(0).resolves({
      data: {
        IsGoalieStats: '0',
      },
    })

    axiosPost.onCall(1).resolves({
      data: {
        Skater: [
          {
            SeasonNumber: 2022,
          },
        ],
      },
    })

    axiosPost.onCall(2).resolves({
      data: {
        SkaterLevels: [
          {
            LevelName: 'Liiga',
            LevelTeams: [{ TeamName: 'HIFK' }],
            LevelGoals: 3,
            LevelAssists: 2,
            LevelPenMinutes: 19,
            PlayedLevelGames: 1,
          },
        ],
      },
    })

    await UserModel.updateOne(
      { _id: leijonatUser.user._id },
      {
        $set: {
          externalLinks: [
            {
              type: 'LEIJONAT',
              url: 'https://www.leijonat.fi/pelaajat?lkq=12345',
              scrapable: true,
            },
          ],
        },
      }
    )

    await syncStats({ logger })

    const stats = await UserStatsModel.find({ user: leijonatUser.user._id })

    assert.strictEqual(stats.length, 1)
    assert.strictEqual(stats[0].gamesPlayed, 1)
    assert.strictEqual(stats[0].goals, 3)
    assert.strictEqual(stats[0].assists, 2)
    assert.strictEqual(stats[0].pim, 19)

    assert.strictEqual(axiosPost.calledThrice, true)
  })

  it('should sync stats with leionat link (Goalie)', async () => {
    const leijonatUser = await generateRandomUser()

    const axiosPost = sinon.stub(axios, 'post')
    axiosPost.onCall(0).resolves({
      data: {
        IsGoalieStats: '1',
      },
    })

    axiosPost.onCall(1).resolves({
      data: {
        Skater: [
          {
            SeasonNumber: 2022,
          },
        ],
      },
    })

    axiosPost.onCall(2).resolves({
      data: {
        GoalieLevels: [
          {
            LevelName: 'Liiga',
            LevelTeams: [{ TeamName: 'HIFK' }],
            InLevelPlayedGames: 3,
            LevelGoalsAgainst: 5,
            LevelSavePerc: 98.8,
          },
          {
            LevelName: 'Liiga 2',
            LevelTeams: [{ TeamName: 'HIFK Youth' }],
            InLevelPlayedGames: 0,
            LevelGoalsAgainst: 0,
            LevelSavePerc: 0,
          },
        ],
      },
    })

    await UserModel.updateOne(
      { _id: leijonatUser.user._id },
      {
        $set: {
          externalLinks: [
            {
              type: 'LEIJONAT',
              url: 'https://www.leijonat.fi/pelaajat?lkq=12345',
              scrapable: true,
            },
          ],
        },
      }
    )

    await syncStats({ logger })

    const stats = await UserStatsModel.find({ user: leijonatUser.user._id })

    assert.strictEqual(stats.length, 2)
    assert.strictEqual(stats[0].gamesPlayed, 3)
    assert.strictEqual(stats[0].gaa, 1.67)
    assert.strictEqual(stats[0].svp, 0.988)

    assert.strictEqual(stats[1].gamesPlayed, 0)
    assert.strictEqual(stats[1].gaa, 0)
    assert.strictEqual(stats[1].svp, 0)

    assert.strictEqual(axiosPost.calledThrice, true)
  })
})
