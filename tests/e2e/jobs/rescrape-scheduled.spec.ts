import assert from 'assert'

import { logger } from '../../../src/container'
import { rescrapeScheduled } from '../../../src/jobs/rescrape-scheduled'
import {
  CrawlerDomainConfigModel,
  CrawlerEntity,
} from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../../src/modules/stats/models/game/game'
import { LeagueStatsModel } from '../../../src/modules/stats/models/leagueStats/leagueStats'
import { generateDiscoverUrl, generateLeagueStat } from '../../utils/stats'
import { leagueStatsMock } from '../services/leagueStatsSyncer.helper'
import { evaluateStub } from '..'

describe('Rescrape scheduled job', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await GameModel.deleteMany({})
    await LeagueStatsModel.deleteMany({})
  })

  it('should scrape scheduled discovered URL for league', async () => {
    const discoveredUrl = await generateDiscoverUrl({
      entity: CrawlerEntity.LEAGUE,
      scraperInfo: {
        lastScrapedAt: new Date(Date.now() - 20 * 60 * 1000),
        scrapeAttempts: 1,
        scrapeSuccessful: true,
        scheduledForScrapeAt: new Date(Date.now() - 10 * 60 * 1000),
      },
    })

    await generateLeagueStat({
      discoveredUrl,
      leagueStats: { ...leagueStatsMock.leagueStats[0], points: 0 },
    })

    evaluateStub.resolves(leagueStatsMock)

    await rescrapeScheduled({ logger })

    const leagueStats = await LeagueStatsModel.find()

    assert.strictEqual(leagueStats.length, 1)
    // it should update points
    assert.strictEqual(leagueStats[0].points, 10)
  })
})
