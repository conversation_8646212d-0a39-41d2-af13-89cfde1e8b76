import assert from 'assert'

import { logger } from '../../../src/container'
import { rescrapeGames } from '../../../src/jobs/rescrape-games'
import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../../src/modules/stats/models/game/game'
import { generateDiscoverUrl, generateRandomGameStats } from '../../utils/stats'
import { checkGameStats, gameStatsMock } from '../services/gameSyncer.helper'
import { evaluateStub } from '..'

describe('Rescraper game job', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await GameModel.deleteMany({})
  })

  it('should rescrape game within a specified date range', async () => {
    const discoveredUrl = await generateDiscoverUrl({
      scraperInfo: {
        lastScrapedAt: new Date(Date.now() - 20 * 60 * 1000),
        scrapeAttempts: 1,
        scrapeSuccessful: false,
        scheduledForScrapeAt: new Date(Date.now() - 10 * 60 * 1000),
      },
      extractionScript: 'return { homeTeam: "Home", awayTeam: "Away" }',
    })

    evaluateStub.resolves(gameStatsMock)

    const date = new Date('2022-01-01')
    await generateRandomGameStats({
      gameStatsPayload: {
        externalInfo: {
          homeTeamName: 'HC Sparta Praha',
          awayTeamName: 'HC Oceláři Třinec',
          leagueName: 'Czech Extraliga',
        },
        date,
        externalId: '123',
        discoveredUrl,
      },
    })

    await rescrapeGames({ logger }, 'startDate=2022-01-01', 'endDate=2022-01-01')

    const game = await GameModel.findOne({ externalId: '123' })
    const discoveredUrl2 = await DiscoveredUrlModel.findOne({ externalId: '123' })
    checkGameStats(game!)

    assert.strictEqual(discoveredUrl2?.scraperInfo?.scrapeAttempts, 2)
    assert.strictEqual(discoveredUrl2?.scraperInfo?.scrapeSuccessful, true)
    assert.strictEqual(discoveredUrl2?.scraperInfo?.scheduledForScrapeAt, null)
  })

  it('should not rescrape game if date range is not specified', async () => {
    const rescrapePromise = rescrapeGames({ logger })
    await assert.rejects(rescrapePromise, {
      name: 'Error',
      message: 'startDate and endDate are required',
    })

    const game = await GameModel.findOne({ externalId: '123' })

    await assert.rejects(
      async () => {
        await rescrapeGames({ logger })
      },
      {
        name: 'Error',
        message: 'startDate and endDate are required',
      }
    )
    assert.strictEqual(game, null)
  })
})
