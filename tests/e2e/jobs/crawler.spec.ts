import assert from 'assert'

import { logger } from '../../../src/container'
import { crawler } from '../../../src/jobs/crawler'
import {
  CrawlerDomainConfigModel,
  CrawlerEntity,
  CrawlerUrlConfig,
  FetchType,
} from '../../../src/modules/admin/models/crawlerDomainConfig'
import { CrawlerRunStatsModel } from '../../../src/modules/admin/models/crawlerRunStats'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { axiosGetStub, puppeteerPageContentStub } from '..'

describe('Crawler job', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await CrawlerRunStatsModel.deleteMany({})
  })

  afterEach(() => {
    axiosGetStub.resetHistory()
    puppeteerPageContentStub.resetHistory()
  })

  it("should discover URLs and return all of them after it's done", async () => {
    const urlConfig = new CrawlerUrlConfig()
    urlConfig.entity = CrawlerEntity.PLAYER
    urlConfig.initialUrls = ['https://ceskyhokej.cz']
    urlConfig.externalIdPatternRegExp = '\/detail\/(\\d+)$'
    urlConfig.urlPatternRegExp = '^https:\/\/.*ceskyhokej\\.cz\/player\/detail\/\\d+.*$'
    urlConfig.allowedQueryParams = []

    await CrawlerDomainConfigModel.create({
      domain: 'ceskyhokej.cz',
      urlConfigs: [urlConfig],
    })

    const axiosGetStub1 = axiosGetStub.onFirstCall().resolves({
      data: '<a href="https://ceskyhokej.cz/player/detail/1?param=1"></a> <a href="https://ceskyhokej.cz/player/detail/2"></a>',
    })

    const axiosGetStub2 = axiosGetStub.onSecondCall().resolves({
      data: '<a href="https://ceskyhokej.cz/player/detail/3"></a> <a href="https://ceskyhokej.cz/game/detail/1"> <a href="https://www.ceskyhokej.cz/game/detail/1"></a>',
    })

    const axiosGetStubRest = axiosGetStub.resolves({
      data: '<a href="https://external-url.com"></a>',
    })

    await crawler({ logger })

    const discoveredUrls = await DiscoveredUrlModel.find()

    assert.strictEqual(discoveredUrls.length, 3)

    assert.strictEqual(discoveredUrls[0].url, 'https://ceskyhokej.cz/player/detail/1')
    assert.strictEqual(discoveredUrls[0].entity, CrawlerEntity.PLAYER)
    assert.strictEqual(discoveredUrls[0].externalId, '1')

    assert.strictEqual(discoveredUrls[1].url, 'https://ceskyhokej.cz/player/detail/2')
    assert.strictEqual(discoveredUrls[1].entity, CrawlerEntity.PLAYER)
    assert.strictEqual(discoveredUrls[1].externalId, '2')

    assert.strictEqual(discoveredUrls[2].url, 'https://ceskyhokej.cz/player/detail/3')
    assert.strictEqual(discoveredUrls[2].entity, CrawlerEntity.PLAYER)
    assert.strictEqual(discoveredUrls[2].externalId, '3')

    axiosGetStub1.calledOnceWith(`https://ceskyhokej.cz`)
    axiosGetStub2.calledOnceWith(`https://ceskyhokej.cz/player/detail/1`)
    axiosGetStubRest.calledOnceWith(`https://ceskyhokej.cz/player/detail/2`)
    axiosGetStubRest.calledOnceWith(`https://ceskyhokej.cz/player/detail/3`)

    // check stats
    const stats = await CrawlerRunStatsModel.find()

    assert.strictEqual(stats.length, 1)
    assert.strictEqual(stats[0].totalVisitedUrls, 5)
    assert.strictEqual(stats[0].totalDiscoveredUrls, 3)
    assert.strictEqual(stats[0].newlyDiscoveredUrls, 3)
  })

  it('should discover URLs based on param extractors', async () => {
    const urlConfig = new CrawlerUrlConfig()
    urlConfig.entity = CrawlerEntity.PLAYER_STATS
    urlConfig.initialUrls = ['https://ceskyhokej.cz/competition/player-stats/1']
    urlConfig.externalIdPatternRegExp = '\/player-stats\/(\d+)'
    urlConfig.urlPatternRegExp = '^https:\/\/.*ceskyhokej\.cz\/competition\/player-stats\/\\d+.*$'
    urlConfig.allowedQueryParams = [
      'playerCompetitionStatsWidget-filter[competitionPart]',
      'playerCompetitionStatsWidget-page',
    ]
    urlConfig.paramExtractors = [
      {
        selector: 'select[name="competitionPart"]',
        queryParam: 'playerCompetitionStatsWidget-filter[competitionPart]',
      },
    ]

    await CrawlerDomainConfigModel.create({
      domain: 'ceskyhokej.cz',
      urlConfigs: [urlConfig],
    })

    axiosGetStub.onFirstCall().resolves({
      data: `<html><body><div class="form-field">
					<label for="frm-playerCompetitionStatsWidget-playerCompetitionStatsFilterForm-competitionPart" class="h5">Soutěž</label>
					<select name="competitionPart" data-minimum-results-for-search="1" data-ajaxonchange="true" id="frm-playerCompetitionStatsWidget-playerCompetitionStatsFilterForm-competitionPart" class="form-control" data-gtm-form-interact-field-id="0"><option value="">Všechny</option><option value="1404">Liga starších žáků "A" - O umístění sk. "E" 2024/25</option><option value="1405">Liga starších žáků "A" - O umístění sk. "F" 2024/25</option></select>
				</div></body></html>`,
    })

    await crawler({ logger })

    const discoveredUrls = await DiscoveredUrlModel.find()
    assert.strictEqual(discoveredUrls.length, 2)

    assert.strictEqual(
      discoveredUrls[0].url,
      'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1404'
    )
    assert.strictEqual(discoveredUrls[0].entity, CrawlerEntity.PLAYER_STATS)

    assert.strictEqual(
      discoveredUrls[1].url,
      'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1405'
    )
    assert.strictEqual(discoveredUrls[1].entity, CrawlerEntity.PLAYER_STATS)

    axiosGetStub.calledOnceWith(`https://ceskyhokej.cz`)
  })

  it('should discover URLs with combinations of param extractors', async () => {
    const urlConfig = new CrawlerUrlConfig()
    urlConfig.entity = CrawlerEntity.PLAYER_STATS
    urlConfig.initialUrls = ['https://ceskyhokej.cz/competition/player-stats/1']
    urlConfig.externalIdPatternRegExp = '\/player-stats\/(\d+)'
    urlConfig.urlPatternRegExp = '^https:\/\/.*ceskyhokej\.cz\/competition\/player-stats\/\\d+.*$'
    urlConfig.allowedQueryParams = [
      'playerCompetitionStatsWidget-filter[competitionPart]',
      'seasonId',
      'playerCompetitionStatsWidget-page',
    ]
    urlConfig.paramExtractors = [
      {
        selector: 'select[name="competitionPart"]',
        queryParam: 'playerCompetitionStatsWidget-filter[competitionPart]',
      },
      {
        selector: 'select[name="season"]',
        queryParam: 'seasonId',
      },
    ]

    await CrawlerDomainConfigModel.create({
      domain: 'ceskyhokej.cz',
      urlConfigs: [urlConfig],
    })

    axiosGetStub.onFirstCall().resolves({
      data: `<html><body>
          <div class="form-field">
            <label for="frm-playerCompetitionStatsWidget-playerCompetitionStatsFilterForm-competitionPart" class="h5">Soutěž</label>
            <select name="competitionPart" data-minimum-results-for-search="1" data-ajaxonchange="true" id="frm-playerCompetitionStatsWidget-playerCompetitionStatsFilterForm-competitionPart" class="form-control" data-gtm-form-interact-field-id="0">
              <option value="">Všechny</option>
              <option value="1404">Liga starších žáků "A" - O umístění sk. "E" 2024/25</option>
              <option value="1405">Liga starších žáků "A" - O umístění sk. "F" 2024/25</option>
            </select>
          </div>
          <div class="form-field">
            <label for="frm-playerCompetitionStatsWidget-playerCompetitionStatsFilterForm-season" class="h5">Sezóna</label>
            <select name="season" data-minimum-results-for-search="1" data-ajaxonchange="true" id="frm-playerCompetitionStatsWidget-playerCompetitionStatsFilterForm-season" class="form-control" data-gtm-form-interact-field-id="1">
              <option value="">Všechny</option>
              <option value="2023">2023/2024</option>
              <option value="2024">2024/2025</option>
            </select>
          </div>
        </body></html>`,
    })

    await crawler({ logger })

    const discoveredUrls = await DiscoveredUrlModel.find()

    assert.strictEqual(discoveredUrls.length, 8)

    // Check for URLs with single parameters
    const singleParamUrls = discoveredUrls.filter((url) => {
      const urlObj = new URL(url.url)
      return urlObj.searchParams.size === 1
    })
    assert.strictEqual(singleParamUrls.length, 4)

    // Check for URLs with parameter combinations
    const combinationUrls = discoveredUrls.filter((url) => {
      const urlObj = new URL(url.url)
      return urlObj.searchParams.size > 1
    })
    assert.strictEqual(combinationUrls.length, 4)

    // Verify specific combinations exist
    const urlStrings = discoveredUrls.map((url) => url.url)

    // Check for single param URLs
    assert.ok(
      urlStrings.includes(
        'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1404'
      )
    )
    assert.ok(
      urlStrings.includes(
        'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1405'
      )
    )
    assert.ok(urlStrings.includes('https://ceskyhokej.cz/competition/player-stats/1?seasonId=2023'))
    assert.ok(urlStrings.includes('https://ceskyhokej.cz/competition/player-stats/1?seasonId=2024'))

    // Check for combination URLs
    assert.ok(
      urlStrings.includes(
        'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1404&seasonId=2023'
      )
    )
    assert.ok(
      urlStrings.includes(
        'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1404&seasonId=2024'
      )
    )
    assert.ok(
      urlStrings.includes(
        'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1405&seasonId=2023'
      )
    )
    assert.ok(
      urlStrings.includes(
        'https://ceskyhokej.cz/competition/player-stats/1?playerCompetitionStatsWidget-filter[competitionPart]=1405&seasonId=2024'
      )
    )

    axiosGetStub.calledOnceWith(`https://ceskyhokej.cz/competition/player-stats/1`)
  })

  it('should discover url based on relevant url patterns with puppeteer', async () => {
    const urlConfig = new CrawlerUrlConfig()
    urlConfig.entity = CrawlerEntity.PLAYER
    urlConfig.initialUrls = ['https://eliteprospects.com/leagues']
    urlConfig.externalIdPatternRegExp = '\/player\/(\\d+).*$'
    urlConfig.urlPatternRegExp = '^https:\/\/.*eliteprospects\.com\/player\/\\d+.*$'

    await CrawlerDomainConfigModel.create({
      domain: 'eliteprospects.com',
      relevantUrlPatterns: [
        '^https:\/\/(www\.)?eliteprospects\.com\/league\/[^\s?#]+\/stats(?:\/[^\s?#]+)?(?:\\?page=\\d+)?$',
      ],
      urlConfigs: [urlConfig],
      fetchType: FetchType.Puppeteer,
    })

    puppeteerPageContentStub
      .onFirstCall()
      .resolves(
        `<html><body><a href="/league/nhl/stats?page=1">Player Stats</a><a href="/league/nhl/stats?ignoredLink=1">Player Stats 2</a></body></html>`
      )

    puppeteerPageContentStub
      .onSecondCall()
      .resolves(`<html><body><a href="/player/123">Eduard Sale</a></body></html>`)

    await crawler({ logger })

    const discoveredUrls = await DiscoveredUrlModel.find()

    assert.strictEqual(discoveredUrls.length, 1)

    assert.strictEqual(discoveredUrls[0].url, 'https://eliteprospects.com/player/123')
    assert.strictEqual(discoveredUrls[0].entity, CrawlerEntity.PLAYER)
    assert.strictEqual(discoveredUrls[0].externalId, '123')
  })
})
