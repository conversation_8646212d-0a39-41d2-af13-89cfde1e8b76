import assert from 'assert'
import dayjs from 'dayjs'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { CURRENT_SEASON } from '../../src/helpers/league'
import { CrawlerDomainConfigModel } from '../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../src/modules/stats/models/game/game'
import { LeagueModel } from '../../src/modules/stats/models/league'
import { TeamModel } from '../../src/modules/stats/models/team'
import { UserStatsModel } from '../../src/modules/stats/models/userStats'
import {
  LeagueAlreadyExistsError,
  LeagueGenderCategory,
  LeagueLevel,
  LeagueType,
} from '../graphql/components'
import { client } from '../utils/graphql'
import { generateDiscoverUrl, generateRandomGameStats } from '../utils/stats'
import {
  generateRandomAdminUser,
  generateRandomLeague,
  generateRandomTeam,
  generateRandomUser,
} from '../utils/users'

describe('League resolver', () => {
  describe('create league mutation', () => {
    it('should return created league', async () => {
      const adminUser = await generateRandomAdminUser()

      const response = await client(adminUser.tokens.accessToken).createLeague({
        input: {
          name: 'Test league admin create',
          countries: ['US', 'CA'],
          genderCategory: LeagueGenderCategory.Men,
          level: LeagueLevel.Junior,
          alternativeNames: [{ name: 'Test league admin create alternative name' }],
          website: 'https://testleague.com',
          instagramUrl: 'https://instagram.com/testleague',
          facebookUrl: 'https://facebook.com/testleague',
          twitterUrl: 'https://twitter.com/testleague',
          tiktokUrl: 'https://tiktok.com/testleague',
          eliteProspectSlug: 'testleague',
          sourceUrl: 'https://source.com/testleague',
          teamAutoConnectRules: {
            country: 'US',
            shortName: 'Test 14',
            shortNameInclude: true,
          },
        },
      })

      assert.strictEqual(response.createLeague.league?.name, 'Test league admin create')
      assert.deepStrictEqual(response.createLeague.league?.countries, ['US', 'CA'])
      assert.strictEqual(response.createLeague.league?.genderCategory, LeagueGenderCategory.Men)
      assert.strictEqual(response.createLeague.league?.level, LeagueLevel.Junior)
      assert.deepStrictEqual(response.createLeague.league?.alternativeNames, [
        {
          name: 'Test league admin create alternative name',
          slug: 'test-league-admin-create-alternative-name',
        },
      ])
      assert.strictEqual(response.createLeague.league?.website, 'https://testleague.com')
      assert.strictEqual(
        response.createLeague.league?.instagramUrl,
        'https://instagram.com/testleague'
      )
      assert.strictEqual(
        response.createLeague.league?.facebookUrl,
        'https://facebook.com/testleague'
      )
      assert.strictEqual(response.createLeague.league?.twitterUrl, 'https://twitter.com/testleague')
      assert.strictEqual(response.createLeague.league?.tiktokUrl, 'https://tiktok.com/testleague')
      assert.strictEqual(response.createLeague.league.eliteProspectSlug, 'testleague')
      assert.strictEqual(response.createLeague.league.sourceUrl, 'https://source.com/testleague')
      assert.strictEqual(response.createLeague.league.teamAutoConnectRules?.country, 'US')
      assert.strictEqual(response.createLeague.league.teamAutoConnectRules?.shortName, 'Test 14')
      assert.strictEqual(
        response.createLeague.league.teamAutoConnectRules?.shortNameSlug,
        'test-14'
      )
      assert.strictEqual(response.createLeague.league.teamAutoConnectRules?.shortNameInclude, true)
    })

    it('should catch error if league exists', async () => {
      const adminUser = await generateRandomAdminUser()

      await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league admin create', countries: ['US'] },
      })

      try {
        await client(adminUser.tokens.accessToken).createLeague({
          input: { name: 'Test league admin create', countries: ['US'] },
        })
      } catch (err) {
        assert.strictEqual(err.response.errors[0].message, 'League already exists')
      }
    })

    it('should auto assing league to teams if auto connect rules are set', async () => {
      const adminUser = await generateRandomAdminUser()
      const team = await generateRandomTeam({
        name: 'Auto sync team-1',
        organization: undefined,
        shortNameSlug: 'u-14',
        alternativeNames: [],
        leagues: [],
        country: 'US',
      })

      const response = await client(adminUser.tokens.accessToken).createLeague({
        input: {
          name: 'Test league admin create auto connect',
          countries: ['US'],
          teamAutoConnectRules: {
            country: 'US',
            shortName: 'u-14',
            shortNameInclude: false,
          },
        },
      })

      assert.strictEqual(
        response.createLeague.league?.name,
        'Test league admin create auto connect'
      )

      const updatedTeam = await TeamModel.findById(team._id)
      assert.deepStrictEqual(
        updatedTeam?.leagues.map((id) => id.toString()),
        [response.createLeague.league?.id]
      )
    })

    it('should create league with publicAlternativeName', async () => {
      const adminUser = await generateRandomAdminUser()

      const response = await client(adminUser.tokens.accessToken).createLeague({
        input: {
          name: 'Test league with public alternative name',
          countries: ['US'],
          publicAlternativeName: 'Public League Name',
        },
      })

      assert.strictEqual(
        response.createLeague.league?.name,
        'Test league with public alternative name'
      )
      assert.strictEqual(response.createLeague.league?.publicAlternativeName, 'Public League Name')
    })

    it('should auto assing league to teams if auto connect rules are set - short name include', async () => {
      const adminUser = await generateRandomAdminUser()
      const team = await generateRandomTeam({
        name: 'Auto sync team 2',
        organization: undefined,
        shortNameSlug: 'pre-u-14-post',
        alternativeNames: [],
        leagues: [],
        country: 'US',
      })

      const response = await client(adminUser.tokens.accessToken).createLeague({
        input: {
          name: 'Test league admin create auto connect - short name included',
          countries: ['US'],
          teamAutoConnectRules: {
            country: 'US',
            shortName: 'u-14',
            shortNameInclude: true,
          },
        },
      })

      assert.strictEqual(
        response.createLeague.league?.name,
        'Test league admin create auto connect - short name included'
      )

      const updatedTeam = await TeamModel.findById(team._id)
      assert.deepStrictEqual(
        updatedTeam?.leagues.map((id) => id.toString()),
        [response.createLeague.league?.id]
      )
    })
  })

  describe('update league mutation', () => {
    it('should return updated league', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league admin update', countries: ['SK'] },
      })

      const response = await client(adminUser.tokens.accessToken).updateLeague({
        input: {
          name: 'Test league admin updated',
          countries: ['CZ'],
          genderCategory: LeagueGenderCategory.Men,
          level: LeagueLevel.Junior,
          alternativeNames: [{ name: 'Test league admin create alternative name' }],
          website: 'https://testleague.com',
          instagramUrl: 'https://instagram.com/testleague',
          facebookUrl: 'https://facebook.com/testleague',
          twitterUrl: 'https://twitter.com/testleague',
          tiktokUrl: 'https://tiktok.com/testleague',
          eliteProspectSlug: 'testleague',
          sourceUrl: 'https://source.com/testleague',
        },
        updateLeagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(response.updateLeague.league?.name, 'Test league admin updated')
      assert.deepStrictEqual(response.updateLeague.league?.countries, ['CZ'])
      assert.strictEqual(response.updateLeague.league?.genderCategory, LeagueGenderCategory.Men)
      assert.strictEqual(response.updateLeague.league?.level, LeagueLevel.Junior)
      assert.deepStrictEqual(response.updateLeague.league?.alternativeNames, [
        {
          name: 'Test league admin create alternative name',
          slug: 'test-league-admin-create-alternative-name',
        },
      ])
      assert.strictEqual(response.updateLeague.league?.website, 'https://testleague.com')
      assert.strictEqual(
        response.updateLeague.league?.instagramUrl,
        'https://instagram.com/testleague'
      )
      assert.strictEqual(
        response.updateLeague.league?.facebookUrl,
        'https://facebook.com/testleague'
      )
      assert.strictEqual(response.updateLeague.league?.twitterUrl, 'https://twitter.com/testleague')
      assert.strictEqual(response.updateLeague.league?.tiktokUrl, 'https://tiktok.com/testleague')
      assert.strictEqual(response.updateLeague.league.eliteProspectSlug, 'testleague')
      assert.strictEqual(response.updateLeague.league.sourceUrl, 'https://source.com/testleague')
    })

    it('should catch error if league not found', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'test', countries: ['US'] },
      })

      const findByIdAndUpdateStub = sinon
        .stub(LeagueModel, 'findByIdAndUpdate')
        .throws(new Error('Database error'))

      const response = await client(adminUser.tokens.accessToken).updateLeague({
        input: { name: 'test', countries: ['US'] },
        updateLeagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(
        (response.updateLeague.errors?.[0] as LeagueAlreadyExistsError).message,
        'League not found'
      )

      findByIdAndUpdateStub.restore()
    })

    it('should update publicAlternativeName field', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league public alternative name', countries: ['SK'] },
      })

      const response = await client(adminUser.tokens.accessToken).updateLeague({
        input: {
          publicAlternativeName: 'Public Name',
        },
        updateLeagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(response.updateLeague.league?.name, 'Test league public alternative name')
      assert.strictEqual(response.updateLeague.league?.publicAlternativeName, 'Public Name')
    })

    it('should auto assing league to teams if auto connect rules are set', async () => {
      const adminUser = await generateRandomAdminUser()
      const team = await generateRandomTeam({
        name: 'Auto sync team update',
        slug: 'auto-sync-team',
        organization: undefined,
        shortNameSlug: 'u-15',
        alternativeNames: [],
        leagues: [],
        country: 'US',
      })

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league admin update auto connect', countries: ['SK'] },
      })

      const response = await client(adminUser.tokens.accessToken).updateLeague({
        input: {
          countries: ['US'],
          teamAutoConnectRules: {
            country: 'US',
            shortName: 'U 15',
            shortNameInclude: false,
          },
        },
        updateLeagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(
        response.updateLeague.league?.name,
        'Test league admin update auto connect'
      )

      const updatedTeam = await TeamModel.findById(team._id)
      assert.deepStrictEqual(
        updatedTeam?.leagues.map((id) => id.toString()),
        [response.updateLeague.league?.id]
      )
    })
  })

  describe('delete league mutation', () => {
    it('should return true if league is deleted', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league admin delete', countries: ['US'] },
      })

      const response = await client(adminUser.tokens.accessToken).deleteLeague({
        deleteLeagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(response.deleteLeague, true)
    })

    it('should throw error if league is in use', async () => {
      const adminUser = await generateRandomAdminUser()
      const user = await generateRandomUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league in use admin delete', countries: ['US'] },
      })
      const idLeague = new Types.ObjectId(league.createLeague.league?.id)

      await UserStatsModel.create({
        user: user.user._id,
        league: idLeague,
        season: '2021',
      })

      try {
        await client(adminUser.tokens.accessToken).deleteLeague({
          deleteLeagueId: league.createLeague.league?.id,
        })
      } catch (err) {
        assert.strictEqual(
          err.response.errors[0].message,
          'League is in use and cannot be deleted.'
        )
      }
    })

    it('should return false if league is not deleted', async () => {
      const adminUser = await generateRandomAdminUser()

      const league = await client(adminUser.tokens.accessToken).createLeague({
        input: { name: 'Test league admin delete', countries: ['US'] },
      })

      const findByIdAndDeleteStub = sinon
        .stub(LeagueModel, 'findByIdAndDelete')
        .throws(new Error('Database error'))

      const response = await client(adminUser.tokens.accessToken).deleteLeague({
        deleteLeagueId: league.createLeague.league?.id,
      })

      assert.strictEqual(response.deleteLeague, false)

      findByIdAndDeleteStub.restore()
    })
  })

  describe('league query', () => {
    beforeEach(async () => {
      await LeagueModel.deleteMany({})
    })

    it('should return league by id or slug', async () => {
      const randomLeague = await generateRandomLeague('Test League')

      const response = await client().league({
        slug: randomLeague.slug,
      })

      assert.strictEqual(response.league?.name, 'Test League')
      assert.deepStrictEqual(response.league?.countries, ['CZ'])
    })

    it('should return null if league not found', async () => {
      const randomLeague = await generateRandomLeague('Test League')

      const findByIdStub = sinon.stub(LeagueModel, 'findOne').throws(new Error('Database error'))

      const response = await client().league({
        slug: randomLeague.slug,
      })

      assert.strictEqual(response.league, null)

      findByIdStub.restore
    })
  })

  describe('leagues query', () => {
    beforeEach(async () => {
      await LeagueModel.deleteMany({})
    })

    it('should return all leagues', async () => {
      // Create test leagues
      for (let i = 0; i < 5; i++) {
        await generateRandomLeague(`Test league ${i}`, 'US', `test-league-${i}`)
      }

      // Fetch all leagues
      const response = await client().leagues()

      // Verify response
      assert.strictEqual(response.leagues.edges.length, 5)
      assert.strictEqual(response.leagues.pageInfo.hasNextPage, false)
      assert.strictEqual(response.leagues.pageInfo.hasPreviousPage, false)
      assert.strictEqual(response.leagues.pageInfo.totalCount, 5)
    })

    it('should handle search query filtering', async () => {
      // Create leagues with different names and properties
      await generateRandomLeague(
        'SE league',
        'SE',
        'se-league',
        LeagueGenderCategory.Men,
        LeagueLevel.Professional,
        LeagueType.League,
        true
      )

      await generateRandomLeague(
        'SE Tournament 2',
        'SE',
        'se-tournament-2',
        LeagueGenderCategory.Women,
        LeagueLevel.Junior,
        LeagueType.Tournament,
        true
      )

      await generateRandomLeague(
        'FI league 1',
        'FI',
        'fi-league',
        LeagueGenderCategory.Mixed,
        LeagueLevel.College,
        LeagueType.League,
        false
      )

      // Test with no filter (should return all leagues)
      const allLeaguesResponse = await client().leagues()
      assert.strictEqual(allLeaguesResponse.leagues.edges.length, 3)

      // Test with search query filter
      const searchQueryResponse = await client().leagues({
        filter: {
          searchQuery: 'SE',
        },
      })

      assert.strictEqual(searchQueryResponse.leagues.edges.length, 2)
      // Verify the correct leagues are returned
      const leagueNames = searchQueryResponse.leagues.edges.map((edge) => edge.node.name).sort()
      assert.deepStrictEqual(leagueNames, ['SE Tournament 2', 'SE league'].sort())
    })

    it('should search by alternativeNames and publicAlternativeName', async () => {
      // Create a league with alternativeNames
      await generateRandomLeague(
        'Main League Name',
        'US',
        'main-league-name',
        LeagueGenderCategory.Men,
        LeagueLevel.Professional,
        LeagueType.League,
        true,
        undefined,
        [
          { name: 'Alternative League Name', slug: 'alternative-league-name' },
          { name: 'Another Alt Name', slug: 'another-alt-name' },
        ]
      )

      // Create a league with publicAlternativeName
      await LeagueModel.create({
        name: 'Second League',
        countries: ['CA'],
        slug: 'second-league',
        genderCategory: LeagueGenderCategory.Men,
        level: LeagueLevel.Professional,
        type: LeagueType.League,
        publicAlternativeName: 'Public Alternative League Name',
      })

      // Create a third league with neither matching
      await generateRandomLeague(
        'Third League',
        'UK',
        'third-league',
        LeagueGenderCategory.Men,
        LeagueLevel.Professional,
        LeagueType.League
      )

      // Test searching by alternativeName
      const altNameResponse = await client().leagues({
        filter: {
          searchQuery: 'Alternative',
        },
      })

      assert.strictEqual(altNameResponse.leagues.edges.length, 2)
      const altNameLeagues = altNameResponse.leagues.edges.map((edge) => edge.node.name).sort()
      assert.deepStrictEqual(altNameLeagues, ['Main League Name', 'Second League'].sort())

      // Test searching by specific alternativeName
      const specificAltNameResponse = await client().leagues({
        filter: {
          searchQuery: 'Another Alt',
        },
      })

      assert.strictEqual(specificAltNameResponse.leagues.edges.length, 1)
      assert.strictEqual(specificAltNameResponse.leagues.edges[0].node.name, 'Main League Name')

      // Test searching by publicAlternativeName
      const publicAltNameResponse = await client().leagues({
        filter: {
          searchQuery: 'Public Alternative',
        },
      })

      assert.strictEqual(publicAltNameResponse.leagues.edges.length, 1)
      assert.strictEqual(publicAltNameResponse.leagues.edges[0].node.name, 'Second League')
    })

    it('should handle pagination', async () => {
      // Create admin user for authorization
      const adminUser = await generateRandomAdminUser()

      // Create more leagues than the default page size (10)
      for (let i = 0; i < 12; i++) {
        await client(adminUser.tokens.accessToken).createLeague({
          input: { name: `Test leagues pagination ${i}`, countries: ['US'] },
        })
      }

      // Get first page
      const firstPageResponse = await client().leagues({
        filter: {
          searchQuery: 'pagination',
        },
      })

      // Verify first page
      assert.strictEqual(firstPageResponse.leagues.edges.length, 10)
      assert.strictEqual(firstPageResponse.leagues.pageInfo.hasNextPage, true)
      assert.strictEqual(firstPageResponse.leagues.pageInfo.hasPreviousPage, false)
      assert.strictEqual(firstPageResponse.leagues.pageInfo.totalCount, 12)

      // Get second page using cursor from first page
      const secondPageResponse = await client().leagues({
        filter: {
          searchQuery: 'pagination',
        },
        pagination: { after: firstPageResponse.leagues.pageInfo.endCursor, first: 5 },
      })

      // Verify second page
      assert.strictEqual(secondPageResponse.leagues.edges.length, 2)
      assert.strictEqual(secondPageResponse.leagues.pageInfo.hasNextPage, false)
      assert.strictEqual(secondPageResponse.leagues.pageInfo.hasPreviousPage, true)
    })

    it('should handle errors gracefully', async () => {
      // Create a stub for LeagueModel.findPaged to simulate an error
      const findStub = sinon.stub(LeagueModel, 'find').throws(new Error('Database error'))

      try {
        // Attempt to fetch leagues - should throw an error
        try {
          await client().leagues()
          assert.fail('Expected an error to be thrown')
        } catch (err) {
          // Verify the error message
          assert.strictEqual(err.message.includes('Database error'), true)
        }
      } finally {
        // Restore the stub
        findStub.restore()
      }
    })
  })

  describe('league hasGames', async () => {
    beforeEach(async () => {
      await GameModel.deleteMany({})
      await CrawlerDomainConfigModel.deleteMany({})
      await DiscoveredUrlModel.deleteMany({})
      await LeagueModel.deleteMany({})
    })

    it('should return true if league has games in current season', async () => {
      const discoveredUrl = await generateDiscoverUrl()
      const randomLeague = await generateRandomLeague('Test League')

      await generateRandomGameStats({
        nameSuffix: 'LeagueHasGames',
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '5',
          discoveredUrl,
          league: randomLeague._id,
          season: CURRENT_SEASON,
          externalInfo: {
            homeTeamName: 'Home Team',
            awayTeamName: 'Away Team',
            leagueName: randomLeague.name,
          },
        },
      })

      const response = await client().league({
        slug: randomLeague.slug,
      })

      assert.strictEqual(response.league?.hasGames, true)
    })

    it('should return false if league has no games', async () => {
      const discoveredUrl = await generateDiscoverUrl()
      const randomLeague = await generateRandomLeague('Test League')

      await generateRandomGameStats({
        nameSuffix: 'LeagueHasNoGames',
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '5',
          discoveredUrl,
          externalInfo: {
            homeTeamName: 'Home Team',
            awayTeamName: 'Away Team',
            leagueName: 'Different League',
          },
        },
      })

      const response = await client().league({
        slug: randomLeague.slug,
      })

      assert.strictEqual(response.league?.hasGames, false)
    })

    it('should handle errors gracefully', async () => {
      const randomLeague = await generateRandomLeague('Test League Error')

      // Create a stub for GameStatsModel.exists to simulate an error
      const existsStub = sinon.stub(GameModel, 'exists').throws(new Error('Database error'))

      try {
        const response = await client().league({
          slug: randomLeague.slug,
        })

        // Should return null when an error occurs
        assert.strictEqual(response.league?.hasGames, null)
      } finally {
        // Restore the stub
        existsStub.restore()
      }
    })

    it('should only consider current season games for hasGames field', async () => {
      const discoveredUrl = await generateDiscoverUrl()
      const randomLeague = await generateRandomLeague('Test League Multiple Seasons')

      // Create a game in a previous season
      await generateRandomGameStats({
        nameSuffix: 'PreviousSeason',
        gameStatsPayload: {
          date: dayjs().subtract(1, 'year').toDate(),
          externalId: '6',
          discoveredUrl,
          league: randomLeague._id,
          season: '2022-2023', // Previous season
          externalInfo: {
            homeTeamName: 'Home Team Previous',
            awayTeamName: 'Away Team Previous',
            leagueName: randomLeague.name,
          },
        },
      })

      // First check - should return false with only previous season games
      let response = await client().league({
        slug: randomLeague.slug,
      })
      assert.strictEqual(response.league?.hasGames, false)

      // Create a game in the current season
      const currentSeasonGame = await generateRandomGameStats({
        nameSuffix: 'CurrentSeason',
        gameStatsPayload: {
          date: dayjs().subtract(1, 'days').toDate(),
          externalId: '7',
          discoveredUrl,
          league: randomLeague._id,
          season: CURRENT_SEASON, // Current season
          externalInfo: {
            homeTeamName: 'Home Team Current',
            awayTeamName: 'Away Team Current',
            leagueName: randomLeague.name,
          },
        },
      })

      // Second check - should return true with both previous and current season games
      response = await client().league({
        slug: randomLeague.slug,
      })
      assert.strictEqual(response.league?.hasGames, true)

      // Now delete the current season game
      await GameModel.deleteOne({ _id: currentSeasonGame._id })

      // Third check - should return false again with only previous season games
      response = await client().league({
        slug: randomLeague.slug,
      })
      assert.strictEqual(response.league?.hasGames, false)
    })

    it('should handle different league object structures with getLeague()', async () => {
      // Test with a regular League object
      const league = new LeagueModel({
        name: 'Test League',
        slug: 'test-league-getleague',
        countries: ['US'],
      })

      // Call getLeague() and verify it returns the object itself
      const result1 = league.getLeague()
      assert.strictEqual(result1._id, league._id)

      const mockDoc = { _doc: { _id: new Types.ObjectId(), name: 'Test Doc League' } }
      const result2 = LeagueModel.prototype.getLeague.call(mockDoc)
      assert.strictEqual(result2._id, mockDoc._doc._id)
      assert.strictEqual(result2.name, 'Test Doc League')
    })
  })
})
