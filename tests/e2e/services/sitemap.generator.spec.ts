import { strictEqual } from 'assert'
import sinon from 'sinon'
import Container from 'typedi'

import { OrganizationModel } from '../../../src/modules/stats/models/organization'
import { TeamModel } from '../../../src/modules/stats/models/team'
import { UserModel } from '../../../src/modules/user/models'
import { AgencyModel } from '../../../src/modules/user/models/agency'
import { S3Service } from '../../../src/services/s3'
import { SitemapGenerator } from '../../../src/services/web/sitemap.generator'
import {
  generateRandomAgency,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from '../../utils/users'

const NUM_PLAYERS = 100
const NUM_AGENCIES = 100
const NUM_TEAMS = 100

export const generatePlayers = async () => {
  let num = NUM_PLAYERS
  const users = []
  while (num--) {
    users.push(await generateRandomUser())
  }

  return users
}

export const generateAgencies = async () => {
  let num = NUM_AGENCIES
  const agencies = []
  while (num--) {
    agencies.push(await generateRandomAgency(`Athlets LLC ${num}`, 'https://example.com', true))
  }

  return agencies
}

export const generateTeams = async () => {
  let num = NUM_TEAMS
  const teams = []
  const orgs = []
  while (num--) {
    const org = await generateRandomOrganization(`Team Organization ${num}`)
    orgs.push(org)

    teams.push(await generateRandomTeam({ name: `Team ${num}`, organization: org.id }))
  }

  return {
    orgs,
    teams,
  }
}

describe('Sitemap generator', () => {
  beforeEach(async () => {
    await UserModel.deleteMany({})
    await AgencyModel.deleteMany({})
    await TeamModel.deleteMany({})
    await OrganizationModel.deleteMany({})
  })

  after(async () => {
    await UserModel.deleteMany({})
    await AgencyModel.deleteMany({})
    await TeamModel.deleteMany({})
    await OrganizationModel.deleteMany({})
  })

  it('should generate all players URLs', async () => {
    const users = await generatePlayers()

    const sitemapGen = new SitemapGenerator()
    const urls = await sitemapGen.generatePlayersURLs()

    strictEqual(NUM_PLAYERS, urls.length)

    strictEqual(`https://www.graet.com/${users[0].user.slug}/`, urls[0])
    const randomIndex = Math.floor(Math.random() * users.length)
    strictEqual(`https://www.graet.com/${users[randomIndex].user.slug}/`, urls[randomIndex])
  }).timeout(60 * 1000)

  it('should generate all agencies URLs', async () => {
    const agencies = await generateAgencies()

    const sitemapGen = new SitemapGenerator()
    const urls = await sitemapGen.generateAgenciesURLs()

    strictEqual(NUM_AGENCIES, urls.length)

    strictEqual(`https://www.graet.com/agency/${agencies[0].slug}/`, urls[0])
    const randomIndex = Math.floor(Math.random() * agencies.length)
    strictEqual(`https://www.graet.com/agency/${agencies[randomIndex].slug}/`, urls[randomIndex])
  }).timeout(60 * 1000)

  it('should generate all teams URLs', async () => {
    const { orgs, teams } = await generateTeams()

    const sitemapGen = new SitemapGenerator()
    const urls = await sitemapGen.generateTeamsURLs()

    strictEqual(NUM_TEAMS, urls.length)

    strictEqual(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      `https://www.graet.com/team/${orgs[0].slug}/${teams[0].shortNameSlug}/`,
      urls[0]
    )
    const randomIndex = Math.floor(Math.random() * teams.length)
    strictEqual(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      `https://www.graet.com/team/${orgs[randomIndex].slug}/${teams[randomIndex].shortNameSlug}/`,
      urls[randomIndex]
    )
  }).timeout(60 * 1000)

  it('should generate all sitemaps URLs', async () => {
    await generatePlayers()
    await generateAgencies()
    await generateTeams()

    const s3Service = Container.get(S3Service)
    const uploadFromBuffer = sinon.stub(s3Service, 'uploadFromBuffer')
    const downloadToBuffer = sinon.stub(s3Service, 'downloadToBuffer')
    Container.set('s3.service', {
      uploadFromBuffer,
      downloadToBuffer,
    })

    const sitemapGen = new SitemapGenerator()

    const sitemaps = await Promise.all([
      sitemapGen.generateSiteMap('player'),
      sitemapGen.generateSiteMap('agency'),
      sitemapGen.generateSiteMap('team'),
    ])

    strictEqual(true, Array.isArray(sitemaps))
    strictEqual(3, sitemaps.length)

    Container.set('s3.service', s3Service)
  }).timeout(60 * 1000)
})
