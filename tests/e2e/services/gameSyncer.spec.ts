import assert from 'assert'
import Container from 'typedi'

import { CrawlerDomainConfigModel } from '../../../src/modules/admin/models/crawlerDomainConfig'
import { DiscoveredUrlModel } from '../../../src/modules/admin/models/discoveredUrl'
import { GameModel } from '../../../src/modules/stats/models/game/game'
import GameSyncerService from '../../../src/modules/stats/services/gameSyncer.service'
import { generateDiscoverUrl, generateRandomGameStats } from '../../utils/stats'
import { evaluateStub } from '..'

import { checkGameStats, gameStatsMock, resolveOpeanAiStats } from './gameSyncer.helper'

describe('GameSyncer', () => {
  beforeEach(async () => {
    await CrawlerDomainConfigModel.deleteMany({})
    await DiscoveredUrlModel.deleteMany({})
    await GameModel.deleteMany({})
  })

  it('should sync game stats from URL', async () => {
    const gameStatsSyncer = Container.get(GameSyncerService)
    const discoveredUrl = await generateDiscoverUrl()

    resolveOpeanAiStats()

    await gameStatsSyncer.syncData(discoveredUrl)

    const gameStats = await GameModel.findOne({ externalId: '123' })

    checkGameStats(gameStats!)
  })

  it('should update game stats if they already exist', async () => {
    const gameStatsSyncer = Container.get(GameSyncerService)

    const discoveredUrl = await generateDiscoverUrl()

    await generateRandomGameStats({
      gameStatsPayload: {
        externalInfo: {
          homeTeamName: 'HC Slavia Praha',
          awayTeamName: 'HC Litvinov',
          leagueName: 'Test League',
        },
        date: new Date('2022-01-01'),
        externalId: '123',
        discoveredUrl: discoveredUrl._id,
      },
    })

    resolveOpeanAiStats()

    await gameStatsSyncer.syncData(discoveredUrl)

    const gameStats = await GameModel.findOne({ externalId: '123' })

    checkGameStats(gameStats!)
  })

  it('should fail and not save any data if there is no result from evaluation', async () => {
    evaluateStub.resolves(null)

    const gameStatsSyncer = Container.get(GameSyncerService)

    const discoveredUrl = await generateDiscoverUrl()

    await assert.rejects(gameStatsSyncer.syncData(discoveredUrl))

    const gameStats = await GameModel.find()

    assert.strictEqual(gameStats.length, 0)
  })

  it('should continue without saving game stats if crawler config does not have extraction script', async () => {
    evaluateStub.resolves(null)

    const gameStatsSyncer = Container.get(GameSyncerService)

    const discoveredUrl = await generateDiscoverUrl({
      extractionScript: null,
    })

    await gameStatsSyncer.syncData(discoveredUrl)

    const gameStats = await GameModel.find()

    assert.strictEqual(gameStats.length, 0)
  })

  it('should mark URL as not successful if it failed to scrape 8 times', async () => {
    const gameStatsSyncer = Container.get(GameSyncerService)

    const discoveredUrl = await generateDiscoverUrl()

    evaluateStub.resolves({
      gameStats: {
        ...gameStatsMock.gameStats,
        events: [],
        awayTeamRoster: [],
        homeTeamRoster: [],
        homeTeamStats: {},
        awayTeamStats: {},
      },
    })

    for (let i = 0; i < 8; i++) {
      try {
        await gameStatsSyncer.syncData(discoveredUrl)
      } catch (err) {
        console.log(err)
      }
    }

    const discoveredUrlUpdated = await DiscoveredUrlModel.findOne({ externalId: '123' })

    assert.strictEqual(discoveredUrlUpdated?.scraperInfo?.scrapeAttempts, 8)
    assert.strictEqual(discoveredUrlUpdated?.scraperInfo?.scrapeSuccessful, false)
  })

  it('should mark URL as unsuccessful if scraper did not return any data', async () => {
    evaluateStub.resolves(null)

    const gameStatsSyncer = Container.get(GameSyncerService)

    const discoveredUrl = await generateDiscoverUrl()

    await assert.rejects(gameStatsSyncer.syncData(discoveredUrl))

    const discoveredUrlUpdated = await DiscoveredUrlModel.findOne({ externalId: '123' })

    assert.strictEqual(discoveredUrlUpdated?.scraperInfo?.scrapeAttempts, 1)
    assert.strictEqual(discoveredUrlUpdated?.scraperInfo?.unsuccessfulScrapes, 1)
    assert.strictEqual(discoveredUrlUpdated?.scraperInfo?.scrapeSuccessful, false)
  })
})
