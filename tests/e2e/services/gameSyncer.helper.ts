import assert from 'assert'

import { Game, GameScoreType } from '../../../src/modules/stats/models/game/game'
import { GameEventType } from '../../../src/modules/stats/models/game/gameEvent'
import { RosterPosition } from '../../../src/modules/stats/models/game/rosterPlayer'
import { evaluateStub } from '..'

export const userInfoMock = {
  userInfo: {
    id: '123',
    name: '<PERSON>',
    age: 25,
    country: 'US',
  },
}

// this is mock of data returned by scraper
export const gameStatsMock = {
  gameStats: {
    date: '2022-01-01',
    homeTeamName: 'HC Sparta Praha',
    awayTeamName: 'HC Oceláři Třinec',
    leagueName: 'Czech Extraliga',
    homeTeamStats: {
      score: 3,
      shotsOnGoal: 40,
      faceoffs: 30,
      penalties: 10,
      penaltyMinutes: 10,
      powerPlayGoals: 1,
      goalieSaves: 35,
      blockedShots: 10,
    },
    awayTeamStats: {
      score: 2,
      shotsOnGoal: 30,
      faceoffs: 20,
      penalties: 12,
      powerPlayGoals: 0,
      goalieSaves: 28,
      blockedShots: 10,
      penaltyMinutes: 12,
    },
    periodScores: '1:0, 1:1, 1:1',
    scores: [
      {
        homeTeam: 1,
        awayTeam: 0,
        type: GameScoreType.Regular,
      },
      {
        homeTeam: 1,
        awayTeam: 1,
        type: GameScoreType.Regular,
      },
      {
        homeTeam: 1,
        awayTeam: 1,
        type: GameScoreType.Regular,
      },
    ],
    homeTeamRoster: [
      {
        playerName: 'Dominik Hasek',
        teamName: 'HC Sparta Praha',
        saves: 35,
        goalsAgainst: 2,
        svp: 0.946,
        jerseyNumber: 1,
        position: RosterPosition.Defenseman,
      },
      {
        playerName: 'John Doe',
        teamName: 'HC Sparta Praha',
        goals: 1,
        assists: 1,
        points: 2,
        pim: 2,
        plusMinus: 1,
        position: RosterPosition.Forward,
      },
    ],
    awayTeamRoster: [
      {
        playerName: 'Patrik Doe',
        teamName: 'HC Oceláři Třinec',
        goals: 2,
        assists: 0,
        points: 2,
        pim: 4,
        plusMinus: 2,
        position: RosterPosition.Defenseman,
      },
    ],
    events: [
      {
        time: '10:00',
        type: GameEventType.Goal,
        isHomeTeam: true,
        playerName: 'John Doe',
        teamName: 'HC Sparta Praha',
        assists: [],
        playerExternalId: '123',
      },
      {
        time: '12:00',
        type: GameEventType.Goal,
        isHomeTeam: false,
        playerName: 'Patrik Doe',
        teamName: 'HC Oceláři Třinec',
        assists: [
          {
            playerName: 'Pavel Zacha',
            playerExternalId: '1234',
          },
        ],
        playerExternalId: '321',
      },
      {
        time: '15:00',
        type: GameEventType.Penalty,
        isHomeTeam: true,
        playerName: 'John Doe',
        teamName: 'HC Sparta Praha',
        assists: [],
        playerExternalId: '123',
      },
    ],
  },
}

export const resolveOpeanAiStats = () => {
  evaluateStub.resolves(gameStatsMock)
}

export const checkGameStats = (game: Game) => {
  assert.strictEqual(game?.date.toISOString(), '2022-01-01T00:00:00.000Z')
  assert.strictEqual(game?.externalInfo!.homeTeamName, 'HC Sparta Praha')
  assert.strictEqual(game?.externalInfo.awayTeamName, 'HC Oceláři Třinec')
  assert.strictEqual(game?.externalInfo.leagueName, 'Czech Extraliga')
  assert.strictEqual(game?.stats.homeTeam?.score, 3)
  assert.strictEqual(game?.stats.awayTeam?.score, 2)
  assert.strictEqual(game?.stats.homeTeam?.faceoffs, 30)
  assert.strictEqual(game?.stats.awayTeam?.faceoffs, 20)
  assert.strictEqual(game?.stats.homeTeam.shotsOnGoal, 40)
  assert.strictEqual(game?.stats.awayTeam.shotsOnGoal, 30)
  assert.strictEqual(game?.stats.homeTeam.goalieSaves, 35)
  assert.strictEqual(game?.stats.awayTeam.goalieSaves, 28)
  assert.strictEqual(game?.stats.homeTeam.penalties, 10)
  assert.strictEqual(game?.stats.awayTeam.penalties, 12)
  assert.strictEqual(game?.stats.homeTeam.powerPlayGoals, 1)
  assert.strictEqual(game?.stats.awayTeam.powerPlayGoals, 0)
  assert.strictEqual(game?.stats.homeTeam.blockedShots, 10)
  assert.strictEqual(game?.stats.awayTeam.blockedShots, 10)
  assert.strictEqual(game?.stats.homeTeam.penaltyMinutes, 10)
  assert.strictEqual(game?.stats.awayTeam.penaltyMinutes, 12)
  assert.strictEqual(game?.stats.periodScores, '1:0, 1:1, 1:1')

  assert.strictEqual(game.homeTeamRoster.length, 2)
  assert.strictEqual(game.awayTeamRoster.length, 1)

  const homeGoalie = game.homeTeamRoster.find(
    (player) => player.externalInfo.playerName === 'Dominik Hasek'
  )
  assert.strictEqual(homeGoalie?.goalsAgainst, 2)
  assert.strictEqual(homeGoalie?.saves, 35)
  assert.strictEqual(homeGoalie?.svp, 0.946)
  assert.strictEqual(homeGoalie?.jerseyNumber, 1)

  const homePlayer = game.homeTeamRoster.find(
    (player) => player.externalInfo.playerName === 'John Doe'
  )
  assert.strictEqual(homePlayer?.goals, 1)
  assert.strictEqual(homePlayer?.assists, 1)
  assert.strictEqual(homePlayer?.points, 2)
  assert.strictEqual(homePlayer?.pim, 2)
  assert.strictEqual(homePlayer?.plusMinus, 1)
  assert.strictEqual(homePlayer?.position, RosterPosition.Forward)

  const awayPlayer = game.awayTeamRoster.find(
    (player) => player.externalInfo.playerName === 'Patrik Doe'
  )

  assert.strictEqual(awayPlayer?.goals, 2)
  assert.strictEqual(awayPlayer?.assists, 0)
  assert.strictEqual(awayPlayer?.points, 2)
  assert.strictEqual(awayPlayer?.pim, 4)
  assert.strictEqual(awayPlayer?.plusMinus, 2)
  assert.strictEqual(awayPlayer?.position, RosterPosition.Defenseman)
  assert.strictEqual(game.events.length, 3)

  const firstEvent = game.events.find((event) => event.externalInfo.playerName === 'John Doe')
  assert.strictEqual(firstEvent?.type, GameEventType.Goal)
  assert.strictEqual(firstEvent?.time, '10:00')
  assert.strictEqual(firstEvent?.assists?.length, 0)
  assert.strictEqual(firstEvent?.externalInfo.teamName, 'HC Sparta Praha')
  assert.strictEqual(firstEvent?.externalInfo.playerName, 'John Doe')
  assert.strictEqual(firstEvent?.externalInfo.playerExternalId, '123')

  const secondEvent = game.events.find((event) => event.externalInfo.playerName === 'Patrik Doe')
  assert.strictEqual(secondEvent?.type, GameEventType.Goal)
  assert.strictEqual(secondEvent?.time, '12:00')
  assert.strictEqual(secondEvent?.assists?.length, 1)
  assert.strictEqual(secondEvent?.assists[0].playerName, 'Pavel Zacha')
  assert.strictEqual(secondEvent?.assists[0].playerExternalId, '1234')
  assert.strictEqual(secondEvent?.externalInfo.teamName, 'HC Oceláři Třinec')
  assert.strictEqual(secondEvent?.externalInfo.playerName, 'Patrik Doe')
  assert.strictEqual(secondEvent?.externalInfo.playerExternalId, '321')

  const thirdEvent = game.events.find(
    (event) => event.externalInfo.playerName === 'John Doe' && event.type === GameEventType.Penalty
  )
  assert.strictEqual(thirdEvent?.type, GameEventType.Penalty)
  assert.strictEqual(thirdEvent?.time, '15:00')
  assert.strictEqual(thirdEvent?.assists?.length, 0)
  assert.strictEqual(thirdEvent?.externalInfo.teamName, 'HC Sparta Praha')
  assert.strictEqual(thirdEvent?.externalInfo.playerName, 'John Doe')
  assert.strictEqual(thirdEvent?.externalInfo.playerExternalId, '123')
}
