import assert from 'assert'
import axios from 'axios'
import config from 'config'
import sinon from 'sinon'

import { TeamModel } from '../../src/modules/stats/models/team'
import { UserModel } from '../../src/modules/user/models'
import { Role as dbrole } from '../../src/modules/user/models/user'
import { FeedActivityVerb } from '../../src/services/helpers/streamFeed'
import { PushNotificationType } from '../../src/services/pushNotification'
import { client } from '../utils/graphql'
import {
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
  userStatsQuery,
} from '../utils/users'

import {
  BioGender,
  BioHandedness,
  BioPlayerType,
  BioPosition,
  BioSchoolType,
  ContactType,
  ExternalLinkType,
} from './../graphql/components'
import {
  amplitudeTrackStub,
  pushNotificationServiceStub,
  slackClientStub,
  streamFeedAddActivityStub,
} from '.'

import { faker } from '@faker-js/faker'

describe('User resolver', () => {
  describe('Update User mutation', () => {
    beforeEach(async () => {
      await UserModel.deleteMany({})
    })

    it('can update user - set and unset weight and height', async () => {
      const user = await generateRandomUser()

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          bio: {
            position: BioPosition.Center,
            weight: { kilograms: 100, pounds: 200 },
            height: { feet: 6, inches: 1, centimeters: 100 },
          },
        },
      })

      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.bio?.weight?.kilograms, 100)
      assert.strictEqual(response.updateUser.user?.bio?.weight?.pounds, 200)
      assert.strictEqual(response.updateUser.user?.bio?.height?.feet, 6)
      assert.strictEqual(response.updateUser.user?.bio?.height?.inches, 1)

      const response2 = await client(user.tokens.accessToken).updateUser({
        input: {
          bio: {
            position: BioPosition.Center,
            weight: null,
            height: null,
          },
        },
      })

      assert.strictEqual(response2.updateUser.errors.length, 0)
      assert.strictEqual(response2.updateUser.user?.bio?.weight, null)
      assert.strictEqual(response2.updateUser.user?.bio?.height, null)

      const response3 = await client(user.tokens.accessToken).updateUser({
        input: {
          bio: {
            position: BioPosition.Center,
            weight: { kilograms: 100, pounds: 200 },
            height: { feet: 6, inches: 1, centimeters: 100 },
          },
        },
      })

      assert.strictEqual(response3.updateUser.errors.length, 0)
      assert.strictEqual(response3.updateUser.user?.bio?.weight?.kilograms, 100)
      assert.strictEqual(response3.updateUser.user?.bio?.weight?.pounds, 200)
      assert.strictEqual(response3.updateUser.user?.bio?.height?.feet, 6)
      assert.strictEqual(response3.updateUser.user?.bio?.height?.inches, 1)
    })

    it('can update user profile', async () => {
      const randomUser = await generateRandomUser({ firstname: null, lastname: null })
      const randomEmail = faker.internet.email()
      const team = await TeamModel.create({ name: 'test team' })
      const response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          firstname: 'John',
          lastname: 'Doe',
          dateOfBirth: new Date('2000-03-25'),
          country: 'SK',
          contact: {
            firstname: 'Jana',
            lastname: 'Doeova',
            email: randomEmail,
            phoneNumber: {
              countryPrefix: '421',
              number: '123456789',
            },
            type: ContactType.Mother,
          },
          bio: {
            position: BioPosition.Center,
            handedness: BioHandedness.Right,
            playerType: BioPlayerType.Hybrid,
            schoolType: BioSchoolType.GraduateSchool,
            gender: BioGender.Male,
            height: {
              feet: 6,
              inches: 1,
              centimeters: 100,
            },
            weight: {
              pounds: 180,
              kilograms: 100,
            },
            birthplace: 'Náchod',
            nameOfSchool: 'SPŠ Stavební Náchod',
            graduationYear: 2015,
            cumulativeGPA: 1,
            coreGPA: 2,
            unweightedGPA: 3,
            weightedGPA: 4,
            satScore: 1100,
            actScore: 25,
            toeflScore: 20,
            ncaaEligibility: true,
            naiaEligibility: false,
          },
          phoneNumber: {
            countryPrefix: '421',
            number: '987654321',
          },
          externalLinks: [
            {
              name: 'Seznam',
              url: 'https://www.seznam.cz/',
              type: ExternalLinkType.Custom,
            },
          ],
          boostMessage: 'test boostmessage',
          boost: {
            currency: 'EUR',
            goalAmount: 15000,
          },
          currentTeam: team.id,
        },
      })

      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.firstname, 'John')
      assert.strictEqual(response.updateUser.user?.lastname, 'Doe')
      assert.strictEqual(response.updateUser.user?.slug, 'john-doe')
      assert.strictEqual(
        response.updateUser.user?.dateOfBirth,
        new Date('2000-03-25').toISOString()
      )
      assert.strictEqual(response.updateUser.user?.country, 'SK')
      assert.strictEqual(response.updateUser.user?.contact?.firstname, 'Jana')
      assert.strictEqual(response.updateUser.user?.contact?.lastname, 'Doeova')
      assert.strictEqual(response.updateUser.user?.contact?.email, randomEmail)
      assert.strictEqual(response.updateUser.user?.contact?.phoneNumber?.countryPrefix, '421')
      assert.strictEqual(response.updateUser.user?.contact?.phoneNumber?.number, '123456789')
      assert.strictEqual(response.updateUser.user?.contact?.type, ContactType.Mother)
      assert.strictEqual(response.updateUser.user?.bio?.position, BioPosition.Center)
      assert.strictEqual(response.updateUser.user?.bio?.handedness, BioHandedness.Right)
      assert.strictEqual(response.updateUser.user?.bio?.playerType, BioPlayerType.Hybrid)
      assert.strictEqual(response.updateUser.user?.bio?.schoolType, BioSchoolType.GraduateSchool)
      assert.strictEqual(response.updateUser.user?.bio?.gender, BioGender.Male)
      assert.strictEqual(response.updateUser.user?.bio?.height?.feet, 6)
      assert.strictEqual(response.updateUser.user?.bio?.height?.inches, 1)
      assert.strictEqual(response.updateUser.user?.bio?.height?.centimeters, 100)
      assert.strictEqual(response.updateUser.user?.bio?.weight?.pounds, 180)
      assert.strictEqual(response.updateUser.user?.bio?.weight?.kilograms, 100)
      assert.strictEqual(response.updateUser.user?.bio?.birthplace, 'Náchod')
      assert.strictEqual(response.updateUser.user?.bio?.nameOfSchool, 'SPŠ Stavební Náchod')
      assert.strictEqual(response.updateUser.user?.bio?.graduationYear, 2015)
      assert.strictEqual(response.updateUser.user?.bio?.cumulativeGPA, 1)
      assert.strictEqual(response.updateUser.user?.bio?.coreGPA, 2)
      assert.strictEqual(response.updateUser.user?.bio?.unweightedGPA, 3)
      assert.strictEqual(response.updateUser.user?.bio?.weightedGPA, 4)
      assert.strictEqual(response.updateUser.user?.bio?.satScore, 1100)
      assert.strictEqual(response.updateUser.user?.bio?.actScore, 25)
      assert.strictEqual(response.updateUser.user?.bio?.toeflScore, 20)
      assert.strictEqual(response.updateUser.user?.bio?.ncaaEligibility, true)
      assert.strictEqual(response.updateUser.user?.bio?.naiaEligibility, false)
      assert.strictEqual(response.updateUser.user?.phoneNumber?.countryPrefix, '421')
      assert.strictEqual(response.updateUser.user?.phoneNumber?.number, '987654321')
      assert.strictEqual(response.updateUser.user?.externalLinks?.[0].name, 'Seznam')
      assert.strictEqual(response.updateUser.user?.externalLinks?.[0].url, 'https://www.seznam.cz/')
      assert.strictEqual(response.updateUser.user?.externalLinks?.[0].type, ExternalLinkType.Custom)
      assert.strictEqual(response.updateUser.user?.boost?.currency, 'EUR')
      assert.strictEqual(response.updateUser.user?.boost?.goalAmount, 15000)
      assert.strictEqual(response.updateUser.user?.currentTeam?.id, team.id)

      const updatedUser = await UserModel.findById(randomUser.user._id).populate(['currentTeam'])
      assert.strictEqual(updatedUser?.firstname, 'John')
      assert.strictEqual(updatedUser?.lastname, 'Doe')
      assert.strictEqual(updatedUser?.slug, 'john-doe')
      assert.strictEqual(
        updatedUser?.dateOfBirth?.toISOString(),
        new Date('2000-03-25').toISOString()
      )
      assert.strictEqual(updatedUser?.country, 'SK')
      assert.strictEqual(updatedUser?.contact?.firstname, 'Jana')
      assert.strictEqual(updatedUser?.contact?.lastname, 'Doeova')
      assert.strictEqual(updatedUser?.contact?.email, randomEmail)
      assert.strictEqual(updatedUser?.contact?.phoneNumber?.countryPrefix, '421')
      assert.strictEqual(updatedUser?.contact?.phoneNumber?.number, '123456789')
      assert.strictEqual(updatedUser?.contact?.type, ContactType.Mother)
      assert.strictEqual(updatedUser?.bio?.position, BioPosition.Center)
      assert.strictEqual(updatedUser?.bio?.handedness, BioHandedness.Right)
      assert.strictEqual(updatedUser?.bio?.playerType, BioPlayerType.Hybrid)
      assert.strictEqual(updatedUser?.bio?.schoolType, BioSchoolType.GraduateSchool)
      assert.strictEqual(updatedUser?.bio?.height?.feet, 6)
      assert.strictEqual(updatedUser?.bio?.height?.inches, 1)
      assert.strictEqual(updatedUser?.bio?.height?.centimeters, 100)
      assert.strictEqual(updatedUser?.bio?.weight?.pounds, 180)
      assert.strictEqual(updatedUser?.bio?.weight?.kilograms, 100)
      assert.strictEqual(updatedUser?.bio?.birthplace, 'Náchod')
      assert.strictEqual(updatedUser?.bio?.nameOfSchool, 'SPŠ Stavební Náchod')
      assert.strictEqual(updatedUser?.bio?.graduationYear, 2015)
      assert.strictEqual(updatedUser?.bio?.cumulativeGPA, 1)
      assert.strictEqual(updatedUser?.bio?.coreGPA, 2)
      assert.strictEqual(updatedUser?.bio?.unweightedGPA, 3)
      assert.strictEqual(updatedUser?.bio?.weightedGPA, 4)
      assert.strictEqual(updatedUser?.bio?.satScore, 1100)
      assert.strictEqual(updatedUser?.bio?.actScore, 25)
      assert.strictEqual(updatedUser?.bio?.toeflScore, 20)
      assert.strictEqual(updatedUser?.bio?.ncaaEligibility, true)
      assert.strictEqual(updatedUser?.bio?.naiaEligibility, false)
      assert.strictEqual(updatedUser?.phoneNumber?.countryPrefix, '421')
      assert.strictEqual(updatedUser?.phoneNumber?.number, '987654321')
      assert.strictEqual(updatedUser?.externalLinks?.[0].name, 'Seznam')
      assert.strictEqual(updatedUser?.externalLinks?.[0].url, 'https://www.seznam.cz/')
      assert.strictEqual(updatedUser?.externalLinks?.[0].type, ExternalLinkType.Custom)
      assert.strictEqual(updatedUser?.boost?.currency, 'EUR')
      assert.strictEqual(updatedUser?.boost?.goalAmount, 15000)
      assert.strictEqual(updatedUser?.currentTeam?.id, team.id)

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should send push notification to all users in organization, if user changes current team', async () => {
      const organization = await generateRandomOrganization('Test change team Org')
      const team1 = await generateRandomTeam({ name: 'Test team 1', organization: organization.id })
      const team2 = await generateRandomTeam({ name: 'Test team 2', organization: organization.id })
      const team3 = await generateRandomTeam({ name: 'Test team 3', organization: organization.id })
      const user = await generateRandomUser({ team: team1.id })
      const user2 = await generateRandomUser({ team: team2.id })
      const joiningUser = await generateRandomUser()

      await client(joiningUser.tokens.accessToken).updateUser({
        input: {
          currentTeam: team3.id,
        },
      })

      sinon.assert.calledTwice(pushNotificationServiceStub.sendPushNotification)
      const firstCall = pushNotificationServiceStub.sendPushNotification.getCall(0).args
      const secondCall = pushNotificationServiceStub.sendPushNotification.getCall(1).args

      const title = 'New teammate on GRAET! 👋'
      const message = `Go say hi to ${joiningUser.user.name}, who has just joined ${organization.name}.`

      assert.strictEqual(firstCall[0].name, user.user.name)
      assert.strictEqual(firstCall[1].message, message)
      assert.strictEqual(firstCall[1].title, title)
      assert.strictEqual(firstCall[1].type, PushNotificationType.NEW_TEAM_MEMBER)
      assert.strictEqual(
        firstCall[1].key,
        `newTeammate-${user.user._id}-${joiningUser.user._id}-${new Date().getMonth()}`
      )
      assert.strictEqual(firstCall[1].params.destinationPath, `/${joiningUser.user.slug}`)

      assert.strictEqual(secondCall[0].name, user2.user.name)
      assert.strictEqual(secondCall[1].message, message)
      assert.strictEqual(secondCall[1].title, title)
      assert.strictEqual(secondCall[1].type, PushNotificationType.NEW_TEAM_MEMBER)
      assert.strictEqual(
        secondCall[1].key,
        `newTeammate-${user2.user._id}-${joiningUser.user._id}-${new Date().getMonth()}`
      )
      assert.strictEqual(secondCall[1].params.destinationPath, `/${joiningUser.user.slug}`)

      sinon.assert.calledTwice(streamFeedAddActivityStub)

      sinon.assert.calledWith(
        streamFeedAddActivityStub,
        sinon.match({
          actor: joiningUser.user._id.toString(),
          object: 'user',
          verb: FeedActivityVerb.NEW_TEAM_MEMBER,
          actor_name: joiningUser.user?.name,
          actor_slug: joiningUser.user?.slug,
          foreign_id: joiningUser.user._id.toString(),
          actor_team: organization.name,
        })
      )
    })

    it('should update user profile and keep custom links, even if client is not sending them', async () => {
      const randomUser = await generateRandomUser()

      await UserModel.updateOne(
        {
          _id: randomUser.user._id,
        },
        {
          externalLinks: [
            {
              name: 'Seznam',
              url: 'https://www.seznam.cz/',
              type: ExternalLinkType.Custom,
            },
          ],
        }
      )

      const response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          externalLinks: [],
        },
      })

      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.externalLinks?.length, 1)
      assert.strictEqual(response.updateUser.user?.externalLinks?.[0].name, 'Seznam')
    })

    it("should update user's dateOfBirth and check that it was not changed (only once is allowed)", async () => {
      const randomUser = await generateRandomUser()
      let response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          dateOfBirth: new Date('2000-03-25'),
        },
      })
      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(
        response.updateUser.user?.dateOfBirth,
        new Date('2000-03-25').toISOString()
      )

      response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          dateOfBirth: new Date('2001-01-26'),
        },
      })

      const updatedUser = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(
        updatedUser?.dateOfBirth?.toISOString(),
        new Date('2000-03-25').toISOString()
      )

      sinon.assert.calledTwice(amplitudeTrackStub) // we call updateUser twice, so two calls on amplitude
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it("should update user's country and check the change", async () => {
      const randomUser = await generateRandomUser()
      let response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          country: 'SK',
        },
      })
      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.country, 'SK')

      response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          country: 'CZ',
        },
      })

      const updatedUser = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(updatedUser?.country, 'SK')

      sinon.assert.calledTwice(amplitudeTrackStub) // we call updateUser twice, so two calls on amplitude
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it("should update user's firstname and check the change", async () => {
      const randomUser = await generateRandomUser({ firstname: null })
      let response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          firstname: 'John',
        },
      })
      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.firstname, 'John')

      response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          firstname: 'Peter',
        },
      })

      const updateUser = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(updateUser?.firstname, 'John')

      sinon.assert.calledTwice(amplitudeTrackStub) // we call updateUser twice, so two calls on amplitude
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it("should update user's lastname and check the change", async () => {
      const randomUser = await generateRandomUser({ firstname: 'Franta', lastname: null })
      let response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          lastname: 'Doe',
        },
      })
      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.lastname, 'Doe')

      response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          lastname: 'Houska',
        },
      })

      const updateUser = await UserModel.findById(randomUser.user._id)
      assert.strictEqual(updateUser?.lastname, 'Doe')

      sinon.assert.calledTwice(amplitudeTrackStub) // we call updateUser twice, so two calls on amplitude
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should return UserNotFoundError if user is not found', async () => {
      const randomUser = await generateRandomUser()
      await UserModel.deleteOne({ _id: randomUser.user._id })

      const response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          firstname: 'John',
        },
      })

      assert.strictEqual(response.updateUser.user, null)
      assert.strictEqual(response.updateUser.errors.length, 1)

      assert.strictEqual(
        response.updateUser.errors[0].message,
        `User with id ${randomUser.user._id} not found`
      )
    })

    it('should generate a unique slug if a user with the same slug exists', async () => {
      const user1 = await generateRandomUser({
        firstname: null,
        lastname: null,
        role: dbrole.PLAYER,
      })

      const user2 = await generateRandomUser({
        firstname: null,
        lastname: null,
        role: dbrole.PLAYER,
      })

      await client(user1.tokens.accessToken).updateUser({
        input: {
          firstname: 'Peter',
          lastname: 'Okurka',
        },
      })
      await client(user2.tokens.accessToken).updateUser({
        input: {
          firstname: 'Peter',
          lastname: 'Okurka',
        },
      })

      const updatedUser1 = await UserModel.findById(user1.user._id)
      const updatedUser2 = await UserModel.findById(user2.user._id)
      assert.strictEqual(updatedUser1?.slug, 'peter-okurka')
      assert.strictEqual(updatedUser2?.slug?.includes('peter-okurka-'), true)

      sinon.assert.calledTwice(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(0),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: user1.user._id.toString() })
      )
      sinon.assert.calledWith(
        amplitudeTrackStub.getCall(1),
        'User Updated',
        sinon.match({}),
        sinon.match({ user_id: user2.user._id.toString() })
      )
    })

    it('should unset attributes from user if null is passed', async function () {
      const randomUser = await generateRandomUser()

      const response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          phoneNumber: {
            countryPrefix: '421',
            number: '123456789',
          },
        },
      })
      assert.strictEqual(response.updateUser.errors.length, 0)

      const updatedResponse = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          phoneNumber: null,
        },
      })
      assert.strictEqual(updatedResponse.updateUser.errors.length, 0)

      const updateUser = await UserModel.findById(randomUser.user._id)

      assert.strictEqual(updateUser?.phoneNumber, undefined)

      sinon.assert.calledTwice(amplitudeTrackStub) // we call updateUser twice, so two calls on amplitude
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should send slack message if user added Elite prospect link', async () => {
      const axiosGetStub = sinon.stub(axios, 'get').resolves({
        data: Buffer.from('mocked data'),
      })
      const axiosPostStub = sinon.stub(axios, 'post').resolves(userStatsQuery())

      const randomUser = await generateRandomUser({ firstname: 'Karel', lastname: 'Barel' })

      const response = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          externalLinks: [
            {
              name: 'Elite Prospects',
              url: 'https://www.eliteprospects.com/player/123456',
              type: ExternalLinkType.EliteProspects,
            },
          ],
        },
      })

      assert.strictEqual(response.updateUser.errors.length, 0)

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `User Karel Barel has added Elite Prospects link., ${response.updateUser.user?.externalLinks?.find((link) => link.type === ExternalLinkType.EliteProspects)?.url}, view in <http://admin.${config.domain}/dashboard/users/form/?id=${response.updateUser.user?.id}|admin panel>`
          )
        )
      )

      sinon.assert.calledOnce(axiosPostStub)

      axiosGetStub.restore()
      axiosPostStub.restore()

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should send slack message if user updated Elite prospect link', async () => {
      const axiosGetStub = sinon.stub(axios, 'get').resolves({
        data: Buffer.from('mocked data'),
      })
      const axiosPostStub = sinon.stub(axios, 'post').resolves(userStatsQuery('team-slug-2'))

      const randomUser = await generateRandomUser({ firstname: 'Test', lastname: 'User' })

      await UserModel.updateOne(
        { _id: randomUser.user._id },
        {
          $set: {
            externalLinks: [
              {
                name: 'Elite Prospects',
                url: 'https://www.eliteprospects.com/player/123456',
                type: ExternalLinkType.EliteProspects,
              },
            ],
          },
        }
      )

      const response2 = await client(randomUser.tokens.accessToken).updateUser({
        input: {
          externalLinks: [
            {
              name: 'Elite Prospects',
              url: 'https://www.eliteprospects.com/player/654321',
              type: ExternalLinkType.EliteProspects,
            },
          ],
        },
      })
      assert.strictEqual(response2.updateUser.errors.length, 0)

      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has('channel', 'db-stage')
      )
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `User ${response2.updateUser.user?.name} has updated Elite Prospects link., ${response2.updateUser.user?.externalLinks?.find((link) => link.type === ExternalLinkType.EliteProspects)?.url}, view in <http://admin.${config.domain}/dashboard/users/form/?id=${response2.updateUser.user?.id}|admin panel>`
          )
        )
      )

      sinon.assert.calledOnce(axiosPostStub)

      axiosGetStub.restore()
      axiosPostStub.restore()

      sinon.assert.calledOnce(amplitudeTrackStub)
      sinon.assert.calledWith(
        amplitudeTrackStub,
        'User Updated',
        {},
        { user_id: randomUser.user._id.toString() }
      )
    })

    it('should update user intro', async () => {
      const user = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
      })

      const intro =
        'This is my professional introduction. I am a sports professional with extensive experience in the field.'

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          bio: {
            intro,
          },
        },
      })

      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.bio?.intro, intro)

      const updatedUser = await UserModel.findById(user.user._id)
      assert.strictEqual(updatedUser?.bio?.intro, intro)
    })

    it('should validate intro length', async () => {
      const user = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
      })

      const longIntro = 'a'.repeat(401)

      try {
        await client(user.tokens.accessToken).updateUser({
          input: {
            bio: {
              intro: longIntro,
            },
          },
        })
      } catch (error) {
        assert.strictEqual(error.response?.errors.length, 1)
        assert.strictEqual(error.response?.errors[0].message, 'Argument Validation Error')
      }
    })

    it('should not send slack notification for non-sports professionals', async () => {
      const user = await generateRandomUser({
        role: dbrole.PLAYER,
      })

      const intro = 'This is my introduction as a player.'

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          bio: {
            intro,
          },
        },
      })

      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.bio?.intro, intro)

      // Verify no slack notification was sent
      sinon.assert.notCalled(slackClientStub.chat.postMessage)
    })

    it('should send slack notification for sports professionals', async () => {
      const user = await generateRandomUser({
        role: dbrole.SPORTS_PROFESSIONAL,
      })

      const intro = 'This is my professional introduction.'

      const response = await client(user.tokens.accessToken).updateUser({
        input: {
          bio: {
            intro,
          },
        },
      })

      assert.strictEqual(response.updateUser.errors.length, 0)
      assert.strictEqual(response.updateUser.user?.bio?.intro, intro)

      // Verify slack notification was sent
      sinon.assert.calledWith(
        slackClientStub.chat.postMessage,
        sinon.match.has(
          'text',
          sinon.match(
            `User (${user.user.name} <https://www.graet.com/${user.user.slug}|${user.user.name} at Graet>) added intro: ${intro}`
          )
        )
      )
    })
  })
})
