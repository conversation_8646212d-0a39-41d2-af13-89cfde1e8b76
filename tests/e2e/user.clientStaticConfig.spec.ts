import assert from 'assert'

import { client } from '../utils/graphql'
import { generateRandomUser } from '../utils/users'

describe('User clientStaticConfig', () => {
  it('should return clientStaticConfig for current user', async () => {
    const randomUser = await generateRandomUser()

    const response = await client(randomUser.tokens.accessToken).currentUser()

    assert.strictEqual(response.currentUser.clientStaticConfig.inviteContest.enabled, true)
  })
})
