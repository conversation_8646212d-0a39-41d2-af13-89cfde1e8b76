import assert from 'assert'

import { StatsSource, UserStatsModel } from '../../src/modules/stats/models/userStats'
import { client } from '../utils/graphql'
import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from '../utils/users'

describe('Aggregated User Stats', () => {
  beforeEach(async () => {
    await UserStatsModel.deleteMany({})
  })

  it('should correctly aggregate stats from the same season, team, and league', async () => {
    // Create test data
    const randomUser = await generateRandomUser()
    const organization = await generateRandomOrganization('Test Aggregation Org')
    const league = await generateRandomLeague('Test Aggregation League')
    const team = await generateRandomTeam({
      name: 'Test Aggregation Team',
      organization: organization.id,
    })

    // Create stats with stages for the same season, team, and league
    await UserStatsModel.create({
      user: randomUser.user._id,
      team: team.id,
      league: league._id,
      season: '2023-2024',
      gamesPlayed: 10,
      goals: 5,
      assists: 7,
      pim: 4,
      plusMinus: 3,
      source: StatsSource.SCRAPER,
      externalInfo: {
        externalTeamName: 'Test Aggregation Team',
        externalTeamCountry: 'CZ',
        externalLeagueName: 'Test Aggregation League',
        externalLeagueCountry: 'CZ',
        stage: { value: 'Regular Season', label: 'Regular Season' },
      },
    })

    await UserStatsModel.create({
      user: randomUser.user._id,
      team: team.id,
      league: league._id,
      season: '2023-2024',
      gamesPlayed: 5,
      goals: 3,
      assists: 2,
      pim: 2,
      plusMinus: 1,
      source: StatsSource.SCRAPER,
      externalInfo: {
        externalTeamName: 'Test Aggregation Team',
        externalTeamCountry: 'CZ',
        externalLeagueName: 'Test Aggregation League',
        externalLeagueCountry: 'CZ',
        stage: { value: 'Playoffs', label: 'Playoffs' },
      },
    })

    // Query through GraphQL API
    const response = await client(randomUser.tokens.accessToken).userStats({
      filter: {
        user: randomUser.user._id.toString(),
      },
    })

    // Verify results
    assert.strictEqual(
      response.userStats.edges.length,
      1,
      'Should return a single aggregated entry'
    )
    assert.strictEqual(
      response.userStats.edges[0].node.gamesPlayed,
      15,
      'Games played should be summed'
    )
    assert.strictEqual(response.userStats.edges[0].node.goals, 8, 'Goals should be summed')
    assert.strictEqual(response.userStats.edges[0].node.assists, 9, 'Assists should be summed')
    assert.strictEqual(response.userStats.edges[0].node.pim, 6, 'PIM should be summed')
    assert.strictEqual(response.userStats.edges[0].node.plusMinus, 4, 'Plus/minus should be summed')
    assert.strictEqual(
      response.userStats.edges[0].node.season,
      '2023-2024',
      'Season should be preserved'
    )
  })

  it('should prioritize SCRAPER source over EP source', async () => {
    // Create test data
    const randomUser = await generateRandomUser()
    const organization = await generateRandomOrganization('Test Source Priority Org')
    const league = await generateRandomLeague('Test Source Priority League')
    const team = await generateRandomTeam({
      name: 'Test Source Priority Team',
      organization: organization.id,
    })

    // Create EP stats
    const _epStats = await UserStatsModel.create({
      user: randomUser.user._id,
      team: team.id,
      league: league._id,
      season: '2023-2024',
      gamesPlayed: 20,
      goals: 10,
      assists: 15,
      source: StatsSource.EP,
      externalInfo: {
        externalTeamName: 'Test Source Priority Team EP',
        externalLeagueName: 'Test Source Priority League EP',
      },
    })

    // Create SCRAPER stats
    await UserStatsModel.create({
      user: randomUser.user._id,
      team: team.id,
      league: league._id,
      season: '2023-2024',
      gamesPlayed: 18,
      goals: 8,
      assists: 12,
      source: StatsSource.SCRAPER,
      externalInfo: {
        externalTeamName: 'Test Source Priority Team Scraper',
        externalLeagueName: 'Test Source Priority League Scraper',
      },
    })

    // Query through GraphQL API
    const response = await client(randomUser.tokens.accessToken).userStats({
      filter: {
        user: randomUser.user._id.toString(),
      },
    })

    // Verify results
    assert.strictEqual(response.userStats.edges.length, 1, 'Should return a single entry')
    assert.strictEqual(response.userStats.edges[0].node.gamesPlayed, 18, 'Should use SCRAPER stats')
    assert.strictEqual(response.userStats.edges[0].node.goals, 8, 'Should use SCRAPER stats')
    assert.strictEqual(response.userStats.edges[0].node.assists, 12, 'Should use SCRAPER stats')
  })

  it('should correctly handle stats without stages', async () => {
    // Create test data
    const randomUser = await generateRandomUser()
    const organization = await generateRandomOrganization('Test No Stage Org')
    const league = await generateRandomLeague('Test No Stage League')
    const team = await generateRandomTeam({
      name: 'Test No Stage Team',
      organization: organization.id,
    })

    // Create stats without stage
    const _noStageStats = await UserStatsModel.create({
      user: randomUser.user._id,
      team: team.id,
      league: league._id,
      season: '2023-2024',
      gamesPlayed: 25,
      goals: 12,
      assists: 18,
      source: StatsSource.SCRAPER,
    })

    // Create stats with stage
    const _withStageStats = await UserStatsModel.create({
      user: randomUser.user._id,
      team: team.id,
      league: league._id,
      season: '2023-2024',
      gamesPlayed: 5,
      goals: 3,
      assists: 2,
      source: StatsSource.SCRAPER,
      externalInfo: {
        stage: { value: 'Playoffs', label: 'Playoffs' },
      },
    })

    // Query through GraphQL API
    const response = await client(randomUser.tokens.accessToken).userStats({
      filter: {
        user: randomUser.user._id.toString(),
      },
    })

    // Verify results
    assert.strictEqual(response.userStats.edges.length, 1, 'Should return a single entry')
    assert.strictEqual(
      response.userStats.edges[0].node.gamesPlayed,
      25,
      'Should use stats without stage'
    )
    assert.strictEqual(response.userStats.edges[0].node.goals, 12, 'Should use stats without stage')
    assert.strictEqual(
      response.userStats.edges[0].node.assists,
      18,
      'Should use stats without stage'
    )
  })

  it('should correctly aggregate stats from different seasons, teams, or leagues', async () => {
    // Create test data
    const randomUser = await generateRandomUser()
    const organization = await generateRandomOrganization('Test Multiple Org')
    const league1 = await generateRandomLeague('Test League 1')
    const league2 = await generateRandomLeague('Test League 2')
    const team1 = await generateRandomTeam({
      name: 'Test Team 1',
      organization: organization.id,
    })
    const team2 = await generateRandomTeam({
      name: 'Test Team 2',
      organization: organization.id,
    })

    // Create stats for different seasons
    await UserStatsModel.create({
      user: randomUser.user._id,
      team: team1.id,
      league: league1._id,
      season: '2022-2023',
      gamesPlayed: 30,
      goals: 15,
      assists: 20,
      source: StatsSource.SCRAPER,
    })

    await UserStatsModel.create({
      user: randomUser.user._id,
      team: team1.id,
      league: league1._id,
      season: '2023-2024',
      gamesPlayed: 28,
      goals: 14,
      assists: 18,
      source: StatsSource.SCRAPER,
    })

    // Create stats for different teams
    await UserStatsModel.create({
      user: randomUser.user._id,
      team: team2.id,
      league: league1._id,
      season: '2023-2024',
      gamesPlayed: 10,
      goals: 5,
      assists: 7,
      source: StatsSource.SCRAPER,
    })

    // Create stats for different leagues
    await UserStatsModel.create({
      user: randomUser.user._id,
      team: team1.id,
      league: league2._id,
      season: '2023-2024',
      gamesPlayed: 8,
      goals: 4,
      assists: 6,
      source: StatsSource.SCRAPER,
    })

    // Query through GraphQL API
    const response = await client(randomUser.tokens.accessToken).userStats({
      filter: {
        user: randomUser.user._id.toString(),
      },
      pagination: {
        first: 10, // Make sure we get all entries
      },
    })

    // Verify results
    assert.strictEqual(
      response.userStats.edges.length,
      4,
      'Should keep separate entries for different seasons/teams/leagues'
    )

    // Find each entry and verify
    const season1Entry = response.userStats.edges.find(
      (edge) => edge.node.season === '2022-2023' && edge.node.team?.id === team1.id
    )?.node
    const season2Entry = response.userStats.edges.find(
      (edge) =>
        edge.node.season === '2023-2024' &&
        edge.node.team?.id === team1.id &&
        edge.node.league?.id === league1._id.toString()
    )?.node
    const team2Entry = response.userStats.edges.find(
      (edge) => edge.node.team?.id === team2.id
    )?.node
    const league2Entry = response.userStats.edges.find(
      (edge) => edge.node.league?.id === league2._id.toString()
    )?.node

    assert.ok(season1Entry, 'Should have entry for season 2022-2023')
    assert.ok(season2Entry, 'Should have entry for season 2023-2024 with team1 and league1')
    assert.ok(team2Entry, 'Should have entry for team2')
    assert.ok(league2Entry, 'Should have entry for league2')

    assert.strictEqual(season1Entry?.gamesPlayed, 30)
    assert.strictEqual(season2Entry?.gamesPlayed, 28)
    assert.strictEqual(team2Entry?.gamesPlayed, 10)
    assert.strictEqual(league2Entry?.gamesPlayed, 8)
  })
})
