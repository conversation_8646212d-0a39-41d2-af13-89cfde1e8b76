import assert from 'assert'
import sinon from 'sinon'

import { UserModel } from '../../src/modules/user/models'
import { generateRandomUser } from '../utils/users'

import { client } from './../utils/graphql'
import { stripeClientStub } from '.'

describe('Balance resolver', () => {
  it('should throw an error if user not found', async () => {
    const user = await generateRandomUser()
    const findByIdStub = sinon.stub(UserModel, 'findById').resolves(null)

    try {
      await client(user.tokens.accessToken).stripeAccountBalance()
      assert.fail('Expected error was not thrown')
    } catch (err) {
      assert.strictEqual(
        err.response.errors[0].message,
        'Failed to get account balance. Please try again later.'
      )
    }

    sinon.assert.calledOnce(findByIdStub)
    sinon.restore()
  })

  it("should respond if user don't have stripe account", async () => {
    const user = await generateRandomUser()

    const response = await client(user.tokens.accessToken).stripeAccountBalance()
    assert.strictEqual(response.stripeAccountBalance.balance.available.amount, 0)
    assert.strictEqual(response.stripeAccountBalance.balance.available.currency, 'USD')
    assert.strictEqual(response.stripeAccountBalance.balance.pending.amount, 0)
    assert.strictEqual(response.stripeAccountBalance.balance.pending.currency, 'USD')
    assert.strictEqual(response.stripeAccountBalance.transactions.length, 0)
  })

  it('should respond with user balance', async () => {
    const user = await generateRandomUser()

    stripeClientStub.balance.retrieve.resolves({
      available: [{ amount: 100, currency: 'USD' }],
      pending: [{ amount: 10, currency: 'CZ' }],
    })

    stripeClientStub.payouts.list.resolves({
      data: [
        {
          id: 'payout_1',
          amount: 700,
          currency: 'usd',
          status: 'available',
          description: 'Payout for August',
          created: **********,
        },
        {
          id: 'payout_2',
          amount: 300,
          currency: 'CZ',
          status: 'pending',
          description: 'Payout for September',
          created: **********,
        },
      ],
    })
    await UserModel.updateOne(
      {
        _id: user.user._id,
      },
      {
        stripeAccountId: 'valid-stripe-account-id',
      }
    )

    const response = await client(user.tokens.accessToken).stripeAccountBalance()

    assert.strictEqual(response.stripeAccountBalance.balance.available.amount, 100)
    assert.strictEqual(response.stripeAccountBalance.balance.available.currency, 'USD')
    assert.strictEqual(response.stripeAccountBalance.balance.pending.amount, 10)
    assert.strictEqual(response.stripeAccountBalance.balance.pending.currency, 'CZ')
    assert.strictEqual(response.stripeAccountBalance.transactions.length, 2)
    assert.strictEqual(response.stripeAccountBalance.transactions[0].id, 'payout_1')
    assert.strictEqual(response.stripeAccountBalance.transactions[0].status, 'available')
    assert.strictEqual(response.stripeAccountBalance.transactions[0].payout.amount, 700)
    assert.strictEqual(response.stripeAccountBalance.transactions[0].payout.currency, 'usd')
    assert.strictEqual(
      response.stripeAccountBalance.transactions[0].createdAt,
      new Date(********** * 1000).toISOString()
    )
    assert.strictEqual(response.stripeAccountBalance.transactions[1].id, 'payout_2')
    assert.strictEqual(response.stripeAccountBalance.transactions[1].status, 'pending')
    assert.strictEqual(response.stripeAccountBalance.transactions[1].payout.amount, 300)
    assert.strictEqual(response.stripeAccountBalance.transactions[1].payout.currency, 'CZ')
    assert.strictEqual(
      response.stripeAccountBalance.transactions[1].createdAt,
      new Date(********** * 1000).toISOString()
    )
  })
})
