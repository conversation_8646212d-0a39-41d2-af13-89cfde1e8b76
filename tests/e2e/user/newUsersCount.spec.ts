import assert from 'assert'

import { getMongoDBConnection } from '../../../src/helpers/mongoose.connection'
import { UserModel } from '../../../src/modules/user/models'
import { client } from '../../utils/graphql'
import { generateRandomUser } from '../../utils/users'

describe('New Users Count Query', () => {
  beforeEach(async () => {
    await UserModel.deleteMany({})
  })

  it('should correctly count users from a specific country registered since April 20th', async () => {
    // Create a date for April 20th of the current year (fixed date)
    const sinceDate = new Date(2025, 3, 20) // Month is 0-indexed, so 3 = April

    // Create some users with different countries and dates
    // 1. Czech user created before the date (should not be counted)
    const beforeDate = new Date(sinceDate)
    beforeDate.setDate(beforeDate.getDate() - 2)
    const userBefore = await generateRandomUser({ country: 'CZ' })
    const connection = getMongoDBConnection()
    // update user via direct connection, so we can update createdAt
    await connection?.db
      ?.collection('users')
      .updateOne({ _id: userBefore.user._id }, { $set: { createdAt: beforeDate } })

    // 2. Czech users created after the date (should be counted)
    const afterDate = new Date(sinceDate)
    afterDate.setDate(afterDate.getDate() + 1)
    const userAfter1 = await generateRandomUser({ country: 'CZ' })
    await UserModel.updateOne({ _id: userAfter1.user._id }, { createdAt: afterDate })

    const userAfter2 = await generateRandomUser({ country: 'CZ' })
    await UserModel.updateOne({ _id: userAfter2.user._id }, { createdAt: afterDate })

    // 3. Non-Czech user created after the date (should not be counted)
    const userSK = await generateRandomUser({ country: 'SK' })
    await UserModel.updateOne({ _id: userSK.user._id }, { createdAt: afterDate })

    const czResponse = await client().newUsersCount({
      country: 'CZ',
    })

    assert.strictEqual(czResponse.newUsersCount.count, 2)

    // Query the newUsersCount for a country with no matching users
    const usResponse = await client().newUsersCount({
      country: 'US',
    })

    // Verify the response - should return 0 for a country with no users
    assert.strictEqual(usResponse.newUsersCount.count, 0)
  })
})
