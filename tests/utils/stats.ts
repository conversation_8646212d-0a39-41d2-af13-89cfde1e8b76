import {
  CrawlerDomainConfigModel,
  CrawlerEntity,
  FetchType,
} from '../../src/modules/admin/models/crawlerDomainConfig'
import {
  DiscoveredUrl,
  DiscoveredUrlModel,
  DiscoveredUrlScraperInfo,
} from '../../src/modules/admin/models/discoveredUrl'
import { Game, GameModel } from '../../src/modules/stats/models/game/game'
import {
  generateLeagueStatsKey,
  LeagueStats,
  LeagueStatsModel,
} from '../../src/modules/stats/models/leagueStats/leagueStats'
import { Team } from '../../src/modules/stats/models/team'

import {
  generateRandomLeague,
  generateRandomOrganization,
  generateRandomTeam,
  generateRandomUser,
} from './users'

import { Ref } from '@typegoose/typegoose'

type GenerateDiscoverUrlProps = {
  entity?: CrawlerEntity
  extractionScript?: string | null
  scraperInfo?: DiscoveredUrlScraperInfo | null
  fetchType?: FetchType
}

export const generateDiscoverUrl = async (
  props?: GenerateDiscoverUrlProps | undefined,
  domainNameSuffix: string = ''
) => {
  const domain = `example${domainNameSuffix}.com`

  const crawlerDomainConfig = await CrawlerDomainConfigModel.create({
    domain,
    urlConfigs: [
      {
        entity: props?.entity ?? 'game',
        extractionScript:
          props?.extractionScript === null
            ? null
            : (props?.extractionScript ?? 'return { homeTeam: "Home", awayTeam: "Away" }'),
        urlPatternRegExp: domain,
        fetchType: props?.fetchType ?? FetchType.Puppeteer,
      },
    ],
  })

  const discoveredUrl = await DiscoveredUrlModel.create({
    url: `https://${domain}`,
    externalId: '123',
    entity: props?.entity ?? CrawlerEntity.GAME,
    crawlerDomainConfig,
    scraperInfo: props?.scraperInfo,
  })

  return discoveredUrl
}

export const generateLeagueStat = async ({
  discoveredUrl,
  leagueStats,
}: {
  discoveredUrl: DiscoveredUrl
  leagueStats: Omit<LeagueStats, 'crawlerDomainConfig' | 'discoveredUrl' | '_id' | 'syncEntities'>
}) => {
  return await LeagueStatsModel.create({
    crawlerDomainConfig: discoveredUrl.crawlerDomainConfig,
    discoveredUrl,
    ...leagueStats,
    key: generateLeagueStatsKey(leagueStats as LeagueStats),
  })
}

export const generateRandomGameStats = async ({
  nameSuffix,
  gameStatsPayload = {},
  numPlayersInEachTeam = 2,
}: {
  nameSuffix?: string
  gameStatsPayload?: Partial<
    Omit<Game, 'homeTeam' | 'awayTeam'> & {
      homeTeam: Ref<Team> | null | undefined
      awayTeam: Ref<Team> | null | undefined
    }
  >
  numPlayersInEachTeam?: number
}): Promise<Game> => {
  const playersHome = await Promise.all(
    Array(numPlayersInEachTeam)
      .fill(null)
      .map(() => generateRandomUser())
  )

  const playersAway = await Promise.all(
    Array(numPlayersInEachTeam)
      .fill(null)
      .map(() => generateRandomUser())
  )

  const homeOrganization =
    (gameStatsPayload.homeTeam as Team)?.organization ??
    (await generateRandomOrganization(`TestGameStatsHomeOrganization${nameSuffix}`))

  const awayOrganization =
    (gameStatsPayload.awayTeam as Team)?.organization ??
    (await generateRandomOrganization(`TestGameStatsAwayOrganization${nameSuffix}`))

  const league = await generateRandomLeague(`TestGameStatsLeague${nameSuffix}`)
  const team1 =
    gameStatsPayload.homeTeam === undefined
      ? await generateRandomTeam({
          name: `TestGameStatsTeam1${nameSuffix}`,
          organization: homeOrganization._id.toString(),
        })
      : null
  const team2 =
    gameStatsPayload.awayTeam === undefined
      ? await generateRandomTeam({
          name: `TestGameStatsTeam2${nameSuffix}`,
          organization: awayOrganization._id.toString(),
        })
      : null

  const discoveredUrl =
    !gameStatsPayload?.discoveredUrl && (await generateDiscoverUrl(undefined, nameSuffix))

  return await GameModel.create({
    date: gameStatsPayload?.date || new Date(),
    externalId: gameStatsPayload?.externalId || `${Date.now()}`,
    discoveredUrl: gameStatsPayload?.discoveredUrl || (discoveredUrl && discoveredUrl._id),
    externalInfo: gameStatsPayload?.externalInfo || {
      homeTeamName: `TestGameStatsTeam1${nameSuffix}`,
      awayTeamName: `TestGameStatsTeam2${nameSuffix}`,
      leagueName: `TestGameStatsLeague${nameSuffix}`,
    },
    events: gameStatsPayload?.events || [],
    stats: gameStatsPayload?.stats || {},
    homeTeamRoster:
      gameStatsPayload?.homeTeamRoster ||
      playersHome.map((player) => ({ player: player.user._id })),
    awayTeamRoster:
      gameStatsPayload?.awayTeamRoster ||
      playersAway.map((player) => ({ player: player.user._id })),
    homeTeam: team1?._id ?? null,
    awayTeam: team2?._id ?? null,
    league: gameStatsPayload?.league?._id || league._id,
    ignore: gameStatsPayload?.ignore ?? false,
    ...gameStatsPayload,
  })
}
