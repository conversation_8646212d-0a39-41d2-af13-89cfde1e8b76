import { isArray } from 'class-validator'
import config from 'config'
import jwt from 'jsonwebtoken'
import moment from 'moment'
import { Types } from 'mongoose'
import sinon from 'sinon'

import { ContextUser } from '../../src/'
import { slugifyString } from '../../src/helpers/string'
import { AdminUser, AdminUserModel } from '../../src/modules/admin/models/adminUser'
import { LeagueModel } from '../../src/modules/stats/models/league'
import { OrganizationModel } from '../../src/modules/stats/models/organization'
import { Team, TeamModel } from '../../src/modules/stats/models/team'
import { EPPlayerStats } from '../../src/modules/stats/services/eliteprospects/graphql'
import { ExternalConnection } from '../../src/modules/stats/types/shared'
import { recalculateTeamPlayersCount } from '../../src/modules/user/helpers/currentTeam'
import { UserModel } from '../../src/modules/user/models'
import { AgencyModel } from '../../src/modules/user/models/agency'
import { BioPosition, Device, Role, SubRole, User } from '../../src/modules/user/models/user'
import { CreateAgencyInput } from '../../src/modules/user/types/agency'
import { UpdateUserInputAdmin } from '../../src/modules/user/types/user'
import { UserRole } from '../../src/types/general'
import {
  AlternativeName,
  BioGender,
  BioHandedness,
  BioPlayerType,
  BioPosition as BioPositionGraphQL,
  BioSchoolType,
  ContactType,
  ExternalLinkType,
  LeagueGenderCategory,
  LeagueLevel,
  LeagueType,
  SportCategory,
  UpdateTeamInput,
  WallVideo,
} from '../graphql/components'

import { client } from './graphql'

import { faker } from '@faker-js/faker'

export interface GenerateRandomUserResponse {
  user: User
  tokens: {
    accessToken: string
  }
}

export interface GenerateRandomAdminUserResponse {
  user: AdminUser
  tokens: {
    accessToken: string
  }
}

type GenerateRandomTeamInput = Omit<UpdateTeamInput, 'alternativeNames'> & {
  shortNameSlug?: string
  slug?: string
  alternativeNames?: string[]
  id?: Types.ObjectId
}

export const generateRandomTeam = async (input?: GenerateRandomTeamInput) => {
  return await TeamModel.create({
    _id: input?.id,
    name: input?.name ?? 'Team 1',
    slug: slugifyString(input?.name as string),
    organization:
      input?.organization ?? (await OrganizationModel.findOne({ name: 'Test organization' }))?.id,
    shortNameSlug: input?.shortNameSlug ?? 'T1',
    alternativeNames: input?.alternativeNames
      ? (input?.alternativeNames).map((name) => ({
          name,
          slug: slugifyString(name),
        }))
      : [],
    leagues: input?.leagues ?? [],
    country: input?.country ?? 'CZ',
  })
}

export const generateRandomAgency = async (
  name: string = 'test agency',
  website: string = 'https://www.example.com',
  integrated: boolean = false,
  categories: SportCategory[] = [SportCategory.Hockey],
  restInput: Partial<CreateAgencyInput> = {}
) => {
  return await AgencyModel.create({
    name,
    website,
    integrated,
    categories,
    slug: slugifyString(name),
    ...restInput,
  })
}

export const generateRandomOrganization = async (
  name: string = 'Test organization',
  country: string = 'CZ',
  checked: boolean | undefined = undefined,
  slug: string = slugifyString(name),
  logo: string | undefined = undefined
) => {
  return await OrganizationModel.create({ name, country, slug, checked, logo })
}

export const generateRandomLeague = async (
  name: string = 'Test league',
  country: string | string[] = 'CZ',
  slug: string = slugifyString(name),
  genderCategory: LeagueGenderCategory = LeagueGenderCategory.Men,
  level: LeagueLevel = LeagueLevel.Professional,
  type: LeagueType = LeagueType.League,
  scraped: boolean | undefined = undefined,
  checked: boolean | undefined = undefined,
  alternativeNames: AlternativeName[] = []
) => {
  return await LeagueModel.create({
    name,
    countries: isArray(country) ? country : [country],
    slug,
    genderCategory,
    level,
    type,
    scraped,
    checked,
    alternativeNames,
  })
}

export const generateRandomUser = async (
  input: Partial<
    UpdateUserInputAdmin & {
      team: Team | undefined | null
      subrole: SubRole | undefined
      agency: Types.ObjectId | undefined
      highlights: Partial<WallVideo>[] | undefined
      avatar: string | undefined
      registeredDevices: Device[] | undefined
      externalConnections: ExternalConnection[]
      position?: BioPosition | undefined
      possibleYearsOfBirth?: number[]
      numberOfVideos?: number
    }
  > = {}
): Promise<GenerateRandomUserResponse> => {
  const userTeam =
    input.team === null ? null : input.team || (await TeamModel.findOne({ name: 'Team 1' }))?.id
  const generatedFirstname =
    input.firstname === null ? null : input.firstname || faker.person.firstName()
  const generatedLastname =
    input.lastname === null ? null : input.lastname || faker.person.lastName()

  const slug = slugifyString(`${generatedFirstname} ${generatedLastname}`)

  const user = await UserModel.create({
    ...input,
    ...(generatedFirstname !== null ? { slug, nonUniqueSlug: slug } : {}),
    email: faker.internet.email({
      firstName: `${generatedFirstname}`,
      lastName: `${generatedLastname}${faker.lorem.word()}`,
    }),
    possibleYearsOfBirth: input.possibleYearsOfBirth,
    numberOfVideos: input.numberOfVideos,
    firstname: generatedFirstname,
    lastname: generatedLastname,
    nonUniqueSlug: slugifyString(`${generatedFirstname} ${generatedLastname}`),
    currentTeam: userTeam,
    role: input.role ?? Role.PLAYER,
    subrole: input.subrole,
    country: input.country,
    dateOfBirth: input.dateOfBirth,
    highlights: input.highlights,
    verificationCode: {
      code: '1234',
      expiresAt: new Date(Date.now() + 1000 * 60 * 60),
    },
    registeredDevices: input.registeredDevices,
    ...(input.agency ? { bio: { agency: input.agency } } : {}),
    ...(input.position ? { bio: { position: input.position } } : {}),
    ...(input.externalConnections ? { externalConnections: input.externalConnections } : {}),
  })

  // recalculate team players count
  if (userTeam) {
    await recalculateTeamPlayersCount(user, userTeam)
  }

  const payload: ContextUser = {
    id: user._id.toString(),
    email: user.email,
    role: UserRole.USER,
  }

  return {
    user,
    tokens: {
      accessToken: jwt.sign(payload, config.jwt.secret, { expiresIn: '9000d' }),
    },
  }
}

export const generateRandomAdminUser = async (
  firstname: string | null | undefined = undefined,
  lastname: string | null | undefined = undefined
): Promise<GenerateRandomAdminUserResponse> => {
  const generatedFirstname = firstname === null ? null : firstname || faker.person.firstName()
  const generatedLastname = lastname === null ? null : lastname || faker.person.lastName()

  const adminUser = await AdminUserModel.create({
    firstname: generatedFirstname,
    lastname: generatedLastname,
    email: faker.internet.email(),
  })

  const payload: ContextUser = {
    id: adminUser._id.toString(),
    email: adminUser.email,
    admin: true,
    role: UserRole.ADMIN,
  }

  return {
    user: adminUser,
    tokens: {
      accessToken: jwt.sign(payload, config.jwt.secret, { expiresIn: '9000d' }),
    },
  }
}

export const getUserUpdateInput = async () => {
  const randomEmail = faker.internet.email()
  const team = await TeamModel.findOne({ name: 'Team 1' })

  return {
    firstname: 'Evzen',
    lastname: 'Hnizdo',
    dateOfBirth: new Date('2010-03-25'),
    country: 'CZ',
    contact: {
      firstname: 'Jana',
      lastname: 'Hnizdova',
      email: randomEmail,
      phoneNumber: {
        countryPrefix: '421',
        number: '123456789',
      },
      type: ContactType.Mother,
    },
    bio: {
      position: BioPositionGraphQL.Center,
      handedness: BioHandedness.Right,
      playerType: BioPlayerType.Hybrid,
      schoolType: BioSchoolType.GraduateSchool,
      gender: BioGender.Male,
      height: {
        feet: 9,
        inches: 2,
        centimeters: 185,
      },
      weight: {
        pounds: 100,
        kilograms: 60,
      },
      birthplace: 'Náchod',
      nameOfSchool: 'SPŠ Stavební Náchod',
      graduationYear: 2015,
      cumulativeGPA: 1,
      coreGPA: 2,
      unweightedGPA: 3,
      weightedGPA: 4,
      satScore: 1100,
      actScore: 25,
      toeflScore: 20,
      ncaaEligibility: true,
      naiaEligibility: false,
    },
    phoneNumber: {
      countryPrefix: '421',
      number: '987654321',
    },
    externalLinks: [
      {
        name: 'Seznam',
        url: 'https://www.seznam.cz/',
        type: ExternalLinkType.Custom,
      },
    ],
    boostMessage: 'test boostmessage',
    boost: {
      currency: 'EUR',
      goalAmount: 15000,
    },
    currentTeam: team?.id,
  }
}

export async function createUserViews(user: User, views: number, date: Date) {
  const clock = sinon.useFakeTimers(moment(date).utc().startOf('month').startOf('day').toDate())

  for (let i = 0; i < views; i++) {
    clock.tick(4 * 60 * 60 * 1000)

    await client().visitUser({
      visitUserId: user._id,
    })
  }

  clock.restore()
}

export const userStatsQuery = (
  teamSlug: string = 'team-slug',
  id = '1',
  edges: EPPlayerStats[] = [
    {
      id,
      leagueName: 'NHL',
      leagueType: 'Professional',
      postseasonType: 'Regular Season',
      league: {
        slug: 'nhl',
        name: 'National Hockey League',
        country: {
          iso_3166_1_alpha_2: 'US',
        },
      },
      team: {
        slug: teamSlug,
        name: 'Team Name',
        country: {
          iso_3166_1_alpha_2: 'CA',
        },
        logo: {
          large: 'https://www.example.com/logo.png',
        },
      },
      season: {
        slug: '2023-2024',
        startYear: 2023,
        endYear: 2024,
      },
      regularStats: {
        GP: 82,
        G: 30,
        A: 45,
        L: 0,
        PIM: 25,
        PM: 20,
        W: 0,
        T: 0,
        GAA: 2.5,
        SVP: 0.915,
        SO: 5,
      },
      postseasonStats: {
        GP: 20,
        G: 10,
        A: 15,
        L: 0,
        PIM: 5,
        PM: 10,
        W: 0,
        T: 0,
        GAA: 2.0,
        SVP: 0.92,
        SO: 2,
      },
    },
  ]
) => {
  return {
    data: {
      data: {
        playerStats: {
          edges,
        },
      },
    },
  }
}
