import { WallVideoModel } from '../../src/modules/user/models'
import { User } from '../../src/modules/user/models/user'
import { WallVideoSegmentation } from '../../src/modules/user/models/wallVideo'
import { WallVideoType } from '../graphql/components'

type GenerateWallVideoInput = {
  user: Partial<User>
  path: string
  title?: string
  createdAt?: Date
  width?: number
  height?: number
  type?: WallVideoType
  size?: number
  duration?: number
  thumbnail?: string
  segmentation?: Partial<WallVideoSegmentation>
}

export const generateWallVideo = async (input: GenerateWallVideoInput) => {
  await new Promise((resolve) => setTimeout(resolve, 1))
  return await WallVideoModel.create({
    user: input.user._id,
    title: input.title,
    createdAt: input.createdAt || new Date(),
    path: input.path,
    type: input.type || WallVideoType.Offensive,
    width: input.width || 640,
    height: input.height || 480,
    uploadFinished: true,
    size: input.size || 1000,
    duration: input.duration || 1500,
    thumbnail: input.thumbnail || 'defaultThumbnail',
    segmentation: input.segmentation,
  })
}

export const generateWallVideoWithSegmentation = async (
  user: User,
  path: string,
  width: number = 640,
  height: number = 480,
  type: WallVideoType = WallVideoType.Offensive,
  size: number = 1000,
  duration: number = 1500,
  thumbnail: string = 'defaultThumbnail',
  segmentation: WallVideoSegmentation = {
    path: 'defaultPath',
    finished: true,
    selectionX: 10,
    selectionY: 10,
    frame: 10,
    createdAt: new Date(),
  }
) => {
  return await WallVideoModel.create({
    user: user._id,
    path,
    type,
    width,
    height,
    uploadFinished: true,
    size,
    duration,
    segmentation,
    thumbnail,
  })
}
