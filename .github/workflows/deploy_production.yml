name: Deploy to production

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Setup ArgoCD CLI
      uses: imajeetyadav/argocd-cli@v1
    - name: Deploy to production
      run: |
        echo ${{ github.ref_name }}
        argocd login --username admin --password ${{ secrets.ARGO_CD_PASSWORD_PRODUCTION }} --grpc-web ${{ secrets.ARGO_CD_URL_PRODUCTION }}
        argocd app patch api --patch '[{"op": "replace", "path": "/spec/source/targetRevision", "value": "${{ github.ref_name }}"}]'
        argocd app set api -p image.tag=${{ github.ref_name }}
