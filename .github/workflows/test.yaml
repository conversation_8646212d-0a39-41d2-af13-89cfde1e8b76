name: Run tests

on:
  push:
    branches: [ main ]
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
    branches:
      - '*'

jobs:
  test:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v4
        with:
          node-version: 21.6
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Build the stack
        run: docker compose up -d
      - name: Install dependencies
        run: pnpm i
      - name: Build
        run: pnpm build
      - name: Linter
        run: pnpm lint
      - name: Tests
        run: pnpm coverage
      - name: SonarCloud Scan
        uses: SonarSource/sonarqube-scan-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} 
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

