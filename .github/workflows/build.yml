name: Build, push and deploy to staging

on:
  push:
    branches: [ main ]

jobs:
  build:
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: eu-west-1
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Bump version and push tag
        id: tag_version
        uses: mathieudutour/github-tag-action@v6.1
        with:
          github_token: ${{ secrets.GIT_TOKEN }}

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: eu-west-1
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Build and push to ECR
        run: |
          docker build -t api .
          docker tag api:latest 845702489680.dkr.ecr.eu-west-1.amazonaws.com/api:latest
          docker tag api:latest 845702489680.dkr.ecr.eu-west-1.amazonaws.com/api:${{ github.sha }}
          docker tag api:latest 845702489680.dkr.ecr.eu-west-1.amazonaws.com/api:${{steps.tag_version.outputs.new_tag}}
          docker push 845702489680.dkr.ecr.eu-west-1.amazonaws.com/api:latest
          docker push 845702489680.dkr.ecr.eu-west-1.amazonaws.com/api:${{ github.sha }}
          docker push 845702489680.dkr.ecr.eu-west-1.amazonaws.com/api:${{steps.tag_version.outputs.new_tag}}
      - name: Setup ArgoCD CLI
        uses: imajeetyadav/argocd-cli@v1
      - name: Deploy staging
        run: |
          argocd login --username admin --password ${{ secrets.ARGO_CD_PASSWORD }} --grpc-web ${{ secrets.ARGO_CD_URL_STAGING }}
          argocd app patch api --patch '[{"op": "replace", "path": "/spec/source/targetRevision", "value": "${{steps.tag_version.outputs.new_tag}}"}]'
          argocd app set api -p image.tag=${{steps.tag_version.outputs.new_tag}}
      - name: Upload source maps to Sentry
        run: |
          pnpm i
          pnpm build
          pnpm sentry:sourcemaps