{"name": "api", "version": "1.0.0", "main": "index.js", "license": "MIT", "ts-node": {"esm": true}, "scripts": {"lint": "eslint .", "format": "eslint . --fix", "lint:quiet": "eslint . --quiet", "lint:fix": "eslint . --fix", "test": "NODE_ENV=test mocha --config=tests/.mocharc.json 'tests/e2e/**/*.ts' 'tests/unit/**/*.ts'", "test:f": "NODE_ENV=test mocha --config=tests/.mocharc.json", "prepush": "pnpm lint:quiet && pnpm tsc && pnpm test", "coverage": "nyc pnpm test", "codegen": "graphql-codegen --config codegen.ts", "build": "tsc", "start": "node $NODE_DEBUG_OPTION ./dist/index.js ", "dev": "nodemon src/index.ts | pino-pretty", "docker:db:up": "docker-compose -f docker-compose.local.yaml up -d", "docker:db:down": "docker-compose -f docker-compose.local.yaml down", "db:dev": "pnpm run docker:db:up && pnpm run dev", "job:sync-stats": "node ./dist/jobs syncStats", "job:push-notifications": "node ./dist/jobs pushNotifications", "job:remove-deleted-users": "node ./dist/jobs removeDeletedUsers", "job:crawler": "node ./dist/jobs crawler", "job:scraper": "node ./dist/jobs scraper", "job:rescrape-games": "node ./dist/src/jobs rescrapeGames", "job:rescrape-scheduled": "node ./dist/jobs rescrapeScheduled", "job:sync-games": "node ./dist/jobs syncGames", "job:generate-sitemaps": "node ./dist/jobs generateSitemaps", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org graet --project api ./dist && sentry-cli sourcemaps upload --org graet --project api ./dist", "job:send-batch-notification": "node ./dist/jobs sendBatchNotification", "dev:job": "NODE_ENV=dev npx ts-node src/jobs/index.ts", "job:mark-e2e-tester-account-for-deletion": "node ./dist/jobs markE2eTesterAccountForDeletion"}, "dependencies": {"@amplitude/analytics-node": "^1.3.6", "@apollo/cache-control-types": "^1.0.3", "@apollo/server": "^4.11.2", "@aws-sdk/client-mediaconvert": "^3.699.0", "@aws-sdk/client-s3": "^3.701.0", "@aws-sdk/client-ses": "^3.699.0", "@aws-sdk/client-sns": "^3.699.0", "@aws-sdk/client-sqs": "^3.716.0", "@aws-sdk/s3-request-presigner": "^3.701.0", "@fastify/cors": "^11.0.1", "@fastify/request-context": "^6.2.0", "@langchain/core": "^0.3.26", "@langchain/openai": "^0.3.16", "@nitra/as-integrations-fastify": "^3.0.2", "@promster/apollo": "^14.0.0", "@promster/server": "^14.0.0", "@sentry/cli": "^2.42.0", "@sentry/node": "^9.1.0", "@sentry/profiling-node": "^9.22.0", "@slack/web-api": "^7.7.0", "@t.voslar/ts-cache-decorator": "^2.2.1", "@typegoose/typegoose": "12.8.0", "@types/validator": "^13.12.2", "apple-signin-auth": "^1.7.8", "axios": "^1.7.9", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "commander": "^12.1.0", "config": "^3.3.12", "csv-parse": "^5.6.0", "dataloader": "^2.2.3", "dayjs": "^1.11.13", "email-validator": "^2.0.4", "express": "^4.21.2", "fastify": "^5.3.2", "fastify-fingerprint": "^1.4.1", "getstream": "^8.4.1", "google-auth-library": "^9.15.0", "graphql": "^16.9.0", "graphql-parse-resolve-info": "^4.13.0", "graphql-request": "^6.1.0", "graphql-scalars": "^1.24.0", "graphql-tag": "^2.12.6", "https": "^1.0.0", "i18n-iso-countries": "^7.14.0", "jsdom": "^25.0.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.7", "latinize": "^1.0.0", "lodash": "^4.17.21", "moment": "^2.30.1", "mongo-dot-notation": "^3.1.1", "mongoose": "^8.9.5", "mongoose-delete-ts": "^2.2.2", "mustache": "^4.2.0", "openai": "^4.73.1", "pino": "^8.21.0", "prom-client": "^15.1.3", "puppeteer-core": "^23.10.1", "qs": "^6.13.1", "reflect-metadata": "^0.2.2", "sharp": "^0.33.5", "slugify": "^1.6.6", "stream-chat": "^8.49.0", "string-strip-html": "^8.5.0", "stripe": "^14.25.0", "ts-patch-mongoose": "^2.9.0", "type-graphql": "2.0.0-rc.2", "typedi": "^0.10.0", "validator": "^13.12.0", "zod": "^3.23.8"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript-graphql-request": "^6.2.0", "@types/bunyan": "^1.8.11", "@types/config": "^3.3.5", "@types/express": "^4.17.21", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.7", "@types/latinize": "^0.2.18", "@types/lodash": "^4.17.16", "@types/mocha": "^10.0.10", "@types/mustache": "^4.2.5", "@types/node": "^22.10.1", "@types/qs": "^6.9.17", "@types/sinon": "^17.0.3", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "mocha": "^11.1.0", "nock": "^14.0.2", "nodemon": "^3.1.9", "nyc": "^17.1.0", "pino-pretty": "^13.0.0", "prettier": "^3.4.1", "sinon": "^18.0.1", "ts-node": "^10.9.2", "typescript": "^5.4.2"}, "nyc": {"statements": 0, "branches": 0, "functions": 0, "lines": 0, "watermarks": {"lines": [80, 90], "functions": [80, 90], "branches": [80, 90], "statements": [80, 90]}, "exclude": ["node_modules", "config", "coverage", "tests", "docs", "i18n", "build", "dist", "infra", "**/*.d.ts", "src/migrations"], "extension": ["*.js", ".ts"], "reporter": ["lcov", "text-summary"], "cache": true, "all": true, "check-coverage": true, "report-dir": "./coverage"}}