FROM node:22.12.0-alpine AS node

FROM node AS build

WORKDIR /usr/app

COPY package.json pnpm-lock.yaml ./
COPY tsconfig.json ./
COPY ./src ./src

RUN apk add --no-cache --virtual .gyp \
        python3 \
        git \
        make \
        g++ 

RUN wget -qO /bin/pnpm "https://github.com/pnpm/pnpm/releases/latest/download/pnpm-linuxstatic-x64" && chmod +x /bin/pnpm

RUN pnpm i --frozen-lockfile
RUN pnpm build

FROM node AS production

WORKDIR /usr/app

RUN wget -qO /bin/pnpm "https://github.com/pnpm/pnpm/releases/latest/download/pnpm-linuxstatic-x64" && chmod +x /bin/pnpm

RUN apk add chromium        

COPY package.json pnpm-lock.yaml ./

RUN NODE_ENV=production pnpm i

COPY config ./config
COPY assets ./assets
COPY --from=build /usr/app/dist ./dist

CMD [ "node", "./dist/index.js" ]
