{
  "ts-node": {
    "files": true
  },
  "compilerOptions": {
    "module": "commonjs",
    "lib": ["es2021", "dom"],
    "target": "es2021",
    "esModuleInterop": true,
    "allowJs": false,
    "sourceMap": true,
    "outDir": "dist",
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "strict": true,
    "strictPropertyInitialization": false,
    "useUnknownInCatchVariables": false,
    "inlineSources": true,
    "resolveJsonModule": true,

    // Set `sourceRoot` to  "/" to strip the build path prefix
    // from generated source code references.
    // This improves issue grouping in Sentry.
    "sourceRoot": "/"
  },
  "include": ["src", "tests"],
  "exclude": ["node_modules", "docs", "coverage", ".nyc_output"]
}
