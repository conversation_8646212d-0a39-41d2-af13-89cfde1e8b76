{"mongo": {"url": ""}, "logging": {"level": "info"}, "assetsUrl": "https://assets.graet.com/", "webUrl": "https://www.graet.com/", "aws": {"assetsBucket": "graet-production-assets", "emailFrom": "GRAET <<EMAIL>>", "sns": {"androidArn": "arn:aws:sns:eu-west-1:************:app/GCM/Graet", "iosArn": "arn:aws:sns:eu-west-1:************:app/APNS/Graet"}, "sqs": {"segmentationQueueUrl": "https://sqs.eu-west-1.amazonaws.com/************/VideoSegmentation-production.fifo"}}, "domain": "graet.com", "jwt": {"secret": "i5qFJaT14", "expiresIn": "1d"}, "slack": {"token": "", "channels": {"messages": "db-dm", "newAccount": "db-registrations", "newStripeAccount": "db-updates", "videoUpload": "db-updates", "externalLinks": "shadow-player-account-claims", "boost": "db-boost", "deleteAccount": "db-updates", "dbUserAgency": "db-user-agency", "shadowAccountClaim": "shadow-player-account-claims", "gameStatsNotifications": "db-game-notifications", "dbProfessionals": "db-professionals"}}, "amplitude": {"apiKey": ""}, "stripe": {"secretKey": "", "publishableKey": "", "webhookConnectSecret": "", "url": "https://dashboard.stripe.com/connect/accounts/"}, "emailTemplates": {"VerifyEmail": "VerifyEmail", "ContactMessage": "ContactMessage", "BoostProfileSender": "BoostProfileSender", "BoostProfileReceiver": "BoostProfileReceiver", "AgentInvitation": "AgentInvitation", "UnreadMessage": "UnreadChatMessage"}, "pushNotifications": {"delayInMinutes": 30}, "segmentation": {"url": "https://sapi.graet.com"}, "getstream": {"apiKey": "key", "apiSecret": "secret", "appId": "1358469"}, "cache": {"memcached": {"url": "memcached.default.svc.cluster.local:11211"}}, "oxylabs": {"username": "", "password": ""}}