# Discovery System

This document provides an overview of the Graet discovery system, which consists of a crawler for generating discovery URLs and a scraper for extracting and processing data from these URLs.

## System Overview

The discovery system is responsible for:
1. Discovering URLs of interest through web crawling
2. Scraping data from these URLs
3. Processing and storing the extracted data
4. Scheduling re-scraping of dynamic content

The system handles several entity types:
- Games
- Players
- Leagues
- Player Stats

## Architecture

```mermaid
flowchart TD
    subgraph DS["Discovery System"]
        direction TB
        Crawler["Crawler Job"]
        Scraper["Scraper Job"]
        RescrapeSched["Rescrape Scheduled Job"]
        SyncEntities["Sync Entities Jobs"]
    end

    subgraph DB["Database"]
        direction TB
        DiscoveredURL["DiscoveredURL Collection"]
        Game["Game Collection"]
        UserInfo["UserInfo Collection"]
        LeagueStats["LeagueStats Collection"]
        UserStats["UserStats Collection"]
    end

    subgraph SV["Services"]
        direction TB
        GameSyncer["GameSyncer Service"]
        UserInfoSyncer["UserInfoSyncer Service"]
        LeagueStatsSyncer["LeagueStatsSyncer Service"]
        UserStatsSyncer["UserStatsSyncer Service"]
        ScraperService["Scraper Service"]
    end

    DS --- DB
    DB --- SV
```

## Crawler Component

The crawler is responsible for discovering URLs of interest based on configured patterns and rules.

```mermaid
sequenceDiagram
    participant CrawlerJob
    participant WebCrawler
    participant DB as Database

    CrawlerJob->>DB: Get CrawlerDomainConfigs

    loop For each CrawlerDomainConfig
        CrawlerJob->>WebCrawler: Create crawler with config
        WebCrawler->>WebCrawler: Initialize with urlConfigs

        loop For each initialUrl
            WebCrawler->>WebCrawler: Add to crawl queue
        end

        WebCrawler->>WebCrawler: Process queue

        loop For each URL in queue
            WebCrawler->>WebCrawler: Fetch URL content
            WebCrawler->>WebCrawler: Extract links
            WebCrawler->>WebCrawler: Apply paramExtractors
            WebCrawler->>WebCrawler: Match against urlPatternRegExp
            WebCrawler->>WebCrawler: Add matching URLs to results
            WebCrawler->>WebCrawler: Add new URLs to queue

            alt Batch size reached
                WebCrawler->>CrawlerJob: Process batch
                CrawlerJob->>DB: Bulk upsert DiscoveredURLs
            end
        end

        WebCrawler->>CrawlerJob: Process final batch
        CrawlerJob->>DB: Bulk upsert DiscoveredURLs
    end
```

## Scraper Component

The scraper processes discovered URLs and extracts structured data from them.

```mermaid
flowchart TD
    subgraph "Scraper Job Flow"
        SJ[Scraper Job]
        RSJ[Rescrape Scheduled Job]
        Process[Processing Logic]

        SJ -->|Find URLs without scraperInfo| Process
        RSJ -->|Find URLs with scheduledForScrapeAt <= now| Process

        Process --> Switch{Entity Type?}
        Switch -->|GAME| GameSyncer[GameSyncer]
        Switch -->|PLAYER| UserInfoSyncer
        Switch -->|LEAGUE| LeagueSyncer[LeagueStatsSyncer]
        Switch -->|PLAYER_STATS| UserStatsSyncer
    end

    subgraph "Syncer Service Flow"
        Syncer[BaseSyncer]
        Scraper[Scraper Service]
        Puppeteer[Puppeteer]
        Parser[Response Parser]
        Result[Parsed Result]
        DB[(Database)]

        Syncer -->|Uses| Scraper
        Scraper -->|Uses| Puppeteer
        Puppeteer -->|Executes extraction script| Parser
        Parser -->|Validates with zod schema| Result
        Syncer -->|Creates/Updates| DB
        Syncer -->|Updates scraperInfo| DB
    end
```

### Key Components

#### Scraper Jobs
- **scraper.ts**: Processes URLs without scraperInfo
- **rescrape-scheduled.ts**: Processes URLs with scheduledForScrapeAt

#### Syncer Services
- **GameSyncerService**: Processes game data
- **UserInfoSyncerService**: Processes player information
- **LeagueStatsSyncerService**: Processes league standings
- **UserStatsSyncerService**: Processes player statistics

## Data Flow

```mermaid
sequenceDiagram
    participant SJ as Scraper Job
    participant SS as Syncer Service
    participant SC as Scraper
    participant PB as Puppeteer Browser
    participant DB as Database

    SJ->>DB: Find URLs to scrape

    loop For each URL
        SJ->>SS: syncData(url)
        SS->>SC: getEntityData(url, extractionScript)
        SC->>PB: Setup browser if needed
        SC->>PB: Open page with URL
        SC->>PB: Execute extraction script
        PB-->>SC: Return extracted data
        SC->>SC: Validate with schema
        SC-->>SS: Return structured data

        alt Data extraction successful
            SS->>DB: Create or update entity
            SS->>DB: Update URL with scraperInfo (success)

            alt Current season or future game
                SS->>DB: Set scheduledForScrapeAt
            end
        else Data extraction failed
            SS->>DB: Update URL with scraperInfo (failure)

            alt Not max attempts
                SS->>DB: Set scheduledForScrapeAt
            end
        end
    end
```

## DiscoveredURL Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Discovered: Crawler finds URL

    Discovered --> Pending: URL saved to database
    Pending --> Scraping: Scraper job picks up URL

    Scraping --> Success: Data extracted successfully
    Scraping --> Failure: Data extraction failed

    Success --> Completed: Static content
    Success --> Scheduled: Dynamic content

    Failure --> Scheduled: Retry scheduled
    Failure --> Abandoned: Max attempts reached

    Scheduled --> Scraping: Rescrape job picks up URL

    Completed --> [*]
    Abandoned --> [*]
```

## Configuration

### CrawlerDomainConfig Example
```json
{
  "domain": "ceskyhokej.cz",
  "urlConfigs": [
    {
      "entity": "PLAYER_STATS",
      "initialUrls": ["https://ceskyhokej.cz/competition/player-stats/1"],
      "externalIdPatternRegExp": "\\/player-stats\\/(\\d+)",
      "urlPatternRegExp": "^https:\\/\\/.*ceskyhokej\\.cz\\/competition\\/player-stats\\/\\d+.*$",
      "allowedQueryParams": [
        "playerCompetitionStatsWidget-filter[competitionPart]",
        "playerCompetitionStatsWidget-page"
      ],
      "paramExtractors": [
        {
          "selector": "select[name=\"competitionPart\"]",
          "queryParam": "playerCompetitionStatsWidget-filter[competitionPart]"
        }
      ],
      "extractionScript": "function() { /* JavaScript to extract data */ }"
    }
  ]
}
```
