# Graet API

[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=alert_status&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Maintainability Rating](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=sqale_rating&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Technical Debt](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=sqale_index&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Coverage](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=coverage&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Code Smells](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=code_smells&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Duplicated Lines (%)](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=duplicated_lines_density&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Lines of Code](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=ncloc&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Reliability Rating](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=reliability_rating&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Security Rating](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=security_rating&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api) [![Vulnerabilities](https://sonarcloud.io/api/project_badges/measure?project=graet_api&metric=vulnerabilities&token=90fadaa17237a58e60c602611d38cfec04e12253)](https://sonarcloud.io/summary/new_code?id=graet_api)

## Installation

This repository is using asdf for version management. You can install asdf using `brew install asdf` and then run `asdf install` to install all required versions.

`pnpm i`
`pnpm dev`

## E2E testing

Run `pnpm test` to run e2e tests which are calling our GraphQL API.
Run `pnpm test:f {path to specific test file, of glob}` to run e2e tests which are calling our GraphQL API in specific folder or file.
Locally you need to first run `docker-compose up -d` to spin up mongodb.

To see coverage run `pnpm coverage` which will generate `coverage` folder with `index.html` file which can be opened in browser to see details.

Whenever you need to test new Query/Mutation you need to add client GraphQL definition into `tests/graphql` folder, after adding `.graphql` file run `pnpm codegen` which will generate operation and it's definition.

## Nodejs upgrade

To upgrade nodejs update version in .tool-versions, Dockerfile and package.json (types). After that you need to install new version of nodejs using `asdf install` and run `pnpm i` to install new dependencies.

You might need to install pnpm globally using `npm i -g pnpm` if you don't have it installed.

## Sync indexes

To sync indexes you need to set `SYNC_INDEXES=true` environment variable. It is necessary to run it for staging and production environment.
