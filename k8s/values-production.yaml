name: api
environment: production
minReplicas: 2
maxReplicas: 15
image:
  repository: api
  tag: latest
container:
  port: 5010
resources:
  requests:
    cpu: 150m
    memory: 256Mi
  limits:
    cpu: 800m
    memory: 700Mi

ingress:
  host: api.graet.com
  secretName: api

httpGet:
  path: /health

aws:
  region: eu-west-1

externalDns:
  exclude: true

cronJobs:
  syncStats:
    name: "sync-stats"
    schedule: "*/15 * * * *"
    command:
    - pnpm
    - job:sync-stats
  pushNotifications:
    name: "push-notifications"
    schedule: "*/10 * * * *"
    command:
    - pnpm
    - job:push-notifications
    nodeSelector:
      job-type: jobs-low-tier
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule
  removeDeletedUsers:
    name: "remove-deleted-users"
    schedule: "*/1 * * * *"
    command:
    - pnpm
    - job:remove-deleted-users
    nodeSelector:
      job-type: jobs-low-tier
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule
  crawler:
    name: "crawler"
    schedule: "0 0 * * 0"
    command:
    - pnpm
    - job:crawler
    nodeSelector:
      job-type: jobs
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule
  scraper:
    name: "scraper"
    schedule: "*/10 * * * *"
    command:
    - pnpm
    - job:scraper
    nodeSelector:
      job-type: jobs-low-tier
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule
  rescrape-scheduled:
    name: "rescrape-scheduled"
    schedule: "*/5 * * * *"
    command:
    - pnpm
    - job:rescrape-scheduled
    nodeSelector:
      job-type: jobs-low-tier
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule
  sync-games:
    name: "sync-games"
    schedule: "*/5 * * * *"
    command:
    - pnpm
    - job:sync-games
    nodeSelector:
      job-type: jobs-low-tier
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule
  generate-sitemaps:
    name: "generate-sitemaps"
    schedule: "0 0 * * *"
    command:
    - pnpm
    - job:generate-sitemaps
    nodeSelector:
      job-type: jobs-low-tier
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule
  send-batch-notification:
    name: "send-batch-notification"
    schedule: "*/1 * * * *"
    command:
    - pnpm
    - job:send-batch-notification
    nodeSelector:
      job-type: jobs-low-tier
    tolerations:
    - key: jobs
      operator: Exists
      effect: NoSchedule

alb:
  group: mainproduction

env:
- name: mongodb-ext-secret
  data:
    MONGO_DB_URI: url
- name: ep-ext-secret
  data:
    EP_API_KEY: apiKey
- name: geoip-ext-secret
  data:
    GEOIP_KEY: key
- name: amplitude-ext-secret
  data:
    AMPLITUDE_API_KEY: apiKey
- name: slack-ext-secret
  data:
    SLACK_TOKEN: token
- name: openai-ext-secret
  data:
    OPENAI_API_KEY: apiKey
- name: stripe-ext-secret
  data:
    STRIPE_SECRET_KEY: secretKey
    STRIPE_PUBLISHABLE_KEY: publishableKey
    STRIPE_WEBHOOK_CONNECT_SECRET: webhookConnectSecret
- name: chatstream-ext-secret
  data:
    CHATSTREAM_API_KEY: apiKey
    CHATSTREAM_SECRET_KEY: secretKey
- name: oxylabs-ext-secret
  data:
    OXYLABS_USERNAME: username
    OXYLABS_PASSWORD: password

consumer:
  name: segmentation-consumer
  command:
  - node
  - dist/consumers/video-segmentation/index.js
  container:
    port: 5012
  resources:
    requests:
      cpu: 200m
      memory: 200Mi
    limits:
      cpu: 400m
      memory: 400Mi
  liveness:
    path: /live
    delay: 5
    timeout: 5
    period: 10
  readiness:
    path: /health
    delay: 5
    timeout: 5
    period: 10
