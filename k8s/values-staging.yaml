name: api
environment: staging
minReplicas: 2
image:
  repository: api
  tag: latest
container:
  port: 5010
resources:
  requests:
    cpu: 150m
    memory: 256Mi
  limits:
    cpu: 800m
    memory: 700Mi

ingress:
  host: api.graet.dev
  secretName: api

httpGet:
  path: /health

aws:
  region: eu-west-1

externalDns:
  exclude: true

cronJobs:
  syncStats:
    name: "sync-stats"
    schedule: "30 * * * *"
    command:
    - pnpm
    - job:sync-stats
  pushNotifications:
    name: "push-notifications"
    schedule: "* * * * *"
    command:
    - pnpm
    - job:push-notifications
  removeDeletedUsers:
    name: "remove-deleted-users"
    schedule: "*/10 * * * *"
    command:
    - pnpm
    - job:remove-deleted-users
  crawler:
    name: "crawler"
    schedule: "0 0 * * 0"
    command:
    - pnpm
    - job:crawler
  scraper:
    name: "scraper"
    schedule: "*/10 * * * *"
    command:
    - pnpm
    - job:scraper
  rescrape-scheduled:
    name: "rescrape-scheduled"
    schedule: "*/5 * * * *"
    command:
    - pnpm
    - job:rescrape-scheduled
  sync-games:
    name: "sync-games"
    schedule: "*/5 * * * *"
    command:
    - pnpm
    - job:sync-games
  generate-sitemaps:
    name: "generate-sitemaps"
    schedule: "0 0 * * *"
    command:
    - pnpm
    - job:generate-sitemaps
  send-batch-notification:
    name: "send-batch-notification"
    schedule: "*/15 * * * *"
    command:
    - pnpm
    - job:send-batch-notification
  mark-e2e-tester-account-for-deletion:
    name: "mark-e2e-tester-account-for-deletion"
    schedule: "5 0 * * *"
    command:
    - pnpm
    - job:mark-e2e-tester-account-for-deletion

env:
- name: mongodb-ext-secret
  data:
    MONGO_DB_URI: url
- name: ep-ext-secret
  data:
    EP_API_KEY: apiKey
- name: geoip-ext-secret
  data:
    GEOIP_KEY: key
- name: amplitude-ext-secret
  data:
    AMPLITUDE_API_KEY: apiKey
- name: slack-ext-secret
  data:
    SLACK_TOKEN: token
- name: openai-ext-secret
  data:
    OPENAI_API_KEY: apiKey
- name: stripe-ext-secret
  data:
    STRIPE_SECRET_KEY: secretKey
    STRIPE_PUBLISHABLE_KEY: publishableKey
    STRIPE_WEBHOOK_CONNECT_SECRET: webhookConnectSecret
- name: chatstream-ext-secret
  data:
    CHATSTREAM_API_KEY: apiKey
    CHATSTREAM_SECRET_KEY: secretKey
- name: oxylabs-ext-secret
  data:
    OXYLABS_USERNAME: username
    OXYLABS_PASSWORD: password

consumer:
  name: segmentation-consumer
  command:
  - node
  - dist/consumers/video-segmentation/index.js
  container:
    port: 5012
  resources:
    requests:
      cpu: 200m
      memory: 200Mi
    limits:
      cpu: 400m
      memory: 400Mi
  liveness:
    path: /live
    delay: 5
    timeout: 5
    period: 10
  readiness:
    path: /health
    delay: 5
    timeout: 5
    period: 10
