import type { CodegenConfig } from '@graphql-codegen/cli'

const config: CodegenConfig = {
  overwrite: true,
  schema: "http://localhost:5010/",
  // schema: "http://0.0.0.0:5010/",
  documents: 'tests/graphql/**/*.graphql',
  generates: {
    'tests/graphql/components.ts': {
      config: {
        withHooks: false,
      },
      plugins: [
        'typescript',
        'typescript-operations',
        'typescript-graphql-request'
      ],
    },
  },
}

export default config
